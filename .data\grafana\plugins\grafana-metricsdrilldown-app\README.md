# Grafana Metrics Drilldown

The Grafana Metrics Drilldown app provides a queryless experience for browsing Prometheus-compatible metrics. Quickly find related metrics without writing PromQL queries.

## Requirements

Requires Grafana 11.6.0 or newer.

## Getting Started

See the [docs](https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/metrics/#standalone-experience) for more info using Grafana Metrics Drilldown.

## Documentation

- [DOCS](https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/metrics)
- [CHANGELOG](https://github.com/grafana/metrics-drilldown/releases)
- [GITHUB](https://github.com/grafana/metrics-drilldown)

## Contributing

We love accepting contributions! If your change is minor, please feel free submit a [pull request](https://help.github.com/articles/about-pull-requests/). If your change is larger, or adds a feature, please file an issue beforehand so that we can discuss the change. You're welcome to file an implementation pull request immediately as well, although we generally lean towards discussing the change and then reviewing the implementation separately.

### Bugs

If your issue is a bug, please open one [here](https://github.com/grafana/metrics-drilldown/issues/new).

### Changes

We do not have a formal proposal process for changes or feature requests. If you have a change you would like to see in Grafana Metrics Drilldown, please [file an issue](https://github.com/grafana/metrics-drilldown/issues/new) with the necessary details.
