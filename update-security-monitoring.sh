#!/bin/bash
# CTFd Security Monitoring - Update/Fix Script
# Run this script anytime to ensure the security monitoring is properly configured

set -e

echo "================================================"
echo "CTFd Security Monitoring - Update & Fix"
echo "================================================"
echo

# Detect docker-compose command
if docker compose version >/dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

# Function to backup file
backup_file() {
    local file=$1
    if [ -f "$file" ]; then
        cp "$file" "${file}.backup.$(date +%Y%m%d_%H%M%S)"
        echo "  📋 Backed up $file"
    fi
}

# Fix docker-compose.yml
echo "🔧 Fixing docker-compose.yml..."

# Create a Python script to fix the docker-compose.yml properly
cat > fix_docker_compose.py << 'EOF'
#!/usr/bin/env python3
import yaml
import sys

def fix_docker_compose():
    with open('docker-compose.yml', 'r') as f:
        data = yaml.safe_load(f)
    
    # Remove version if it exists
    if 'version' in data:
        del data['version']
    
    # Fix CTFd environment
    if 'services' in data and 'ctfd' in data['services']:
        env = data['services']['ctfd'].get('environment', [])
        
        # Ensure security_monitor is in PLUGIN_WHITELIST
        whitelist_found = False
        for i, e in enumerate(env):
            if e.startswith('PLUGIN_WHITELIST='):
                if 'security_monitor' not in e:
                    env[i] = e.rstrip() + ',security_monitor'
                whitelist_found = True
                break
        
        if not whitelist_found:
            env.append('PLUGIN_WHITELIST=web_desktop,challenges,dynamic_challenges,flags,ctfd-whale,security_monitor')
        
        data['services']['ctfd']['environment'] = env
    
    # Fix promtail volumes
    if 'services' in data and 'promtail' in data['services']:
        volumes = [
            './monitoring/promtail-config.yaml:/etc/promtail/config.yml:ro',
            './.data/CTFd/logs:/var/log/CTFd:ro',
            './.data/nginx/logs:/var/log/nginx:ro'
        ]
        data['services']['promtail']['volumes'] = volumes
    
    # Write back
    with open('docker-compose.yml', 'w') as f:
        yaml.dump(data, f, default_flow_style=False, sort_keys=False)
    
    print("✅ docker-compose.yml fixed")

if __name__ == '__main__':
    try:
        fix_docker_compose()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
EOF

# Run the Python script if Python is available
if command -v python3 >/dev/null 2>&1; then
    python3 fix_docker_compose.py
    rm fix_docker_compose.py
else
    echo "  ⚠️  Python3 not available, using sed instead..."
    
    # Remove version line
    sed -i '/^version:/d' docker-compose.yml 2>/dev/null || true
    
    # Fix promtail paths
    sed -i 's|\.data/CTFd/logs:|./.data/CTFd/logs:|g' docker-compose.yml
    sed -i 's|\.data/nginx/logs:|./.data/nginx/logs:|g' docker-compose.yml
fi

# Ensure all directories exist
echo "📁 Ensuring directory structure..."
directories=(
    ".data/CTFd/logs"
    ".data/nginx/logs"
    ".data/prometheus"
    ".data/grafana"
    ".data/loki"
    "logs"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
done
chmod -R 755 .data/ logs/
echo "✅ Directories ready"

# Create empty log files if they don't exist
touch .data/CTFd/logs/security.log
touch .data/nginx/logs/access.log
touch .data/nginx/logs/error.log

# Quick health check function
health_check() {
    echo
    echo "🏥 Health Check:"
    
    # Check if containers are running
    local ctfd_running=$($DOCKER_COMPOSE ps ctfd 2>/dev/null | grep -c "Up" || echo "0")
    local grafana_running=$($DOCKER_COMPOSE ps grafana 2>/dev/null | grep -c "Up" || echo "0")
    
    if [ "$ctfd_running" -eq "1" ]; then
        echo "  ✅ CTFd is running"
        
        # Check if plugin loaded
        if $DOCKER_COMPOSE logs ctfd 2>&1 | tail -100 | grep -q "security_monitor"; then
            echo "  ✅ Security Monitor plugin detected in logs"
        else
            echo "  ⚠️  Security Monitor not found in recent logs"
        fi
    else
        echo "  ❌ CTFd is not running"
    fi
    
    if [ "$grafana_running" -eq "1" ]; then
        echo "  ✅ Grafana is running"
    else
        echo "  ❌ Grafana is not running"
    fi
}

# Run health check
health_check

# Ask user what to do
echo
echo "What would you like to do?"
echo "  1) Restart all services"
echo "  2) Restart only CTFd"
echo "  3) View logs"
echo "  4) Just check status"
echo "  5) Exit"
echo
read -p "Enter choice (1-5): " choice

case $choice in
    1)
        echo "Restarting all services..."
        $DOCKER_COMPOSE down
        $DOCKER_COMPOSE up -d
        sleep 10
        health_check
        ;;
    2)
        echo "Restarting CTFd..."
        $DOCKER_COMPOSE restart ctfd
        sleep 5
        health_check
        ;;
    3)
        echo "Recent CTFd logs:"
        $DOCKER_COMPOSE logs ctfd --tail 50
        ;;
    4)
        # Already did health check
        ;;
    5)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "Invalid choice"
        ;;
esac

echo
echo "================================================"
echo "Access Points:"
echo "  📊 CTFd Admin: http://localhost:8000/admin"
echo "  🔒 Security Monitor: http://localhost:8000/admin/plugins/security_monitor/dashboard"
echo "  📈 Grafana: http://localhost:3000 (admin/admin)"
echo "================================================"
