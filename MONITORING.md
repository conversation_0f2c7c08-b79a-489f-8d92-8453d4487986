# CTFd Complete Monitoring Setup

## Overview

This setup provides comprehensive monitoring for CTFd with:
- ✅ Security monitoring plugin with web dashboard
- ✅ Prometheus metrics collection
- ✅ Grafana visualization dashboards
- ✅ Loki log aggregation
- ✅ Nginx rate limiting

## Quick Start

```bash
# Make setup script executable
chmod +x setup-monitoring.sh

# Run the setup
./setup-monitoring.sh
```

## Components

### 1. Security Monitor Plugin
- **URL**: http://localhost:82/admin/monitor
- **Features**:
  - Real-time request tracking
  - Failed login monitoring
  - Rate limit detection
  - Security alerts
  - Prometheus metrics export

### 2. Grafana Dashboards
- **URL**: http://localhost:3000
- **Login**: admin/admin
- **Pre-configured Dashboard**: CTFd Security Dashboard
- **Metrics**:
  - Active users gauge
  - Request rate graph
  - Failed logins counter
  - Top endpoints pie chart
  - Response time histogram
  - Rate limit violation logs

### 3. Prometheus
- **URL**: http://localhost:9090
- **Metrics collected**:
  - `ctfd_requests_total`: Total requests
  - `ctfd_requests_by_endpoint`: Requests per endpoint
  - `ctfd_failed_logins`: Failed login attempts
  - `ctfd_active_users`: Active user count
  - `ctfd_response_time_seconds`: Response times

### 4. Loki
- **URL**: http://localhost:3100
- **Log sources**:
  - CTFd application logs
  - Nginx access/error logs
  - Docker container logs
  - Security plugin logs

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│      CTFd       │────▶│   Prometheus    │────▶│    Grafana     │
│  with Security  │     │                 │     │                │
│     Plugin      │     └─────────────────┘     └─────────────────┘
│                 │              ▲                        ▲
└─────────────────┘              │                        │
         │                       │                        │
         │                       │                        │
         ▼                       │                        │
┌─────────────────┐              │                        │
│                 │              │                        │
│   Log Files     │──────────────┴────────────────────────┘
│                 │                                       
└─────────────────┘◀──── Promtail ────▶ Loki
```

## Using the Dashboards

### Grafana Dashboard Features

1. **Active Users Gauge**: Shows current active users
2. **Request Rate Graph**: Requests per minute over time
3. **Failed Logins Counter**: Total failed login attempts
4. **Top Endpoints Pie Chart**: Most accessed endpoints
5. **Response Time Bar Chart**: Average response times
6. **Rate Limit Logs**: Real-time rate limit violations
7. **Failed Login Logs**: Recent failed login attempts

### Creating Alerts

1. In Grafana, go to a panel and click the alert icon
2. Set conditions (e.g., when failed logins > 10)
3. Configure notification channels (email, Slack, etc.)

## Monitoring Best Practices

### What to Monitor

1. **Security Events**:
   - Failed login spikes (possible brute force)
   - Rate limit violations (possible DDoS)
   - Unusual endpoint access patterns

2. **Performance**:
   - Response times > 1 second
   - Request rate anomalies
   - Container resource usage

3. **Availability**:
   - CTFd service health
   - Database connectivity
   - Container status

### Alert Thresholds

- **Failed Logins**: > 10 in 5 minutes
- **Rate Limits**: > 50 violations in 5 minutes
- **Response Time**: > 2 seconds average
- **Active Users**: > 100 concurrent
- **Container Count**: > 50 active

## Troubleshooting

### Plugin Not Loading?
```bash
# Check logs
docker logs ctf-paltform-ctfd-1

# Verify plugin is in whitelist
grep PLUGIN_WHITELIST docker-compose.yml
```

### No Metrics in Prometheus?
```bash
# Check if metrics endpoint works
curl http://localhost:82/admin/monitor/api/prometheus

# Check Prometheus targets
http://localhost:9090/targets
```

### Grafana Dashboard Empty?
1. Check data sources are configured
2. Verify Prometheus is collecting data
3. Check time range in Grafana

### Logs Not Appearing?
```bash
# Check Promtail
docker logs ctf-paltform-promtail-1

# Verify log files exist
ls -la .data/CTFd/logs/
ls -la .data/nginx/logs/
```

## Customization

### Adding Custom Metrics

Edit `CTFd/plugins/security_monitor/__init__.py`:
```python
# Add new metric
metrics.increment('custom_metric', labels='value')

# Export in prometheus endpoint
output.append(f'ctfd_custom_metric{{label="value"}} {count}')
```

### Creating Custom Dashboards

1. Create new dashboard in Grafana
2. Add panels with Prometheus queries
3. Save and export as JSON
4. Place in `monitoring/grafana/provisioning/dashboards/`

## Security Notes

- Change Grafana admin password after first login
- Restrict access to monitoring endpoints in production
- Monitor the monitors - ensure Prometheus/Grafana stay healthy
- Regularly review security alerts and adjust thresholds

## Resource Usage

- Prometheus: ~256MB RAM
- Grafana: ~256MB RAM
- Loki: ~256MB RAM
- Promtail: ~128MB RAM
- **Total**: ~900MB additional RAM for monitoring

Worth it for comprehensive visibility! 📊
