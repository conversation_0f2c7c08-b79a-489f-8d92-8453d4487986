#!/bin/bash
# Debug Security Monitor data collection

echo "=== Security Monitor Data Debug ==="
echo

# Check if Prometheus endpoint is returning metrics
echo "📊 Checking Prometheus metrics endpoint:"
curl -s http://localhost:8000/admin/plugins/security_monitor/api/prometheus 2>/dev/null | head -20
echo

# Check if tables were created
echo "🗄️ Checking database tables:"
docker compose exec db mysql -uctfd -pctfd ctfd -e "SHOW TABLES LIKE '%security%';" 2>/dev/null || echo "Could not connect to database"

# Check if there's any data in the tables
echo
echo "📈 Checking security events data:"
docker compose exec db mysql -uctfd -pctfd ctfd -e "SELECT COUNT(*) as count FROM security_events;" 2>/dev/null || echo "No security_events table or no data"

echo
echo "🚨 Checking security alerts data:"
docker compose exec db mysql -uctfd -pctfd ctfd -e "SELECT COUNT(*) as count FROM security_alerts;" 2>/dev/null || echo "No security_alerts table or no data"

# Generate some test events
echo
echo "🔧 Generating test security events..."

# Try a few failed logins
echo "  - Attempting failed logins..."
for i in {1..5}; do
    curl -s -X POST http://localhost:8000/login \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "name=baduser&password=wrongpass" > /dev/null 2>&1
    echo -n "."
done
echo " Done"

# Generate some rapid requests to trigger rate limits
echo "  - Generating rapid requests..."
for i in {1..20}; do
    curl -s http://localhost:8000/login > /dev/null 2>&1 &
done
wait
echo "  Done"

# Wait for data to be processed
sleep 5

# Check metrics again
echo
echo "📊 Checking metrics after generating events:"
curl -s http://localhost:8000/admin/plugins/security_monitor/api/prometheus 2>/dev/null | grep -E "ctfd_|# HELP" | head -20

# Check Prometheus targets
echo
echo "🎯 Checking Prometheus target status:"
curl -s http://localhost:9090/api/v1/targets | jq '.data.activeTargets[] | select(.labels.job=="ctfd") | {job: .labels.job, health: .health, lastError: .lastError}' 2>/dev/null || echo "Could not check Prometheus targets"
