#!/bin/bash
# setup-monitoring.sh - Complete monitoring setup for CTFd

echo "Setting up complete CTFd monitoring with Grafana..."

# 1. Create necessary directories
echo "Creating directories..."
mkdir -p .data/nginx/logs
mkdir -p .data/CTFd/logs
mkdir -p .data/prometheus
mkdir -p .data/grafana
mkdir -p .data/loki
mkdir -p monitoring/grafana/provisioning/{dashboards,datasources}

# 2. Set permissions
chmod +x scripts/security-check.sh 2>/dev/null || true

# 3. Create plugin log directory
mkdir -p CTFd/plugins/security_monitor/logs

# 4. Pull images first
echo "Pulling Docker images..."
docker-compose pull

# 5. Start services
echo "Starting services..."
docker-compose down
docker-compose up -d

# 6. Wait for services to be ready
echo "Waiting for services to start..."
sleep 30

# 7. Check service status
echo ""
echo "Service Status:"
echo "==============="
docker-compose ps

echo ""
echo "✅ Monitoring setup complete!"
echo ""
echo "Access points:"
echo "- CTFd: http://localhost:82"
echo "- Grafana: http://localhost:3000 (admin/admin)"
echo "- Prometheus: http://localhost:9090"
echo "- Loki: http://localhost:3100"
echo "- Security Monitor: http://localhost:82/admin/monitor (after login)"
echo ""
echo "Security features enabled:"
echo "- Nginx rate limiting"
echo "- CTFd security plugin with metrics"
echo "- Prometheus metrics collection"
echo "- Grafana dashboards"
echo "- Loki log aggregation"
echo ""
echo "To view logs:"
echo "- docker logs ctf-paltform-ctfd-1"
echo "- docker logs ctf-paltform-grafana-1"
echo ""
echo "Grafana Dashboard:"
echo "1. Login to Grafana (admin/admin)"
echo "2. Go to Dashboards → Browse"
echo "3. Open 'CTFd Security Dashboard'"
