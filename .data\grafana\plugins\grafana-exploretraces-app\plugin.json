{"$schema": "https://raw.githubusercontent.com/grafana/grafana/main/docs/sources/developers/plugins/plugin.schema.json", "autoEnabled": true, "dependencies": {"grafanaDependency": ">=11.3.0", "plugins": []}, "extensions": {"addedLinks": [{"description": "Open current query in the Traces Drilldown app", "targets": ["grafana/dashboard/panel/menu"], "title": "Open in Traces Drilldown"}, {"description": "Try our new queryless experience for traces", "targets": ["grafana/explore/toolbar/action"], "title": "Open in Grafana Traces Drilldown"}], "exposedComponents": [{"description": "A button that opens a traces view in the Traces Drilldown app.", "id": "grafana-exploretraces-app/open-in-explore-traces-button/v1", "title": "Open in Traces Drilldown button"}], "extensionPoints": [{"id": "grafana-exploretraces-app/investigation/v1"}]}, "id": "grafana-exploretraces-app", "includes": [{"action": "datasources:explore", "addToNav": true, "defaultNav": true, "name": "Explore", "path": "/a/grafana-exploretraces-app/", "type": "page"}], "info": {"author": {"name": "<PERSON><PERSON>"}, "build": {"time": 1745502056821, "repo": "https://github.com/grafana/traces-drilldown", "branch": "main", "hash": "93a398ad2ff53309bbdd22e993110978d5643516", "build": 545}, "description": "Use Rate, Errors, and Duration (RED) metrics derived from traces to investigate errors within complex distributed systems.", "keywords": ["app", "tempo", "traces", "explore"], "links": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/grafana/explore-traces"}, {"name": "Report bug", "url": "https://github.com/grafana/explore-traces/issues/new"}], "logos": {"large": "img/logo.svg", "small": "img/logo.svg"}, "screenshots": [{"name": "histogram-breakdown", "path": "img/histogram-breakdown.png"}, {"name": "errors-metric-flow", "path": "img/errors-metric-flow.png"}, {"name": "errors-root-cause", "path": "img/errors-root-cause.png"}], "updated": "2025-04-24", "version": "1.0.0"}, "name": "Grafana Traces Drilldown", "preload": true, "type": "app"}