define(["react","@grafana/data","@grafana/ui","@grafana/runtime","@emotion/css","rxjs","react-dom","react-router","moment","lodash"],((e,r,t,a,n,o,i,s,c,l)=>(()=>{"use strict";var u,p,d,f,m={1159:e=>{e.exports=s},1269:e=>{e.exports=o},1454:(e,r,t)=>{t.d(r,{$U:()=>l,$V:()=>N,$d:()=>C,Ao:()=>y,BS:()=>A,CE:()=>h,D5:()=>s,EY:()=>g,Ld:()=>E,MV:()=>p,PL:()=>d,PU:()=>P,Sr:()=>u,V2:()=>j,Vl:()=>T,W5:()=>_,X0:()=>R,ZM:()=>U,ZV:()=>B,a5:()=>f,bD:()=>L,bw:()=>i,cd:()=>c,gP:()=>S,gR:()=>v,jx:()=>V,nr:()=>I,pf:()=>k,s9:()=>Y,sv:()=>W,u0:()=>z,uK:()=>q,ui:()=>b,vR:()=>F,x5:()=>m,xT:()=>O,xc:()=>$,y2:()=>D,z:()=>w,zM:()=>x,zd:()=>M});var a=t(7781),n=t(2533);function o(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var i=function(e){return e.Explore="explore",e.Home="home",e}({});const s=`/a/${n.id}/explore`,c="grafana.drilldown.traces.datasource",l="grafana.drilldown.traces.homepage.filters",u="grafana.drilldown.traces.bookmarks",p="repeat(auto-fit, minmax(400px, 1fr))",d="No data for selected query",f="Please try removing some filters or changing your query.",m=" && ",g="ds",v="${ds}",h="primarySignal",y="filters",b="${primarySignal} && ${filters}",x="homeFilter",w="groupBy",S="spanListColumns",P="metric",k="latencyThreshold",O="${latencyThreshold}",$="partialLatencyThreshold",E="${partialLatencyThreshold}",T={uid:v},j="actionView",_="primarySignal",N="selection",D="All",L="Resource",A="Span",C="resource.",M="span.",R="event.",B="event:",z=["resource.service.name","resource.service.namespace","resource.service.version","resource.cluster","resource.environment","resource.namespace","resource.deployment.environment","resource.k8s.namespace.name","resource.k8s.pod.name","resource.k8s.container.name","resource.k8s.node.name"],V=["name","kind","rootName","rootServiceName","status","statusMessage","span.http.status_code"],q=["duration","event:name","nestedSetLeft","nestedSetParent","nestedSetRight","span:duration","span:id","trace:duration","trace:id","traceDuration"],U=["status","span:status","rootName","rootService","rootServiceName","trace:rootName","trace:rootService","trace:rootServiceName"],I=1e3;class W extends a.BusEventWithPayload{}o(W,"type","timeseries-data-received");class F extends a.BusEventWithPayload{}o(F,"type","trace-opened");const Y=[{id:"filterByRefId",options:{exclude:"streaming-progress"}}]},2007:e=>{e.exports=t},2468:e=>{e.exports=c},2533:e=>{e.exports=JSON.parse('{"id":"grafana-exploretraces-app"}')},3241:e=>{e.exports=l},5959:r=>{r.exports=e},6089:e=>{e.exports=n},7781:e=>{e.exports=r},8398:e=>{e.exports=i},8531:e=>{e.exports=a}},g={};function v(e){var r=g[e];if(void 0!==r)return r.exports;var t=g[e]={id:e,loaded:!1,exports:{}};return m[e].call(t.exports,t,t.exports,v),t.loaded=!0,t.exports}v.m=m,v.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return v.d(r,{a:r}),r},p=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,v.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var t=Object.create(null);v.r(t);var a={};u=u||[null,p({}),p([]),p(p)];for(var n=2&r&&e;"object"==typeof n&&!~u.indexOf(n);n=p(n))Object.getOwnPropertyNames(n).forEach((r=>a[r]=()=>e[r]));return a.default=()=>e,v.d(t,a),t},v.d=(e,r)=>{for(var t in r)v.o(r,t)&&!v.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},v.f={},v.e=e=>Promise.all(Object.keys(v.f).reduce(((r,t)=>(v.f[t](e,r),r)),[])),v.u=e=>e+".js",v.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),v.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),d={},f="grafana-exploretraces-app:",v.l=(e,r,t,a)=>{if(d[e])d[e].push(r);else{var n,o;if(void 0!==t)for(var i=document.getElementsByTagName("script"),s=0;s<i.length;s++){var c=i[s];if(c.getAttribute("src")==e||c.getAttribute("data-webpack")==f+t){n=c;break}}n||(o=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,v.nc&&n.setAttribute("nonce",v.nc),n.setAttribute("data-webpack",f+t),n.src=e),d[e]=[r];var l=(r,t)=>{n.onerror=n.onload=null,clearTimeout(u);var a=d[e];if(delete d[e],n.parentNode&&n.parentNode.removeChild(n),a&&a.forEach((e=>e(t))),r)return r(t)},u=setTimeout(l.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=l.bind(null,n.onerror),n.onload=l.bind(null,n.onload),o&&document.head.appendChild(n)}},v.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},v.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),v.p="public/plugins/grafana-exploretraces-app/",(()=>{var e={231:0};v.f.j=(r,t)=>{var a=v.o(e,r)?e[r]:void 0;if(0!==a)if(a)t.push(a[2]);else{var n=new Promise(((t,n)=>a=e[r]=[t,n]));t.push(a[2]=n);var o=v.p+v.u(r),i=new Error;v.l(o,(t=>{if(v.o(e,r)&&(0!==(a=e[r])&&(e[r]=void 0),a)){var n=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+n+": "+o+")",i.name="ChunkLoadError",i.type=n,i.request=o,a[1](i)}}),"chunk-"+r,r)}};var r=(r,t)=>{var a,n,[o,i,s]=t,c=0;if(o.some((r=>0!==e[r]))){for(a in i)v.o(i,a)&&(v.m[a]=i[a]);s&&s(v)}for(r&&r(t);c<o.length;c++)n=o[c],v.o(e,n)&&e[n]&&e[n][0](),e[n]=0},t=self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})();var h={};v.r(h),v.d(h,{plugin:()=>N});var y=v(5959),b=v.n(y),x=v(7781),w=v(2007);const S=(0,y.lazy)((()=>v.e(67).then(v.bind(v,67)))),P=[{id:"grafana-exploretraces-app/open-in-explore-traces-button/v1",title:"Open in Traces Drilldown button",description:"A button that opens a traces view in the Traces drilldown app.",component:function(e){return b().createElement(y.Suspense,{fallback:b().createElement(w.LinkButton,{variant:"secondary",disabled:!0},"Open in Traces Drilldown")},b().createElement(S,e))}}];var k=v(1454);const O=[{targets:x.PluginExtensionPoints.DashboardPanelMenu,title:"Open in Traces Drilldown",description:"Open current query in the Traces Drilldown app",path:E(),configure:e=>$(e)},{targets:x.PluginExtensionPoints.ExploreToolbarAction,title:"Open in Grafana Traces Drilldown",description:"Try our new queryless experience for traces",path:E(),configure:e=>$(e)}];function $(e){var r,t,a;if(!e)return;const n=e.targets.find((e=>{var r;return"tempo"===(null===(r=e.datasource)||void 0===r?void 0:r.type)}));if(!n||!(null===(r=n.datasource)||void 0===r?void 0:r.uid))return;const o=null===(t=n.filters)||void 0===t?void 0:t.filter((e=>e.scope&&e.tag&&e.operator&&e.value&&e.value.length));if(!o||0===o.length)return;const i=new URLSearchParams;i.append(`var-${k.EY}`,(null===(a=n.datasource)||void 0===a?void 0:a.uid)||"");const s=(0,x.toURLRange)(e.timeRange);i.append("from",String(s.from)),i.append("to",String(s.to));const c=o.find((e=>"status"===e.tag));return c&&i.append(`var-${k.PU}`,"error"===c.value?"errors":"rate"),i.append("var-primarySignal","true"),(e=>e.filter((e=>"status"!==e.tag)).map((e=>`${e.scope}${function(e){return function(e){return T.some((r=>r.tag===e.tag&&r.scope===e.scope))}(e)?":":"."}(e)}${e.tag}|${e.operator}|${e.value}`)))(o).forEach((e=>i.append(`var-${k.Ao}`,e))),{path:`${E(i)}`}}function E(e){return`${k.D5}${e?`?${e.toString()}`:""}`}const T=["event:name","event:timeSinceStart","instrumentation:name","instrumentation:version","link:spanID","link:traceID","span:duration","span:id","span:kind","span:name","span:status","span:statusMessage","trace:duration","trace:id","trace:rootName","trace:rootService"].map((e=>{const[r,t]=e.split(":");return{scope:r,tag:t}})),j=(0,y.lazy)((()=>v.e(142).then(v.bind(v,3142)))),_=(0,y.lazy)((()=>v.e(327).then(v.bind(v,8327)))),N=(new x.AppPlugin).setRootPage(j).addConfigPage({title:"Configuration",icon:"cog",body:_,id:"configuration"});for(const e of O)N.addLink(e);for(const e of P)N.exposeComponent(e);return h})()));
//# sourceMappingURL=module.js.map