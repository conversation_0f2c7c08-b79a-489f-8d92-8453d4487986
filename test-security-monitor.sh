#!/bin/bash
# Test Security Monitor plugin

echo "=== CTFd Security Monitor Plugin Test ==="
echo

# Check if plugin directory exists
if [ -d "CTFd/plugins/security_monitor" ]; then
    echo "✓ Security Monitor plugin directory exists"
else
    echo "✗ Security Monitor plugin directory NOT found"
    exit 1
fi

# Check required files
required_files=(
    "CTFd/plugins/security_monitor/__init__.py"
    "CTFd/plugins/security_monitor/models.py"
    "CTFd/plugins/security_monitor/monitor.py"
    "CTFd/plugins/security_monitor/routes.py"
    "CTFd/plugins/security_monitor/alerts.py"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file exists"
    else
        echo "✗ $file NOT found"
    fi
done

# Check if security_monitor is in PLUGIN_WHITELIST
if grep -q "security_monitor" docker-compose.yml; then
    echo "✓ security_monitor is in PLUGIN_WHITELIST"
else
    echo "✗ security_monitor NOT in PLUGIN_WHITELIST"
fi

# Check monitoring stack
echo
echo "=== Monitoring Stack Status ==="
if grep -q "grafana:" docker-compose.yml; then
    echo "✓ Grafana service configured"
fi
if grep -q "prometheus:" docker-compose.yml; then
    echo "✓ Prometheus service configured"
fi
if grep -q "loki:" docker-compose.yml; then
    echo "✓ Loki service configured"
fi

echo
echo "=== Next Steps ==="
echo "1. Restart CTFd container: docker-compose restart ctfd"
echo "2. Access Admin Panel at: http://localhost:8000/admin"
echo "3. Look for 'Security Monitor' in the admin menu"
echo "4. Access Grafana at: http://localhost:3000 (admin/admin)"
echo
echo "If the plugin doesn't appear, check:"
echo "- Docker logs: docker-compose logs ctfd"
echo "- CTFd logs in .data/CTFd/logs/"
