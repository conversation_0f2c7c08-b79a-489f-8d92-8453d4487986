"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[826],{6826:(e,t,n)=>{n.r(t),n.d(t,{default:()=>w,updatePlugin:()=>h});var r=n(5959),a=n.n(r),i=n(6089),l=n(3241),o=n(1269),c=n(7781),s=n(8531),u=n(2007),p=n(5953);function d(e,t,n,r,a,i,l){try{var o=e[i](l),c=o.value}catch(e){return void n(e)}o.done?t(c):Promise.resolve(c).then(r,a)}function m(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function l(e){d(i,r,a,l,o,"next",e)}function o(e){d(i,r,a,l,o,"throw",e)}l(void 0)}))}}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const g=e=>({colorWeak:i.css`
    color: ${e.colors.text.secondary};
  `,icon:(0,i.css)({marginLeft:e.spacing(1)}),label:(0,i.css)({alignItems:"center",display:"flex",marginBottom:e.spacing(.75)}),marginTop:i.css`
    margin-top: ${e.spacing(3)};
  `,marginTopXl:i.css`
    margin-top: ${e.spacing(6)};
  `}),b=function(){var e=m((function*(e,t){try{yield h(e,t),s.locationService.reload()}catch(e){p.v.error(e,{msg:"Error while updating the plugin"})}}));return function(t,n){return e.apply(this,arguments)}}(),y={appConfig:{container:"data-testid ac-container",interval:"data-testid ac-interval-input",submit:"data-testid ac-submit-form"}},h=function(){var e=m((function*(e,t){const n=(0,s.getBackendSrv)().fetch({data:t,method:"POST",url:`/api/plugins/${e}/settings`});return(yield(0,o.lastValueFrom)(n)).data}));return function(t,n){return e.apply(this,arguments)}}(),O=e=>{try{if(e){const t=c.rangeUtil.intervalToSeconds(e);return(0,l.isNumber)(t)&&t>=3600}return!0}catch(e){}return!1},w=({plugin:e})=>{const t=(0,u.useStyles2)(g),{enabled:n,jsonData:i,pinned:l}=e.meta;var o,c;const[s,p]=(0,r.useState)({interval:null!==(o=null==i?void 0:i.interval)&&void 0!==o?o:"",isValid:O(null!==(c=null==i?void 0:i.interval)&&void 0!==c?c:"")});return a().createElement("div",{"data-testid":y.appConfig.container},a().createElement(u.FieldSet,{label:"Settings"},a().createElement(u.Field,{invalid:!O(s.interval),error:'Interval is invalid. Please enter an interval longer then "60m". For example: 3d, 1w, 1m',description:a().createElement("span",null,"The maximum interval that can be selected in the time picker within the Grafana Logs Drilldown app. If empty, users can select any time range interval in Grafana Logs Drilldown. ",a().createElement("br",null),"Example values: 7d, 24h, 2w"),label:"Maximum time picker interval",className:t.marginTop},a().createElement(u.Input,{width:60,id:"interval","data-testid":y.appConfig.interval,label:"Max interval",value:null==s?void 0:s.interval,placeholder:"7d",onChange:e=>{const t=e.target.value.trim();p(f(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){v(e,t,n[t])}))}return e}({},s),{interval:t,isValid:O(t)}))}})),a().createElement("div",{className:t.marginTop},a().createElement(u.Button,{type:"submit","data-testid":y.appConfig.submit,onClick:()=>b(e.meta.id,{enabled:n,jsonData:{interval:s.interval},pinned:l}),disabled:!O(s.interval)},"Save settings"))))}}}]);
//# sourceMappingURL=826.js.map?_cache=a2cff326ac9c20ddbaf7