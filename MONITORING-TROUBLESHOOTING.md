# CTFd Monitoring Troubleshooting Guide

## Grafana Shows "No Data"

### 1. Check Prometheus is Scraping CTFd

```bash
# Access Prometheus targets
http://localhost:9090/targets

# Look for:
- ctfd (http://ctfd:8000/admin/plugins/security_monitor/api/prometheus)
- Status should be "UP"
```

### 2. Test Metrics Endpoint Directly

```bash
# From your host machine:
curl http://localhost:82/admin/plugins/security_monitor/api/prometheus

# Should return metrics like:
# ctfd_requests_total 123
# ctfd_active_users 5
# etc.
```

### 3. Check Plugin is Loaded

```bash
# Check CTFd logs
docker logs ctf-paltform-ctfd-1 | grep security_monitor

# Should see:
# "Loaded module, <module 'CTFd.plugins.security_monitor'..."
```

### 4. Generate Some Traffic

The metrics need activity to show data:

```bash
# Generate some requests
for i in {1..10}; do curl http://localhost:82/; done

# Try to trigger failed logins
for i in {1..5}; do 
  curl -X POST http://localhost:82/login \
    -d "name=fake&password=wrong" 
done

# Wait 30 seconds for Prometheus to scrape
```

### 5. Check Specific Dashboards

#### CTFd Platform Metrics Dashboard
- Shows: Active users, request rate, failed logins, top endpoints
- Metrics used: `ctfd_requests_total`, `ctfd_active_users`, `ctfd_failed_logins`

#### CTFd Security Events Dashboard  
- Shows: Security events, rate limit violations, alerts
- Metrics used: `ctfd_security_events_total`, `ctfd_rate_limit_violations_1h`

#### Nginx Access Logs Dashboard
- Shows: Rate limit blocks, error logs
- Uses Loki for log aggregation

## Common Issues

### Issue: "Unauthorized" when accessing metrics

**Solution**: The metrics endpoint requires authentication. Make sure:
1. You're logged into CTFd as an admin
2. Prometheus is configured to scrape without auth (public endpoint)

### Issue: No logs in Loki

**Solution**: Check Promtail configuration:
```bash
docker logs ctf-paltform-promtail-1

# Should show it's tailing:
# /var/log/CTFd/*.log
# /var/log/nginx/*.log
```

### Issue: Grafana can't connect to datasources

**Solution**: Check network connectivity:
```bash
# From inside Grafana container
docker exec ctf-paltform-grafana-1 wget -O- http://prometheus:9090/api/v1/query?query=up
docker exec ctf-paltform-grafana-1 wget -O- http://loki:3100/ready
```

## Quick Validation Steps

1. **Check all services are running**:
```bash
docker-compose ps
# All should be "Up"
```

2. **Check metrics are being generated**:
```bash
# Visit CTFd admin panel
http://localhost:82/admin

# Navigate to Security Monitor
http://localhost:82/admin/plugins/security_monitor/dashboard

# You should see stats here
```

3. **Check Grafana datasources**:
- Login to Grafana: http://localhost:3000 (admin/admin)
- Go to Configuration → Data Sources
- Test both Prometheus and Loki connections

4. **Import dashboards manually if needed**:
- In Grafana, go to Dashboards → Import
- Upload JSON files from `monitoring/grafana/provisioning/dashboards/`

## Testing Each Component

### Test Rate Limiting
```bash
# Trigger rate limits
for i in {1..20}; do curl http://localhost:82/login; done

# Check Nginx logs
docker exec ctf-paltform-nginx-1 tail /var/log/nginx/error.log
# Should see "limiting requests"
```

### Test Security Monitoring
```bash
# Trigger failed logins
curl -X POST http://localhost:82/login -d "name=admin&password=wrongpass"

# Check security log
docker exec ctf-paltform-ctfd-1 tail /var/log/CTFd/security.log
```

### Test Metrics Export
```bash
# Check raw metrics
curl http://localhost:82/admin/plugins/security_monitor/api/prometheus | grep ctfd_

# Should see various metrics
```

## Dashboard Descriptions

1. **CTFd Platform Metrics** (`ctfd-platform`)
   - Overall platform health
   - Request rates and response times
   - User activity
   - Best for: General monitoring

2. **CTFd Security Events** (`ctfd-security-events`)
   - Security-specific metrics
   - Failed logins, rate limits, alerts
   - Best for: Security monitoring

3. **Nginx Access Logs** (`nginx-logs`)
   - Rate limit violations
   - HTTP errors
   - Best for: DDoS/abuse detection

## Still Not Working?

1. **Restart everything**:
```bash
docker-compose down
docker-compose up -d
```

2. **Check logs of each service**:
```bash
docker-compose logs ctfd
docker-compose logs prometheus
docker-compose logs grafana
docker-compose logs loki
docker-compose logs promtail
```

3. **Verify file permissions**:
```bash
ls -la .data/CTFd/logs/
ls -la .data/nginx/logs/
# Should be writable by containers
```

4. **Enable debug logging**:
- Add to CTFd environment: `LOG_LEVEL=DEBUG`
- Check Prometheus scrape debug: http://localhost:9090/config

Remember: Metrics need time to accumulate. After setup, wait 5-10 minutes and generate some traffic before expecting full dashboards.
