{"version": 3, "file": "module.js", "mappings": ";8MACIA,EADAC,ECAAC,EACAC,E,sECEJ,IACE,KAAiB,QACb,QAAkBC,MAAM,EAAG,QAAkBC,YAAY,KAAO,GAChE,0C,2CCDN,MAAMC,GAA4BC,EAAAA,EAAAA,OAAK,IAAM,gCAgBtC,MAAMC,EAAoB,CAC/B,CACEC,UAhBJ,SAA4CC,GAC1C,OACE,kBAACC,EAAAA,SAAQA,CACPC,SACE,kBAACC,EAAAA,WAAUA,CAACC,QAAQ,YAAYC,UAAAA,GAAS,2BAK3C,kBAACT,EAA8BI,GAGrC,EAKIM,YAAa,6DACbC,GAAI,yDACJC,MAAO,kC,0UChBX,MAAMC,GAAMZ,EAAAA,EAAAA,MAAIA,GAAC,YACf,MAAM,cAAEa,SAAwB,yEAExBC,QAASC,SAAwB,yEACjCD,QAASE,SAA0B,+BACnCF,QAASG,SAAsB,8BAQvC,OANAF,IAEIF,YACIK,QAAQC,IAAI,CAACH,IAAmBC,OAGjC,4BACT,KAEMG,GAAYpB,EAAAA,EAAAA,MAAIA,GAAC,YACrB,aAAa,6BACf,KAEaqB,GAAS,IAAIC,EAAAA,WAAgBC,YAAYX,GAAKY,cAAc,CACvEC,KAAML,EACNM,KAAM,MACNhB,GAAI,gBACJC,MAAO,kBAGT,IAAK,MAAMgB,KAAcC,EAAAA,GACvBP,EAAOQ,QAAQF,GAGjB,IAAK,MAAMG,KAA0B7B,EACnCoB,EAAOU,gBAAgBD,E,+MCdzB,MAAME,EAAe,yBACfrB,EAAQ,WAAWqB,IACnBvB,EAAc,6BAA6BuB,SAC3CN,EAAO,UAEAO,EAAkB,CAC7BC,oBAAqB,4CAaVN,EAA2B,CACtC,CACEO,QAASC,EAAAA,sBAAsBC,mBAC/B1B,QACAF,cACAiB,OACAY,KAAMC,IACNC,UAAWC,GAEb,CACEN,QAASC,EAAAA,sBAAsBM,qBAC/B/B,QACAF,cACAiB,OACAY,KAAMC,IACNC,UAAWC,IAIf,SAASE,EAAgBC,GACvB,OAAKA,GACIC,EAAAA,EAGX,CAGO,SAASC,EAAmBF,GACjC,OAAOA,aAAAA,EAAAA,EAAOG,QAAQ,QAAS,KACjC,CAEO,SAASC,EAAqBJ,GACnC,OAAKA,GAKEK,EAAAA,EAAAA,IAA8BH,EAAmBF,IAJ/CC,EAAAA,EAKX,CAUA,SAASJ,EAAqDS,G,IAKzCC,EAoBsCA,EAxBzD,IAAKD,EACH,OAEF,MAAMC,EAAYD,EAAQf,QAAQiB,MAAMC,I,IAAWA,E,MAA4B,UAAX,QAAjBA,EAAAA,EAAOC,kBAAPD,IAAAA,OAAAA,EAAAA,EAAmBE,KAAe,IACrF,IAAKJ,KAAkC,QAApBA,EAAAA,EAAUG,kBAAVH,IAAAA,OAAAA,EAAAA,EAAsBK,KACvC,OAGF,MAAMC,EAAON,EAAUM,MACjB,OAAEC,EAAM,aAAEC,EAAY,YAAEC,EAAW,eAAEC,IAAmBC,EAAAA,EAAAA,IAAoBL,EAAMP,EAASC,GAC3FY,EAAgBJ,EAAaP,MAAMY,IAAaC,EAAAA,EAAAA,IAAoBD,EAASE,YAGnF,IAAKH,EACH,OAIF,MACMI,EAAaC,EADGL,EAAcnB,MAAMyB,MAAM,KAAK,IAErD,IAAIC,EAAYP,EAAcQ,MAAQC,EAAAA,GAAe,UAAYT,EAAcQ,IAE/EZ,EAAac,MAAMC,GAAOA,EAAEH,MAAQD,GAAa,EAAI,IAErD,IAAIK,EAASC,EAAgBC,EAAcC,aAAkC,QAApB3B,EAAAA,EAAUG,kBAAVH,IAAAA,OAAAA,EAAAA,EAAsBK,IAAK,IAAIuB,iBACxFJ,EAASC,EAAgBC,EAAcG,cAAe9B,EAAQ+B,UAAUC,KAAKC,UAAUC,WAAYT,GACnGA,EAASC,EAAgBC,EAAcQ,YAAanC,EAAQ+B,UAAUK,GAAGH,UAAUC,WAAYT,GAE/F,IAAK,MAAMY,KAAe5B,EAAc,CAEtC,GAAI4B,EAAYhC,OAASiC,EAAAA,EAAUC,QACjC,SAGF,MAAMC,EAA6B,GAAGH,EAAYhB,OAAOgB,EAAYrB,YAAYyB,EAC/E3C,EAAqBuC,EAAY3C,WAC9B+C,EAAoB7C,EAAmByC,EAAY3C,UAExD+B,EAASiB,EAAmBf,EAAcgB,OAAQH,EAA4Bf,EAChF,CAEA,GAAIf,EACF,IAAK,MAAMkC,KAAclC,EACvBe,EAASiB,EACPf,EAAckB,YACd,GAAGD,EAAWvB,OAAOoB,EAAoBG,EAAW5B,aAAayB,EAC/DhD,EAAgBmD,EAAWlD,UAE7B+B,GAIN,GAAIjB,aAAAA,EAAAA,EAAQsC,OACV,IAAK,MAAMC,KAASvC,EAClB,GAAIuC,EAAM1C,OAASiC,EAAAA,EAAUU,mBAEzBvB,EADEsB,EAAM1B,MAAQ4B,EAAAA,GACPP,EACPf,EAAcuB,OACd,GAAGH,EAAM1B,OAAO0B,EAAM/B,YAAYyB,EAAoBhD,EAAgBsD,EAAMrD,UAC5E+B,GAGOiB,EACPf,EAAcwB,SACd,GAAGJ,EAAM1B,OAAO0B,EAAM/B,YAAYyB,EAChC3C,EAAqBiD,EAAMrD,WACxB+C,EAAoB7C,EAAmBmD,EAAMrD,UAClD+B,OAGC,CACL,MAAM2B,EAA8B,CAClC1D,MAAOqD,EAAMrD,MACb2D,OAAQN,EAAMM,QAGVC,EAAuB,GAAGP,EAAM1B,OAAO0B,EAAM/B,YAAYyB,EAC7D3C,EAAqByD,KAAKC,UAAUJ,QAtFJ1D,EAuFH0D,EAAW1D,MAtF3CA,EAIE+C,EAAoB7C,EAAmBF,IAHrCC,EAAAA,KAuFH8B,EAASiB,EAAmBf,EAAc8B,OAAQH,EAAsB7B,EAC1E,CA1FC,IAAmC/B,EA6FxC,GAAIiB,aAAAA,EAAAA,EAAgBmC,OAAQ,CAC1B,MAAMY,EAA6B,GAEnC,IAAK,MAAMX,KAASpC,EAClB+C,EAASC,KAAK,CACZtD,KAAM0C,EAAM/B,WAAa4C,EAAAA,GAAgBC,MAAQ,UAAY,UAC7DC,QAASrE,EAAgBsD,EAAMrD,SAInC,IAAIqE,GAAiBC,EAAAA,EAAAA,GAAqBN,GAE1CjC,EAASiB,EAAmBf,EAAcsC,SAAUV,KAAKC,UAAUE,GAAWjC,GAC9EA,EAASiB,EAAmBf,EAAcuC,iBAAkBH,EAAgBtC,EAC9E,CAEA,MAAO,CACLrC,KAAMC,EAAa,YAAY+B,KAAaH,SAAmBQ,GAEnE,CAEO,SAASpC,EAAaD,EAAO,WAAY+E,GAC9C,MAAO,MAAMC,EAAAA,KAAgBhF,IAAO+E,EAAY,IAAIA,EAAUjC,aAAe,IAC/E,CAEO,MAAMP,EAAgB,CAC3BC,aAAc,OAAOyC,EAAAA,KACrBvC,cAAe,OACfK,YAAa,KACbQ,OAAQ,OAAO2B,EAAAA,KACfb,OAAQ,OAAOc,EAAAA,KACfpB,SAAU,OAAOqB,EAAAA,KACjBtB,OAAQ,OAAOuB,EAAAA,KACf5B,YAAa,OAAO6B,EAAAA,KACpBT,SAAUU,EAAAA,GACVT,iBAAkB,OAAOS,EAAAA,MAIpB,SAASjD,EAAgBL,EAAuB3B,EAAekF,G,IAC3BA,EAAzC,MAAMC,EAAe,IAAIhD,gBAAsC,QAAtB+C,EAAAA,aAAAA,EAAAA,EAAc1C,kBAAd0C,IAAAA,EAAAA,EAA4BE,EAAAA,gBAAgBC,aAGrF,OAFAF,EAAaG,IAAI3D,EAAK3B,GAEfmF,CACT,CAEO,SAASnC,EACdrB,EACA3B,EACAkF,GAEA,MAAMK,EAAWH,EAAAA,gBAAgBI,c,IACQN,EAAzC,MAAMC,EAAe,IAAIhD,gBAAsC,QAAtB+C,EAAAA,aAAAA,EAAAA,EAAc1C,kBAAd0C,IAAAA,EAAAA,EAA4BK,EAASE,QAG9E,OAFAN,EAAaO,OAAO/D,EAAK3B,GAElBmF,CACT,CAEO,SAAS3D,EAAamE,GAC3B,OACEC,EAAAA,EAAAA,IAAgCD,GAE7BxF,QAAQ,MAAO,KACfA,QAAQ,MAAO,IAEtB,CAqBO,SAAS4C,EAAoB/C,GAClC,OAnBF,SAAkCA,GAChC,OAAIA,QACK,GAIF,KAAK6F,OAAO1F,SAASH,EAAO,UACrC,CAYS8F,CAVF,SAAiC9F,GACtC,OAAIA,QACK,GAIO,MAAM6F,OAAO1F,SAASH,EAAO,UAC/C,CAGkC+F,CAAwB/F,GAC1D,C,kCC3QO,eAAK4C,G,2DAAAA,C,CAAL,C,oNCKA,eAAKoD,G,0EAAAA,C,CAAL,C,IAOA,WAAKC,G,mBAAAA,C,CAAL,C,IAIA,WAAKC,G,+CAAAA,C,CAAL,C,IAMA,MAAMC,E,sUAAW,IAAKH,EAAkBE,GA4BxC,eAAKE,G,2EAAAA,C,CAAL,C,IAOA,WAAKlC,G,yCAAAA,C,CAAL,C,IAKA,WAAKmC,G,2EAAAA,C,CAAL,C,2gBCxDP,MAAMC,EAAiB,CACrBC,IAAK7B,EAAAA,GACL8B,Q,UAGWC,EAAS,CACpBC,MAAO,CAACC,EAAsBrG,KAC5B,MAAMsG,EAAM,KAAKN,EAAmBhG,GACpCuG,QAAQH,MAAMC,EAAKC,GACnBE,EAAeH,EAAKC,EAAI,EAE1BG,KAAM,CAACC,EAAa1G,KAClB,MAAMsG,EAAM,KAAKN,EAAmBhG,GAEpC2G,EAAgBD,EAAKJ,EAAI,EAE3BM,KAAM,CAACF,EAAa1G,KAClB,MAAMsG,EAAM,KAAKN,EAAmBhG,GACpCuG,QAAQK,KAAKF,EAAKJ,GAClBO,EAAgBH,EAAKJ,EAAI,GAIvBK,EAAkB,CAACD,EAAa1G,KACpC,KACE8G,EAAAA,EAAAA,SAAQJ,EAAK1G,EACf,CAAE,MAAO+G,GACPR,QAAQK,KAAK,4BACf,GAGIC,EAAkB,CAACH,EAAa1G,KACpC,KACEgH,EAAAA,EAAAA,YAAWN,EAAK1G,EAClB,CAAE,MAAO+G,GACPR,QAAQK,KAAK,8BAA+B,CAAE5G,UAAS0G,OACzD,GAgCF,MAAMF,EAAiB,CAACH,EAAmCY,KACzD,IAAIjH,EAAUiH,EACd,KA3BF,SAAmCZ,EAA2BrG,GAC5D,GAAmB,iBAARqG,GAA4B,OAARA,KACzBa,EAAAA,EAAAA,IAASb,IACXc,OAAOC,KAAKf,GAAKgB,SAAShG,IACxB,MAAM3B,EAAQ2G,EAAIhF,GACG,iBAAV3B,GAAuC,kBAAVA,GAAwC,iBAAVA,IACpEM,EAAQqB,GAAO3B,EAAMwC,WACvB,IAIAoF,EAAQjB,IACV,GAAwB,iBAAbA,EAAIkB,MAAkC,OAAblB,EAAIkB,KACtC,IACEvH,EAAQuH,KAAOhE,KAAKC,UAAU6C,EAAIkB,KACpC,CAAE,MAAOR,GAET,KAC6B,iBAAbV,EAAIkB,MAAyC,kBAAblB,EAAIkB,MAA0C,iBAAblB,EAAIkB,OACrFvH,EAAQuH,KAAOlB,EAAIkB,KAAKrF,WAIhC,CAKIsF,CAA0BnB,EAAKrG,GAE3BqG,aAAeoB,OACjBC,EAAAA,EAAAA,UAASrB,EAAKrG,GACU,iBAARqG,GAChBqB,EAAAA,EAAAA,UAAS,IAAID,MAAMpB,GAAMrG,GAChBqG,GAAsB,iBAARA,EACnBrG,EAAQ0G,KACVgB,EAAAA,EAAAA,UAAS,IAAID,MAAMzH,EAAQ0G,KAAM1G,IAEjC0H,EAAAA,EAAAA,UAAS,IAAID,MAAM,gBAAiBzH,IAGtC0H,EAAAA,EAAAA,UAAS,IAAID,MAAM,iBAAkBzH,EAEzC,CAAE,MAAO+G,GACPR,QAAQH,MAAM,4BAA6B,CAAEpG,UAASqG,OACxD,GAGIiB,EAAW5H,GACR,SAAUA,C,yNClDZ,MAAMiI,EAaX,eAAOC,CAASC,GACd,OAAO,IAAIF,EAAaE,EAAK7F,KAAM6F,EAAKzF,GAAIyF,EAAMA,EAAKxH,KACzD,CAEAyH,QAAAA,CAASC,GACP,OAAOC,KAAKhG,MAAQ+F,EAAS/F,MAAQgG,KAAK5F,IAAM2F,EAAS3F,EAC3D,CAEA6F,aAAAA,CAAcC,GACZ,OAAOA,EAAMC,UAAUH,KAAKhG,KAAMgG,KAAK5F,GACzC,CAjBAgG,WAAAA,CAAYpG,EAAcI,EAAYiG,EAAyBhI,GAL/D2B,EAAAA,KAAAA,YAAAA,GACAI,EAAAA,KAAAA,UAAAA,GACA/B,EAAAA,KAAAA,YAAAA,GACAgI,EAAAA,KAAAA,kBAAAA,GAGEL,KAAKhG,KAAOA,EACZgG,KAAK5F,GAAKA,EACV4F,KAAK3H,KAAOA,EACZ2H,KAAKK,WAAaA,CACpB,EAeK,SAASC,EAAkBJ,EAAeK,GAC/C,MAAMC,EAAsB,GAS5B,OARmBnF,EAAAA,GAAOoF,MAAMP,GAC3BQ,QAAQ,CACXC,MAAQd,UACYe,IAAdL,GAA2BA,EAAUM,SAAShB,EAAKxH,KAAK7C,MAC1DgL,EAAM7E,KAAKkE,EAAKA,KAClB,IAGGW,CACT,CAEA,SAASM,EAA4BjB,EAAkBxH,GACrD,GAAIwH,EAAKxH,KAAK7C,KAAO6C,EACnB,MAAO,CAACsH,EAAaC,SAASC,IAGhC,MAAMkB,EAA4B,GAClC,IAAIC,EAAM,EACNC,EAAQpB,EAAKqB,WAAWF,GAC5B,KAAOC,GACLF,EAAUpF,QAAQmF,EAA4BG,EAAO5I,IACrD2I,EAAMC,EAAM7G,GACZ6G,EAAQpB,EAAKqB,WAAWF,GAE1B,OAAOD,CACT,CAoCA,SAASI,EACPC,EACAC,EACA3I,EACA4I,EACAtI,GAEA,MAAMuI,EAAkBvI,IAAa8E,EAAAA,GAAa0D,OAASxI,IAAa8E,EAAAA,GAAa2D,cAC/EC,EAAoBN,EAAgBP,SAAS,SAAWU,EAI9D,GAAoB,MAAhBF,GAAuBE,EAAiB,CAE1C,MAAMI,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBvJ,QAAQ8J,EAAqB,KACjE,MAAO,GAAoB,MAAhBN,EAAqB,CAE9B,MAAMQ,EAA2B,IAAID,OAAO,QAAU,KACtDR,EAAkBA,EAAgBvJ,QAAQgK,EAA0B,KACpE,MAAMF,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBvJ,QAAQ8J,EAAqB,KACjE,CAeA,OAbID,IAEFN,EAAkBA,EAAgBvJ,QAAQ,OAAQ,KAGpDa,EAAYiD,KAAK,CACftC,IAAKqI,EACD3D,EAAAA,GAAwB+D,gBAAgB5H,WACxC6D,EAAAA,GAAwBgE,cAAc7H,WAAa,IAAMoH,EAAMpH,WACnElB,SAAUA,EACVtB,MAAO0J,IAGFA,CACT,CAEA,SAASY,EAAoBZ,EAAyBzI,EAAqCK,GACzF,MAAM6I,EAA2B,IAAID,OAAO,MAAO,KACnDR,EAAkBA,EAAgBvJ,QAAQgK,EAA0B,KACpElJ,EAAegD,KAAK,CAClB3C,WACAtB,MAAO0J,GAEX,CAoDA,SAASa,EAAwBC,GAC/B,OAAIpB,EAA4BoB,EAASC,EAAAA,IAAKrH,OACrCsH,EAAAA,GAAeC,IACbvB,EAA4BoB,EAASI,EAAAA,IAAKxH,OAC5CsH,EAAAA,GAAeG,GACbzB,EAA4BoB,EAASM,EAAAA,IAAK1H,OAC5CsH,EAAAA,GAAeK,IACb3B,EAA4BoB,EAASQ,EAAAA,IAAK5H,OAC5CsH,EAAAA,GAAeO,QAGxBpE,QAAQK,KAAK,2BAGf,CAEA,SAASgE,EAAuBV,GAC9B,OAAIpB,EAA4BoB,EAASW,EAAAA,IAAI/H,OACpCsH,EAAAA,GAAeU,MACbhC,EAA4BoB,EAASa,EAAAA,IAAKjI,OAC5CsH,EAAAA,GAAeY,SACblC,EAA4BoB,EAASe,EAAAA,IAAInI,OAC3CsH,EAAAA,GAAec,WACbpC,EAA4BoB,EAASiB,EAAAA,IAAKrI,OAC5CsH,EAAAA,GAAegB,mBADjB,CAKT,CAsFO,SAASxK,EACdsH,EACAlI,EACAC,GAOA,MAAMoL,EAA+B,GAC/B3K,EAAgC,GAChCC,EAAsC,GACtCH,EAAwB,GACxBM,EAAWwH,EAAkBJ,EAAO,CAACoD,EAAAA,KAE3C,GAAwB,IAApBxK,EAASgC,OACX,MAAO,CAAErC,aAAc4K,GAUzB,OAlRF,SAA2BnD,EAAemD,GAExC,MAAME,EAAajD,EAAkBJ,EAAO,CAACsD,EAAAA,KAC7C,IAAK,MAAMtB,KAAWqB,EAAY,CAChC,MAAME,EAAqB3C,EAA4BoB,EAASwB,EAAAA,IAChE,IAAKD,GAAoD,IAA9BA,EAAmB3I,OAC5C,SAGF,MAAM6I,EAAgB7C,EAA4BoB,EAAS0B,EAAAA,IACrD5K,EAAWkH,EAAMC,UAAUsD,EAAmB,GAAGrJ,GAAIuJ,EAAc,GAAG3J,MACtEX,EAAMoK,EAAmB,GAAGxD,cAAcC,GAC1CxI,EAAQiM,EAAcE,KAAK9D,GAAaG,EAAMC,UAAUJ,EAAS/F,KAAO,EAAG+F,EAAS3F,GAAK,KAAI,GAGhGf,GACA3B,IACAsB,IAAaoJ,EAAAA,GAAeY,UAC3BhK,IAAaoJ,EAAAA,GAAeU,OAC5B9J,IAAaoJ,EAAAA,GAAec,YAC5BlK,IAAaoJ,EAAAA,GAAegB,gBAKhCC,EAAO1H,KAAK,CACVtC,MACAL,WACAX,KAAMiC,EAAAA,EAAUC,QAChB7C,SAEJ,CACF,CA8OEoM,CAFsBhD,EAA4BhI,EAAS,GAAIwK,EAAAA,IAAU,GAAGrD,cAAcC,GAEzDmD,GA3LnC,SAA0BnD,EAAexH,EAA+BC,GACtE,MAAMoL,EAAiBzD,EAAkBJ,EAAO,CAAC8D,EAAAA,KACjD,IAAK,MAAO1C,EAAOY,KAAY6B,EAAeE,UAAW,CACvD,MAAMC,EAAQpD,EAA4BoB,EAASiC,EAAAA,IAC7CC,EAAatD,EAA4BoB,EAASmC,EAAAA,IAClDC,EAAWxD,EAA4BoB,EAASa,EAAAA,IAChDwB,EAAiBzD,EAA4BoB,EAASiB,EAAAA,IACtDqB,EAAiB1D,EAA4BoB,EAASuC,EAAAA,IACtDC,EAAiB5D,EAA4BoB,EAASyC,EAAAA,IAEtDC,EAAuBC,EAAyB3C,GAEtD,IAAK,MAAM4C,KAAuBF,EAAsB,CACtD,MAAMvD,EAAcnB,EAAMC,WAAU2E,aAAAA,EAAAA,EAAqB9K,MAAO,EAAG8K,aAAAA,EAAAA,EAAqB9K,MAGxF,IAAIoH,EAAkBlB,EAAMC,WAAU2E,aAAAA,EAAAA,EAAqB9K,MAAO,GAAG8K,aAAAA,EAAAA,EAAqB1K,IAAK,GAE/F,GAAIgH,EAAgBtG,OAAQ,CAC1B,IAAI9B,EACJ,GAAIkL,EAAMpJ,OACR9B,EAAW8E,EAAAA,GAAajC,WACnB,GAAIyI,EAASxJ,OAClB9B,EAAW8E,EAAAA,GAAaiH,mBACnB,GAAIR,EAAezJ,OACxB9B,EAAW8E,EAAAA,GAAa2D,mBACnB,GAAI2C,EAAWtJ,OACpB9B,EAAW8E,EAAAA,GAAa0D,WACnB,GAAIgD,EAAe1J,OACxB9B,EAAW4C,EAAAA,GAAgBC,UACtB,KAAI6I,EAAe5J,OAEnB,CACLyD,QAAQK,KAAK,sBAAuB,CAClCsB,MAAOA,EAAMC,UAAU+B,EAAQlI,KAAMkI,EAAQ9H,MAG/C,QACF,CAPEpB,EAAW4C,EAAAA,GAAgBmJ,aAO7B,CAEM/L,IAAa4C,EAAAA,GAAgBC,OAAS7C,IAAa4C,EAAAA,GAAgBmJ,cACvE5D,EAAuBC,EAAiBC,EAAa3I,EAAa4I,EAAOtI,GAEzEgJ,EAAoBZ,EAAiBzI,EAAgBK,EAEzD,CACF,CACF,CACF,CA4IEgM,CAAiB9E,EAAOxH,EAAaC,GA5GvC,SAAqBuH,EAAe1H,EAAuBR,EAAsCC,G,IAC7ED,EAAlB,MAAMiN,EAAwB,QAAZjN,EAAAA,EAAQuH,YAARvH,IAAAA,OAAAA,EAAAA,EAAckN,OAAOhN,MAAMiN,GAAUA,EAAMC,QAAUnN,EAAUmN,QAE3EC,EAAY/E,EAAkBJ,EAAO,CAACoF,EAAAA,KAC5C,IAAK,MAAMpD,KAAWmD,EAAW,C,IAsBbE,EArBlB,MACMC,EADW7F,EAAaC,SAASsC,GACXjC,cAAcC,GAI1C,GAHqBgC,EAAQuD,SAASH,EAAAA,IAIpC,SAIF,GAAmC,cAA/BE,EAAWrF,UAAU,EAAG,GAC1B,SAKF,MAAMuF,EAAepF,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAKzF,IAAK,CAACuL,EAAAA,KACvEC,EAAatF,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAKzF,IAAK,CAACyL,EAAAA,KAIrEC,EAA4B,QAAhBP,EADIzE,EAA4BoB,EAASwB,EAAAA,IAC3B,UAAd6B,IAAAA,OAAAA,EAAAA,EAAkBtF,cAAcC,GAG5C6F,EAAmBjF,EAA4BoB,EAAS0B,EAAAA,IACxDoC,EAAmBlF,EAA4BoB,EAAS+D,EAAAA,IACxDC,EAAkBpF,EAA4BoB,EAASiE,EAAAA,IACvDC,EAAqBtF,EAA4BoB,EAASmE,EAAAA,IAEhE,IAAIjL,EAAoBpC,EAmBpBsN,EAlBJ,GAAIP,EAAiBjL,OACnB9B,EAAW4J,EAAuBV,GAElC9G,EAAa8E,EAAMC,UAAU4F,EAAiB,GAAG/L,KAAO,EAAG+L,EAAiB,GAAG3L,GAAK,QAC/E,GAAI4L,EAAiBlL,OAC1BM,EAAa4K,EAAiB,GAAG/F,cAAcC,GAC/ClH,EAAWiJ,EAAwBC,QAC9B,GAAIkE,EAAmBtL,OAC5B9B,EAAWiJ,EAAwBC,GACnC9G,EAAagL,EAAmB,GAAGnG,cAAcC,OAC5C,KAAIgG,EAAgBpL,OAIzB,SAHA9B,EAAWiJ,EAAwBC,GACnC9G,EAAa8K,EAAgB,GAAGjG,cAAcC,EAGhD,C,IAOcqG,EAGd,GANItB,IAGFqB,EAA6CrB,QAAjCsB,GAAAA,EAAAA,EAAAA,GAAsBT,EAAWb,UAAjCsB,IAAAA,EAAAA,OAA+C3F,GAGzD5H,EAAU,CACZ,IAAIqC,EACAqK,EAAa5K,QAAU8K,EAAW9K,OACpCO,EAAS,QACAqK,EAAa5K,OACtBO,EAAS,SACAuK,EAAW9K,OACpBO,EAAS,OAGTiL,EAAYhM,EAAAA,EAAUU,mBAGxBxC,EAAOmD,KAAK,CACVtC,IAAKyM,EACL9M,SAAUA,EACVqC,SACAhD,KAAMiO,QAAAA,EAAahM,EAAAA,EAAUkM,OAC7B9O,MAAO0D,GAEX,CACF,CACF,CA2BEqL,CAAYvG,EAAO1H,EAAQR,EAASC,GAE7B,CAAEO,SAAQC,aAAc4K,EAAQ3K,cAAaC,iBACtD,CAqBO,MAAM+N,EAAU,EAChB,SAASC,EAAazG,GAC3B,OAA2C,IArBtC,SAAyBA,EAAe0G,GAC7C,IAAIC,GAAkB,EAUtB,OATaxL,EAAAA,GAAOoF,MAAMP,GACrBQ,QAAQ,CACXC,MAAO,EAAGtI,WACR,GAAIA,EAAK7C,KAAOoR,EAEd,OADAC,GAAkB,GACX,CACT,IAGGA,CACT,CASSA,CAAgB3G,EAAOwG,EAChC,CAEA,SAAS7B,EAAyBxB,GAChC,MAAM7C,EAAsB,GAC5B,IAAIX,EAA0BwD,EAC9B,EAAG,CACD,MAAMyD,EAASjH,EAAK4F,SAAS7B,EAAAA,IACzBkD,IAAWjH,EAAK4F,SAAS5H,EAAAA,KAC3B2C,EAAM7E,KAAKmL,GAEbjH,EAAOA,EAAK4F,SAASsB,EAAAA,GACvB,OAAiB,MAARlH,GAET,OAAOW,CACT,C,wDC1ZO,WAAKwG,G,+DAAAA,C,CAAL,C,IA8BA,SAAST,EAAsBU,EAAkB9B,EAAkB7D,EAAQ,G,IAC9D6D,EAAlB,MAAM+B,EAAwD,QAA5C/B,EAAAA,EAAM3M,OAAON,MAAM6C,GAAyB,eAAfA,EAAMoM,cAAnChC,IAAAA,OAAAA,EAAAA,EAA2DiC,OAAO9F,GACpF,IAAK4F,EACH,OAAO,KAET,OAAQA,EAAUD,IAChB,IAAK,IACH,OAAO3M,EAAAA,EAAUC,QACnB,IAAK,IACH,OAAOD,EAAAA,EAAUU,mBACnB,IAAK,IACH,OAAOV,EAAAA,EAAUkM,OACnB,QACE,OAAO,KAEb,C,8JC7CA,MAAMa,EAASC,GAAyC,iBAANA,GAAwB,OAANA,EAE7D,SAASC,EAA+BhI,EAAciI,GAC3D,OAAOA,KAAQjI,CACjB,CAEA,MAAMkI,EAAYC,GAA6B,iBAANA,GAAkBA,GAAM,GAEpDxI,EAAYyI,GAAgE,iBAARA,EAE1E,SAASC,EAAiBpO,GAC/B,IAAIqO,EAAoB,GACxB,GAAIC,MAAMC,QAAQvO,GAChB,IAAK,IAAIwO,EAAI,EAAGA,EAAIxO,EAAEsB,OAAQkN,IAC5BH,EAAQlM,KAAK8L,EAASjO,EAAEwO,KAG5B,OAAOH,CACT,CAEO,SAASI,EAAuBX,GACrC,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,QAAUC,EAAQD,EAAG,OAASA,EAEtE,GAAIY,EAAU,CACZ,MAAMC,EAA8B,iBAAjBD,EAASC,KAAoBD,EAASC,IACnD3S,EAA4B,iBAAhB0S,EAAS1S,IAAmB0S,EAAS1S,GACvD,IAAW,IAAPA,IAAwB,IAAR2S,EAClB,MAAO,CAAE3S,KAAI2S,MAEjB,CAEA,OAAO,CACT,CAEO,SAASC,EAA4Bd,GAC1C,MAAoB,iBAANA,IAAyB,SAANA,GAAsB,UAANA,IAAkBA,CACrE,CACO,SAASe,EAAoBf,GAClC,MAAiB,iBAANA,GAAkBA,IAAMgB,EAAAA,cAAcC,UAAUrO,WAClDoO,EAAAA,cAAcC,UAGN,iBAANjB,GAAkBA,IAAMgB,EAAAA,cAAcE,WAAWtO,YACnDoO,EAAAA,cAAcE,UAIzB,CAEO,SAASC,EAAiBnB,GAC/B,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,UAAYC,EAAQD,EAAG,WAAaA,EAE5E,GAAIY,EAAU,CACZ,MAAM7M,EACuB,iBAApB6M,EAAS7M,SACK,WAApB6M,EAAS7M,QACY,SAApB6M,EAAS7M,QACW,UAApB6M,EAAS7M,QACW,uBAApB6M,EAAS7M,SACX6M,EAAS7M,OACL3D,EAAkC,iBAAnBwQ,EAASxQ,OAAsBwQ,EAASxQ,MAE7D,IAAe,IAAX2D,IAA8B,IAAV3D,EACtB,MAAO,CAAE2D,SAAQ3D,QAErB,CAEA,OAAO,CACT,CAEO,SAASgR,EAAyBpB,GACvC,MAAMY,EAAWb,EAAMC,IAAMpI,EAASoI,IAAMA,EAE5C,GAAIY,EAAU,CACZ,MAAM9I,EAAOD,OAAOC,KAAK8I,GACnBS,EAAuC,CAAC,EAC9C,IAAK,IAAIX,EAAI,EAAGA,EAAI5I,EAAKtE,OAAQkN,IAAK,CACpC,MAAM3O,EAAM+F,EAAK4I,GACXtQ,EAAQwQ,EAAS9I,EAAK4I,IACP,iBAAVtQ,IACTiR,EAAatP,GAAO3B,EAExB,CAEA,OAAOiR,CACT,CAEA,OAAO,CACT,CAEO,SAASC,EAAgBC,GAC9B,MAAMC,EAAQzB,EAAMwB,IAAiBtB,EAAQsB,EAAc,OAAStB,EAAQsB,EAAc,SAAWA,EACrG,GAAIC,EAAO,CACT,MAAM1O,EAAKqN,EAASqB,EAAM1O,IACpBJ,EAAOyN,EAASqB,EAAM9O,MAC5B,GAAII,GAAMJ,EACR,MAAO,CAAEA,OAAMI,KAEnB,CAGF,CAEO,SAAS2O,EAAmBhK,GACjC,MAAML,EAAM2I,EAAMtI,IAAMwI,EAAQxI,EAAG,UAAY0I,EAAS1I,EAAEX,OAC1D,GAAIM,EACF,OAAOA,CAGX,CAEO,SAASsK,EAAqBC,GACnC,OAAQA,GACN,KAAKvL,EAAAA,GAAcoF,MACnB,KAAKpF,EAAAA,GAAcsF,SACnB,KAAKtF,EAAAA,GAAcwF,WACnB,KAAKxF,EAAAA,GAAc0F,cACnB,KAAKxF,EAAAA,GAAgB+E,GACrB,KAAK/E,EAAAA,GAAgB6E,IACrB,KAAK7E,EAAAA,GAAgB2E,GACrB,KAAK3E,EAAAA,GAAgByE,IACnB,OAAO4G,EACT,QACE,MAAM,IAAIC,EAAe,wBAE/B,CAEO,MAAMA,UAAuBzJ,O,qFCnI7B,MAAM1G,EAAuBkQ,GAC3BA,IAAOpL,EAAAA,GAASiF,OAASmG,IAAOpL,EAAAA,GAASqF,WAErCiG,EAAuBF,GAC3BA,IAAOpL,EAAAA,GAASmF,UAAYiG,IAAOpL,EAAAA,GAASuF,cAExCgG,EAAmBH,GACvBA,IAAOpL,EAAAA,GAASqF,YAAc+F,IAAOpL,EAAAA,GAASuF,cAE1CiG,EAAqBJ,GACzBK,EAAAA,GAAqBzI,SAASoI,E,+FCVhC,SAASM,EAAuBN,GACrC,GAAIA,IAAOpL,EAAAA,GAASmF,SAClB,MAAO,YAET,GAAIiG,IAAOpL,EAAAA,GAASuF,cAClB,MAAO,uBAET,GAAI6F,IAAOpL,EAAAA,GAASiF,MAClB,MAAO,SAET,GAAImG,IAAOpL,EAAAA,GAASqF,WAClB,MAAO,gBAET,GAAI+F,IAAOpL,EAAAA,GAAS0E,GAClB,MAAO,YAET,GAAI0G,IAAOpL,EAAAA,GAAS8E,GAClB,MAAO,eAET,GAAIsG,IAAOpL,EAAAA,GAAS4E,IAClB,MAAO,2BAET,GAAIwG,IAAOpL,EAAAA,GAASwE,IAClB,MAAO,wBAGT,MAAMjE,EAAQ,IAAIqB,MAAM,qBAExB,MADAtB,EAAAA,EAAOC,MAAMA,EAAO,CAAEM,IAAK,mBAAoB1F,SAAUiQ,IACnD7K,CACR,CC3BO,MAAMoL,EAAY,CAAC3L,EAAAA,GAASiF,MAAOjF,EAAAA,GAASmF,SAAUnF,EAAAA,GAASqF,WAAYrF,EAAAA,GAASuF,eAAeS,KAExG,CAACnM,EAAO4J,EAAOmI,KACR,CACLlU,YAAagU,EAAuB7R,GACpCgS,MAAOhS,EACPA,YAISiS,EAAmB,CAAC9L,EAAAA,GAASiF,MAAOjF,EAAAA,GAASqF,YAAYW,KAA8BnM,IAAW,CAC7GnC,YAAagU,EAAuB7R,GACpCgS,MAAOhS,EACPA,YAGW4R,EAAuB,CAACzL,EAAAA,GAAS8E,GAAI9E,EAAAA,GAAS4E,IAAK5E,EAAAA,GAAS0E,GAAI1E,EAAAA,GAASwE,KAEzEuH,EAAmBN,EAAqBzF,KAA8BnM,IAAW,CAC5FnC,YAAagU,EAAuB7R,GACpCgS,MAAOhS,EACPA,YAGWmS,EAAyC,CACpD,CAAEH,MAAO,QAAShS,MAAOoG,EAAAA,GAAajC,OACtC,CAAE6N,MAAO,gBAAiBhS,MAAOoG,EAAAA,GAAaiH,eAC9C,CAAE2E,MAAO,QAAShS,MAAOoG,EAAAA,GAAa0D,OACtC,CAAEkI,MAAO,gBAAiBhS,MAAOoG,EAAAA,GAAa2D,e,iBCvBzC,SAASqI,EAAgC7Q,GAC9C,OAAOA,EAAWpB,QAAQ,MAAO,QAAQA,QAAQ,MAAO,OAAOA,QAAQ,KAAM,MAC/E,CCRO,SAASmE,EAAqBN,GACnC,MACMqO,EADkBrO,EAAS2H,QAAQvH,GAA6B,YAAjBA,EAAQzD,OAE1DwL,KAAKmG,GAAM,OAAOF,EAAgCE,EAAElO,cACpDmO,KAAK,KACLC,OAEGC,EAAkBzO,EAAS2H,QAAQvH,GAA6B,YAAjBA,EAAQzD,OAC7D,IAAI+R,EAAsB,GAU1B,OATID,EAAgBrP,OAAS,IAEzBsP,EAD6B,IAA3BD,EAAgBrP,OACI,OAAOgP,EAAgCK,EAAgB,GAAGrO,YAE1D,MAAMqO,EACzBtG,KAAKmG,GAAM,IAAIF,EAAgCE,EAAElO,cACjDmO,KAAK,WAGL,GAAGF,KAAuBK,IAAsBF,MACzD,C,2eCSO,MAAM5N,EAAa,UACb+N,EAAkB,aAClBC,EAAqB,kBACrBC,EAA0B,qBAC1BhO,EAAa,SACbiO,EAAkB,YAClBC,EAAsB,mBACtBC,EAAwB,qBACxBC,EAA0B,aAC1BnO,EAAe,WACfoO,EAAoB,cACpBjO,EAAe,WACfkO,EAAoB,cACpBpO,EAAa,SACbqO,EAAkB,YAClBC,EAAqB,UACrBC,EAAqB,UACrBC,EAA0B,aAC1BC,EAA2B,uBAE3BC,EAAoB,gBACpBC,EAAyB,mBACzB/O,EAAiB,KACjBgP,EAAsB,QACtBC,EAAkB,aAClBC,EAAuB,gBAEvBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAoB,UAAUH,iDAC9BI,EAAmB,UAAUJ,wCAE7BK,EAAmB,WAEnBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAkB,eAGlBrP,EAAmB,cACnBsP,EAAwB,iBACxBC,EAA2B,IAAI5B,MAAoBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBF,KAAwBtB,KAAmBiB,IAEpLS,EAA6B,IAAI7B,MAAoBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBN,KAAqBlB,IAChK2B,EAA0C,IAAI9B,MAAoBS,KAAmBJ,KAAyBG,KAAqBmB,KAnB/F,UAAUT,oDAmBmJd,IAEjM2B,EAA8B,IAAI/B,MAAoBI,KAAuBG,KAAqBC,KAAqBmB,KAAyBF,KAAwBtB,IACxK6B,EAAgC,IAAIhC,MAAoBO,KAAqBC,KAAqBiB,IAClGQ,EAAkC,GAAGjC,KAAmBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBxB,IAC9I+B,EAAiB,CAAEjU,IAAK+S,GACxBmB,EAAqB,SACrBvR,EAAuB,iBACvB3B,EAAe,eACfmT,EAAmB,UACnBC,EAAyB,yBAEzB/U,EAAuB,KAIvBgV,EAAgC,UACtC,SAASrP,EAAgC5F,EAAQ,IACtD,OAAIA,EAAMkV,WAAWD,GACZjV,EAAMyI,UAAUwM,EAA8B7R,QAEhDpD,CACT,CACO,SAASmV,EAA4BnV,EAAQ,IAClD,OAAOA,EAAMkV,WAAWD,EAC1B,CACO,SAAS5U,GAA8BL,EAAQ,IACpD,OAAOiV,EAAgCjV,CACzC,C,WC9GAoV,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,WCAjBH,EAAOC,QAAUG,C,WCAjBJ,EAAOC,QAAUI,C,WCAjBL,EAAOC,QAAUK,C,WCAjBN,EAAOC,QAAUM,C,WCAjBP,EAAOC,QAAUO,C,WCAjBR,EAAOC,QAAUQ,C,UCAjBT,EAAOC,QAAUS,C,WCAjBV,EAAOC,QAAUU,C,WCAjBX,EAAOC,QAAUW,C,WCAjBZ,EAAOC,QAAUY,C,wSCGjB,MAAMC,EAAsB,KAC5B,IAAIC,EAAa,EACjB,MAAMC,EACF,WAAA1N,CAAYpG,EAAMI,GACd4F,KAAKhG,KAAOA,EACZgG,KAAK5F,GAAKA,CACd,EAOJ,MAAM2T,EAIF,WAAA3N,CAAY4N,EAAS,CAAC,GAClBhO,KAAKxK,GAAKqY,IACV7N,KAAKiO,UAAYD,EAAOC,QACxBjO,KAAKkO,YAAcF,EAAOE,aAAe,MACrC,MAAM,IAAIzO,MAAM,uDACnB,EACL,CAUA,GAAA0O,CAAItS,GACA,GAAImE,KAAKiO,QACL,MAAM,IAAIG,WAAW,0CAGzB,MAFoB,mBAATvS,IACPA,EAAQwS,EAASxS,MAAMA,IACnBxD,IACJ,IAAIiW,EAASzS,EAAMxD,GACnB,YAAkBuI,IAAX0N,EAAuB,KAAO,CAACtO,KAAMsO,EAAO,CAE3D,EAQJP,EAASQ,SAAW,IAAIR,EAAS,CAAEG,YAAaM,GAAOA,EAAIrV,MAAM,OAMjE4U,EAASU,SAAW,IAAIV,EAAS,CAAEG,YAAaM,GAAOA,EAAIrV,MAAM,OAMjE4U,EAASW,MAAQ,IAAIX,EAAS,CAAEG,YAAaM,GAAOA,EAAIrV,MAAM,OAY9D4U,EAASY,QAAU,IAAIZ,EAAS,CAAEG,YAAaxW,IACvC,GAAIA,GAAkB,OAATA,GAA2B,OAATA,GAA2B,QAATA,EAC7C,MAAM,IAAI0W,WAAW,8BAAgC1W,GACzD,OAAOA,GAAS,MAAM,IAO9BqW,EAASa,YAAc,IAAIb,EAAS,CAAEE,SAAS,IAO/CF,EAASc,UAAY,IAAId,EAAS,CAAEE,SAAS,IAM7CF,EAASe,QAAU,IAAIf,EAAS,CAAEE,SAAS,IAM3C,MAAMc,EACF,WAAA3O,CAIA4O,EAUAC,EAIA5T,GACI2E,KAAKgP,KAAOA,EACZhP,KAAKiP,QAAUA,EACfjP,KAAK3E,OAASA,CAClB,CAIA,UAAO6T,CAAIF,GACP,OAAOA,GAAQA,EAAK/Z,OAAS+Z,EAAK/Z,MAAM8Y,EAASe,QAAQtZ,GAC7D,EAEJ,MAAM2Z,EAAUhQ,OAAOiQ,OAAO,MAI9B,MAAMf,EAIF,WAAAjO,CAOA+G,EAIAlS,EAKAO,EAIA6Z,EAAQ,GACJrP,KAAKmH,KAAOA,EACZnH,KAAK/K,MAAQA,EACb+K,KAAKxK,GAAKA,EACVwK,KAAKqP,MAAQA,CACjB,CAIA,aAAOC,CAAOC,GACV,IAAIta,EAAQsa,EAAKta,OAASsa,EAAKta,MAAM6F,OAASqE,OAAOiQ,OAAO,MAAQD,EAChEE,GAASE,EAAKC,IAAM,EAAuB,IAAMD,EAAKE,QAAU,EAA2B,IAC1FF,EAAKnR,MAAQ,EAAyB,IAAmB,MAAbmR,EAAKpI,KAAe,EAA6B,GAC9F9O,EAAO,IAAIgW,EAASkB,EAAKpI,MAAQ,GAAIlS,EAAOsa,EAAK/Z,GAAI6Z,GACzD,GAAIE,EAAKta,MACL,IAAK,IAAIya,KAAOH,EAAKta,MAGjB,GAFK6S,MAAMC,QAAQ2H,KACfA,EAAMA,EAAIrX,IACVqX,EAAK,CACL,GAAIA,EAAI,GAAGzB,QACP,MAAM,IAAIG,WAAW,8CACzBnZ,EAAMya,EAAI,GAAGla,IAAMka,EAAI,EAC3B,CAER,OAAOrX,CACX,CAKA,IAAAmP,CAAKA,GAAQ,OAAOxH,KAAK/K,MAAMuS,EAAKhS,GAAK,CAIzC,SAAIma,GAAU,OAAqB,EAAb3P,KAAKqP,OAAgC,CAAG,CAI9D,aAAIO,GAAc,OAAqB,EAAb5P,KAAKqP,OAAoC,CAAG,CAItE,WAAIQ,GAAY,OAAqB,EAAb7P,KAAKqP,OAAkC,CAAG,CAKlE,eAAIS,GAAgB,OAAqB,EAAb9P,KAAKqP,OAAsC,CAAG,CAK1E,EAAAU,CAAG5I,GACC,GAAmB,iBAARA,EAAkB,CACzB,GAAInH,KAAKmH,MAAQA,EACb,OAAO,EACX,IAAIuH,EAAQ1O,KAAKwH,KAAKuG,EAASW,OAC/B,QAAOA,GAAQA,EAAMsB,QAAQ7I,IAAS,CAC1C,CACA,OAAOnH,KAAKxK,IAAM2R,CACtB,CASA,YAAOtL,CAAMgI,GACT,IAAIoM,EAAS9Q,OAAOiQ,OAAO,MAC3B,IAAK,IAAI5H,KAAQ3D,EACb,IAAK,IAAIsD,KAAQK,EAAKrO,MAAM,KACxB8W,EAAO9I,GAAQtD,EAAI2D,GAC3B,OAAQ3H,IACJ,IAAK,IAAIqQ,EAASrQ,EAAK2H,KAAKuG,EAASW,OAAQ1G,GAAK,EAAGA,GAAKkI,EAASA,EAAOpV,OAAS,GAAIkN,IAAK,CACxF,IAAImI,EAAQF,EAAOjI,EAAI,EAAInI,EAAKsH,KAAO+I,EAAOlI,IAC9C,GAAImI,EACA,OAAOA,CACf,EAER,EAKJ9B,EAAS+B,KAAO,IAAI/B,EAAS,GAAIlP,OAAOiQ,OAAO,MAAO,EAAG,GAUzD,MAAMiB,EAKF,WAAAjQ,CAIAkQ,GACItQ,KAAKsQ,MAAQA,EACb,IAAK,IAAItI,EAAI,EAAGA,EAAIsI,EAAMxV,OAAQkN,IAC9B,GAAIsI,EAAMtI,GAAGxS,IAAMwS,EACf,MAAM,IAAIoG,WAAW,8EACjC,CAMA,MAAAmC,IAAUtb,GACN,IAAIub,EAAW,GACf,IAAK,IAAInY,KAAQ2H,KAAKsQ,MAAO,CACzB,IAAIG,EAAW,KACf,IAAK,IAAIC,KAAUzb,EAAO,CACtB,IAAIkZ,EAAMuC,EAAOrY,GACb8V,IACKsC,IACDA,EAAWtR,OAAOwR,OAAO,CAAC,EAAGtY,EAAKpD,QACtCwb,EAAStC,EAAI,GAAG3Y,IAAM2Y,EAAI,GAElC,CACAqC,EAAS7U,KAAK8U,EAAW,IAAIpC,EAAShW,EAAK8O,KAAMsJ,EAAUpY,EAAK7C,GAAI6C,EAAKgX,OAAShX,EACtF,CACA,OAAO,IAAIgY,EAAQG,EACvB,EAEJ,MAAMI,EAAa,IAAIC,QAAWC,EAAkB,IAAID,QAKxD,IAAIE,GACJ,SAAWA,GAMPA,EAASA,EAAyB,eAAI,GAAK,iBAM3CA,EAASA,EAA2B,iBAAI,GAAK,mBAM7CA,EAASA,EAAuB,aAAI,GAAK,eAOzCA,EAASA,EAAyB,eAAI,GAAK,gBAC9C,CA1BD,CA0BGA,IAAaA,EAAW,CAAC,IAiB5B,MAAMC,EAIF,WAAA5Q,CAIA/H,EAIA4Y,EAKAlQ,EAIAjG,EAIA7F,GASI,GARA+K,KAAK3H,KAAOA,EACZ2H,KAAKiR,SAAWA,EAChBjR,KAAKe,UAAYA,EACjBf,KAAKlF,OAASA,EAIdkF,KAAK/K,MAAQ,KACTA,GAASA,EAAM6F,OAAQ,CACvBkF,KAAK/K,MAAQkK,OAAOiQ,OAAO,MAC3B,IAAK,IAAK5H,EAAM9P,KAAUzC,EACtB+K,KAAK/K,MAAqB,iBAARuS,EAAmBA,EAAOA,EAAKhS,IAAMkC,CAC/D,CACJ,CAIA,QAAAwC,GACI,IAAI4U,EAAUC,EAAYG,IAAIlP,MAC9B,GAAI8O,IAAYA,EAAQG,QACpB,OAAOH,EAAQE,KAAK9U,WACxB,IAAI+W,EAAW,GACf,IAAK,IAAIC,KAAMlR,KAAKiR,SAAU,CAC1B,IAAIzC,EAAM0C,EAAGhX,WACTsU,IACIyC,IACAA,GAAY,KAChBA,GAAYzC,EAEpB,CACA,OAAQxO,KAAK3H,KAAK8O,MACb,KAAKgK,KAAKnR,KAAK3H,KAAK8O,QAAUnH,KAAK3H,KAAKwX,QAAUtU,KAAKC,UAAUwE,KAAK3H,KAAK8O,MAAQnH,KAAK3H,KAAK8O,OACzF8J,EAASnW,OAAS,IAAMmW,EAAW,IAAM,IAFzBA,CAG7B,CAMA,MAAAG,CAAOC,EAAO,GACV,OAAO,IAAIC,EAAWtR,KAAKuR,QAASF,EACxC,CAMA,QAAAG,CAASxQ,EAAKyQ,EAAO,EAAGJ,EAAO,GAC3B,IAAIK,EAAQd,EAAW1B,IAAIlP,OAASA,KAAKuR,QACrCH,EAAS,IAAIE,EAAWI,GAG5B,OAFAN,EAAOO,OAAO3Q,EAAKyQ,GACnBb,EAAW5T,IAAIgD,KAAMoR,EAAOQ,OACrBR,CACX,CAKA,WAAIG,GACA,OAAO,IAAIM,EAAS7R,KAAM,EAAG,EAAG,KACpC,CAYA,OAAA8R,CAAQ9Q,EAAKyQ,EAAO,GAChB,IAAI5R,EAAOkS,EAAYnB,EAAW1B,IAAIlP,OAASA,KAAKuR,QAASvQ,EAAKyQ,GAAM,GAExE,OADAb,EAAW5T,IAAIgD,KAAMH,GACdA,CACX,CAQA,YAAAmS,CAAahR,EAAKyQ,EAAO,GACrB,IAAI5R,EAAOkS,EAAYjB,EAAgB5B,IAAIlP,OAASA,KAAKuR,QAASvQ,EAAKyQ,GAAM,GAE7E,OADAX,EAAgB9T,IAAIgD,KAAMH,GACnBA,CACX,CAQA,YAAAoS,CAAajR,EAAKyQ,EAAO,GACrB,OAwcR,SAAuBzC,EAAMhO,EAAKyQ,GAC9B,IAAIS,EAAQlD,EAAKgD,aAAahR,EAAKyQ,GAAOU,EAAS,KACnD,IAAK,IAAIC,EAAOF,aAAiBL,EAAWK,EAAQA,EAAMla,QAAQqa,OAAQD,EAAMA,EAAOA,EAAKC,OACxF,GAAID,EAAK9Q,MAAQ,EAAG,CAChB,IAAI+Q,EAASD,EAAKC,QACjBF,IAAWA,EAAS,CAACD,KAASvW,KAAK0W,EAAOP,QAAQ9Q,EAAKyQ,IACxDW,EAAOC,CACX,KACK,CACD,IAAIC,EAAQvD,EAAYG,IAAIkD,EAAKpD,MAEjC,GAAIsD,GAASA,EAAMrD,SAAWqD,EAAMrD,QAAQ,GAAGjV,MAAQgH,GAAOsR,EAAMrD,QAAQqD,EAAMrD,QAAQnU,OAAS,GAAGV,IAAM4G,EAAK,CAC7G,IAAIuR,EAAO,IAAIV,EAASS,EAAMtD,KAAMsD,EAAMrD,QAAQ,GAAGjV,KAAOoY,EAAKpY,MAAO,EAAGoY,IAC1ED,IAAWA,EAAS,CAACD,KAASvW,KAAKoW,EAAYQ,EAAMvR,EAAKyQ,GAAM,GACrE,CACJ,CAEJ,OAAOU,EAASK,EAAUL,GAAUD,CACxC,CA1deO,CAAczS,KAAMgB,EAAKyQ,EACpC,CAQA,OAAA/Q,CAAQ6O,GACJ,IAAI,MAAE5O,EAAK,MAAE+R,EAAK,KAAE1Y,EAAO,EAAC,GAAEI,EAAK4F,KAAKlF,QAAWyU,EAC/C8B,EAAO9B,EAAK8B,MAAQ,EAAGsB,GAAQtB,EAAON,EAAS6B,kBAAoB,EACvE,IAAK,IAAIC,EAAI7S,KAAKoR,OAAOC,EAAON,EAAS6B,oBAAqB,CAC1D,IAAIE,GAAU,EACd,GAAID,EAAE7Y,MAAQI,GAAMyY,EAAEzY,IAAMJ,KAAU2Y,GAAQE,EAAExa,KAAKyX,cAA4B,IAAbnP,EAAMkS,IAAe,CACrF,GAAIA,EAAEE,aACF,SACJD,GAAU,CACd,CACA,KACQA,GAAWJ,IAAUC,IAASE,EAAExa,KAAKyX,cACrC4C,EAAMG,IACNA,EAAEG,eAHD,CAKL,IAAKH,EAAER,SACH,OACJS,GAAU,CACd,CACJ,CACJ,CAKA,IAAAtL,CAAKA,GACD,OAAQA,EAAKyG,QAAiCjO,KAAK/K,MAAQ+K,KAAK/K,MAAMuS,EAAKhS,SAAMoL,EAA1DZ,KAAK3H,KAAKmP,KAAKA,EAC1C,CAMA,cAAIyL,GACA,IAAI3E,EAAS,GACb,GAAItO,KAAK/K,MACL,IAAK,IAAIO,KAAMwK,KAAK/K,MAChBqZ,EAAO3S,KAAK,EAAEnG,EAAIwK,KAAK/K,MAAMO,KACrC,OAAO8Y,CACX,CAMA,OAAA4E,CAAQlF,EAAS,CAAC,GACd,OAAOhO,KAAKiR,SAASnW,QAAU,EAA+BkF,KAC1DmT,EAAa9E,EAAS+B,KAAMpQ,KAAKiR,SAAUjR,KAAKe,UAAW,EAAGf,KAAKiR,SAASnW,OAAQ,EAAGkF,KAAKlF,QAAQ,CAACmW,EAAUlQ,EAAWjG,IAAW,IAAIkW,EAAKhR,KAAK3H,KAAM4Y,EAAUlQ,EAAWjG,EAAQkF,KAAKiT,aAAajF,EAAOoF,UAAY,EAAEnC,EAAUlQ,EAAWjG,IAAW,IAAIkW,EAAK3C,EAAS+B,KAAMa,EAAUlQ,EAAWjG,IAClT,CAKA,YAAOuY,CAAM9T,GAAQ,OA4tBzB,SAAmBA,GACf,IAAI+T,EACJ,IAAI,OAAEC,EAAM,QAAEC,EAAO,gBAAEC,EAAkB7F,EAAmB,OAAE8F,EAAS,GAAE,cAAEC,EAAgBH,EAAQlD,MAAMxV,QAAWyE,EAChH6R,EAAStJ,MAAMC,QAAQwL,GAAU,IAAIK,EAAiBL,EAAQA,EAAOzY,QAAUyY,EAC/EjD,EAAQkD,EAAQlD,MAChB1B,EAAc,EAAGC,EAAY,EACjC,SAASgF,EAASC,EAAaC,EAAQ9C,EAAUlQ,EAAWiT,EAAUC,GAClE,IAAI,GAAEze,EAAE,MAAE0e,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAC3BiD,EAAmBxF,EAAWyF,EAAiB1F,EACnD,KAAOwF,EAAO,GAAG,CAEb,GADAhD,EAAOmD,QACM,GAATH,EAAsC,CACtC,IAAIvU,EAAO6T,EAAOle,GAGlB,OAFAyb,EAAStV,KAAKkE,QACdkB,EAAUpF,KAAKuY,EAAQJ,EAE3B,CACK,IAAa,GAATM,EAEL,YADAxF,EAAcpZ,GAGb,IAAa,GAAT4e,EAEL,YADAvF,EAAYrZ,GAIZ,MAAM,IAAI4Y,WAAW,6BAA6BgG,IAE1D,CACA,IAAsBvU,EAAM0T,EAAxBlb,EAAOiY,EAAM9a,GACbgf,EAAWN,EAAQJ,EACvB,GAAIK,EAAMD,GAAST,IAAoBF,EAASkB,EAAerD,EAAOpQ,IAAM+S,EAAQC,IAAY,CAE5F,IAAIzU,EAAO,IAAImV,YAAYnB,EAAOa,KAAOb,EAAOoB,MAC5CC,EAASxD,EAAOpQ,IAAMuS,EAAOa,KAAM9S,EAAQ/B,EAAKzE,OACpD,KAAOsW,EAAOpQ,IAAM4T,GAChBtT,EAAQuT,EAAatB,EAAOW,MAAO3U,EAAM+B,GAC7CzB,EAAO,IAAIiV,EAAWvV,EAAM4U,EAAMZ,EAAOW,MAAOV,GAChDgB,EAAWjB,EAAOW,MAAQJ,CAC9B,KACK,CACD,IAAIc,EAASxD,EAAOpQ,IAAMoT,EAC1BhD,EAAOmD,OACP,IAAIQ,EAAgB,GAAIC,EAAiB,GACrCC,EAAgBzf,GAAMme,EAAgBne,GAAM,EAC5C0f,EAAY,EAAGC,EAAUhB,EAC7B,KAAO/C,EAAOpQ,IAAM4T,GACZK,GAAiB,GAAK7D,EAAO5b,IAAMyf,GAAiB7D,EAAOgD,MAAQ,GAC/DhD,EAAO+C,KAAOgB,EAAU1B,IACxB2B,EAAeL,EAAeC,EAAgBd,EAAOgB,EAAW9D,EAAO+C,IAAKgB,EAASF,EAAeZ,EAAkBC,GACtHY,EAAYH,EAAcja,OAC1Bqa,EAAU/D,EAAO+C,KAErB/C,EAAOmD,QAEFN,EAAQ,KACboB,EAAanB,EAAOU,EAAQG,EAAeC,GAG3CnB,EAASK,EAAOU,EAAQG,EAAeC,EAAgBC,EAAehB,EAAQ,GAOtF,GAJIgB,GAAiB,GAAKC,EAAY,GAAKA,EAAYH,EAAcja,QACjEsa,EAAeL,EAAeC,EAAgBd,EAAOgB,EAAWhB,EAAOiB,EAASF,EAAeZ,EAAkBC,GACrHS,EAAcO,UACdN,EAAeM,UACXL,GAAiB,GAAKC,EAAY,EAAG,CACrC,IAAIK,EAAOC,EAAand,EAAMic,GAC9BzU,EAAOsT,EAAa9a,EAAM0c,EAAeC,EAAgB,EAAGD,EAAcja,OAAQ,EAAGqZ,EAAMD,EAAOqB,EAAMA,EAC5G,MAEI1V,EAAOuT,EAAS/a,EAAM0c,EAAeC,EAAgBb,EAAMD,EAAOG,EAAmBF,EAAKG,EAElG,CACArD,EAAStV,KAAKkE,GACdkB,EAAUpF,KAAK6Y,EACnB,CACA,SAASa,EAAavB,EAAaC,EAAQ9C,EAAUlQ,GACjD,IAAIP,EAAQ,GACRiV,EAAY,EAAGC,GAAU,EAC7B,KAAOtE,EAAOpQ,IAAM+S,GAAQ,CACxB,IAAI,GAAEve,EAAE,MAAE0e,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAC/B,GAAIgD,EAAO,EACPhD,EAAOmD,WAEN,IAAImB,GAAU,GAAKxB,EAAQwB,EAC5B,MAGIA,EAAS,IACTA,EAASvB,EAAMV,GACnBjT,EAAM7E,KAAKnG,EAAI0e,EAAOC,GACtBsB,IACArE,EAAOmD,MACX,CACJ,CACA,GAAIkB,EAAW,CACX,IAAIlC,EAAS,IAAImB,YAAwB,EAAZe,GACzBvB,EAAQ1T,EAAMA,EAAM1F,OAAS,GACjC,IAAK,IAAIkN,EAAIxH,EAAM1F,OAAS,EAAG6a,EAAI,EAAG3N,GAAK,EAAGA,GAAK,EAC/CuL,EAAOoC,KAAOnV,EAAMwH,GACpBuL,EAAOoC,KAAOnV,EAAMwH,EAAI,GAAKkM,EAC7BX,EAAOoC,KAAOnV,EAAMwH,EAAI,GAAKkM,EAC7BX,EAAOoC,KAAOA,EAElB1E,EAAStV,KAAK,IAAImZ,EAAWvB,EAAQ/S,EAAM,GAAK0T,EAAOV,IACvDzS,EAAUpF,KAAKuY,EAAQJ,EAC3B,CACJ,CACA,SAAS0B,EAAand,EAAMuW,GACxB,MAAO,CAACqC,EAAUlQ,EAAWjG,KACzB,IAAgD8a,EAAMC,EAAlDhH,EAAY,EAAGiH,EAAQ7E,EAASnW,OAAS,EAC7C,GAAIgb,GAAS,IAAMF,EAAO3E,EAAS6E,cAAmB9E,EAAM,CACxD,IAAK8E,GAASF,EAAKvd,MAAQA,GAAQud,EAAK9a,QAAUA,EAC9C,OAAO8a,GACPC,EAAgBD,EAAKpO,KAAKuG,EAASc,cACnCA,EAAY9N,EAAU+U,GAASF,EAAK9a,OAAS+a,EACrD,CACA,OAAOzC,EAAS/a,EAAM4Y,EAAUlQ,EAAWjG,EAAQ+T,EAAWD,EAAY,CAElF,CACA,SAASwG,EAAenE,EAAUlQ,EAAWgV,EAAM/N,EAAGhO,EAAMI,EAAI/B,EAAMwW,EAAWD,GAC7E,IAAImG,EAAgB,GAAIC,EAAiB,GACzC,KAAO/D,EAASnW,OAASkN,GACrB+M,EAAcpZ,KAAKsV,EAAS+E,OAC5BhB,EAAerZ,KAAKoF,EAAUiV,MAAQD,EAAO/b,GAEjDiX,EAAStV,KAAKyX,EAASI,EAAQlD,MAAMjY,GAAO0c,EAAeC,EAAgB5a,EAAKJ,EAAM6U,EAAYzU,EAAIwU,IACtG7N,EAAUpF,KAAK3B,EAAO+b,EAC1B,CACA,SAAS3C,EAAS/a,EAAM4Y,EAAUlQ,EAAWjG,EAAQ+T,EAAWD,EAAa3Z,GACzE,GAAI2Z,EAAa,CACb,IAAIqH,EAAO,CAAClI,EAASa,YAAaA,GAClC3Z,EAAQA,EAAQ,CAACghB,GAAMC,OAAOjhB,GAAS,CAACghB,EAC5C,CACA,GAAIpH,EAAY,GAAI,CAChB,IAAIoH,EAAO,CAAClI,EAASc,UAAWA,GAChC5Z,EAAQA,EAAQ,CAACghB,GAAMC,OAAOjhB,GAAS,CAACghB,EAC5C,CACA,OAAO,IAAIjF,EAAK3Y,EAAM4Y,EAAUlQ,EAAWjG,EAAQ7F,EACvD,CACA,SAASwf,EAAe0B,EAASnC,GAO7B,IAAIoC,EAAOhF,EAAOgF,OACdhC,EAAO,EAAGF,EAAQ,EAAGS,EAAO,EAAG0B,EAAWD,EAAKjC,IAAMV,EACrDnF,EAAS,CAAE8F,KAAM,EAAGF,MAAO,EAAGS,KAAM,GACxCvC,EAAM,IAAK,IAAI2B,EAASqC,EAAKpV,IAAMmV,EAASC,EAAKpV,IAAM+S,GAAS,CAC5D,IAAIuC,EAAWF,EAAKhC,KAEpB,GAAIgC,EAAK5gB,IAAMwe,GAAYsC,GAAY,EAAG,CAGtChI,EAAO8F,KAAOA,EACd9F,EAAO4F,MAAQA,EACf5F,EAAOqG,KAAOA,EACdA,GAAQ,EACRP,GAAQ,EACRgC,EAAK7B,OACL,QACJ,CACA,IAAIC,EAAW4B,EAAKpV,IAAMsV,EAC1B,GAAIA,EAAW,GAAK9B,EAAWT,GAAUqC,EAAKlC,MAAQmC,EAClD,MACJ,IAAIE,EAAeH,EAAK5gB,IAAMme,EAAgB,EAAI,EAC9C6C,EAAYJ,EAAKlC,MAErB,IADAkC,EAAK7B,OACE6B,EAAKpV,IAAMwT,GAAU,CACxB,GAAI4B,EAAKhC,KAAO,EAAG,CACf,IAAkB,GAAdgC,EAAKhC,KAGL,MAAMhC,EAFNmE,GAAgB,CAGxB,MACSH,EAAK5gB,IAAMme,IAChB4C,GAAgB,GAEpBH,EAAK7B,MACT,CACAL,EAAQsC,EACRpC,GAAQkC,EACR3B,GAAQ4B,CACZ,CAMA,OALIvC,EAAW,GAAKI,GAAQ+B,KACxB7H,EAAO8F,KAAOA,EACd9F,EAAO4F,MAAQA,EACf5F,EAAOqG,KAAOA,GAEXrG,EAAO8F,KAAO,EAAI9F,OAAS1N,CACtC,CACA,SAASiU,EAAa4B,EAAalD,EAAQjS,GACvC,IAAI,GAAE9L,EAAE,MAAE0e,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAE/B,GADAA,EAAOmD,OACHH,GAAQ,GAAK5e,EAAKme,EAAe,CACjC,IAAI+C,EAAapV,EACjB,GAAI8S,EAAO,EAAG,CACV,IAAIQ,EAASxD,EAAOpQ,KAAOoT,EAAO,GAClC,KAAOhD,EAAOpQ,IAAM4T,GAChBtT,EAAQuT,EAAa4B,EAAalD,EAAQjS,EAClD,CACAiS,IAASjS,GAASoV,EAClBnD,IAASjS,GAAS6S,EAAMsC,EACxBlD,IAASjS,GAAS4S,EAAQuC,EAC1BlD,IAASjS,GAAS9L,CACtB,MACkB,GAAT4e,EACLxF,EAAcpZ,GAEA,GAAT4e,IACLvF,EAAYrZ,GAEhB,OAAO8L,CACX,CACA,IAAI2P,EAAW,GAAIlQ,EAAY,GAC/B,KAAOqQ,EAAOpQ,IAAM,GAChB6S,EAAStU,EAAK2U,OAAS,EAAG3U,EAAKkX,aAAe,EAAGxF,EAAUlQ,GAAY,EAAG,GAC9E,IAAIjG,EAAgC,QAAtBwY,EAAK/T,EAAKzE,cAA2B,IAAPwY,EAAgBA,EAAMrC,EAASnW,OAASiG,EAAU,GAAKkQ,EAAS,GAAGnW,OAAS,EACxH,OAAO,IAAIkW,EAAKV,EAAM/Q,EAAKoX,OAAQ1F,EAASqE,UAAWvU,EAAUuU,UAAWxa,EAChF,CA17BgC8b,CAAUrX,EAAO,EAKjDyR,EAAK6F,MAAQ,IAAI7F,EAAK3C,EAAS+B,KAAM,GAAI,GAAI,GAC7C,MAAMwD,EACF,WAAAxT,CAAYmT,EAAQjS,GAChBtB,KAAKuT,OAASA,EACdvT,KAAKsB,MAAQA,CACjB,CACA,MAAI9L,GAAO,OAAOwK,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CAC/C,SAAI4S,GAAU,OAAOlU,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CAClD,OAAI6S,GAAQ,OAAOnU,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CAChD,QAAI8S,GAAS,OAAOpU,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CACjD,OAAIN,GAAQ,OAAOhB,KAAKsB,KAAO,CAC/B,IAAAiT,GAASvU,KAAKsB,OAAS,CAAG,CAC1B,IAAA8U,GAAS,OAAO,IAAIxC,EAAiB5T,KAAKuT,OAAQvT,KAAKsB,MAAQ,EAQnE,MAAMwT,EAIF,WAAA1U,CAIAmT,EAIAzY,EAIAkC,GACIgD,KAAKuT,OAASA,EACdvT,KAAKlF,OAASA,EACdkF,KAAKhD,IAAMA,CACf,CAIA,QAAI3E,GAAS,OAAOgW,EAAS+B,IAAM,CAInC,QAAAlW,GACI,IAAIoU,EAAS,GACb,IAAK,IAAIhN,EAAQ,EAAGA,EAAQtB,KAAKuT,OAAOzY,QACpCwT,EAAO3S,KAAKqE,KAAK8W,YAAYxV,IAC7BA,EAAQtB,KAAKuT,OAAOjS,EAAQ,GAEhC,OAAOgN,EAAOrE,KAAK,IACvB,CAIA,WAAA6M,CAAYxV,GACR,IAAI9L,EAAKwK,KAAKuT,OAAOjS,GAAQyV,EAAW/W,KAAKuT,OAAOjS,EAAQ,GACxDjJ,EAAO2H,KAAKhD,IAAIsT,MAAM9a,GAAK8Y,EAASjW,EAAK8O,KAI7C,GAHI,KAAKgK,KAAK7C,KAAYjW,EAAKwX,UAC3BvB,EAAS/S,KAAKC,UAAU8S,IAExByI,IADJzV,GAAS,GAEL,OAAOgN,EACX,IAAI2C,EAAW,GACf,KAAO3P,EAAQyV,GACX9F,EAAStV,KAAKqE,KAAK8W,YAAYxV,IAC/BA,EAAQtB,KAAKuT,OAAOjS,EAAQ,GAEhC,OAAOgN,EAAS,IAAM2C,EAAShH,KAAK,KAAO,GAC/C,CAIA,SAAA+M,CAAUN,EAAYK,EAAUE,EAAKjW,EAAKyQ,GACtC,IAAI,OAAE8B,GAAWvT,KAAMkX,GAAQ,EAC/B,IAAK,IAAIlP,EAAI0O,EAAY1O,GAAK+O,KACtBI,EAAU1F,EAAMzQ,EAAKuS,EAAOvL,EAAI,GAAIuL,EAAOvL,EAAI,MAC/CkP,EAAOlP,EACHiP,EAAM,IAHsBjP,EAAIuL,EAAOvL,EAAI,IAOvD,OAAOkP,CACX,CAIA,KAAAviB,CAAMyiB,EAAQC,EAAMrd,GAChB,IAAIsd,EAAItX,KAAKuT,OACTgE,EAAO,IAAI7C,YAAY2C,EAAOD,GAASI,EAAM,EACjD,IAAK,IAAIxP,EAAIoP,EAAQzB,EAAI,EAAG3N,EAAIqP,GAAO,CACnCE,EAAK5B,KAAO2B,EAAEtP,KACduP,EAAK5B,KAAO2B,EAAEtP,KAAOhO,EACrB,IAAII,EAAKmd,EAAK5B,KAAO2B,EAAEtP,KAAOhO,EAC9Bud,EAAK5B,KAAO2B,EAAEtP,KAAOoP,EACrBI,EAAMC,KAAKC,IAAIF,EAAKpd,EACxB,CACA,OAAO,IAAI0a,EAAWyC,EAAMC,EAAKxX,KAAKhD,IAC1C,EAEJ,SAASma,EAAU1F,EAAMzQ,EAAKhH,EAAMI,GAChC,OAAQqX,GACJ,KAAM,EAAqB,OAAOzX,EAAOgH,EACzC,KAAM,EAAyB,OAAO5G,GAAM4G,GAAOhH,EAAOgH,EAC1D,KAAK,EAAqB,OAAOhH,EAAOgH,GAAO5G,EAAK4G,EACpD,KAAK,EAAwB,OAAOhH,GAAQgH,GAAO5G,EAAK4G,EACxD,KAAK,EAAoB,OAAO5G,EAAK4G,EACrC,KAAK,EAAuB,OAAO,EAE3C,CACA,SAAS+Q,EAAYlS,EAAMmB,EAAKyQ,EAAMkG,GAGlC,IAFA,IAAIrE,EAEGzT,EAAK7F,MAAQ6F,EAAKzF,KACpBqX,EAAO,EAAI5R,EAAK7F,MAAQgH,EAAMnB,EAAK7F,KAAOgH,KAC1CyQ,GAAQ,EAAI5R,EAAKzF,IAAM4G,EAAMnB,EAAKzF,GAAK4G,IAAM,CAC9C,IAAIqR,GAAUsF,GAAY9X,aAAgBgS,GAAYhS,EAAKyB,MAAQ,EAAI,KAAOzB,EAAKwS,OACnF,IAAKA,EACD,OAAOxS,EACXA,EAAOwS,CACX,CACA,IAAIhB,EAAOsG,EAAW,EAAI5G,EAAS6G,eAEnC,GAAID,EACA,IAAK,IAAIvF,EAAOvS,EAAMwS,EAASD,EAAKC,OAAQA,EAAQD,EAAOC,EAAQA,EAASD,EAAKC,OACzED,aAAgBP,GAAYO,EAAK9Q,MAAQ,IAA+C,QAAxCgS,EAAKjB,EAAO1R,MAAMK,EAAKyQ,EAAMJ,UAA0B,IAAPiC,OAAgB,EAASA,EAAGtZ,OAASoY,EAAKpY,OAC1I6F,EAAOwS,GAEnB,OAAS,CACL,IAAIH,EAAQrS,EAAKc,MAAMK,EAAKyQ,EAAMJ,GAClC,IAAKa,EACD,OAAOrS,EACXA,EAAOqS,CACX,CACJ,CACA,MAAM2F,EACF,MAAAzG,CAAOC,EAAO,GAAK,OAAO,IAAIC,EAAWtR,KAAMqR,EAAO,CACtD,QAAA5L,CAASpN,EAAMyf,EAAS,KAAMC,EAAQ,MAClC,IAAIC,EAAIC,EAAYjY,KAAM3H,EAAMyf,EAAQC,GACxC,OAAOC,EAAEld,OAASkd,EAAE,GAAK,IAC7B,CACA,WAAAC,CAAY5f,EAAMyf,EAAS,KAAMC,EAAQ,MACrC,OAAOE,EAAYjY,KAAM3H,EAAMyf,EAAQC,EAC3C,CACA,OAAAjG,CAAQ9Q,EAAKyQ,EAAO,GAChB,OAAOM,EAAY/R,KAAMgB,EAAKyQ,GAAM,EACxC,CACA,YAAAO,CAAahR,EAAKyQ,EAAO,GACrB,OAAOM,EAAY/R,KAAMgB,EAAKyQ,GAAM,EACxC,CACA,YAAAyG,CAAalgB,GACT,OAAOmgB,EAAiBnY,KAAKqS,OAAQra,EACzC,CACA,0BAAAogB,CAA2BpX,GACvB,IAAIoR,EAAOpS,KAAKqY,YAAYrX,GAAMnB,EAAOG,KACzC,KAAOoS,GAAM,CACT,IAAIwD,EAAOxD,EAAKkG,UAChB,IAAK1C,GAAQA,EAAKxb,IAAMgY,EAAKhY,GACzB,MACAwb,EAAKvd,KAAKwX,SAAW+F,EAAK5b,MAAQ4b,EAAKxb,IACvCyF,EAAOuS,EACPA,EAAOwD,EAAK2C,aAGZnG,EAAOwD,CAEf,CACA,OAAO/V,CACX,CACA,QAAIA,GAAS,OAAOG,IAAM,CAC1B,QAAIuU,GAAS,OAAOvU,KAAKqS,MAAQ,EAErC,MAAMR,UAAiBgG,EACnB,WAAAzX,CAAYwR,EAAO5X,EAEnBsH,EAAOkX,GACHC,QACAzY,KAAK4R,MAAQA,EACb5R,KAAKhG,KAAOA,EACZgG,KAAKsB,MAAQA,EACbtB,KAAKwY,QAAUA,CACnB,CACA,QAAIngB,GAAS,OAAO2H,KAAK4R,MAAMvZ,IAAM,CACrC,QAAI8O,GAAS,OAAOnH,KAAK4R,MAAMvZ,KAAK8O,IAAM,CAC1C,MAAI/M,GAAO,OAAO4F,KAAKhG,KAAOgG,KAAK4R,MAAM9W,MAAQ,CACjD,SAAA4d,CAAU1Q,EAAGiP,EAAKjW,EAAKyQ,EAAMJ,EAAO,GAChC,IAAK,IAAIgB,EAASrS,OAAQ,CACtB,IAAK,IAAI,SAAEiR,EAAQ,UAAElQ,GAAcsR,EAAOT,MAAO7S,EAAIkY,EAAM,EAAIhG,EAASnW,QAAU,EAAGkN,GAAKjJ,EAAGiJ,GAAKiP,EAAK,CACnG,IAAI1C,EAAOtD,EAASjJ,GAAIkM,EAAQnT,EAAUiH,GAAKqK,EAAOrY,KACtD,GAAKmd,EAAU1F,EAAMzQ,EAAKkT,EAAOA,EAAQK,EAAKzZ,QAE9C,GAAIyZ,aAAgBO,EAAY,CAC5B,GAAIzD,EAAON,EAAS4H,eAChB,SACJ,IAAIrX,EAAQiT,EAAKyC,UAAU,EAAGzC,EAAKhB,OAAOzY,OAAQmc,EAAKjW,EAAMkT,EAAOzC,GACpE,GAAInQ,GAAS,EACT,OAAO,IAAIsX,EAAW,IAAIC,EAAcxG,EAAQkC,EAAMvM,EAAGkM,GAAQ,KAAM5S,EAC/E,MACK,GAAK+P,EAAON,EAAS6B,mBAAuB2B,EAAKlc,KAAKyX,aAAegJ,EAASvE,GAAQ,CACvF,IAAIzF,EACJ,KAAMuC,EAAON,EAASgI,gBAAkBjK,EAAUC,EAAYG,IAAIqF,MAAWzF,EAAQG,QACjF,OAAO,IAAI4C,EAAS/C,EAAQE,KAAMkF,EAAOlM,EAAGqK,GAChD,IAAIH,EAAQ,IAAIL,EAAS0C,EAAML,EAAOlM,EAAGqK,GACzC,OAAQhB,EAAON,EAAS6B,mBAAsBV,EAAM7Z,KAAKyX,YAAcoC,EACjEA,EAAMwG,UAAUzB,EAAM,EAAI1C,EAAKtD,SAASnW,OAAS,EAAI,EAAGmc,EAAKjW,EAAKyQ,EAC5E,CACJ,CACA,GAAKJ,EAAON,EAAS6B,mBAAsBP,EAAOha,KAAKyX,YACnD,OAAO,KAMX,GAJI9H,EADAqK,EAAO/Q,OAAS,EACZ+Q,EAAO/Q,MAAQ2V,EAEfA,EAAM,GAAK,EAAI5E,EAAOmG,QAAQ5G,MAAMX,SAASnW,OACrDuX,EAASA,EAAOmG,SACXnG,EACD,OAAO,IACf,CACJ,CACA,cAAIU,GAAe,OAAO/S,KAAK0Y,UAAU,EAAG,EAAG,EAAG,EAAwB,CAC1E,aAAIJ,GAAc,OAAOtY,KAAK0Y,UAAU1Y,KAAK4R,MAAMX,SAASnW,OAAS,GAAI,EAAG,EAAG,EAAwB,CACvG,UAAAoG,CAAWF,GAAO,OAAOhB,KAAK0Y,UAAU,EAAG,EAAG1X,EAAK,EAAqB,CACxE,WAAAqX,CAAYrX,GAAO,OAAOhB,KAAK0Y,UAAU1Y,KAAK4R,MAAMX,SAASnW,OAAS,GAAI,EAAGkG,GAAM,EAAsB,CACzG,KAAAL,CAAMK,EAAKyQ,EAAMJ,EAAO,GACpB,IAAIvC,EACJ,KAAMuC,EAAON,EAAS6G,kBAAoB9I,EAAUC,EAAYG,IAAIlP,KAAK4R,SAAW9C,EAAQG,QAAS,CACjG,IAAI+J,EAAOhY,EAAMhB,KAAKhG,KACtB,IAAK,IAAI,KAAEA,EAAI,GAAEI,KAAQ0U,EAAQG,QAC7B,IAAKwC,EAAO,EAAIzX,GAAQgf,EAAOhf,EAAOgf,KACjCvH,EAAO,EAAIrX,GAAM4e,EAAO5e,EAAK4e,GAC9B,OAAO,IAAInH,EAAS/C,EAAQE,KAAMF,EAAQG,QAAQ,GAAGjV,KAAOgG,KAAKhG,MAAO,EAAGgG,KAEvF,CACA,OAAOA,KAAK0Y,UAAU,EAAG,EAAG1X,EAAKyQ,EAAMJ,EAC3C,CACA,qBAAA4H,GACI,IAAIC,EAAMlZ,KACV,KAAOkZ,EAAI7gB,KAAKyX,aAAeoJ,EAAIV,SAC/BU,EAAMA,EAAIV,QACd,OAAOU,CACX,CACA,UAAI7G,GACA,OAAOrS,KAAKwY,QAAUxY,KAAKwY,QAAQS,wBAA0B,IACjE,CACA,eAAIjG,GACA,OAAOhT,KAAKwY,SAAWxY,KAAKsB,OAAS,EAAItB,KAAKwY,QAAQE,UAAU1Y,KAAKsB,MAAQ,EAAG,EAAG,EAAG,GAAyB,IACnH,CACA,eAAIiX,GACA,OAAOvY,KAAKwY,SAAWxY,KAAKsB,OAAS,EAAItB,KAAKwY,QAAQE,UAAU1Y,KAAKsB,MAAQ,GAAI,EAAG,EAAG,GAAyB,IACpH,CACA,QAAI0N,GAAS,OAAOhP,KAAK4R,KAAO,CAChC,MAAAuH,GAAW,OAAOnZ,KAAK4R,KAAO,CAI9B,QAAA1X,GAAa,OAAO8F,KAAK4R,MAAM1X,UAAY,EAE/C,SAAS+d,EAAYpY,EAAMxH,EAAMyf,EAAQC,GACrC,IAAIqB,EAAMvZ,EAAKuR,SAAU9C,EAAS,GAClC,IAAK8K,EAAIrG,aACL,OAAOzE,EACX,GAAc,MAAVwJ,EACA,IAAK,IAAI3H,GAAQ,GAAQA,GAErB,GADAA,EAAQiJ,EAAI/gB,KAAK0X,GAAG+H,IACfsB,EAAIpG,cACL,OAAO1E,EAEnB,OAAS,CACL,GAAa,MAATyJ,GAAiBqB,EAAI/gB,KAAK0X,GAAGgI,GAC7B,OAAOzJ,EAGX,GAFI8K,EAAI/gB,KAAK0X,GAAG1X,IACZiW,EAAO3S,KAAKyd,EAAIvZ,OACfuZ,EAAIpG,cACL,OAAgB,MAAT+E,EAAgBzJ,EAAS,EACxC,CACJ,CACA,SAAS6J,EAAiBtY,EAAM7H,EAASgQ,EAAIhQ,EAAQ8C,OAAS,GAC1D,IAAK,IAAIkP,EAAInK,EAAMmI,GAAK,EAAGgC,EAAIA,EAAEqI,OAAQ,CACrC,IAAKrI,EACD,OAAO,EACX,IAAKA,EAAE3R,KAAKyX,YAAa,CACrB,GAAI9X,EAAQgQ,IAAMhQ,EAAQgQ,IAAMgC,EAAE7C,KAC9B,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,CACA,MAAM6Q,EACF,WAAAzY,CAAYiS,EAAQkB,EAAQjS,EAAO4S,GAC/BlU,KAAKqS,OAASA,EACdrS,KAAKuT,OAASA,EACdvT,KAAKsB,MAAQA,EACbtB,KAAKkU,MAAQA,CACjB,EAEJ,MAAM0E,UAAmBf,EACrB,QAAI1Q,GAAS,OAAOnH,KAAK3H,KAAK8O,IAAM,CACpC,QAAInN,GAAS,OAAOgG,KAAKhI,QAAQkc,MAAQlU,KAAKhI,QAAQub,OAAOA,OAAOvT,KAAKsB,MAAQ,EAAI,CACrF,MAAIlH,GAAO,OAAO4F,KAAKhI,QAAQkc,MAAQlU,KAAKhI,QAAQub,OAAOA,OAAOvT,KAAKsB,MAAQ,EAAI,CACnF,WAAAlB,CAAYpI,EAASwgB,EAASlX,GAC1BmX,QACAzY,KAAKhI,QAAUA,EACfgI,KAAKwY,QAAUA,EACfxY,KAAKsB,MAAQA,EACbtB,KAAK3H,KAAOL,EAAQub,OAAOvW,IAAIsT,MAAMtY,EAAQub,OAAOA,OAAOjS,GAC/D,CACA,KAAAL,CAAMgW,EAAKjW,EAAKyQ,GACZ,IAAI,OAAE8B,GAAWvT,KAAKhI,QAClBsJ,EAAQiS,EAAOyD,UAAUhX,KAAKsB,MAAQ,EAAGiS,EAAOA,OAAOvT,KAAKsB,MAAQ,GAAI2V,EAAKjW,EAAMhB,KAAKhI,QAAQkc,MAAOzC,GAC3G,OAAOnQ,EAAQ,EAAI,KAAO,IAAIsX,EAAW5Y,KAAKhI,QAASgI,KAAMsB,EACjE,CACA,cAAIyR,GAAe,OAAO/S,KAAKiB,MAAM,EAAG,EAAG,EAAwB,CACnE,aAAIqX,GAAc,OAAOtY,KAAKiB,OAAO,EAAG,EAAG,EAAwB,CACnE,UAAAC,CAAWF,GAAO,OAAOhB,KAAKiB,MAAM,EAAGD,EAAK,EAAqB,CACjE,WAAAqX,CAAYrX,GAAO,OAAOhB,KAAKiB,OAAO,EAAGD,GAAM,EAAsB,CACrE,KAAAL,CAAMK,EAAKyQ,EAAMJ,EAAO,GACpB,GAAIA,EAAON,EAAS4H,eAChB,OAAO,KACX,IAAI,OAAEpF,GAAWvT,KAAKhI,QAClBsJ,EAAQiS,EAAOyD,UAAUhX,KAAKsB,MAAQ,EAAGiS,EAAOA,OAAOvT,KAAKsB,MAAQ,GAAImQ,EAAO,EAAI,GAAK,EAAGzQ,EAAMhB,KAAKhI,QAAQkc,MAAOzC,GACzH,OAAOnQ,EAAQ,EAAI,KAAO,IAAIsX,EAAW5Y,KAAKhI,QAASgI,KAAMsB,EACjE,CACA,UAAI+Q,GACA,OAAOrS,KAAKwY,SAAWxY,KAAKhI,QAAQqa,OAAO4G,uBAC/C,CACA,eAAAI,CAAgBpC,GACZ,OAAOjX,KAAKwY,QAAU,KAAOxY,KAAKhI,QAAQqa,OAAOqG,UAAU1Y,KAAKhI,QAAQsJ,MAAQ2V,EAAKA,EAAK,EAAG,EACjG,CACA,eAAIjE,GACA,IAAI,OAAEO,GAAWvT,KAAKhI,QAClB+f,EAAQxE,EAAOA,OAAOvT,KAAKsB,MAAQ,GACvC,OAAIyW,GAAS/X,KAAKwY,QAAUjF,EAAOA,OAAOvT,KAAKwY,QAAQlX,MAAQ,GAAKiS,EAAOA,OAAOzY,QACvE,IAAI8d,EAAW5Y,KAAKhI,QAASgI,KAAKwY,QAAST,GAC/C/X,KAAKqZ,gBAAgB,EAChC,CACA,eAAId,GACA,IAAI,OAAEhF,GAAWvT,KAAKhI,QAClB8b,EAAc9T,KAAKwY,QAAUxY,KAAKwY,QAAQlX,MAAQ,EAAI,EAC1D,OAAItB,KAAKsB,OAASwS,EACP9T,KAAKqZ,iBAAiB,GAC1B,IAAIT,EAAW5Y,KAAKhI,QAASgI,KAAKwY,QAASjF,EAAOyD,UAAUlD,EAAa9T,KAAKsB,OAAQ,EAAG,EAAG,GACvG,CACA,QAAI0N,GAAS,OAAO,IAAM,CAC1B,MAAAmK,GACI,IAAIlI,EAAW,GAAIlQ,EAAY,IAC3B,OAAEwS,GAAWvT,KAAKhI,QAClBof,EAASpX,KAAKsB,MAAQ,EAAG+V,EAAO9D,EAAOA,OAAOvT,KAAKsB,MAAQ,GAC/D,GAAI+V,EAAOD,EAAQ,CACf,IAAIpd,EAAOuZ,EAAOA,OAAOvT,KAAKsB,MAAQ,GACtC2P,EAAStV,KAAK4X,EAAO5e,MAAMyiB,EAAQC,EAAMrd,IACzC+G,EAAUpF,KAAK,EACnB,CACA,OAAO,IAAIqV,EAAKhR,KAAK3H,KAAM4Y,EAAUlQ,EAAWf,KAAK5F,GAAK4F,KAAKhG,KACnE,CAIA,QAAAE,GAAa,OAAO8F,KAAKhI,QAAQub,OAAOuD,YAAY9W,KAAKsB,MAAQ,EAErE,SAASkR,EAAU8G,GACf,IAAKA,EAAMxe,OACP,OAAO,KACX,IAAIoc,EAAO,EAAGqC,EAASD,EAAM,GAC7B,IAAK,IAAItR,EAAI,EAAGA,EAAIsR,EAAMxe,OAAQkN,IAAK,CACnC,IAAInI,EAAOyZ,EAAMtR,IACbnI,EAAK7F,KAAOuf,EAAOvf,MAAQ6F,EAAKzF,GAAKmf,EAAOnf,MAC5Cmf,EAAS1Z,EACTqX,EAAOlP,EAEf,CACA,IAAIuM,EAAOgF,aAAkB1H,GAAY0H,EAAOjY,MAAQ,EAAI,KAAOiY,EAAOlH,OACtEmH,EAAWF,EAAM3kB,QAKrB,OAJI4f,EACAiF,EAAStC,GAAQ3C,EAEjBiF,EAASC,OAAOvC,EAAM,GACnB,IAAIwC,EAAcF,EAAUD,EACvC,CACA,MAAMG,EACF,WAAAtZ,CAAYkZ,EAAOzZ,GACfG,KAAKsZ,MAAQA,EACbtZ,KAAKH,KAAOA,CAChB,CACA,QAAI0U,GAAS,OAAO/B,EAAUxS,KAAKsZ,MAAQ,EAyB/C,MAAMhI,EAIF,QAAInK,GAAS,OAAOnH,KAAK3H,KAAK8O,IAAM,CAIpC,WAAA/G,CAAYP,EAIZwR,EAAO,GAYH,GAXArR,KAAKqR,KAAOA,EAIZrR,KAAKuT,OAAS,KACdvT,KAAK2Z,MAAQ,GAIb3Z,KAAKsB,MAAQ,EACbtB,KAAK4Z,WAAa,KACd/Z,aAAgBgS,EAChB7R,KAAK6Z,UAAUha,OAEd,CACDG,KAAK4R,MAAQ/R,EAAK7H,QAAQqa,OAC1BrS,KAAKuT,OAAS1T,EAAK7H,QACnB,IAAK,IAAI8hB,EAAIja,EAAK2Y,QAASsB,EAAGA,EAAIA,EAAEtB,QAChCxY,KAAK2Z,MAAMI,QAAQD,EAAExY,OACzBtB,KAAK4Z,WAAa/Z,EAClBG,KAAKga,SAASna,EAAKyB,MACvB,CACJ,CACA,SAAAuY,CAAUha,GACN,QAAKA,IAELG,KAAK4R,MAAQ/R,EACbG,KAAK3H,KAAOwH,EAAKxH,KACjB2H,KAAKhG,KAAO6F,EAAK7F,KACjBgG,KAAK5F,GAAKyF,EAAKzF,IACR,EACX,CACA,QAAA4f,CAAS1Y,EAAOjJ,GACZ2H,KAAKsB,MAAQA,EACb,IAAI,MAAE4S,EAAK,OAAEX,GAAWvT,KAAKuT,OAI7B,OAHAvT,KAAK3H,KAAOA,GAAQkb,EAAOvW,IAAIsT,MAAMiD,EAAOA,OAAOjS,IACnDtB,KAAKhG,KAAOka,EAAQX,EAAOA,OAAOjS,EAAQ,GAC1CtB,KAAK5F,GAAK8Z,EAAQX,EAAOA,OAAOjS,EAAQ,IACjC,CACX,CAIA,KAAA2Y,CAAMpa,GACF,QAAKA,IAEDA,aAAgBgS,GAChB7R,KAAKuT,OAAS,KACPvT,KAAK6Z,UAAUha,KAE1BG,KAAKuT,OAAS1T,EAAK7H,QACZgI,KAAKga,SAASna,EAAKyB,MAAOzB,EAAKxH,OAC1C,CAIA,QAAA6B,GACI,OAAO8F,KAAKuT,OAASvT,KAAKuT,OAAOA,OAAOuD,YAAY9W,KAAKsB,OAAStB,KAAK4R,MAAM1X,UACjF,CAIA,UAAAggB,CAAWjD,EAAKjW,EAAKyQ,GACjB,IAAKzR,KAAKuT,OACN,OAAOvT,KAAKia,MAAMja,KAAK4R,MAAM8G,UAAUzB,EAAM,EAAIjX,KAAK4R,MAAMA,MAAMX,SAASnW,OAAS,EAAI,EAAGmc,EAAKjW,EAAKyQ,EAAMzR,KAAKqR,OACpH,IAAI,OAAEkC,GAAWvT,KAAKuT,OAClBjS,EAAQiS,EAAOyD,UAAUhX,KAAKsB,MAAQ,EAAGiS,EAAOA,OAAOvT,KAAKsB,MAAQ,GAAI2V,EAAKjW,EAAMhB,KAAKuT,OAAOW,MAAOzC,GAC1G,QAAInQ,EAAQ,KAEZtB,KAAK2Z,MAAMhe,KAAKqE,KAAKsB,OACdtB,KAAKga,SAAS1Y,GACzB,CAKA,UAAAyR,GAAe,OAAO/S,KAAKka,WAAW,EAAG,EAAG,EAAwB,CAIpE,SAAA5B,GAAc,OAAOtY,KAAKka,YAAY,EAAG,EAAG,EAAwB,CAIpE,UAAAhZ,CAAWF,GAAO,OAAOhB,KAAKka,WAAW,EAAGlZ,EAAK,EAAqB,CAItE,WAAAqX,CAAYrX,GAAO,OAAOhB,KAAKka,YAAY,EAAGlZ,GAAM,EAAsB,CAQ1E,KAAAL,CAAMK,EAAKyQ,EAAMJ,EAAOrR,KAAKqR,MACzB,OAAKrR,KAAKuT,SAEHlC,EAAON,EAAS4H,iBAAyB3Y,KAAKka,WAAW,EAAGlZ,EAAKyQ,GAD7DzR,KAAKia,MAAMja,KAAK4R,MAAMjR,MAAMK,EAAKyQ,EAAMJ,GAEtD,CAIA,MAAAgB,GACI,IAAKrS,KAAKuT,OACN,OAAOvT,KAAK6Z,UAAW7Z,KAAKqR,KAAON,EAAS6B,iBAAoB5S,KAAK4R,MAAM4G,QAAUxY,KAAK4R,MAAMS,QACpG,GAAIrS,KAAK2Z,MAAM7e,OACX,OAAOkF,KAAKga,SAASha,KAAK2Z,MAAM3D,OACpC,IAAI3D,EAAUrS,KAAKqR,KAAON,EAAS6B,iBAAoB5S,KAAKuT,OAAOlB,OAASrS,KAAKuT,OAAOlB,OAAO4G,wBAE/F,OADAjZ,KAAKuT,OAAS,KACPvT,KAAK6Z,UAAUxH,EAC1B,CAIA,OAAA8H,CAAQlD,GACJ,IAAKjX,KAAKuT,OACN,QAAQvT,KAAK4R,MAAM4G,SACbxY,KAAKia,MAAMja,KAAK4R,MAAMtQ,MAAQ,EAAI,KAC9BtB,KAAK4R,MAAM4G,QAAQE,UAAU1Y,KAAK4R,MAAMtQ,MAAQ2V,EAAKA,EAAK,EAAG,EAAuBjX,KAAKqR,OACvG,IAAI,OAAEkC,GAAWvT,KAAKuT,OAAQ6G,EAAIpa,KAAK2Z,MAAM7e,OAAS,EACtD,GAAImc,EAAM,EAAG,CACT,IAAInD,EAAcsG,EAAI,EAAI,EAAIpa,KAAK2Z,MAAMS,GAAK,EAC9C,GAAIpa,KAAKsB,OAASwS,EACd,OAAO9T,KAAKga,SAASzG,EAAOyD,UAAUlD,EAAa9T,KAAKsB,OAAQ,EAAG,EAAG,GAC9E,KACK,CACD,IAAIyW,EAAQxE,EAAOA,OAAOvT,KAAKsB,MAAQ,GACvC,GAAIyW,GAASqC,EAAI,EAAI7G,EAAOA,OAAOzY,OAASyY,EAAOA,OAAOvT,KAAK2Z,MAAMS,GAAK,IACtE,OAAOpa,KAAKga,SAASjC,EAC7B,CACA,OAAOqC,EAAI,GAAIpa,KAAKia,MAAMja,KAAKuT,OAAOlB,OAAOqG,UAAU1Y,KAAKuT,OAAOjS,MAAQ2V,EAAKA,EAAK,EAAG,EAAuBjX,KAAKqR,MACxH,CAIA,WAAA2B,GAAgB,OAAOhT,KAAKma,QAAQ,EAAI,CAIxC,WAAA5B,GAAgB,OAAOvY,KAAKma,SAAS,EAAI,CACzC,UAAAE,CAAWpD,GACP,IAAI3V,EAAO+Q,GAAQ,OAAEkB,GAAWvT,KAChC,GAAIuT,EAAQ,CACR,GAAI0D,EAAM,GACN,GAAIjX,KAAKsB,MAAQiS,EAAOA,OAAOA,OAAOzY,OAClC,OAAO,OAGX,IAAK,IAAIkN,EAAI,EAAGA,EAAIhI,KAAKsB,MAAO0G,IAC5B,GAAIuL,EAAOA,OAAOA,OAAOvL,EAAI,GAAKhI,KAAKsB,MACnC,OAAO,IAEhBA,QAAO+Q,UAAWkB,EACzB,OAEOjS,QAAOkX,QAASnG,GAAWrS,KAAK4R,OAEvC,KAAOS,IAAU/Q,QAAOkX,QAASnG,GAAWA,GACxC,GAAI/Q,GAAS,EACT,IAAK,IAAI0G,EAAI1G,EAAQ2V,EAAKlY,EAAIkY,EAAM,GAAK,EAAI5E,EAAOT,MAAMX,SAASnW,OAAQkN,GAAKjJ,EAAGiJ,GAAKiP,EAAK,CACzF,IAAIhW,EAAQoR,EAAOT,MAAMX,SAASjJ,GAClC,GAAKhI,KAAKqR,KAAON,EAAS6B,kBACtB3R,aAAiB6T,IAChB7T,EAAM5I,KAAKyX,aACZgJ,EAAS7X,GACT,OAAO,CACf,CAER,OAAO,CACX,CACA,IAAAqZ,CAAKrD,EAAKtW,GACN,GAAIA,GAASX,KAAKka,WAAWjD,EAAK,EAAG,GACjC,OAAO,EACX,OAAS,CACL,GAAIjX,KAAKma,QAAQlD,GACb,OAAO,EACX,GAAIjX,KAAKqa,WAAWpD,KAASjX,KAAKqS,SAC9B,OAAO,CACf,CACJ,CAQA,IAAAkC,CAAK5T,GAAQ,GAAQ,OAAOX,KAAKsa,KAAK,EAAG3Z,EAAQ,CAOjD,IAAA4Z,CAAK5Z,GAAQ,GAAQ,OAAOX,KAAKsa,MAAM,EAAG3Z,EAAQ,CAMlD,MAAAgR,CAAO3Q,EAAKyQ,EAAO,GAEf,MAAOzR,KAAKhG,MAAQgG,KAAK5F,KACpBqX,EAAO,EAAIzR,KAAKhG,MAAQgH,EAAMhB,KAAKhG,KAAOgH,KAC1CyQ,GAAQ,EAAIzR,KAAK5F,IAAM4G,EAAMhB,KAAK5F,GAAK4G,KACnChB,KAAKqS,WAGd,KAAOrS,KAAKka,WAAW,EAAGlZ,EAAKyQ,KAC/B,OAAOzR,IACX,CAKA,QAAIH,GACA,IAAKG,KAAKuT,OACN,OAAOvT,KAAK4R,MAChB,IAAI4I,EAAQxa,KAAK4Z,WAAYtL,EAAS,KAAM2F,EAAQ,EACpD,GAAIuG,GAASA,EAAMxiB,SAAWgI,KAAKuT,OAC/BnB,EAAM,IAAK,IAAI9Q,EAAQtB,KAAKsB,MAAO8Y,EAAIpa,KAAK2Z,MAAM7e,OAAQsf,GAAK,GAAI,CAC/D,IAAK,IAAIvH,EAAI2H,EAAO3H,EAAGA,EAAIA,EAAE2F,QACzB,GAAI3F,EAAEvR,OAASA,EAAO,CAClB,GAAIA,GAAStB,KAAKsB,MACd,OAAOuR,EACXvE,EAASuE,EACToB,EAAQmG,EAAI,EACZ,MAAMhI,CACV,CACJ9Q,EAAQtB,KAAK2Z,QAAQS,EACzB,CAEJ,IAAK,IAAIpS,EAAIiM,EAAOjM,EAAIhI,KAAK2Z,MAAM7e,OAAQkN,IACvCsG,EAAS,IAAIsK,EAAW5Y,KAAKuT,OAAQjF,EAAQtO,KAAK2Z,MAAM3R,IAC5D,OAAOhI,KAAK4Z,WAAa,IAAIhB,EAAW5Y,KAAKuT,OAAQjF,EAAQtO,KAAKsB,MACtE,CAMA,QAAI0N,GACA,OAAOhP,KAAKuT,OAAS,KAAOvT,KAAK4R,MAAMA,KAC3C,CAOA,OAAAlR,CAAQC,EAAO+R,GACX,IAAK,IAAIuB,EAAQ,IAAK,CAClB,IAAIwG,GAAY,EAChB,GAAIza,KAAK3H,KAAKyX,cAA+B,IAAhBnP,EAAMX,MAAiB,CAChD,GAAIA,KAAK+S,aAAc,CACnBkB,IACA,QACJ,CACKjU,KAAK3H,KAAKyX,cACX2K,GAAY,EACpB,CACA,OAAS,CAIL,GAHIA,GAAa/H,GACbA,EAAM1S,MACVya,EAAYza,KAAK3H,KAAKyX,aACjBmE,EACD,OACJ,GAAIjU,KAAKgT,cACL,MACJhT,KAAKqS,SACL4B,IACAwG,GAAY,CAChB,CACJ,CACJ,CAMA,YAAAvC,CAAalgB,GACT,IAAKgI,KAAKuT,OACN,OAAO4E,EAAiBnY,KAAKH,KAAKwS,OAAQra,GAC9C,IAAI,OAAEub,GAAWvT,KAAKuT,QAAQ,MAAEjD,GAAUiD,EAAOvW,IACjD,IAAK,IAAIgL,EAAIhQ,EAAQ8C,OAAS,EAAGsf,EAAIpa,KAAK2Z,MAAM7e,OAAS,EAAGkN,GAAK,EAAGoS,IAAK,CACrE,GAAIA,EAAI,EACJ,OAAOjC,EAAiBnY,KAAK4R,MAAO5Z,EAASgQ,GACjD,IAAI3P,EAAOiY,EAAMiD,EAAOA,OAAOvT,KAAK2Z,MAAMS,KAC1C,IAAK/hB,EAAKyX,YAAa,CACnB,GAAI9X,EAAQgQ,IAAMhQ,EAAQgQ,IAAM3P,EAAK8O,KACjC,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,EAEJ,SAAS8Q,EAAS9J,GACd,OAAOA,EAAKiC,SAASyJ,MAAKxJ,GAAMA,aAAc4D,IAAe5D,EAAG7Y,KAAKyX,aAAegJ,EAAS5H,IACjG,CAgOA,MAAMyJ,EAAgB,IAAI9J,QAC1B,SAASyF,EAASsE,EAAa/a,GAC3B,IAAK+a,EAAY9K,aAAejQ,aAAgBiV,GAAcjV,EAAKxH,MAAQuiB,EACvE,OAAO,EACX,IAAIxG,EAAOuG,EAAczL,IAAIrP,GAC7B,GAAY,MAARuU,EAAc,CACdA,EAAO,EACP,IAAK,IAAInT,KAASpB,EAAKoR,SAAU,CAC7B,GAAIhQ,EAAM5I,MAAQuiB,KAAiB3Z,aAAiB+P,GAAO,CACvDoD,EAAO,EACP,KACJ,CACAA,GAAQkC,EAASsE,EAAa3Z,EAClC,CACA0Z,EAAc3d,IAAI6C,EAAMuU,EAC5B,CACA,OAAOA,CACX,CACA,SAASjB,EAETyH,EAEA3J,EAAUlQ,EAEV/G,EAAMI,EAEN8Z,EAEApZ,EAEA+f,EAEAC,GACI,IAAIC,EAAQ,EACZ,IAAK,IAAI/S,EAAIhO,EAAMgO,EAAI5N,EAAI4N,IACvB+S,GAASzE,EAASsE,EAAa3J,EAASjJ,IAC5C,IAAIgT,EAAWvD,KAAKwD,KAAc,IAARF,EAAe,GACrChG,EAAgB,GAAIC,EAAiB,GA2BzC,OA1BA,SAASkG,EAAOjK,EAAUlQ,EAAW/G,EAAMI,EAAI+gB,GAC3C,IAAK,IAAInT,EAAIhO,EAAMgO,EAAI5N,GAAK,CACxB,IAAIghB,EAAYpT,EAAGqT,EAAata,EAAUiH,GAAIsT,EAAYhF,EAASsE,EAAa3J,EAASjJ,IAEzF,IADAA,IACOA,EAAI5N,EAAI4N,IAAK,CAChB,IAAIuT,EAAWjF,EAASsE,EAAa3J,EAASjJ,IAC9C,GAAIsT,EAAYC,GAAYP,EACxB,MACJM,GAAaC,CACjB,CACA,GAAIvT,GAAKoT,EAAY,EAAG,CACpB,GAAIE,EAAYN,EAAU,CACtB,IAAIQ,EAAOvK,EAASmK,GACpBF,EAAOM,EAAKvK,SAAUuK,EAAKza,UAAW,EAAGya,EAAKvK,SAASnW,OAAQiG,EAAUqa,GAAaD,GACtF,QACJ,CACApG,EAAcpZ,KAAKsV,EAASmK,GAChC,KACK,CACD,IAAItgB,EAASiG,EAAUiH,EAAI,GAAKiJ,EAASjJ,EAAI,GAAGlN,OAASugB,EACzDtG,EAAcpZ,KAAKwX,EAAayH,EAAa3J,EAAUlQ,EAAWqa,EAAWpT,EAAGqT,EAAYvgB,EAAQ,KAAMggB,GAC9G,CACA9F,EAAerZ,KAAK0f,EAAaF,EAASjH,EAC9C,CACJ,CACAgH,CAAOjK,EAAUlQ,EAAW/G,EAAMI,EAAI,IAC9BygB,GAASC,GAAQ/F,EAAeC,EAAgBla,EAC5D,CAkKA,MAAM2gB,EAWF,UAAAC,CAAWC,EAAOC,EAAWC,GAIzB,MAHoB,iBAATF,IACPA,EAAQ,IAAIG,EAAYH,IAC5BE,EAAUA,EAAwCA,EAAO/gB,OAAS+gB,EAAOhY,KAAImU,GAAK,IAAIlK,EAAMkK,EAAEhe,KAAMge,EAAE5d,MAAO,CAAC,IAAI0T,EAAM,EAAG,IAAxG,CAAC,IAAIA,EAAM,EAAG6N,EAAM7gB,SAChCkF,KAAK+b,YAAYJ,EAAOC,GAAa,GAAIC,EACpD,CAIA,KAAApb,CAAMkb,EAAOC,EAAWC,GACpB,IAAIpb,EAAQT,KAAK0b,WAAWC,EAAOC,EAAWC,GAC9C,OAAS,CACL,IAAIG,EAAOvb,EAAMwb,UACjB,GAAID,EACA,OAAOA,CACf,CACJ,EAEJ,MAAMF,EACF,WAAA1b,CAAY0G,GACR9G,KAAK8G,OAASA,CAClB,CACA,UAAIhM,GAAW,OAAOkF,KAAK8G,OAAOhM,MAAQ,CAC1C,KAAAohB,CAAMliB,GAAQ,OAAOgG,KAAK8G,OAAOnS,MAAMqF,EAAO,CAC9C,cAAImiB,GAAe,OAAO,CAAO,CACjC,IAAAC,CAAKpiB,EAAMI,GAAM,OAAO4F,KAAK8G,OAAOnS,MAAMqF,EAAMI,EAAK,EAuCpC,IAAI2T,EAAS,CAAEE,SAAS,ICrvD7C,MAAMoO,EAIF,WAAAjc,CAIA4J,EAKA2P,EAIA2C,EAQAC,EAIAvb,EAMAwb,EAOAjJ,EASAkJ,EAIAC,EAIA7N,EAAY,EAQZwD,GACIrS,KAAKgK,EAAIA,EACThK,KAAK2Z,MAAQA,EACb3Z,KAAKsc,MAAQA,EACbtc,KAAKuc,UAAYA,EACjBvc,KAAKgB,IAAMA,EACXhB,KAAKwc,MAAQA,EACbxc,KAAKuT,OAASA,EACdvT,KAAKyc,WAAaA,EAClBzc,KAAK0c,WAAaA,EAClB1c,KAAK6O,UAAYA,EACjB7O,KAAKqS,OAASA,CAClB,CAIA,QAAAnY,GACI,MAAO,IAAI8F,KAAK2Z,MAAMtW,QAAO,CAACsZ,EAAG3U,IAAMA,EAAI,GAAK,IAAGkO,OAAOlW,KAAKsc,WAAWtc,KAAKgB,MAAMhB,KAAKwc,MAAQ,IAAMxc,KAAKwc,MAAQ,IACzH,CAKA,YAAOtI,CAAMlK,EAAGsS,EAAOtb,EAAM,GACzB,IAAI4b,EAAK5S,EAAE3O,OAAOrD,QAClB,OAAO,IAAIqkB,EAAMrS,EAAG,GAAIsS,EAAOtb,EAAKA,EAAK,EAAG,GAAI,EAAG4b,EAAK,IAAIC,EAAaD,EAAIA,EAAG1I,OAAS,KAAM,EAAG,KACtG,CAOA,WAAIlc,GAAY,OAAOgI,KAAK0c,WAAa1c,KAAK0c,WAAW1kB,QAAU,IAAM,CAMzE,SAAA8kB,CAAUR,EAAOpI,GACblU,KAAK2Z,MAAMhe,KAAKqE,KAAKsc,MAAOpI,EAAOlU,KAAKyc,WAAazc,KAAKuT,OAAOzY,QACjEkF,KAAKsc,MAAQA,CACjB,CAKA,MAAAS,CAAOC,GACH,IAAI1J,EACJ,IAAIW,EAAQ+I,GAAU,GAAkC3kB,EAAgB,MAAT2kB,GAC3D,OAAE3hB,GAAW2E,KAAKgK,EAClBiT,EAAkBjd,KAAKuc,UAAYvc,KAAKgB,IAAM,GAC9Cic,GACAjd,KAAKkd,aAAald,KAAKgB,KAC3B,IAAImc,EAAQ9hB,EAAO+hB,kBAAkB/kB,GAGrC,GAFI8kB,IACAnd,KAAKwc,OAASW,GACL,GAATlJ,EAOA,OANAjU,KAAK8c,UAAUzhB,EAAOgiB,QAAQrd,KAAKsc,MAAOjkB,GAAM,GAAO2H,KAAKuc,WAGxDlkB,EAAOgD,EAAOiiB,eACdtd,KAAKud,UAAUllB,EAAM2H,KAAKuc,UAAWvc,KAAKuc,UAAWU,EAAkB,EAAI,GAAG,QAClFjd,KAAKwd,cAAcnlB,EAAM2H,KAAKuc,WAQlC,IAAIxG,EAAO/V,KAAK2Z,MAAM7e,OAAwB,GAAbmZ,EAAQ,IAAoB,OAAT+I,EAAwC,EAAI,GAC5F9I,EAAQ6B,EAAO/V,KAAK2Z,MAAM5D,EAAO,GAAK/V,KAAKgK,EAAE6R,OAAO,GAAG7hB,KAAMoa,EAAOpU,KAAKuc,UAAYrI,EAIrFE,GAAQ,OAAqF,QAA5Cd,EAAKtT,KAAKgK,EAAE3O,OAAOmY,QAAQlD,MAAMjY,UAA0B,IAAPib,OAAgB,EAASA,EAAGxD,eAC7HoE,GAASlU,KAAKgK,EAAEyT,uBAChBzd,KAAKgK,EAAE0T,oBACP1d,KAAKgK,EAAE2T,qBAAuBvJ,GAEzBpU,KAAKgK,EAAE2T,qBAAuBvJ,IACnCpU,KAAKgK,EAAE0T,kBAAoB,EAC3B1d,KAAKgK,EAAEyT,sBAAwBvJ,EAC/BlU,KAAKgK,EAAE2T,qBAAuBvJ,IAGtC,IAAIqI,EAAa1G,EAAO/V,KAAK2Z,MAAM5D,EAAO,GAAK,EAAG6H,EAAQ5d,KAAKyc,WAAazc,KAAKuT,OAAOzY,OAAS2hB,EAEjG,GAAIpkB,EAAOgD,EAAOiiB,eAA2B,OAATN,EAA0C,CAC1E,IAAIhc,EAAM3F,EAAOwiB,UAAU7d,KAAKsc,MAAO,GAA6Btc,KAAKgB,IAAMhB,KAAKuc,UACpFvc,KAAKud,UAAUllB,EAAM6b,EAAOlT,EAAK4c,EAAQ,GAAG,EAChD,CACA,GAAa,OAATZ,EACAhd,KAAKsc,MAAQtc,KAAK2Z,MAAM5D,OAEvB,CACD,IAAI+H,EAAc9d,KAAK2Z,MAAM5D,EAAO,GACpC/V,KAAKsc,MAAQjhB,EAAOgiB,QAAQS,EAAazlB,GAAM,EACnD,CACA,KAAO2H,KAAK2Z,MAAM7e,OAASib,GACvB/V,KAAK2Z,MAAM3D,MACfhW,KAAKwd,cAAcnlB,EAAM6b,EAC7B,CAKA,SAAAqJ,CAAUQ,EAAM7J,EAAOC,EAAKC,EAAO,EAAG4J,GAAW,GAC7C,GAAY,GAARD,KACE/d,KAAK2Z,MAAM7e,QAAUkF,KAAK2Z,MAAM3Z,KAAK2Z,MAAM7e,OAAS,GAAKkF,KAAKuT,OAAOzY,OAASkF,KAAKyc,YAAa,CAElG,IAAIrD,EAAMpZ,KAAMwP,EAAMxP,KAAKuT,OAAOzY,OAKlC,GAJW,GAAP0U,GAAY4J,EAAI/G,SAChB7C,EAAM4J,EAAIqD,WAAarD,EAAI/G,OAAOoK,WAClCrD,EAAMA,EAAI/G,QAEV7C,EAAM,GAA4B,GAAvB4J,EAAI7F,OAAO/D,EAAM,IAA0B4J,EAAI7F,OAAO/D,EAAM,IAAM,EAAG,CAChF,GAAI0E,GAASC,EACT,OACJ,GAAIiF,EAAI7F,OAAO/D,EAAM,IAAM0E,EAEvB,YADAkF,EAAI7F,OAAO/D,EAAM,GAAK2E,EAG9B,CACJ,CACA,GAAK6J,GAAYhe,KAAKgB,KAAOmT,EAGxB,CACD,IAAI7S,EAAQtB,KAAKuT,OAAOzY,OACxB,GAAIwG,EAAQ,GAA+B,GAA1BtB,KAAKuT,OAAOjS,EAAQ,GAAwB,CACzD,IAAI2c,GAAW,EACf,IAAK,IAAI7L,EAAO9Q,EAAO8Q,EAAO,GAAKpS,KAAKuT,OAAOnB,EAAO,GAAK+B,EAAK/B,GAAQ,EACpE,GAAIpS,KAAKuT,OAAOnB,EAAO,IAAM,EAAG,CAC5B6L,GAAW,EACX,KACJ,CAEJ,GAAIA,EACA,KAAO3c,EAAQ,GAAKtB,KAAKuT,OAAOjS,EAAQ,GAAK6S,GAEzCnU,KAAKuT,OAAOjS,GAAStB,KAAKuT,OAAOjS,EAAQ,GACzCtB,KAAKuT,OAAOjS,EAAQ,GAAKtB,KAAKuT,OAAOjS,EAAQ,GAC7CtB,KAAKuT,OAAOjS,EAAQ,GAAKtB,KAAKuT,OAAOjS,EAAQ,GAC7CtB,KAAKuT,OAAOjS,EAAQ,GAAKtB,KAAKuT,OAAOjS,EAAQ,GAC7CA,GAAS,EACL8S,EAAO,IACPA,GAAQ,EAExB,CACApU,KAAKuT,OAAOjS,GAASyc,EACrB/d,KAAKuT,OAAOjS,EAAQ,GAAK4S,EACzBlU,KAAKuT,OAAOjS,EAAQ,GAAK6S,EACzBnU,KAAKuT,OAAOjS,EAAQ,GAAK8S,CAC7B,MA5BIpU,KAAKuT,OAAO5X,KAAKoiB,EAAM7J,EAAOC,EAAKC,EA6B3C,CAKA,KAAA8J,CAAMlB,EAAQ3kB,EAAM6b,EAAOC,GACvB,GAAa,OAAT6I,EACAhd,KAAK8c,UAAmB,MAATE,EAAuChd,KAAKgB,UAE1D,GAAc,OAATgc,EAaNhd,KAAKgB,IAAMmT,EACXnU,KAAKme,aAAa9lB,EAAM6b,GACpB7b,GAAQ2H,KAAKgK,EAAE3O,OAAO+iB,SACtBpe,KAAKuT,OAAO5X,KAAKtD,EAAM6b,EAAOC,EAAK,OAhBY,CACnD,IAAIkK,EAAYrB,GAAQ,OAAE3hB,GAAW2E,KAAKgK,GACtCmK,EAAMnU,KAAKgB,KAAO3I,GAAQgD,EAAO+iB,WACjCpe,KAAKgB,IAAMmT,EACN9Y,EAAOwiB,UAAUQ,EAAW,KAC7Bre,KAAKuc,UAAYpI,IAEzBnU,KAAK8c,UAAUuB,EAAWnK,GAC1BlU,KAAKme,aAAa9lB,EAAM6b,GACpB7b,GAAQgD,EAAO+iB,SACfpe,KAAKuT,OAAO5X,KAAKtD,EAAM6b,EAAOC,EAAK,EAC3C,CAOJ,CAKA,KAAAmK,CAAMtB,EAAQzI,EAAMgK,EAAWC,GACd,MAATxB,EACAhd,KAAK+c,OAAOC,GAEZhd,KAAKke,MAAMlB,EAAQzI,EAAMgK,EAAWC,EAC5C,CAKA,OAAAC,CAAQ/mB,EAAO6c,GACX,IAAIjT,EAAQtB,KAAKgK,EAAE0J,OAAO5Y,OAAS,GAC/BwG,EAAQ,GAAKtB,KAAKgK,EAAE0J,OAAOpS,IAAU5J,KACrCsI,KAAKgK,EAAE0J,OAAO/X,KAAKjE,GACnB4J,KAEJ,IAAI4S,EAAQlU,KAAKgB,IACjBhB,KAAKuc,UAAYvc,KAAKgB,IAAMkT,EAAQxc,EAAMoD,OAC1CkF,KAAK8c,UAAUvI,EAAML,GACrBlU,KAAKuT,OAAO5X,KAAK2F,EAAO4S,EAAOlU,KAAKuc,WAAY,GAC5Cvc,KAAK0c,YACL1c,KAAK0e,cAAc1e,KAAK0c,WAAWiC,QAAQC,MAAM5e,KAAK0c,WAAW1kB,QAASN,EAAOsI,KAAMA,KAAKgK,EAAE6U,OAAOC,MAAM9e,KAAKgB,IAAMtJ,EAAMoD,SACpI,CAOA,KAAA3B,GACI,IAAIkZ,EAASrS,KACT+e,EAAM1M,EAAOkB,OAAOzY,OAKxB,KAAOikB,EAAM,GAAK1M,EAAOkB,OAAOwL,EAAM,GAAK1M,EAAOkK,WAC9CwC,GAAO,EACX,IAAIxL,EAASlB,EAAOkB,OAAO5e,MAAMoqB,GAAMhJ,EAAO1D,EAAOoK,WAAasC,EAElE,KAAO1M,GAAU0D,GAAQ1D,EAAOoK,YAC5BpK,EAASA,EAAOA,OACpB,OAAO,IAAIgK,EAAMrc,KAAKgK,EAAGhK,KAAK2Z,MAAMhlB,QAASqL,KAAKsc,MAAOtc,KAAKuc,UAAWvc,KAAKgB,IAAKhB,KAAKwc,MAAOjJ,EAAQwC,EAAM/V,KAAK0c,WAAY1c,KAAK6O,UAAWwD,EAClJ,CAKA,eAAA2M,CAAgBzK,EAAMiK,GAClB,IAAIS,EAAS1K,GAAQvU,KAAKgK,EAAE3O,OAAO+iB,QAC/Ba,GACAjf,KAAKud,UAAUhJ,EAAMvU,KAAKgB,IAAKwd,EAAS,GAC5Cxe,KAAKud,UAAU,EAAkBvd,KAAKgB,IAAKwd,EAASS,EAAS,EAAI,GACjEjf,KAAKgB,IAAMhB,KAAKuc,UAAYiC,EAC5Bxe,KAAKwc,OAAS,GAClB,CAOA,QAAA0C,CAASnB,GACL,IAAK,IAAIoB,EAAM,IAAIC,EAAepf,QAAS,CACvC,IAAIgd,EAAShd,KAAKgK,EAAE3O,OAAOgkB,UAAUF,EAAI7C,MAAO,IAAqCtc,KAAKgK,EAAE3O,OAAOikB,UAAUH,EAAI7C,MAAOyB,GACxH,GAAc,GAAVf,EACA,OAAO,EACX,KAAc,MAATA,GACD,OAAO,EACXmC,EAAIpC,OAAOC,EACf,CACJ,CAMA,eAAAuC,CAAgBhL,GACZ,GAAIvU,KAAK2Z,MAAM7e,QAAU,IACrB,MAAO,GACX,IAAI0kB,EAAaxf,KAAKgK,EAAE3O,OAAOmkB,WAAWxf,KAAKsc,OAC/C,GAAIkD,EAAW1kB,OAAS,GAAgCkF,KAAK2Z,MAAM7e,QAAU,IAA0C,CACnH,IAAI2kB,EAAO,GACX,IAAK,IAAW/X,EAAPM,EAAI,EAAMA,EAAIwX,EAAW1kB,OAAQkN,GAAK,GACtCN,EAAI8X,EAAWxX,EAAI,KAAOhI,KAAKsc,OAAStc,KAAKgK,EAAE3O,OAAOikB,UAAU5X,EAAG6M,IACpEkL,EAAK9jB,KAAK6jB,EAAWxX,GAAIN,GAEjC,GAAI1H,KAAK2Z,MAAM7e,OAAS,IACpB,IAAK,IAAIkN,EAAI,EAAGyX,EAAK3kB,OAAS,GAAgCkN,EAAIwX,EAAW1kB,OAAQkN,GAAK,EAAG,CACzF,IAAIN,EAAI8X,EAAWxX,EAAI,GAClByX,EAAK/E,MAAK,CAACgF,EAAG1X,IAAW,EAAJA,GAAU0X,GAAKhY,KACrC+X,EAAK9jB,KAAK6jB,EAAWxX,GAAIN,EACjC,CACJ8X,EAAaC,CACjB,CACA,IAAInR,EAAS,GACb,IAAK,IAAItG,EAAI,EAAGA,EAAIwX,EAAW1kB,QAAUwT,EAAOxT,OAAS,EAAyBkN,GAAK,EAAG,CACtF,IAAIN,EAAI8X,EAAWxX,EAAI,GACvB,GAAIN,GAAK1H,KAAKsc,MACV,SACJ,IAAI3C,EAAQ3Z,KAAK7G,QACjBwgB,EAAMmD,UAAUpV,EAAG1H,KAAKgB,KACxB2Y,EAAM4D,UAAU,EAAkB5D,EAAM3Y,IAAK2Y,EAAM3Y,IAAK,GAAG,GAC3D2Y,EAAMwE,aAAaqB,EAAWxX,GAAIhI,KAAKgB,KACvC2Y,EAAM4C,UAAYvc,KAAKgB,IACvB2Y,EAAM6C,OAAS,IACflO,EAAO3S,KAAKge,EAChB,CACA,OAAOrL,CACX,CAMA,WAAAqR,GACI,IAAI,OAAEtkB,GAAW2E,KAAKgK,EAClB+S,EAAS1hB,EAAOgkB,UAAUrf,KAAKsc,MAAO,GAC1C,KAAc,MAATS,GACD,OAAO,EACX,IAAK1hB,EAAOukB,YAAY5f,KAAKsc,MAAOS,GAAS,CACzC,IAAI9I,EAAQ8I,GAAU,GAAkCgB,EAAgB,MAAThB,EAC3D5kB,EAAS6H,KAAK2Z,MAAM7e,OAAiB,EAARmZ,EACjC,GAAI9b,EAAS,GAAKkD,EAAOgiB,QAAQrd,KAAK2Z,MAAMxhB,GAAS4lB,GAAM,GAAS,EAAG,CACnE,IAAI8B,EAAS7f,KAAK8f,sBAClB,GAAc,MAAVD,EACA,OAAO,EACX9C,EAAS8C,CACb,CACA7f,KAAKud,UAAU,EAAkBvd,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKwc,OAAS,GAClB,CAGA,OAFAxc,KAAKuc,UAAYvc,KAAKgB,IACtBhB,KAAK+c,OAAOA,IACL,CACX,CAMA,mBAAA+C,GACI,IAAI,OAAEzkB,GAAW2E,KAAKgK,EAAG+V,EAAO,GAC5BC,EAAU,CAAC1D,EAAOrI,KAClB,IAAI8L,EAAKlf,SAASyb,GAGlB,OADAyD,EAAKpkB,KAAK2gB,GACHjhB,EAAO4kB,WAAW3D,GAAQU,IAC7B,GAAa,OAATA,QACC,GAAa,MAATA,EAAwC,CAC7C,IAAIkD,GAAUlD,GAAU,IAAoC/I,EAC5D,GAAIiM,EAAS,EAAG,CACZ,IAAInC,EAAgB,MAATf,EAAuC7kB,EAAS6H,KAAK2Z,MAAM7e,OAAkB,EAATolB,EAC/E,GAAI/nB,GAAU,GAAKkD,EAAOgiB,QAAQrd,KAAK2Z,MAAMxhB,GAAS4lB,GAAM,IAAU,EAClE,OAAQmC,GAAU,GAAoC,MAAgCnC,CAC9F,CACJ,KACK,CACD,IAAI5N,EAAQ6P,EAAQhD,EAAQ/I,EAAQ,GACpC,GAAa,MAAT9D,EACA,OAAOA,CACf,IACF,EAEN,OAAO6P,EAAQhgB,KAAKsc,MAAO,EAC/B,CAIA,QAAA6D,GACI,MAAQngB,KAAKgK,EAAE3O,OAAOwiB,UAAU7d,KAAKsc,MAAO,IACxC,IAAKtc,KAAK2f,cAAe,CACrB3f,KAAKud,UAAU,EAAkBvd,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxD,KACJ,CAEJ,OAAOhB,IACX,CAMA,WAAIogB,GACA,GAAyB,GAArBpgB,KAAK2Z,MAAM7e,OACX,OAAO,EACX,IAAI,OAAEO,GAAW2E,KAAKgK,EACtB,OAAgF,OAAzE3O,EAAOkE,KAAKlE,EAAOgkB,UAAUrf,KAAKsc,MAAO,MAC3CjhB,EAAOgkB,UAAUrf,KAAKsc,MAAO,EACtC,CAMA,OAAA+D,GACIrgB,KAAKud,UAAU,EAAkBvd,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKsc,MAAQtc,KAAK2Z,MAAM,GACxB3Z,KAAK2Z,MAAM7e,OAAS,CACxB,CAIA,SAAAwlB,CAAUC,GACN,GAAIvgB,KAAKsc,OAASiE,EAAMjE,OAAStc,KAAK2Z,MAAM7e,QAAUylB,EAAM5G,MAAM7e,OAC9D,OAAO,EACX,IAAK,IAAIkN,EAAI,EAAGA,EAAIhI,KAAK2Z,MAAM7e,OAAQkN,GAAK,EACxC,GAAIhI,KAAK2Z,MAAM3R,IAAMuY,EAAM5G,MAAM3R,GAC7B,OAAO,EACf,OAAO,CACX,CAIA,UAAI3M,GAAW,OAAO2E,KAAKgK,EAAE3O,MAAQ,CAKrC,cAAAmlB,CAAeC,GAAa,OAAOzgB,KAAKgK,EAAE3O,OAAOqlB,QAAQrR,MAAMoR,EAAY,CAC3E,YAAAtC,CAAaJ,EAAM7J,GACXlU,KAAK0c,YACL1c,KAAK0e,cAAc1e,KAAK0c,WAAWiC,QAAQT,MAAMle,KAAK0c,WAAW1kB,QAAS+lB,EAAM/d,KAAMA,KAAKgK,EAAE6U,OAAOC,MAAM5K,IAClH,CACA,aAAAsJ,CAAcO,EAAM7J,GACZlU,KAAK0c,YACL1c,KAAK0e,cAAc1e,KAAK0c,WAAWiC,QAAQ5B,OAAO/c,KAAK0c,WAAW1kB,QAAS+lB,EAAM/d,KAAMA,KAAKgK,EAAE6U,OAAOC,MAAM5K,IACnH,CAIA,WAAAyM,GACI,IAAI/K,EAAO5V,KAAKuT,OAAOzY,OAAS,GAC5B8a,EAAO,IAA2B,GAAtB5V,KAAKuT,OAAOqC,KACxB5V,KAAKuT,OAAO5X,KAAKqE,KAAK0c,WAAWkE,KAAM5gB,KAAKgB,IAAKhB,KAAKgB,KAAM,EACpE,CAIA,aAAA6f,GACI,IAAIjL,EAAO5V,KAAKuT,OAAOzY,OAAS,GAC5B8a,EAAO,IAA2B,GAAtB5V,KAAKuT,OAAOqC,KACxB5V,KAAKuT,OAAO5X,KAAKqE,KAAK6O,UAAW7O,KAAKgB,IAAKhB,KAAKgB,KAAM,EAC9D,CACA,aAAA0d,CAAc1mB,GACV,GAAIA,GAAWgI,KAAK0c,WAAW1kB,QAAS,CACpC,IAAI8oB,EAAQ,IAAIjE,EAAa7c,KAAK0c,WAAWiC,QAAS3mB,GAClD8oB,EAAMF,MAAQ5gB,KAAK0c,WAAWkE,MAC9B5gB,KAAK2gB,cACT3gB,KAAK0c,WAAaoE,CACtB,CACJ,CAIA,YAAA5D,CAAarO,GACLA,EAAY7O,KAAK6O,YACjB7O,KAAK6gB,gBACL7gB,KAAK6O,UAAYA,EAEzB,CAIA,KAAAkS,GACQ/gB,KAAK0c,YAAc1c,KAAK0c,WAAWiC,QAAQqC,QAC3ChhB,KAAK2gB,cACL3gB,KAAK6O,UAAY,GACjB7O,KAAK6gB,eACb,EAEJ,MAAMhE,EACF,WAAAzc,CAAYue,EAAS3mB,GACjBgI,KAAK2e,QAAUA,EACf3e,KAAKhI,QAAUA,EACfgI,KAAK4gB,KAAOjC,EAAQqC,OAASrC,EAAQiC,KAAK5oB,GAAW,CACzD,EAIJ,MAAMonB,EACF,WAAAhf,CAAY8T,GACRlU,KAAKkU,MAAQA,EACblU,KAAKsc,MAAQpI,EAAMoI,MACnBtc,KAAK2Z,MAAQzF,EAAMyF,MACnB3Z,KAAK+V,KAAO/V,KAAK2Z,MAAM7e,MAC3B,CACA,MAAAiiB,CAAOC,GACH,IAAIe,EAAgB,MAATf,EAAuC/I,EAAQ+I,GAAU,GACvD,GAAT/I,GACIjU,KAAK2Z,OAAS3Z,KAAKkU,MAAMyF,QACzB3Z,KAAK2Z,MAAQ3Z,KAAK2Z,MAAMhlB,SAC5BqL,KAAK2Z,MAAMhe,KAAKqE,KAAKsc,MAAO,EAAG,GAC/Btc,KAAK+V,MAAQ,GAGb/V,KAAK+V,MAAsB,GAAb9B,EAAQ,GAE1B,IAAIgN,EAAOjhB,KAAKkU,MAAMlK,EAAE3O,OAAOgiB,QAAQrd,KAAK2Z,MAAM3Z,KAAK+V,KAAO,GAAIgI,GAAM,GACxE/d,KAAKsc,MAAQ2E,CACjB,EAIJ,MAAMC,EACF,WAAA9gB,CAAYuZ,EAAO3Y,EAAKM,GACpBtB,KAAK2Z,MAAQA,EACb3Z,KAAKgB,IAAMA,EACXhB,KAAKsB,MAAQA,EACbtB,KAAKuT,OAASoG,EAAMpG,OACF,GAAdvT,KAAKsB,OACLtB,KAAKmhB,WACb,CACA,aAAO/R,CAAOuK,EAAO3Y,EAAM2Y,EAAM8C,WAAa9C,EAAMpG,OAAOzY,QACvD,OAAO,IAAIomB,EAAkBvH,EAAO3Y,EAAKA,EAAM2Y,EAAM8C,WACzD,CACA,SAAA0E,GACI,IAAI5M,EAAOvU,KAAK2Z,MAAMtH,OACV,MAARkC,IACAvU,KAAKsB,MAAQtB,KAAK2Z,MAAM8C,WAAalI,EAAKkI,WAC1Czc,KAAK2Z,MAAQpF,EACbvU,KAAKuT,OAASgB,EAAKhB,OAE3B,CACA,MAAI/d,GAAO,OAAOwK,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CAC/C,SAAI4S,GAAU,OAAOlU,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CAClD,OAAI6S,GAAQ,OAAOnU,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CAChD,QAAI8S,GAAS,OAAOpU,KAAKuT,OAAOvT,KAAKsB,MAAQ,EAAI,CACjD,IAAAiT,GACIvU,KAAKsB,OAAS,EACdtB,KAAKgB,KAAO,EACM,GAAdhB,KAAKsB,OACLtB,KAAKmhB,WACb,CACA,IAAA/K,GACI,OAAO,IAAI8K,EAAkBlhB,KAAK2Z,MAAO3Z,KAAKgB,IAAKhB,KAAKsB,MAC5D,EAKJ,SAAS8f,EAAYzF,EAAO0F,EAAO3M,aAC/B,GAAoB,iBAATiH,EACP,OAAOA,EACX,IAAIlS,EAAQ,KACZ,IAAK,IAAIzI,EAAM,EAAGsgB,EAAM,EAAGtgB,EAAM2a,EAAM7gB,QAAS,CAC5C,IAAIpD,EAAQ,EACZ,OAAS,CACL,IAAI6c,EAAOoH,EAAM4F,WAAWvgB,KAAQwgB,GAAO,EAC3C,GAAY,KAARjN,EAAqC,CACrC7c,EAAQ,MACR,KACJ,CACI6c,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAIkN,EAAQlN,EAAO,GAMnB,GALIkN,GAAS,KACTA,GAAS,GACTD,GAAO,GAEX9pB,GAAS+pB,EACLD,EACA,MACJ9pB,GAAS,EACb,CACI+R,EACAA,EAAM6X,KAAS5pB,EAEf+R,EAAQ,IAAI4X,EAAK3pB,EACzB,CACA,OAAO+R,CACX,CAEA,MAAMiY,EACF,WAAAthB,GACIJ,KAAKkU,OAAS,EACdlU,KAAKtI,OAAS,EACdsI,KAAKmU,KAAO,EACZnU,KAAK2hB,UAAY,EACjB3hB,KAAK6O,UAAY,EACjB7O,KAAK4hB,KAAO,EACZ5hB,KAAKhI,QAAU,CACnB,EAEJ,MAAM6pB,EAAY,IAAIH,EAOtB,MAAMI,EAIF,WAAA1hB,CAIAub,EAIAE,GACI7b,KAAK2b,MAAQA,EACb3b,KAAK6b,OAASA,EAId7b,KAAKkc,MAAQ,GAIblc,KAAK+hB,SAAW,EAIhB/hB,KAAKgiB,OAAS,GACdhiB,KAAKiiB,UAAY,EAKjBjiB,KAAKuU,MAAQ,EAIbvU,KAAKkiB,MAAQL,EACb7hB,KAAKmiB,WAAa,EAClBniB,KAAKgB,IAAMhB,KAAKoiB,SAAWvG,EAAO,GAAG7hB,KACrCgG,KAAK8I,MAAQ+S,EAAO,GACpB7b,KAAKmU,IAAM0H,EAAOA,EAAO/gB,OAAS,GAAGV,GACrC4F,KAAKqiB,UACT,CAIA,aAAAC,CAAcnH,EAAQoH,GAClB,IAAIzZ,EAAQ9I,KAAK8I,MAAOxH,EAAQtB,KAAKmiB,WACjCnhB,EAAMhB,KAAKgB,IAAMma,EACrB,KAAOna,EAAM8H,EAAM9O,MAAM,CACrB,IAAKsH,EACD,OAAO,KACX,IAAIiT,EAAOvU,KAAK6b,SAASva,GACzBN,GAAO8H,EAAM9O,KAAOua,EAAKna,GACzB0O,EAAQyL,CACZ,CACA,KAAOgO,EAAQ,EAAIvhB,EAAM8H,EAAM1O,GAAK4G,GAAO8H,EAAM1O,IAAI,CACjD,GAAIkH,GAAStB,KAAK6b,OAAO/gB,OAAS,EAC9B,OAAO,KACX,IAAIyZ,EAAOvU,KAAK6b,SAASva,GACzBN,GAAOuT,EAAKva,KAAO8O,EAAM1O,GACzB0O,EAAQyL,CACZ,CACA,OAAOvT,CACX,CAIA,OAAAwhB,CAAQxhB,GACJ,GAAIA,GAAOhB,KAAK8I,MAAM9O,MAAQgH,EAAMhB,KAAK8I,MAAM1O,GAC3C,OAAO4G,EACX,IAAK,IAAI8H,KAAS9I,KAAK6b,OACnB,GAAI/S,EAAM1O,GAAK4G,EACX,OAAOyW,KAAKC,IAAI1W,EAAK8H,EAAM9O,MACnC,OAAOgG,KAAKmU,GAChB,CAYA,IAAAsO,CAAKtH,GACD,IAAkCna,EAAKsN,EAAnCoU,EAAM1iB,KAAK+hB,SAAW5G,EAC1B,GAAIuH,GAAO,GAAKA,EAAM1iB,KAAKkc,MAAMphB,OAC7BkG,EAAMhB,KAAKgB,IAAMma,EACjB7M,EAAStO,KAAKkc,MAAMqF,WAAWmB,OAE9B,CACD,IAAIC,EAAW3iB,KAAKsiB,cAAcnH,EAAQ,GAC1C,GAAgB,MAAZwH,EACA,OAAQ,EAEZ,GADA3hB,EAAM2hB,EACF3hB,GAAOhB,KAAKiiB,WAAajhB,EAAMhB,KAAKiiB,UAAYjiB,KAAKgiB,OAAOlnB,OAC5DwT,EAAStO,KAAKgiB,OAAOT,WAAWvgB,EAAMhB,KAAKiiB,eAE1C,CACD,IAAIja,EAAIhI,KAAKmiB,WAAYrZ,EAAQ9I,KAAK8I,MACtC,KAAOA,EAAM1O,IAAM4G,GACf8H,EAAQ9I,KAAK6b,SAAS7T,GAC1BhI,KAAKgiB,OAAShiB,KAAK2b,MAAMO,MAAMlc,KAAKiiB,UAAYjhB,GAC5CA,EAAMhB,KAAKgiB,OAAOlnB,OAASgO,EAAM1O,KACjC4F,KAAKgiB,OAAShiB,KAAKgiB,OAAOrtB,MAAM,EAAGmU,EAAM1O,GAAK4G,IAClDsN,EAAStO,KAAKgiB,OAAOT,WAAW,EACpC,CACJ,CAGA,OAFIvgB,GAAOhB,KAAKkiB,MAAMrT,YAClB7O,KAAKkiB,MAAMrT,UAAY7N,EAAM,GAC1BsN,CACX,CAMA,WAAAsU,CAAYV,EAAOW,EAAY,GAC3B,IAAI1O,EAAM0O,EAAY7iB,KAAKsiB,cAAcO,GAAY,GAAK7iB,KAAKgB,IAC/D,GAAW,MAAPmT,GAAeA,EAAMnU,KAAKkiB,MAAMhO,MAChC,MAAM,IAAI9F,WAAW,2BACzBpO,KAAKkiB,MAAMxqB,MAAQwqB,EACnBliB,KAAKkiB,MAAM/N,IAAMA,CACrB,CAIA,aAAA2O,CAAcZ,EAAOtN,GACjB5U,KAAKkiB,MAAMxqB,MAAQwqB,EACnBliB,KAAKkiB,MAAM/N,IAAMS,CACrB,CACA,QAAAmO,GACI,GAAI/iB,KAAKgB,KAAOhB,KAAKiiB,WAAajiB,KAAKgB,IAAMhB,KAAKiiB,UAAYjiB,KAAKgiB,OAAOlnB,OAAQ,CAC9E,IAAI,MAAEohB,EAAK,SAAEkG,GAAapiB,KAC1BA,KAAKkc,MAAQlc,KAAKgiB,OAClBhiB,KAAKoiB,SAAWpiB,KAAKiiB,UACrBjiB,KAAKgiB,OAAS9F,EACdlc,KAAKiiB,UAAYG,EACjBpiB,KAAK+hB,SAAW/hB,KAAKgB,IAAMhB,KAAKoiB,QACpC,KACK,CACDpiB,KAAKgiB,OAAShiB,KAAKkc,MACnBlc,KAAKiiB,UAAYjiB,KAAKoiB,SACtB,IAAIY,EAAYhjB,KAAK2b,MAAMO,MAAMlc,KAAKgB,KAClCmT,EAAMnU,KAAKgB,IAAMgiB,EAAUloB,OAC/BkF,KAAKkc,MAAQ/H,EAAMnU,KAAK8I,MAAM1O,GAAK4oB,EAAUruB,MAAM,EAAGqL,KAAK8I,MAAM1O,GAAK4F,KAAKgB,KAAOgiB,EAClFhjB,KAAKoiB,SAAWpiB,KAAKgB,IACrBhB,KAAK+hB,SAAW,CACpB,CACJ,CACA,QAAAM,GACI,OAAIriB,KAAK+hB,UAAY/hB,KAAKkc,MAAMphB,SAC5BkF,KAAK+iB,WACD/iB,KAAK+hB,UAAY/hB,KAAKkc,MAAMphB,QACrBkF,KAAKuU,MAAQ,EAErBvU,KAAKuU,KAAOvU,KAAKkc,MAAMqF,WAAWvhB,KAAK+hB,SAClD,CAKA,OAAA9F,CAAQnC,EAAI,GAER,IADA9Z,KAAK+hB,UAAYjI,EACV9Z,KAAKgB,IAAM8Y,GAAK9Z,KAAK8I,MAAM1O,IAAI,CAClC,GAAI4F,KAAKmiB,YAAcniB,KAAK6b,OAAO/gB,OAAS,EACxC,OAAOkF,KAAKijB,UAChBnJ,GAAK9Z,KAAK8I,MAAM1O,GAAK4F,KAAKgB,IAC1BhB,KAAK8I,MAAQ9I,KAAK6b,SAAS7b,KAAKmiB,YAChCniB,KAAKgB,IAAMhB,KAAK8I,MAAM9O,IAC1B,CAIA,OAHAgG,KAAKgB,KAAO8Y,EACR9Z,KAAKgB,KAAOhB,KAAKkiB,MAAMrT,YACvB7O,KAAKkiB,MAAMrT,UAAY7O,KAAKgB,IAAM,GAC/BhB,KAAKqiB,UAChB,CACA,OAAAY,GAII,OAHAjjB,KAAKgB,IAAMhB,KAAKoiB,SAAWpiB,KAAKmU,IAChCnU,KAAK8I,MAAQ9I,KAAK6b,OAAO7b,KAAKmiB,WAAaniB,KAAK6b,OAAO/gB,OAAS,GAChEkF,KAAKkc,MAAQ,GACNlc,KAAKuU,MAAQ,CACxB,CAIA,KAAAuK,CAAM9d,EAAKkhB,GAUP,GATIA,GACAliB,KAAKkiB,MAAQA,EACbA,EAAMhO,MAAQlT,EACdkhB,EAAMrT,UAAY7N,EAAM,EACxBkhB,EAAMxqB,MAAQwqB,EAAMP,UAAY,GAGhC3hB,KAAKkiB,MAAQL,EAEb7hB,KAAKgB,KAAOA,EAAK,CAEjB,GADAhB,KAAKgB,IAAMA,EACPA,GAAOhB,KAAKmU,IAEZ,OADAnU,KAAKijB,UACEjjB,KAEX,KAAOgB,EAAMhB,KAAK8I,MAAM9O,MACpBgG,KAAK8I,MAAQ9I,KAAK6b,SAAS7b,KAAKmiB,YACpC,KAAOnhB,GAAOhB,KAAK8I,MAAM1O,IACrB4F,KAAK8I,MAAQ9I,KAAK6b,SAAS7b,KAAKmiB,YAChCnhB,GAAOhB,KAAKoiB,UAAYphB,EAAMhB,KAAKoiB,SAAWpiB,KAAKkc,MAAMphB,OACzDkF,KAAK+hB,SAAW/gB,EAAMhB,KAAKoiB,UAG3BpiB,KAAKkc,MAAQ,GACblc,KAAK+hB,SAAW,GAEpB/hB,KAAKqiB,UACT,CACA,OAAOriB,IACX,CAIA,IAAAoc,CAAKpiB,EAAMI,GACP,GAAIJ,GAAQgG,KAAKoiB,UAAYhoB,GAAM4F,KAAKoiB,SAAWpiB,KAAKkc,MAAMphB,OAC1D,OAAOkF,KAAKkc,MAAMvnB,MAAMqF,EAAOgG,KAAKoiB,SAAUhoB,EAAK4F,KAAKoiB,UAC5D,GAAIpoB,GAAQgG,KAAKiiB,WAAa7nB,GAAM4F,KAAKiiB,UAAYjiB,KAAKgiB,OAAOlnB,OAC7D,OAAOkF,KAAKgiB,OAAOrtB,MAAMqF,EAAOgG,KAAKiiB,UAAW7nB,EAAK4F,KAAKiiB,WAC9D,GAAIjoB,GAAQgG,KAAK8I,MAAM9O,MAAQI,GAAM4F,KAAK8I,MAAM1O,GAC5C,OAAO4F,KAAK2b,MAAMS,KAAKpiB,EAAMI,GACjC,IAAIkU,EAAS,GACb,IAAK,IAAI0J,KAAKhY,KAAK6b,OAAQ,CACvB,GAAI7D,EAAEhe,MAAQI,EACV,MACA4d,EAAE5d,GAAKJ,IACPsU,GAAUtO,KAAK2b,MAAMS,KAAK3E,KAAKC,IAAIM,EAAEhe,KAAMA,GAAOyd,KAAKyL,IAAIlL,EAAE5d,GAAIA,IACzE,CACA,OAAOkU,CACX,EAKJ,MAAM6U,EACF,WAAA/iB,CAAYb,EAAM/J,GACdwK,KAAKT,KAAOA,EACZS,KAAKxK,GAAKA,CACd,CACA,KAAA0sB,CAAMvG,EAAOhC,GACT,IAAI,OAAEte,GAAWse,EAAM3P,EACvBoZ,EAAUpjB,KAAKT,KAAMoc,EAAOhC,EAAO3Z,KAAKxK,GAAI6F,EAAOkE,KAAMlE,EAAOgoB,eACpE,EAEJF,EAAWG,UAAUC,WAAaJ,EAAWG,UAAUnuB,SAAWguB,EAAWG,UAAU/S,QAAS,EA+BzD4S,EAAWG,UAAUnuB,SAAWguB,EAAWG,UAAU/S,QAAS,EA4CrG,SAAS6S,EAAU7jB,EAAMoc,EAAOhC,EAAOjL,EAAO8U,EAAWC,GACrD,IAAInH,EAAQ,EAAGoH,EAAY,GAAKhV,GAAO,QAAEgS,GAAY/G,EAAM3P,EAAE3O,OAC7D+W,EAAM,KACGsR,EAAYnkB,EAAK+c,IADX,CAGX,IAAIqH,EAASpkB,EAAK+c,EAAQ,GAI1B,IAAK,IAAItU,EAAIsU,EAAQ,EAAGtU,EAAI2b,EAAQ3b,GAAK,EACrC,IAAKzI,EAAKyI,EAAI,GAAK0b,GAAa,EAAG,CAC/B,IAAI3F,EAAOxe,EAAKyI,GAChB,GAAI0Y,EAAQkD,OAAO7F,MACQ,GAAtBpC,EAAMuG,MAAMxqB,OAAeikB,EAAMuG,MAAMxqB,OAASqmB,GAC7C8F,EAAU9F,EAAMpC,EAAMuG,MAAMxqB,MAAO8rB,EAAWC,IAAc,CAChE9H,EAAMiH,YAAY7E,GAClB,KACJ,CACJ,CACJ,IAAIxJ,EAAOoH,EAAMpH,KAAMuP,EAAM,EAAGC,EAAOxkB,EAAK+c,EAAQ,GAEpD,KAAIX,EAAMpH,KAAO,GAAKwP,EAAOD,GAAsC,OAA/BvkB,EAAKokB,EAAgB,EAAPI,EAAW,IAA7D,CAKA,KAAOD,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtBziB,EAAQqiB,EAASK,GAAOA,GAAO,GAC/BhqB,EAAOuF,EAAK+B,GAAQlH,EAAKmF,EAAK+B,EAAQ,IAAM,MAChD,GAAIiT,EAAOva,EACP+pB,EAAOC,MACN,MAAIzP,GAAQna,GAEZ,CACDkiB,EAAQ/c,EAAK+B,EAAQ,GACrBqa,EAAMM,UACN,SAAS7J,CACb,CALI0R,EAAME,EAAM,CAKhB,CACJ,CACA,KAhBA,CAFI1H,EAAQ/c,EAAKokB,EAAgB,EAAPI,EAAW,EAmBzC,CACJ,CACA,SAASE,EAAW1kB,EAAM2U,EAAO6J,GAC7B,IAAK,IAAexJ,EAAXvM,EAAIkM,EAAiC,QAAnBK,EAAOhV,EAAKyI,IAA4BA,IAC/D,GAAIuM,GAAQwJ,EACR,OAAO/V,EAAIkM,EACnB,OAAQ,CACZ,CACA,SAAS2P,EAAU3B,EAAO3H,EAAM2J,EAAWC,GACvC,IAAIC,EAAQH,EAAWC,EAAWC,EAAa5J,GAC/C,OAAO6J,EAAQ,GAAKH,EAAWC,EAAWC,EAAajC,GAASkC,CACpE,CAGA,MAAMC,EAA4B,oBAAXC,SAA0BA,QAAQC,KAAO,YAAYpT,KAAKmT,QAAQC,IAAIC,KAC7F,IAAIC,EAAW,KACf,SAASC,EAAM1V,EAAMhO,EAAKyQ,GACtB,IAAIL,EAASpC,EAAKoC,OAAOL,EAAS6B,kBAElC,IADAxB,EAAOO,OAAO3Q,KAEV,KAAMyQ,EAAO,EAAIL,EAAOiH,YAAYrX,GAAOoQ,EAAOlQ,WAAWF,IACzD,OAAS,CACL,IAAKyQ,EAAO,EAAIL,EAAOhX,GAAK4G,EAAMoQ,EAAOpX,KAAOgH,KAASoQ,EAAO/Y,KAAKwX,QACjE,OAAO4B,EAAO,EAAIgG,KAAKC,IAAI,EAAGD,KAAKyL,IAAI9R,EAAOhX,GAAK,EAAG4G,EAAM,KACtDyW,KAAKyL,IAAIlU,EAAKlU,OAAQ2c,KAAKC,IAAItG,EAAOpX,KAAO,EAAGgH,EAAM,KAChE,GAAIyQ,EAAO,EAAIL,EAAOmH,cAAgBnH,EAAO4B,cACzC,MACJ,IAAK5B,EAAOiB,SACR,OAAOZ,EAAO,EAAI,EAAIzC,EAAKlU,MACnC,CAEZ,CACA,MAAM,EACF,WAAAsF,CAAYwb,EAAWpI,GACnBxT,KAAK4b,UAAYA,EACjB5b,KAAKwT,QAAUA,EACfxT,KAAKgI,EAAI,EACThI,KAAK2kB,SAAW,KAChB3kB,KAAK4kB,UAAY,EACjB5kB,KAAK6kB,QAAU,EACf7kB,KAAK8kB,MAAQ,GACb9kB,KAAKkU,MAAQ,GACblU,KAAKsB,MAAQ,GACbtB,KAAK+kB,cACT,CACA,YAAAA,GACI,IAAIC,EAAKhlB,KAAK2kB,SAAW3kB,KAAKgI,GAAKhI,KAAK4b,UAAU9gB,OAAS,KAAOkF,KAAK4b,UAAU5b,KAAKgI,KACtF,GAAIgd,EAAI,CAGJ,IAFAhlB,KAAK4kB,SAAWI,EAAGC,UAAYP,EAAMM,EAAGhW,KAAMgW,EAAGhrB,KAAOgrB,EAAG7J,OAAQ,GAAK6J,EAAG7J,OAAS6J,EAAGhrB,KACvFgG,KAAK6kB,OAASG,EAAGE,QAAUR,EAAMM,EAAGhW,KAAMgW,EAAG5qB,GAAK4qB,EAAG7J,QAAS,GAAK6J,EAAG7J,OAAS6J,EAAG5qB,GAC3E4F,KAAK8kB,MAAMhqB,QACdkF,KAAK8kB,MAAM9O,MACXhW,KAAKkU,MAAM8B,MACXhW,KAAKsB,MAAM0U,MAEfhW,KAAK8kB,MAAMnpB,KAAKqpB,EAAGhW,MACnBhP,KAAKkU,MAAMvY,MAAMqpB,EAAG7J,QACpBnb,KAAKsB,MAAM3F,KAAK,GAChBqE,KAAKue,UAAYve,KAAK4kB,QAC1B,MAEI5kB,KAAKue,UAAY,GAEzB,CAEA,MAAA4G,CAAOnkB,GACH,GAAIA,EAAMhB,KAAKue,UACX,OAAO,KACX,KAAOve,KAAK2kB,UAAY3kB,KAAK6kB,QAAU7jB,GACnChB,KAAK+kB,eACT,IAAK/kB,KAAK2kB,SACN,OAAO,KACX,OAAS,CACL,IAAI/O,EAAO5V,KAAK8kB,MAAMhqB,OAAS,EAC/B,GAAI8a,EAAO,EAEP,OADA5V,KAAK+kB,eACE,KAEX,IAAIvV,EAAMxP,KAAK8kB,MAAMlP,GAAOtU,EAAQtB,KAAKsB,MAAMsU,GAC/C,GAAItU,GAASkO,EAAIyB,SAASnW,OAAQ,CAC9BkF,KAAK8kB,MAAM9O,MACXhW,KAAKkU,MAAM8B,MACXhW,KAAKsB,MAAM0U,MACX,QACJ,CACA,IAAIzB,EAAO/E,EAAIyB,SAAS3P,GACpB4S,EAAQlU,KAAKkU,MAAM0B,GAAQpG,EAAIzO,UAAUO,GAC7C,GAAI4S,EAAQlT,EAER,OADAhB,KAAKue,UAAYrK,EACV,KAEX,GAAIK,aAAgBvD,EAAM,CACtB,GAAIkD,GAASlT,EAAK,CACd,GAAIkT,EAAQlU,KAAK4kB,SACb,OAAO,KACX,IAAIzQ,EAAMD,EAAQK,EAAKzZ,OACvB,GAAIqZ,GAAOnU,KAAK6kB,OAAQ,CACpB,IAAIhW,EAAY0F,EAAK/M,KAAKuG,EAASc,WACnC,IAAKA,GAAasF,EAAMtF,EAAY7O,KAAK2kB,SAASvqB,GAC9C,OAAOma,CACf,CACJ,CACAvU,KAAKsB,MAAMsU,KACP1B,EAAQK,EAAKzZ,QAAU2c,KAAKC,IAAI1X,KAAK4kB,SAAU5jB,KAC/ChB,KAAK8kB,MAAMnpB,KAAK4Y,GAChBvU,KAAKkU,MAAMvY,KAAKuY,GAChBlU,KAAKsB,MAAM3F,KAAK,GAExB,MAEIqE,KAAKsB,MAAMsU,KACX5V,KAAKue,UAAYrK,EAAQK,EAAKzZ,MAEtC,CACJ,EAEJ,MAAMsqB,EACF,WAAAhlB,CAAY/E,EAAQwjB,GAChB7e,KAAK6e,OAASA,EACd7e,KAAKqlB,OAAS,GACdrlB,KAAKslB,UAAY,KACjBtlB,KAAKulB,QAAU,GACfvlB,KAAKqlB,OAAShqB,EAAOmqB,WAAW3hB,KAAI8Y,GAAK,IAAI+E,GACjD,CACA,UAAA+D,CAAW9L,GACP,IAAI+L,EAAc,EACdC,EAAO,MACP,OAAEtqB,GAAWse,EAAM3P,GAAG,WAAEwb,GAAenqB,EACvCumB,EAAOvmB,EAAOgkB,UAAU1F,EAAM2C,MAAO,GACrCtkB,EAAU2hB,EAAM+C,WAAa/C,EAAM+C,WAAWkE,KAAO,EACrD/R,EAAY,EAChB,IAAK,IAAI7G,EAAI,EAAGA,EAAIwd,EAAW1qB,OAAQkN,IAAK,CACxC,KAAM,GAAKA,EAAK4Z,GACZ,SACJ,IAAIgE,EAAYJ,EAAWxd,GAAIka,EAAQliB,KAAKqlB,OAAOrd,GACnD,KAAI2d,GAASC,EAAUzwB,aAEnBywB,EAAUrC,YAAcrB,EAAMhO,OAASyF,EAAM3Y,KAAOkhB,EAAMN,MAAQA,GAAQM,EAAMlqB,SAAWA,KAC3FgI,KAAK6lB,kBAAkB3D,EAAO0D,EAAWjM,GACzCuI,EAAMN,KAAOA,EACbM,EAAMlqB,QAAUA,GAEhBkqB,EAAMrT,UAAYqT,EAAM/N,IAAM,KAC9BtF,EAAY4I,KAAKC,IAAIwK,EAAMrT,UAAWA,IACvB,GAAfqT,EAAMxqB,OAA2B,CACjC,IAAIgf,EAAagP,EAIjB,GAHIxD,EAAMP,UAAY,IAClB+D,EAAc1lB,KAAK8lB,WAAWnM,EAAOuI,EAAMP,SAAUO,EAAM/N,IAAKuR,IACpEA,EAAc1lB,KAAK8lB,WAAWnM,EAAOuI,EAAMxqB,MAAOwqB,EAAM/N,IAAKuR,IACxDE,EAAUrV,SACXoV,EAAOzD,EACHwD,EAAchP,GACd,KAEZ,CACJ,CACA,KAAO1W,KAAKulB,QAAQzqB,OAAS4qB,GACzB1lB,KAAKulB,QAAQvP,MAUjB,OATInH,GACA8K,EAAMuD,aAAarO,GAClB8W,GAAQhM,EAAM3Y,KAAOhB,KAAK6e,OAAO1K,MAClCwR,EAAO,IAAIjE,EACXiE,EAAKjuB,MAAQiiB,EAAM3P,EAAE3O,OAAO0qB,QAC5BJ,EAAKzR,MAAQyR,EAAKxR,IAAMwF,EAAM3Y,IAC9B0kB,EAAc1lB,KAAK8lB,WAAWnM,EAAOgM,EAAKjuB,MAAOiuB,EAAKxR,IAAKuR,IAE/D1lB,KAAKslB,UAAYK,EACV3lB,KAAKulB,OAChB,CACA,YAAAS,CAAarM,GACT,GAAI3Z,KAAKslB,UACL,OAAOtlB,KAAKslB,UAChB,IAAIK,EAAO,IAAIjE,GAAa,IAAE1gB,EAAG,EAAEgJ,GAAM2P,EAIzC,OAHAgM,EAAKzR,MAAQlT,EACb2kB,EAAKxR,IAAMsD,KAAKyL,IAAIliB,EAAM,EAAGgJ,EAAE6U,OAAO1K,KACtCwR,EAAKjuB,MAAQsJ,GAAOgJ,EAAE6U,OAAO1K,IAAMnK,EAAE3O,OAAO0qB,QAAU,EAC/CJ,CACX,CACA,iBAAAE,CAAkB3D,EAAO0D,EAAWjM,GAChC,IAAIzF,EAAQlU,KAAK6e,OAAO2D,QAAQ7I,EAAM3Y,KAEtC,GADA4kB,EAAU1D,MAAMliB,KAAK6e,OAAOC,MAAM5K,EAAOgO,GAAQvI,GAC7CuI,EAAMxqB,OAAS,EAAG,CAClB,IAAI,OAAE2D,GAAWse,EAAM3P,EACvB,IAAK,IAAIhC,EAAI,EAAGA,EAAI3M,EAAO4qB,YAAYnrB,OAAQkN,IAC3C,GAAI3M,EAAO4qB,YAAYje,IAAMka,EAAMxqB,MAAO,CACtC,IAAI4W,EAASjT,EAAO6qB,aAAale,GAAGhI,KAAK6e,OAAOzC,KAAK8F,EAAMhO,MAAOgO,EAAM/N,KAAMwF,GAC9E,GAAIrL,GAAU,GAAKqL,EAAM3P,EAAE3O,OAAOqlB,QAAQkD,OAAOtV,GAAU,GAAI,CAC7C,EAATA,EAGD4T,EAAMP,SAAWrT,GAAU,EAF3B4T,EAAMxqB,MAAQ4W,GAAU,EAG5B,KACJ,CACJ,CACR,MAEI4T,EAAMxqB,MAAQ,EACdwqB,EAAM/N,IAAMnU,KAAK6e,OAAO2D,QAAQtO,EAAQ,EAEhD,CACA,SAAAiS,CAAUnJ,EAAQkF,EAAO/N,EAAK7S,GAE1B,IAAK,IAAI0G,EAAI,EAAGA,EAAI1G,EAAO0G,GAAK,EAC5B,GAAIhI,KAAKulB,QAAQvd,IAAMgV,EACnB,OAAO1b,EAIf,OAHAtB,KAAKulB,QAAQjkB,KAAW0b,EACxBhd,KAAKulB,QAAQjkB,KAAW4gB,EACxBliB,KAAKulB,QAAQjkB,KAAW6S,EACjB7S,CACX,CACA,UAAAwkB,CAAWnM,EAAOuI,EAAO/N,EAAK7S,GAC1B,IAAI,MAAEgb,GAAU3C,GAAO,OAAEte,GAAWse,EAAM3P,GAAG,KAAEzK,GAASlE,EACxD,IAAK,IAAI2B,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAAIgL,EAAI3M,EAAOgkB,UAAU/C,EAAOtf,EAAM,EAA0B,IAA8BgL,GAAK,EAAG,CACvG,GAAe,OAAXzI,EAAKyI,GAA2B,CAChC,GAAmB,GAAfzI,EAAKyI,EAAI,GAGR,CACY,GAAT1G,GAA6B,GAAf/B,EAAKyI,EAAI,KACvB1G,EAAQtB,KAAKmmB,UAAUlQ,GAAK1W,EAAMyI,EAAI,GAAIka,EAAO/N,EAAK7S,IAC1D,KACJ,CANI0G,EAAIiO,GAAK1W,EAAMyI,EAAI,EAO3B,CACIzI,EAAKyI,IAAMka,IACX5gB,EAAQtB,KAAKmmB,UAAUlQ,GAAK1W,EAAMyI,EAAI,GAAIka,EAAO/N,EAAK7S,GAC9D,CAEJ,OAAOA,CACX,EAEJ,MAAM8kB,EACF,WAAAhmB,CAAY/E,EAAQsgB,EAAOC,EAAWC,GAClC7b,KAAK3E,OAASA,EACd2E,KAAK2b,MAAQA,EACb3b,KAAK6b,OAASA,EACd7b,KAAKqmB,WAAa,EAClBrmB,KAAKsmB,YAAc,KACnBtmB,KAAKumB,YAAc,EACnBvmB,KAAK0T,OAAS,GACd1T,KAAKwmB,UAAY,KACjBxmB,KAAKyd,uBAAyB,EAC9Bzd,KAAK2d,qBAAuB,EAC5B3d,KAAK0d,kBAAoB,EACzB1d,KAAK6e,OAAS,IAAIiD,EAAYnG,EAAOE,GACrC7b,KAAKqlB,OAAS,IAAID,EAAW/pB,EAAQ2E,KAAK6e,QAC1C7e,KAAKymB,QAAUprB,EAAOmU,IAAI,GAC1B,IAAI,KAAExV,GAAS6hB,EAAO,GACtB7b,KAAK0mB,OAAS,CAACrK,EAAMnI,MAAMlU,KAAM3E,EAAOmU,IAAI,GAAIxV,IAChDgG,KAAK4b,UAAYA,EAAU9gB,QAAUkF,KAAK6e,OAAO1K,IAAMna,EAA6B,EAAtBqB,EAAOsrB,aAC/D,IAAI,EAAe/K,EAAWvgB,EAAOmY,SAAW,IAC1D,CACA,aAAIoT,GACA,OAAO5mB,KAAKumB,WAChB,CAOA,OAAAtK,GACI,IAGI4K,EAASC,EAHTJ,EAAS1mB,KAAK0mB,OAAQ1lB,EAAMhB,KAAKumB,YAEjCQ,EAAY/mB,KAAK0mB,OAAS,GAS9B,GAAI1mB,KAAK0d,kBAAoB,KAAmE,GAAjBgJ,EAAO5rB,OAAa,CAC/F,IAAK4M,GAAKgf,EACV,KAAOhf,EAAEiY,eAAiBjY,EAAEiS,MAAM7e,QAAU4M,EAAEiS,MAAMjS,EAAEiS,MAAM7e,OAAS,IAAMkF,KAAKyd,wBAChFzd,KAAK0d,kBAAoB1d,KAAK2d,qBAAuB,CACzD,CAIA,IAAK,IAAI3V,EAAI,EAAGA,EAAI0e,EAAO5rB,OAAQkN,IAAK,CACpC,IAAI2R,EAAQ+M,EAAO1e,GACnB,OAAS,CAEL,GADAhI,KAAKqlB,OAAOC,UAAY,KACpB3L,EAAM3Y,IAAMA,EACZ+lB,EAAUprB,KAAKge,OAEd,IAAI3Z,KAAKgnB,aAAarN,EAAOoN,EAAWL,GACzC,SAEC,CACIG,IACDA,EAAU,GACVC,EAAgB,IAEpBD,EAAQlrB,KAAKge,GACb,IAAIsN,EAAMjnB,KAAKqlB,OAAOW,aAAarM,GACnCmN,EAAcnrB,KAAKsrB,EAAIvvB,MAAOuvB,EAAI9S,IACtC,EACA,KACJ,CACJ,CACA,IAAK4S,EAAUjsB,OAAQ,CACnB,IAAIosB,EAAWL,GAuhB3B,SAAsBH,GAClB,IAAIjH,EAAO,KACX,IAAK,IAAI9F,KAAS+M,EAAQ,CACtB,IAAIG,EAAUlN,EAAM3P,EAAEwc,WACjB7M,EAAM3Y,KAAO2Y,EAAM3P,EAAE6U,OAAO1K,KAAkB,MAAX0S,GAAmBlN,EAAM3Y,IAAM6lB,IACnElN,EAAM3P,EAAE3O,OAAOwiB,UAAUlE,EAAM2C,MAAO,MACpCmD,GAAQA,EAAKjD,MAAQ7C,EAAM6C,SAC7BiD,EAAO9F,EACf,CACA,OAAO8F,CACX,CAjiBsC0H,CAAaN,GACvC,GAAIK,EAGA,OAAOlnB,KAAKonB,YAAYF,GAE5B,GAAIlnB,KAAK3E,OAAO2lB,OAGZ,MAAM,IAAIqG,YAAY,eAAiBrmB,GAEtChB,KAAKqmB,aACNrmB,KAAKqmB,WAAa,EAC1B,CACA,GAAIrmB,KAAKqmB,YAAcQ,EAAS,CAC5B,IAAIK,EAA6B,MAAlBlnB,KAAKwmB,WAAqBK,EAAQ,GAAG7lB,IAAMhB,KAAKwmB,UAAYK,EAAQ,GAC7E7mB,KAAKsnB,YAAYT,EAASC,EAAeC,GAC/C,GAAIG,EAGA,OAAOlnB,KAAKonB,YAAYF,EAAS/G,WAEzC,CACA,GAAIngB,KAAKqmB,WAAY,CACjB,IAAIkB,EAAkC,GAAnBvnB,KAAKqmB,WAAkB,EAAsB,EAAlBrmB,KAAKqmB,WACnD,GAAIU,EAAUjsB,OAASysB,EAEnB,IADAR,EAAUxtB,MAAK,CAACC,EAAG8d,IAAMA,EAAEkF,MAAQhjB,EAAEgjB,QAC9BuK,EAAUjsB,OAASysB,GACtBR,EAAU/Q,MAEd+Q,EAAUrM,MAAKhT,GAAKA,EAAE6U,UAAYvb,KAClChB,KAAKqmB,YACb,MACK,GAAIU,EAAUjsB,OAAS,EAAG,CAI3B0sB,EAAO,IAAK,IAAIxf,EAAI,EAAGA,EAAI+e,EAAUjsB,OAAS,EAAGkN,IAAK,CAClD,IAAI2R,EAAQoN,EAAU/e,GACtB,IAAK,IAAI2N,EAAI3N,EAAI,EAAG2N,EAAIoR,EAAUjsB,OAAQ6a,IAAK,CAC3C,IAAI4K,EAAQwG,EAAUpR,GACtB,GAAIgE,EAAM2G,UAAUC,IAChB5G,EAAMpG,OAAOzY,OAAS,KAAsCylB,EAAMhN,OAAOzY,OAAS,IAAoC,CACtH,MAAM6e,EAAM6C,MAAQ+D,EAAM/D,OAAW7C,EAAMpG,OAAOzY,OAASylB,EAAMhN,OAAOzY,QAAW,GAG9E,CACDisB,EAAUtN,OAAOzR,IAAK,GACtB,SAASwf,CACb,CALIT,EAAUtN,OAAO9D,IAAK,EAM9B,CACJ,CACJ,CACIoR,EAAUjsB,OAAS,IACnBisB,EAAUtN,OAAO,GAA4BsN,EAAUjsB,OAAS,GACxE,CACAkF,KAAKumB,YAAcQ,EAAU,GAAG/lB,IAChC,IAAK,IAAIgH,EAAI,EAAGA,EAAI+e,EAAUjsB,OAAQkN,IAC9B+e,EAAU/e,GAAGhH,IAAMhB,KAAKumB,cACxBvmB,KAAKumB,YAAcQ,EAAU/e,GAAGhH,KACxC,OAAO,IACX,CACA,MAAA0U,CAAO1U,GACH,GAAsB,MAAlBhB,KAAKwmB,WAAqBxmB,KAAKwmB,UAAYxlB,EAC3C,MAAM,IAAIoN,WAAW,gCACzBpO,KAAKwmB,UAAYxlB,CACrB,CAKA,YAAAgmB,CAAarN,EAAO+M,EAAQvtB,GACxB,IAAI+a,EAAQyF,EAAM3Y,KAAK,OAAE3F,GAAW2E,KACzBqkB,GAAUrkB,KAAKynB,QAAQ9N,GAClC,GAAsB,MAAlB3Z,KAAKwmB,WAAqBtS,EAAQlU,KAAKwmB,UACvC,OAAO7M,EAAMgG,cAAgBhG,EAAQ,KACzC,GAAI3Z,KAAK4b,UAAW,CAChB,IAAI8L,EAAW/N,EAAM+C,YAAc/C,EAAM+C,WAAWiC,QAAQqC,OAAQ2G,EAASD,EAAW/N,EAAM+C,WAAWkE,KAAO,EAChH,IAAK,IAAIgH,EAAS5nB,KAAK4b,UAAUuJ,OAAOjR,GAAQ0T,GAAS,CACrD,IAAI/rB,EAAQmE,KAAK3E,OAAOmY,QAAQlD,MAAMsX,EAAOvvB,KAAK7C,KAAOoyB,EAAOvvB,KAAOgD,EAAOgiB,QAAQ1D,EAAM2C,MAAOsL,EAAOvvB,KAAK7C,KAAO,EACtH,GAAIqG,GAAS,GAAK+rB,EAAO9sB,UAAY4sB,IAAaE,EAAOpgB,KAAKuG,EAASa,cAAgB,IAAM+Y,GAIzF,OAHAhO,EAAM8E,QAAQmJ,EAAQ/rB,IAGf,EAEX,KAAM+rB,aAAkB5W,IAAmC,GAA1B4W,EAAO3W,SAASnW,QAAe8sB,EAAO7mB,UAAU,GAAK,EAClF,MACJ,IAAImR,EAAQ0V,EAAO3W,SAAS,GAC5B,KAAIiB,aAAiBlB,GAA+B,GAAvB4W,EAAO7mB,UAAU,IAG1C,MAFA6mB,EAAS1V,CAGjB,CACJ,CACA,IAAI2V,EAAgBxsB,EAAOgkB,UAAU1F,EAAM2C,MAAO,GAClD,GAAIuL,EAAgB,EAIhB,OAHAlO,EAAMoD,OAAO8K,IAGN,EAEX,GAAIlO,EAAMA,MAAM7e,QAAU,KACtB,KAAO6e,EAAMA,MAAM7e,OAAS,KAAwB6e,EAAMgG,gBAE9D,IAAI4F,EAAUvlB,KAAKqlB,OAAOI,WAAW9L,GACrC,IAAK,IAAI3R,EAAI,EAAGA,EAAIud,EAAQzqB,QAAS,CACjC,IAAIkiB,EAASuI,EAAQvd,KAAM+V,EAAOwH,EAAQvd,KAAMmM,EAAMoR,EAAQvd,KAC1D4N,EAAO5N,GAAKud,EAAQzqB,SAAW3B,EAC/B2uB,EAAalS,EAAO+D,EAAQA,EAAMxgB,QAClCwsB,EAAO3lB,KAAKqlB,OAAOC,UAKvB,GAJAwC,EAAWxJ,MAAMtB,EAAQe,EAAM4H,EAAOA,EAAKzR,MAAQ4T,EAAW9mB,IAAKmT,GAI/DyB,EACA,OAAO,EACFkS,EAAW9mB,IAAMkT,EACtBwS,EAAO/qB,KAAKmsB,GAEZ3uB,EAAMwC,KAAKmsB,EACnB,CACA,OAAO,CACX,CAIA,YAAAC,CAAapO,EAAOoN,GAChB,IAAI/lB,EAAM2Y,EAAM3Y,IAChB,OAAS,CACL,IAAKhB,KAAKgnB,aAAarN,EAAO,KAAM,MAChC,OAAO,EACX,GAAIA,EAAM3Y,IAAMA,EAEZ,OADAgnB,EAAerO,EAAOoN,IACf,CAEf,CACJ,CACA,WAAAO,CAAYZ,EAAQrB,EAAQ0B,GACxB,IAAIG,EAAW,KAAMe,GAAY,EACjC,IAAK,IAAIjgB,EAAI,EAAGA,EAAI0e,EAAO5rB,OAAQkN,IAAK,CACpC,IAAI2R,EAAQ+M,EAAO1e,GAAIka,EAAQmD,EAAOrd,GAAK,GAAIkgB,EAAW7C,EAAkB,GAAVrd,GAAK,IACnE+N,EAAOsO,EAAUrkB,KAAKynB,QAAQ9N,GAAS,OAAS,GACpD,GAAIA,EAAMyG,QAAS,CACf,GAAI6H,EACA,SAMJ,GALAA,GAAY,EACZtO,EAAM0G,UAGKrgB,KAAK+nB,aAAapO,EAAOoN,GAEhC,QACR,CACA,IAAIoB,EAAQxO,EAAMxgB,QAASivB,EAAYrS,EACvC,IAAK,IAAIJ,EAAI,EAAGwS,EAAMxI,eAAiBhK,EAAI,GAA+BA,IAAK,CAI3E,GADW3V,KAAK+nB,aAAaI,EAAOpB,GAEhC,MACA1C,IACA+D,EAAYpoB,KAAKynB,QAAQU,GAAS,OAC1C,CACA,IAAK,IAAIE,KAAU1O,EAAM4F,gBAAgB2C,GAGrCliB,KAAK+nB,aAAaM,EAAQtB,GAE1B/mB,KAAK6e,OAAO1K,IAAMwF,EAAM3Y,KACpBknB,GAAYvO,EAAM3Y,MAClBknB,IACAhG,EAAQ,GAEZvI,EAAMqF,gBAAgBkD,EAAOgG,GAG7BF,EAAerO,EAAOoN,MAEhBG,GAAYA,EAAS1K,MAAQ7C,EAAM6C,SACzC0K,EAAWvN,EAEnB,CACA,OAAOuN,CACX,CAEA,WAAAE,CAAYzN,GAER,OADAA,EAAMoH,QACC/P,EAAKqC,MAAM,CAAEE,OAAQ2N,EAAkB9R,OAAOuK,GACjDnG,QAASxT,KAAK3E,OAAOmY,QACrBmD,MAAO3W,KAAKymB,QACZhT,gBAAiBzT,KAAK3E,OAAOsrB,aAC7BjT,OAAQ1T,KAAK0T,OACbQ,MAAOlU,KAAK6b,OAAO,GAAG7hB,KACtBc,OAAQ6e,EAAM3Y,IAAMhB,KAAK6b,OAAO,GAAG7hB,KACnC2Z,cAAe3T,KAAK3E,OAAOiiB,eACnC,CACA,OAAAmK,CAAQ9N,GACJ,IAAInkB,GAAMivB,IAAaA,EAAW,IAAI5T,UAAU3B,IAAIyK,GAGpD,OAFKnkB,GACDivB,EAASznB,IAAI2c,EAAOnkB,EAAKoO,OAAO0kB,cAActoB,KAAKsmB,gBAChD9wB,EAAKmkB,CAChB,EAEJ,SAASqO,EAAerO,EAAOoN,GAC3B,IAAK,IAAI/e,EAAI,EAAGA,EAAI+e,EAAUjsB,OAAQkN,IAAK,CACvC,IAAIuY,EAAQwG,EAAU/e,GACtB,GAAIuY,EAAMvf,KAAO2Y,EAAM3Y,KAAOuf,EAAMD,UAAU3G,GAG1C,YAFIoN,EAAU/e,GAAGwU,MAAQ7C,EAAM6C,QAC3BuK,EAAU/e,GAAK2R,GAG3B,CACAoN,EAAUprB,KAAKge,EACnB,CACA,MAAM4O,EACF,WAAAnoB,CAAYsQ,EAAQrB,EAAO/Z,GACvB0K,KAAK0Q,OAASA,EACd1Q,KAAKqP,MAAQA,EACbrP,KAAK1K,SAAWA,CACpB,CACA,MAAAsuB,CAAO7F,GAAQ,OAAQ/d,KAAK1K,UAAmC,GAAvB0K,KAAK1K,SAASyoB,EAAY,EAiCtE,MAAMyK,WAAiB/M,EAInB,WAAArb,CAAYmP,GAMR,GALAkJ,QAIAzY,KAAKyoB,SAAW,GACI,IAAhBlZ,EAAKrR,QACL,MAAM,IAAIkQ,WAAW,mBAAmBmB,EAAKrR,+CACjD,IAAIwqB,EAAYnZ,EAAKmZ,UAAUvvB,MAAM,KACrC6G,KAAKsd,cAAgBoL,EAAU5tB,OAC/B,IAAK,IAAIkN,EAAI,EAAGA,EAAIuH,EAAKoZ,gBAAiB3gB,IACtC0gB,EAAU/sB,KAAK,IACnB,IAAIitB,EAAWzpB,OAAOC,KAAKmQ,EAAKsZ,UAAUhlB,KAAImU,GAAKzI,EAAKsZ,SAAS7Q,GAAG,KAChE8Q,EAAY,GAChB,IAAK,IAAI9gB,EAAI,EAAGA,EAAI0gB,EAAU5tB,OAAQkN,IAClC8gB,EAAUntB,KAAK,IACnB,SAASotB,EAAQC,EAAQxhB,EAAM9P,GAC3BoxB,EAAUE,GAAQrtB,KAAK,CAAC6L,EAAMA,EAAK0G,YAAYtK,OAAOlM,KAC1D,CACA,GAAI6X,EAAKuZ,UACL,IAAK,IAAIG,KAAY1Z,EAAKuZ,UAAW,CACjC,IAAIthB,EAAOyhB,EAAS,GACD,iBAARzhB,IACPA,EAAOuG,EAASvG,IACpB,IAAK,IAAIQ,EAAI,EAAGA,EAAIihB,EAASnuB,QAAS,CAClC,IAAIyZ,EAAO0U,EAASjhB,KACpB,GAAIuM,GAAQ,EACRwU,EAAQxU,EAAM/M,EAAMyhB,EAASjhB,UAE5B,CACD,IAAItQ,EAAQuxB,EAASjhB,GAAKuM,GAC1B,IAAK,IAAIoB,GAAKpB,EAAMoB,EAAI,EAAGA,IACvBoT,EAAQE,EAASjhB,KAAMR,EAAM9P,GACjCsQ,GACJ,CACJ,CACJ,CACJhI,KAAKwT,QAAU,IAAInD,EAAQqY,EAAU7kB,KAAI,CAACsD,EAAMa,IAAMqG,EAASiB,OAAO,CAClEnI,KAAMa,GAAKhI,KAAKsd,mBAAgB1c,EAAYuG,EAC5C3R,GAAIwS,EACJ/S,MAAO6zB,EAAU9gB,GACjBwH,IAAKoZ,EAAS5Y,QAAQhI,IAAM,EAC5B5J,MAAY,GAAL4J,EACPyH,QAASF,EAAK2Z,cAAgB3Z,EAAK2Z,aAAalZ,QAAQhI,IAAM,OAE9DuH,EAAK4Z,cACLnpB,KAAKwT,QAAUxT,KAAKwT,QAAQjD,UAAUhB,EAAK4Z,cAC/CnpB,KAAKghB,QAAS,EACdhhB,KAAK2mB,aAAe/Y,EACpB,IAAIwb,EAAahI,EAAY7R,EAAK8Z,WAClCrpB,KAAKhI,QAAUuX,EAAKvX,QACpBgI,KAAKspB,iBAAmB/Z,EAAK0W,aAAe,GAC5CjmB,KAAKimB,YAAc,IAAIvR,YAAY1U,KAAKspB,iBAAiBxuB,QACzD,IAAK,IAAIkN,EAAI,EAAGA,EAAIhI,KAAKspB,iBAAiBxuB,OAAQkN,IAC9ChI,KAAKimB,YAAYje,GAAKhI,KAAKspB,iBAAiBthB,GAAG+V,KACnD/d,KAAKkmB,aAAelmB,KAAKspB,iBAAiBzlB,IAAI0lB,IAC9CvpB,KAAKwpB,OAASpI,EAAY7R,EAAKia,OAAQC,aACvCzpB,KAAKT,KAAO6hB,EAAY7R,EAAKma,WAC7B1pB,KAAKihB,KAAOG,EAAY7R,EAAK0R,MAC7BjhB,KAAK2pB,QAAUpa,EAAKoa,QACpB3pB,KAAKwlB,WAAajW,EAAKiW,WAAW3hB,KAAInM,GAAyB,iBAATA,EAAoB,IAAIyrB,EAAWiG,EAAY1xB,GAASA,IAC9GsI,KAAK6oB,SAAWtZ,EAAKsZ,SACrB7oB,KAAK4pB,SAAWra,EAAKqa,UAAY,CAAC,EAClC5pB,KAAK6pB,mBAAqBta,EAAKsa,oBAAsB,KACrD7pB,KAAKqjB,eAAiB9T,EAAKua,UAC3B9pB,KAAK+pB,UAAYxa,EAAKwa,WAAa,KACnC/pB,KAAKoe,QAAUpe,KAAKwT,QAAQlD,MAAMxV,OAAS,EAC3CkF,KAAK0gB,QAAU1gB,KAAKgqB,eACpBhqB,KAAKwP,IAAMxP,KAAK6oB,SAAS1pB,OAAOC,KAAKY,KAAK6oB,UAAU,GACxD,CACA,WAAA9M,CAAYJ,EAAOC,EAAWC,GAC1B,IAAIpb,EAAQ,IAAI2lB,EAAMpmB,KAAM2b,EAAOC,EAAWC,GAC9C,IAAK,IAAIoO,KAAKjqB,KAAKyoB,SACfhoB,EAAQwpB,EAAExpB,EAAOkb,EAAOC,EAAWC,GACvC,OAAOpb,CACX,CAIA,OAAA4c,CAAQf,EAAOyB,EAAMmM,GAAQ,GACzB,IAAIC,EAAQnqB,KAAKihB,KACjB,GAAIlD,GAAQoM,EAAM,GACd,OAAQ,EACZ,IAAK,IAAInpB,EAAMmpB,EAAMpM,EAAO,KAAM,CAC9B,IAAIqM,EAAWD,EAAMnpB,KAAQ4U,EAAkB,EAAXwU,EAChCjyB,EAASgyB,EAAMnpB,KACnB,GAAI4U,GAAQsU,EACR,OAAO/xB,EACX,IAAK,IAAIgc,EAAMnT,GAAOopB,GAAY,GAAIppB,EAAMmT,EAAKnT,IAC7C,GAAImpB,EAAMnpB,IAAQsb,EACd,OAAOnkB,EACf,GAAIyd,EACA,OAAQ,CAChB,CACJ,CAIA,SAAA0J,CAAUhD,EAAO+N,GACb,IAAI9qB,EAAOS,KAAKT,KAChB,IAAK,IAAIvC,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAA2FuX,EAAvFvM,EAAIhI,KAAKqf,UAAU/C,EAAOtf,EAAM,EAA0B,IAAoCgL,GAAK,EAAG,CAC3G,GAAwB,QAAnBuM,EAAOhV,EAAKyI,IAA4B,CACzC,GAAmB,GAAfzI,EAAKyI,EAAI,GAER,IAAmB,GAAfzI,EAAKyI,EAAI,GACd,OAAOiO,GAAK1W,EAAMyI,EAAI,GAEtB,KAAK,CAJLuM,EAAOhV,EAAKyI,EAAIiO,GAAK1W,EAAMyI,EAAI,GAKvC,CACA,GAAIuM,GAAQ8V,GAAoB,GAAR9V,EACpB,OAAO0B,GAAK1W,EAAMyI,EAAI,EAC9B,CAEJ,OAAO,CACX,CAIA,SAAAqX,CAAU/C,EAAOgO,GACb,OAAOtqB,KAAKwpB,OAAgB,EAARlN,EAAmCgO,EAC3D,CAIA,SAAAzM,CAAUvB,EAAOiO,GACb,OAAQvqB,KAAKqf,UAAU/C,EAAO,GAA4BiO,GAAQ,CACtE,CAIA,WAAA3K,CAAYtD,EAAOU,GACf,QAAShd,KAAKigB,WAAW3D,GAAO9iB,GAAKA,GAAKwjB,GAAgB,MAC9D,CAIA,UAAAiD,CAAW3D,EAAOU,GACd,IAAIwN,EAAQxqB,KAAKqf,UAAU/C,EAAO,GAC9BhO,EAASkc,EAAQxN,EAAOwN,QAAS5pB,EACrC,IAAK,IAAIoH,EAAIhI,KAAKqf,UAAU/C,EAAO,GAAuC,MAAVhO,EAAgBtG,GAAK,EAAG,CACpF,GAAoB,OAAhBhI,KAAKT,KAAKyI,GAA2B,CACrC,GAAwB,GAApBhI,KAAKT,KAAKyI,EAAI,GAGd,MAFAA,EAAIiO,GAAKjW,KAAKT,KAAMyI,EAAI,EAGhC,CACAsG,EAAS0O,EAAO/G,GAAKjW,KAAKT,KAAMyI,EAAI,GACxC,CACA,OAAOsG,CACX,CAKA,UAAAkR,CAAWlD,GACP,IAAIhO,EAAS,GACb,IAAK,IAAItG,EAAIhI,KAAKqf,UAAU/C,EAAO,IAA8BtU,GAAK,EAAG,CACrE,GAAoB,OAAhBhI,KAAKT,KAAKyI,GAA2B,CACrC,GAAwB,GAApBhI,KAAKT,KAAKyI,EAAI,GAGd,MAFAA,EAAIiO,GAAKjW,KAAKT,KAAMyI,EAAI,EAGhC,CACA,KAAwB,EAAnBhI,KAAKT,KAAKyI,EAAI,IAAkD,CACjE,IAAItQ,EAAQsI,KAAKT,KAAKyI,EAAI,GACrBsG,EAAOoM,MAAK,CAACgF,EAAG1X,IAAW,EAAJA,GAAU0X,GAAKhoB,KACvC4W,EAAO3S,KAAKqE,KAAKT,KAAKyI,GAAItQ,EAClC,CACJ,CACA,OAAO4W,CACX,CAMA,SAAAhX,CAAU0W,GAGN,IAAIuJ,EAAOpY,OAAOwR,OAAOxR,OAAOiQ,OAAOoZ,GAASlF,WAAYtjB,MAG5D,GAFIgO,EAAO/Y,QACPsiB,EAAK/D,QAAUxT,KAAKwT,QAAQjD,UAAUvC,EAAO/Y,QAC7C+Y,EAAOwB,IAAK,CACZ,IAAI/Q,EAAOuB,KAAK6oB,SAAS7a,EAAOwB,KAChC,IAAK/Q,EACD,MAAM,IAAI2P,WAAW,yBAAyBJ,EAAOwB,OACzD+H,EAAK/H,IAAM/Q,CACf,CA2BA,OA1BIuP,EAAOwX,aACPjO,EAAKiO,WAAaxlB,KAAKwlB,WAAW3hB,KAAI4mB,IAClC,IAAIta,EAAQnC,EAAOwX,WAAWttB,MAAK8f,GAAKA,EAAEhe,MAAQywB,IAClD,OAAOta,EAAQA,EAAM/V,GAAKqwB,CAAC,KAE/Bzc,EAAOkY,eACP3O,EAAK2O,aAAelmB,KAAKkmB,aAAavxB,QACtC4iB,EAAK+R,iBAAmBtpB,KAAKspB,iBAAiBzlB,KAAI,CAAC6D,EAAGM,KAClD,IAAImI,EAAQnC,EAAOkY,aAAahuB,MAAK8f,GAAKA,EAAEhe,MAAQ0N,EAAEgjB,WACtD,IAAKva,EACD,OAAOzI,EACX,IAAI6H,EAAOpQ,OAAOwR,OAAOxR,OAAOwR,OAAO,CAAC,EAAGjJ,GAAI,CAAEgjB,SAAUva,EAAM/V,KAEjE,OADAmd,EAAK2O,aAAale,GAAKuhB,GAAeha,GAC/BA,CAAI,KAGfvB,EAAO2c,iBACPpT,EAAKvf,QAAUgW,EAAO2c,gBACtB3c,EAAO0S,UACPnJ,EAAKmJ,QAAU1gB,KAAKgqB,aAAahc,EAAO0S,UACvB,MAAjB1S,EAAOgT,SACPzJ,EAAKyJ,OAAShT,EAAOgT,QACrBhT,EAAO4c,OACPrT,EAAKkR,SAAWlR,EAAKkR,SAASvS,OAAOlI,EAAO4c,OACrB,MAAvB5c,EAAO2Y,eACPpP,EAAKoP,aAAe3Y,EAAO2Y,cACxBpP,CACX,CAKA,WAAAsT,GACI,OAAO7qB,KAAKyoB,SAAS3tB,OAAS,CAClC,CAOA,OAAAgwB,CAAQ/M,GACJ,OAAO/d,KAAK+pB,UAAY/pB,KAAK+pB,UAAUhM,GAAQna,OAAOma,GAAQ/d,KAAKoe,SAAWpe,KAAKwT,QAAQlD,MAAMyN,GAAM5W,MAAQ4W,EACnH,CAKA,WAAIgI,GAAY,OAAO/lB,KAAKoe,QAAU,CAAG,CAIzC,WAAI7M,GAAY,OAAOvR,KAAKwT,QAAQlD,MAAMtQ,KAAKwP,IAAI,GAAK,CAIxD,iBAAA4N,CAAkBW,GACd,IAAIgN,EAAO/qB,KAAK6pB,mBAChB,OAAe,MAARkB,EAAe,EAAIA,EAAKhN,IAAS,CAC5C,CAIA,YAAAiM,CAAatJ,GACT,IAAItZ,EAASjI,OAAOC,KAAKY,KAAK4pB,UAAWva,EAAQjI,EAAOvD,KAAI,KAAM,IAClE,GAAI6c,EACA,IAAK,IAAIsK,KAAQtK,EAAQvnB,MAAM,KAAM,CACjC,IAAI3D,EAAK4R,EAAO4I,QAAQgb,GACpBx1B,GAAM,IACN6Z,EAAM7Z,IAAM,EACpB,CACJ,IAAIF,EAAW,KACf,IAAK,IAAI0S,EAAI,EAAGA,EAAIZ,EAAOtM,OAAQkN,IAC/B,IAAKqH,EAAMrH,GACP,IAAK,IAAkCxS,EAA9BmgB,EAAI3V,KAAK4pB,SAASxiB,EAAOY,IAAkC,QAAxBxS,EAAKwK,KAAKT,KAAKoW,QACtDrgB,IAAaA,EAAW,IAAI21B,WAAWjrB,KAAK2pB,QAAU,KAAKn0B,GAAM,EAE9E,OAAO,IAAI+yB,EAAQ7H,EAASrR,EAAO/Z,EACvC,CAKA,kBAAO4Y,CAAYqB,GACf,OAAO,IAAIiZ,GAASjZ,EACxB,EAEJ,SAAS0G,GAAK1W,EAAMwf,GAAO,OAAOxf,EAAKwf,GAAQxf,EAAKwf,EAAM,IAAM,EAAK,CAYrE,SAASwK,GAAeha,GACpB,GAAIA,EAAKmb,SAAU,CACf,IAAI9I,EAAOrS,EAAKgB,OAAS,EAA4B,EACrD,MAAO,CAAC7Y,EAAOiiB,IAAWpK,EAAKmb,SAAShzB,EAAOiiB,IAAU,EAAKiI,CAClE,CACA,OAAOrS,EAAKL,GAChB,CCr1DA,MAoCMgc,GAAgB,CACpBC,KArCa,EAsCbC,OArCW,EAsCXC,OArCW,EAsCXvvB,QArCY,EAsCZwvB,OArCW,EAsCXC,aApCgB,EAqChBC,YApCe,EAqCfC,cApCiB,EAqCjBC,OApCW,GAqCXvQ,OApCW,GAqCXwQ,KApCS,GAqCTC,GApCO,GAqCPC,SApCa,GAqCbC,WApCc,GAqCdC,YApCe,GAqCfC,OA/CW,EAgDXC,WArCe,GAsCfC,KArCS,GAsCTC,KArCS,IA4CLC,GAA0B,CAC9BC,GA5CO,GA6CPC,QA5CY,GA6CZC,IA5CQ,GA6CRC,GA5CO,GA6CPC,OA5CW,GA6CXC,IA5CQ,GA6CRC,IA5CQ,GA6CR/O,MA5CU,GA6CVlG,IA5CQ,GA6CRwL,IA5CQ,GA6CR0J,OA5CW,GA6CXC,OA5CW,GA6CXC,QA5CY,GA6CZC,KA5CS,GA6CTxzB,KA5CS,GA6CTyzB,UA5Cc,IAoDVC,GAAkB,CAACC,UAAU,KAAKC,GAAG,IAAKC,gBAAgB,IAAKC,KAAK,IAAKC,aAAa,IAAKC,gBAAgB,IAAKC,WAAW,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,iBAAiB,IAAKC,iBAAiB,IAAKC,mBAAmB,IAAKC,gBAAgB,IAAKC,eAAe,IAAKC,iBAAiB,IAAKC,MAAM,IAAKC,SAAS,IAAKC,iBAAiB,KACzXhzB,GAASmtB,GAASta,YAAY,CAClChQ,QAAS,GACTsrB,OAAQ,ygGACRE,UAAW,+9KACXzI,KAAM,wvCACNyH,UAAW,k4CACXiB,QAAS,IACTT,aAAc,CAAC,EAAE,IACjBP,gBAAiB,EACjBU,UAAW,4tEACX7D,WAAY,CAAC,EAAG,GAChBqD,SAAU,CAAC,MAAQ,CAAC,EAAE,KACtB5C,YAAa,CAAC,CAAClI,KAAM,GAAI7O,IAAK,CAACxX,EAAOiiB,IAzCX,CAACjiB,GACrBwzB,GAAcxzB,EAAM42B,iBAAmB,EAwCGC,CAAqB72B,IAAU,GAAI,CAACqmB,KAAM,GAAI7O,IAAK,CAACxX,EAAOiiB,IAlBrF,CAACjiB,GACjB00B,GAAwB10B,EAAM42B,iBAAmB,EAiB+DE,CAAiB92B,IAAU,EAAK,GAAG,CAACqmB,KAAM,GAAI7O,IAAKxX,GAASu1B,GAAgBv1B,KAAW,IAC9MoyB,UAAW,IAGPjkB,GAAO,EACXF,GAAS,EAsCTrC,GAAW,GAEXE,GAAU,GACVE,GAAa,GACbb,GAAK,GACL,GAAS,GACTE,GAAM,GACNE,GAAK,GACLE,GAAM,GAINa,GAAa,GAEbG,GAAY,GACZE,GAAY,GACZI,GAAc,GACdE,GAAM,GACN9G,GAAW,GAEXkJ,GAAW,GAUXzB,GAAc,GAId5C,GAAM,GACN2D,GAAW,GACX7D,GAAM,GACNF,GAAM,GACNH,GAAM,GAGNgE,GAAQ,GAGR,GAAS,GAcTsoB,GAAa,E,sEC1MXC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBhuB,IAAjBiuB,EACH,OAAOA,EAAa9hB,QAGrB,IAAID,EAAS4hB,EAAyBE,GAAY,CACjDp5B,GAAIo5B,EACJE,QAAQ,EACR/hB,QAAS,CAAC,GAUX,OANAgiB,EAAoBH,GAAUI,KAAKliB,EAAOC,QAASD,EAAQA,EAAOC,QAAS4hB,GAG3E7hB,EAAOgiB,QAAS,EAGThiB,EAAOC,OACf,C,OAGA4hB,EAAoBM,EAAIF,EC3BxBJ,EAAoB7U,EAAKhN,IACxB,IAAIoiB,EAASpiB,GAAUA,EAAOqiB,WAC7B,IAAOriB,EAAiB,QACxB,IAAM,EAEP,OADA6hB,EAAoBvU,EAAE8U,EAAQ,CAAE11B,EAAG01B,IAC5BA,CAAM,ElCNV16B,EAAW2K,OAAOiwB,eAAkBznB,GAASxI,OAAOiwB,eAAeznB,GAASA,GAASA,EAAa,UAQtGgnB,EAAoBlE,EAAI,SAAS/yB,EAAO2Z,GAEvC,GADU,EAAPA,IAAU3Z,EAAQsI,KAAKtI,IAChB,EAAP2Z,EAAU,OAAO3Z,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP2Z,GAAa3Z,EAAMy3B,WAAY,OAAOz3B,EAC1C,GAAW,GAAP2Z,GAAoC,mBAAf3Z,EAAM23B,KAAqB,OAAO33B,CAC5D,CACA,IAAI43B,EAAKnwB,OAAOiQ,OAAO,MACvBuf,EAAoB3W,EAAEsX,GACtB,IAAIC,EAAM,CAAC,EACXh7B,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAIg7B,EAAiB,EAAPne,GAAY3Z,EAAyB,iBAAX83B,KAAyBj7B,EAAeyb,QAAQwf,GAAUA,EAAUh7B,EAASg7B,GACxHrwB,OAAOswB,oBAAoBD,GAASnwB,SAAShG,GAASk2B,EAAIl2B,GAAO,IAAO3B,EAAM2B,KAI/E,OAFAk2B,EAAa,QAAI,IAAM,EACvBZ,EAAoBvU,EAAEkV,EAAIC,GACnBD,CACR,EmCxBAX,EAAoBvU,EAAI,CAACrN,EAAS2iB,KACjC,IAAI,IAAIr2B,KAAOq2B,EACXf,EAAoBrnB,EAAEooB,EAAYr2B,KAASs1B,EAAoBrnB,EAAEyF,EAAS1T,IAC5E8F,OAAOwwB,eAAe5iB,EAAS1T,EAAK,CAAEu2B,YAAY,EAAM1gB,IAAKwgB,EAAWr2B,IAE1E,ECNDs1B,EAAoBkB,EAAI,CAAC,EAGzBlB,EAAoB5vB,EAAK+wB,GACjB95B,QAAQC,IAAIkJ,OAAOC,KAAKuvB,EAAoBkB,GAAG9S,QAAO,CAACgT,EAAU12B,KACvEs1B,EAAoBkB,EAAEx2B,GAAKy2B,EAASC,GAC7BA,IACL,KCNJpB,EAAoBqB,EAAKF,GAEZA,EAAU,cAAgB,CAAC,GAAK,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,wBAAwBA,GCH7SnB,EAAoBsB,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOlwB,MAAQ,IAAImwB,SAAS,cAAb,EAChB,CAAE,MAAOpxB,GACR,GAAsB,iBAAXqxB,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBzB,EAAoBrnB,EAAI,CAACK,EAAKH,IAAUrI,OAAOmkB,UAAU+M,eAAerB,KAAKrnB,EAAKH,GtCA9E/S,EAAa,CAAC,EACdC,EAAoB,2BAExBi6B,EAAoB2B,EAAI,CAACC,EAAKvU,EAAM3iB,EAAKy2B,KACxC,GAAGr7B,EAAW87B,GAAQ97B,EAAW87B,GAAK50B,KAAKqgB,OAA3C,CACA,IAAIwU,EAAQC,EACZ,QAAW7vB,IAARvH,EAEF,IADA,IAAIq3B,EAAUC,SAASC,qBAAqB,UACpC5oB,EAAI,EAAGA,EAAI0oB,EAAQ51B,OAAQkN,IAAK,CACvC,IAAIN,EAAIgpB,EAAQ1oB,GAChB,GAAGN,EAAEmpB,aAAa,QAAUN,GAAO7oB,EAAEmpB,aAAa,iBAAmBn8B,EAAoB2E,EAAK,CAAEm3B,EAAS9oB,EAAG,KAAO,CACpH,CAEG8oB,IACHC,GAAa,GACbD,EAASG,SAASG,cAAc,WAEzBC,QAAU,QACjBP,EAAOQ,QAAU,IACbrC,EAAoBsC,IACvBT,EAAOU,aAAa,QAASvC,EAAoBsC,IAElDT,EAAOU,aAAa,eAAgBx8B,EAAoB2E,GAExDm3B,EAAO9gB,IAAM6gB,EAC4C,IAArDC,EAAO9gB,IAAIM,QAAQogB,OAAOnzB,SAASk0B,OAAS,OAC/CX,EAAOY,YAAc,aAEtBZ,EAAOa,UAAY1C,EAAoB2C,UAAUxB,GACjDU,EAAOY,YAAc,aAEtB38B,EAAW87B,GAAO,CAACvU,GACnB,IAAIuV,EAAmB,CAAChX,EAAMiX,KAE7BhB,EAAOiB,QAAUjB,EAAOkB,OAAS,KACjCC,aAAaX,GACb,IAAIY,EAAUn9B,EAAW87B,GAIzB,UAHO97B,EAAW87B,GAClBC,EAAOqB,YAAcrB,EAAOqB,WAAWC,YAAYtB,GACnDoB,GAAWA,EAAQvyB,SAAS0yB,GAAQA,EAAGP,KACpCjX,EAAM,OAAOA,EAAKiX,EAAM,EAExBR,EAAUgB,WAAWT,EAAiBU,KAAK,UAAMrxB,EAAW,CAAEvI,KAAM,UAAWF,OAAQq4B,IAAW,MACtGA,EAAOiB,QAAUF,EAAiBU,KAAK,KAAMzB,EAAOiB,SACpDjB,EAAOkB,OAASH,EAAiBU,KAAK,KAAMzB,EAAOkB,QACnDjB,GAAcE,SAASuB,KAAKC,YAAY3B,EAzCkB,CAyCX,EuC5ChD7B,EAAoB3W,EAAKjL,IACH,oBAAXxP,QAA0BA,OAAO60B,aAC1CjzB,OAAOwwB,eAAe5iB,EAASxP,OAAO60B,YAAa,CAAE16B,MAAO,WAE7DyH,OAAOwwB,eAAe5iB,EAAS,aAAc,CAAErV,OAAO,GAAO,ECL9Di3B,EAAoB0D,IAAOvlB,IAC1BA,EAAOwlB,MAAQ,GACVxlB,EAAOmE,WAAUnE,EAAOmE,SAAW,IACjCnE,GCHR6hB,EAAoB3kB,EAAI,0CCCxB2kB,EAAoB2C,UAAY,CAAC,GAAK,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,uD,MCDtgB3C,EAAoBrX,EAAIqZ,SAAS4B,SAAWC,KAAKv1B,SAASw1B,KAK1D,IAAIC,EAAkB,CACrB,IAAK,GAGN/D,EAAoBkB,EAAEla,EAAI,CAACma,EAASC,KAElC,IAAI4C,EAAqBhE,EAAoBrnB,EAAEorB,EAAiB5C,GAAW4C,EAAgB5C,QAAWlvB,EACtG,GAA0B,IAAvB+xB,EAGF,GAAGA,EACF5C,EAASp0B,KAAKg3B,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAI58B,SAAQ,CAAC8b,EAAS+gB,IAAYF,EAAqBD,EAAgB5C,GAAW,CAAChe,EAAS+gB,KAC1G9C,EAASp0B,KAAKg3B,EAAmB,GAAKC,GAGtC,IAAIrC,EAAM5B,EAAoB3kB,EAAI2kB,EAAoBqB,EAAEF,GAEpD1xB,EAAQ,IAAIqB,MAgBhBkvB,EAAoB2B,EAAEC,GAfFiB,IACnB,GAAG7C,EAAoBrnB,EAAEorB,EAAiB5C,KAEf,KAD1B6C,EAAqBD,EAAgB5C,MACR4C,EAAgB5C,QAAWlvB,GACrD+xB,GAAoB,CACtB,IAAIG,EAAYtB,IAAyB,SAAfA,EAAMn5B,KAAkB,UAAYm5B,EAAMn5B,MAChE06B,EAAUvB,GAASA,EAAMr5B,QAAUq5B,EAAMr5B,OAAOuX,IACpDtR,EAAM40B,QAAU,iBAAmBlD,EAAU,cAAgBgD,EAAY,KAAOC,EAAU,IAC1F30B,EAAM+I,KAAO,iBACb/I,EAAM/F,KAAOy6B,EACb10B,EAAM60B,QAAUF,EAChBJ,EAAmB,GAAGv0B,EACvB,CACD,GAEwC,SAAW0xB,EAASA,EAE/D,CACD,EAcF,IAAIoD,EAAuB,CAACC,EAA4B5zB,KACvD,IAGIqvB,EAAUkB,GAHTsD,EAAUC,EAAaC,GAAW/zB,EAGhByI,EAAI,EAC3B,GAAGorB,EAAS1Y,MAAMllB,GAAgC,IAAxBk9B,EAAgBl9B,KAAa,CACtD,IAAIo5B,KAAYyE,EACZ1E,EAAoBrnB,EAAE+rB,EAAazE,KACrCD,EAAoBM,EAAEL,GAAYyE,EAAYzE,IAGhD,GAAG0E,EAAsBA,EAAQ3E,EAClC,CAEA,IADGwE,GAA4BA,EAA2B5zB,GACrDyI,EAAIorB,EAASt4B,OAAQkN,IACzB8nB,EAAUsD,EAASprB,GAChB2mB,EAAoBrnB,EAAEorB,EAAiB5C,IAAY4C,EAAgB5C,IACrE4C,EAAgB5C,GAAS,KAE1B4C,EAAgB5C,GAAW,CAC5B,EAIGyD,EAAqBf,KAA0C,oCAAIA,KAA0C,qCAAK,GACtHe,EAAmBl0B,QAAQ6zB,EAAqBjB,KAAK,KAAM,IAC3DsB,EAAmB53B,KAAOu3B,EAAqBjB,KAAK,KAAMsB,EAAmB53B,KAAKs2B,KAAKsB,G,KClF7D5E,EAAoB,K", "sources": ["webpack://grafana-lokiexplore-app/webpack/runtime/create fake namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/load script", "webpack://grafana-lokiexplore-app/./node_modules/grafana-public-path.js", "webpack://grafana-lokiexplore-app/./services/extensions/exposedComponents.tsx", "webpack://grafana-lokiexplore-app/./module.tsx", "webpack://grafana-lokiexplore-app/./services/extensions/links.ts", "webpack://grafana-lokiexplore-app/./services/fieldsTypes.ts", "webpack://grafana-lokiexplore-app/./services/filterTypes.ts", "webpack://grafana-lokiexplore-app/./services/logger.ts", "webpack://grafana-lokiexplore-app/./services/logqlMatchers.ts", "webpack://grafana-lokiexplore-app/./services/lokiQuery.ts", "webpack://grafana-lokiexplore-app/./services/narrowing.ts", "webpack://grafana-lokiexplore-app/./services/operatorHelpers.ts", "webpack://grafana-lokiexplore-app/./services/getOperatorDescription.ts", "webpack://grafana-lokiexplore-app/./services/operators.ts", "webpack://grafana-lokiexplore-app/./services/extensions/scenesMethods.ts", "webpack://grafana-lokiexplore-app/./services/renderPatternFilters.ts", "webpack://grafana-lokiexplore-app/./services/variables.ts", "webpack://grafana-lokiexplore-app/external amd \"@emotion/css\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/data\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/runtime\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/ui\"", "webpack://grafana-lokiexplore-app/external amd \"lodash\"", "webpack://grafana-lokiexplore-app/external amd \"module\"", "webpack://grafana-lokiexplore-app/external amd \"react\"", "webpack://grafana-lokiexplore-app/external amd \"react-dom\"", "webpack://grafana-lokiexplore-app/external amd \"react-redux\"", "webpack://grafana-lokiexplore-app/external amd \"react-router\"", "webpack://grafana-lokiexplore-app/external amd \"redux\"", "webpack://grafana-lokiexplore-app/external amd \"rxjs\"", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/common/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/lr/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@grafana/lezer-logql/index.es.js", "webpack://grafana-lokiexplore-app/webpack/bootstrap", "webpack://grafana-lokiexplore-app/webpack/runtime/compat get default export", "webpack://grafana-lokiexplore-app/webpack/runtime/define property getters", "webpack://grafana-lokiexplore-app/webpack/runtime/ensure chunk", "webpack://grafana-lokiexplore-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-lokiexplore-app/webpack/runtime/global", "webpack://grafana-lokiexplore-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-lokiexplore-app/webpack/runtime/make namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/node module decorator", "webpack://grafana-lokiexplore-app/webpack/runtime/publicPath", "webpack://grafana-lokiexplore-app/webpack/runtime/compat", "webpack://grafana-lokiexplore-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-lokiexplore-app/webpack/startup"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-lokiexplore-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t\tif (script.src.indexOf(window.location.origin + '/') !== 0) {\n\t\t\tscript.crossOrigin = \"anonymous\";\n\t\t}\n\t\tscript.integrity = __webpack_require__.sriHashes[chunkId];\n\t\tscript.crossOrigin = \"anonymous\";\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "\nimport amdMetaModule from 'amd-module';\n\n__webpack_public_path__ =\n  amdMetaModule && amdMetaModule.uri\n    ? amdMetaModule.uri.slice(0, amdMetaModule.uri.lastIndexOf('/') + 1)\n    : 'public/plugins/grafana-lokiexplore-app/';\n", "import React, { lazy, Suspense } from 'react';\n\nimport { LinkButton } from '@grafana/ui';\n\nimport { OpenInLogsDrilldownButtonProps } from 'Components/OpenInLogsDrilldownButton/types';\nconst OpenInLogsDrilldownButton = lazy(() => import('Components/OpenInLogsDrilldownButton/OpenInLogsDrilldownButton'));\n\nfunction SuspendedOpenInLogsDrilldownButton(props: OpenInLogsDrilldownButtonProps) {\n  return (\n    <Suspense\n      fallback={\n        <LinkButton variant=\"secondary\" disabled>\n          Open in Logs Drilldown\n        </LinkButton>\n      }\n    >\n      <OpenInLogsDrilldownButton {...props} />\n    </Suspense>\n  );\n}\n\nexport const exposedComponents = [\n  {\n    component: SuspendedOpenInLogsDrilldownButton,\n    description: 'A button that opens a logs view in the Logs Drilldown app.',\n    id: `grafana-lokiexplore-app/open-in-explore-logs-button/v1`,\n    title: 'Open in Logs Drilldown button',\n  },\n];\n", "import { lazy } from 'react';\n\nimport { AppPlugin } from '@grafana/data';\n\nimport { exposedComponents } from 'services/extensions/exposedComponents';\nimport { linkConfigs } from 'services/extensions/links';\n\n// Anything imported in this file is included in the main bundle which is pre-loaded in Grafana\n// Don't add imports to this file without lazy loading\n// Link extensions are the exception as they must be included in the main bundle in order to work in core Grafana\nconst App = lazy(async () => {\n  const { wasmSupported } = await import('services/sorting');\n\n  const { default: initRuntimeDs } = await import('services/datasource');\n  const { default: initChangepoint } = await import('@bsull/augurs/changepoint');\n  const { default: initOutlier } = await import('@bsull/augurs/outlier');\n\n  initRuntimeDs();\n\n  if (wasmSupported()) {\n    await Promise.all([initChangepoint(), initOutlier()]);\n  }\n\n  return import('Components/App');\n});\n\nconst AppConfig = lazy(async () => {\n  return await import('./Components/AppConfig/AppConfig');\n});\n\nexport const plugin = new AppPlugin<{}>().setRootPage(App).addConfigPage({\n  body: AppConfig,\n  icon: 'cog',\n  id: 'configuration',\n  title: 'Configuration',\n});\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n\nfor (const exposedComponentConfig of exposedComponents) {\n  plugin.exposeComponent(exposedComponentConfig);\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { PluginExtensionLinkConfig, PluginExtensionPanelContext, PluginExtensionPoints } from '@grafana/data';\nimport { locationService } from '@grafana/runtime';\n\nimport pluginJson from '../../plugin.json';\nimport { LabelType } from '../fieldsTypes';\nimport { PatternFilterOp } from '../filterTypes';\nimport { getMatcherFromQuery } from '../logqlMatchers';\nimport { LokiQuery } from '../lokiQuery';\nimport { isOperatorInclusive } from '../operatorHelpers';\nimport { renderPatternFilters } from '../renderPatternFilters';\nimport {\n  addAdHocFilterUserInputPrefix,\n  AdHocFieldValue,\n  AppliedPattern,\n  EMPTY_VARIABLE_VALUE,\n  LEVEL_VARIABLE_VALUE,\n  SERVICE_NAME,\n  stripAdHocFilterUserInputPrefix,\n  VAR_DATASOURCE,\n  VAR_FIELDS,\n  VAR_LABELS,\n  VAR_LEVELS,\n  VAR_LINE_FILTERS,\n  VAR_METADATA,\n  VAR_PATTERNS,\n} from 'services/variables';\n\nconst PRODUCT_NAME = 'Grafana Logs Drilldown';\nconst title = `Open in ${PRODUCT_NAME}`;\nconst description = `Open current query in the ${PRODUCT_NAME} view`;\nconst icon = 'gf-logs';\n\nexport const ExtensionPoints = {\n  MetricInvestigation: 'grafana-lokiexplore-app/investigation/v1',\n} as const;\n\n/* eslint-disable sort/object-properties */\nexport type LinkConfigs = Array<\n  {\n    targets: string | string[];\n    // eslint-disable-next-line deprecation/deprecation\n  } & Omit<PluginExtensionLinkConfig<PluginExtensionPanelContext>, 'extensionPointId' | 'type'>\n>;\n\n// `plugin.addLink` requires these types; unfortunately, the correct `PluginExtensionAddedLinkConfig` type is not exported with 11.2.x\n// TODO: fix this type when we move to `@grafana/data` 11.3.x\nexport const linkConfigs: LinkConfigs = [\n  {\n    targets: PluginExtensionPoints.DashboardPanelMenu,\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n  {\n    targets: PluginExtensionPoints.ExploreToolbarAction,\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n];\n\nfunction stringifyValues(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n  return value;\n}\n\n// Why are there twice as many escape chars in the url as expected?\nexport function replaceEscapeChars(value?: string): string | undefined {\n  return value?.replace(/\\\\\\\\/g, '\\\\');\n}\n\nexport function stringifyAdHocValues(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n\n  // All label values from explore are already escaped, so we mark them as custom values to prevent them from getting escaped again when rendering the LogQL\n  return addAdHocFilterUserInputPrefix(replaceEscapeChars(value));\n}\n\nexport function stringifyAdHocValueLabels(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n\n  return escapeURLDelimiters(replaceEscapeChars(value));\n}\n\nfunction contextToLink<T extends PluginExtensionPanelContext>(context?: T) {\n  if (!context) {\n    return undefined;\n  }\n  const lokiQuery = context.targets.find((target) => target.datasource?.type === 'loki') as LokiQuery | undefined;\n  if (!lokiQuery || !lokiQuery.datasource?.uid) {\n    return undefined;\n  }\n\n  const expr = lokiQuery.expr;\n  const { fields, labelFilters, lineFilters, patternFilters } = getMatcherFromQuery(expr, context, lokiQuery);\n  const labelSelector = labelFilters.find((selector) => isOperatorInclusive(selector.operator));\n\n  // Require at least one inclusive operator to run a valid Loki query\n  if (!labelSelector) {\n    return undefined;\n  }\n\n  // If there are a bunch of values for the same field, the value slug can get really long, let's just use the first one in the URL\n  const urlLabelValue = labelSelector.value.split('|')[0];\n  const labelValue = replaceSlash(urlLabelValue);\n  let labelName = labelSelector.key === SERVICE_NAME ? 'service' : labelSelector.key;\n  // sort `primary label` first\n  labelFilters.sort((a) => (a.key === labelName ? -1 : 1));\n\n  let params = setUrlParameter(UrlParameters.DatasourceId, lokiQuery.datasource?.uid, new URLSearchParams());\n  params = setUrlParameter(UrlParameters.TimeRangeFrom, context.timeRange.from.valueOf().toString(), params);\n  params = setUrlParameter(UrlParameters.TimeRangeTo, context.timeRange.to.valueOf().toString(), params);\n\n  for (const labelFilter of labelFilters) {\n    // skip non-indexed filters for now\n    if (labelFilter.type !== LabelType.Indexed) {\n      continue;\n    }\n\n    const labelsAdHocFilterURLString = `${labelFilter.key}|${labelFilter.operator}|${escapeURLDelimiters(\n      stringifyAdHocValues(labelFilter.value)\n    )},${escapeURLDelimiters(replaceEscapeChars(labelFilter.value))}`;\n\n    params = appendUrlParameter(UrlParameters.Labels, labelsAdHocFilterURLString, params);\n  }\n\n  if (lineFilters) {\n    for (const lineFilter of lineFilters) {\n      params = appendUrlParameter(\n        UrlParameters.LineFilters,\n        `${lineFilter.key}|${escapeURLDelimiters(lineFilter.operator)}|${escapeURLDelimiters(\n          stringifyValues(lineFilter.value)\n        )}`,\n        params\n      );\n    }\n  }\n  if (fields?.length) {\n    for (const field of fields) {\n      if (field.type === LabelType.StructuredMetadata) {\n        if (field.key === LEVEL_VARIABLE_VALUE) {\n          params = appendUrlParameter(\n            UrlParameters.Levels,\n            `${field.key}|${field.operator}|${escapeURLDelimiters(stringifyValues(field.value))}`,\n            params\n          );\n        } else {\n          params = appendUrlParameter(\n            UrlParameters.Metadata,\n            `${field.key}|${field.operator}|${escapeURLDelimiters(\n              stringifyAdHocValues(field.value)\n            )},${escapeURLDelimiters(replaceEscapeChars(field.value))}`,\n            params\n          );\n        }\n      } else {\n        const fieldValue: AdHocFieldValue = {\n          value: field.value,\n          parser: field.parser,\n        };\n\n        const adHocFilterURLString = `${field.key}|${field.operator}|${escapeURLDelimiters(\n          stringifyAdHocValues(JSON.stringify(fieldValue))\n        )},${stringifyAdHocValueLabels(fieldValue.value)}`;\n\n        params = appendUrlParameter(UrlParameters.Fields, adHocFilterURLString, params);\n      }\n    }\n  }\n  if (patternFilters?.length) {\n    const patterns: AppliedPattern[] = [];\n\n    for (const field of patternFilters) {\n      patterns.push({\n        type: field.operator === PatternFilterOp.match ? 'include' : 'exclude',\n        pattern: stringifyValues(field.value),\n      });\n    }\n\n    let patternsString = renderPatternFilters(patterns);\n\n    params = appendUrlParameter(UrlParameters.Patterns, JSON.stringify(patterns), params);\n    params = appendUrlParameter(UrlParameters.PatternsVariable, patternsString, params);\n  }\n\n  return {\n    path: createAppUrl(`/explore/${labelName}/${labelValue}/logs`, params),\n  };\n}\n\nexport function createAppUrl(path = '/explore', urlParams?: URLSearchParams): string {\n  return `/a/${pluginJson.id}${path}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\nexport const UrlParameters = {\n  DatasourceId: `var-${VAR_DATASOURCE}`,\n  TimeRangeFrom: 'from',\n  TimeRangeTo: 'to',\n  Labels: `var-${VAR_LABELS}`,\n  Fields: `var-${VAR_FIELDS}`,\n  Metadata: `var-${VAR_METADATA}`,\n  Levels: `var-${VAR_LEVELS}`,\n  LineFilters: `var-${VAR_LINE_FILTERS}`,\n  Patterns: VAR_PATTERNS,\n  PatternsVariable: `var-${VAR_PATTERNS}`,\n} as const;\nexport type UrlParameterType = (typeof UrlParameters)[keyof typeof UrlParameters];\n\nexport function setUrlParameter(key: UrlParameterType, value: string, initalParams?: URLSearchParams): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? locationService.getSearch());\n  searchParams.set(key, value);\n\n  return searchParams;\n}\n\nexport function appendUrlParameter(\n  key: UrlParameterType,\n  value: string,\n  initalParams?: URLSearchParams\n): URLSearchParams {\n  const location = locationService.getLocation();\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.append(key, value);\n\n  return searchParams;\n}\n\nexport function replaceSlash(parameter: string): string {\n  return (\n    stripAdHocFilterUserInputPrefix(parameter)\n      // back-slash is converted to forward-slash in the URL, replace that char\n      .replace(/\\//g, '-')\n      .replace(/\\\\/g, '-')\n  );\n}\n\n// Manually copied over from @grafana/scenes so we don't need to import scenes to build links\nfunction escapeUrlCommaDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the comma due to using it as a value/label separator\n  return /,/g[Symbol.replace](value, '__gfc__');\n}\n\nexport function escapeUrlPipeDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the pipe due to using it as a filter separator\n  return (value = /\\|/g[Symbol.replace](value, '__gfp__'));\n}\n\nexport function escapeURLDelimiters(value: string | undefined): string {\n  return escapeUrlCommaDelimiters(escapeUrlPipeDelimiters(value));\n}\n", "// copied from public/app/plugins/datasource/loki/types.ts\nexport enum LabelType {\n  Indexed = 'I',\n  StructuredMetadata = 'S',\n  Parsed = 'P',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { LabelType } from './fieldsTypes';\nimport { ParserType } from './variables';\n\nexport type FilterOpType = LabelFilterOp | NumericFilterOp;\nexport enum LabelFilterOp {\n  Equal = '=',\n  NotEqual = '!=',\n  RegexEqual = '=~',\n  RegexNotEqual = '!~',\n}\n// Line filter doesn't have an operator, so we add an empty space to keep it in URL state\nexport enum LineFormatFilterOp {\n  Empty = ' ',\n}\n\nexport enum NumericFilterOp {\n  gt = '>',\n  lt = '<',\n  gte = '>=',\n  lte = '<=',\n}\nexport const FilterOp = { ...LabelFilterOp, ...NumericFilterOp };\n\nexport type IndexedLabelFilter = {\n  key: string;\n  operator: FilterOpType;\n  type?: LabelType;\n  value: string;\n};\n\nexport type FieldFilter = {\n  key: string;\n  operator: FilterOpType;\n  parser?: ParserType;\n  type?: LabelType;\n  value: string;\n};\n\nexport type LineFilterType = {\n  key: string;\n  operator: LineFilterOp;\n  value: string;\n};\n\nexport type PatternFilterType = {\n  operator: PatternFilterOp;\n  value: string;\n};\n\nexport enum LineFilterOp {\n  match = '|=',\n  negativeMatch = `!=`,\n  regex = '|~',\n  negativeRegex = `!~`,\n}\n\nexport enum PatternFilterOp {\n  match = '|>',\n  negativeMatch = '!>',\n}\n\nexport enum LineFilterCaseSensitive {\n  caseSensitive = 'caseSensitive',\n  caseInsensitive = 'caseInsensitive',\n}\n", "import { LogContext } from '@grafana/faro-web-sdk';\nimport { FetchError, logError, logInfo, logWarning } from '@grafana/runtime';\n\nimport packageJson from '../../package.json';\nimport pluginJson from '../plugin.json';\nimport { isRecord } from './narrowing';\n\nconst defaultContext = {\n  app: pluginJson.id,\n  version: packageJson.version,\n};\n\nexport const logger = {\n  error: (err: Error | unknown, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.error(err, ctx);\n    attemptFaroErr(err, ctx);\n  },\n  info: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.log(msg, ctx);\n    attemptFaroInfo(msg, ctx);\n  },\n  warn: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.warn(msg, ctx);\n    attemptFaroWarn(msg, ctx);\n  },\n};\n\nconst attemptFaroInfo = (msg: string, context?: LogContext) => {\n  try {\n    logInfo(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro event!');\n  }\n};\n\nconst attemptFaroWarn = (msg: string, context?: LogContext) => {\n  try {\n    logWarning(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro warning!', { context, msg });\n  }\n};\n/**\n * Checks unknown error for properties from Records like FetchError and adds them to the context\n * @param err\n * @param context\n */\nfunction populateFetchErrorContext(err: unknown | FetchError, context: LogContext) {\n  if (typeof err === 'object' && err !== null) {\n    if (isRecord(err)) {\n      Object.keys(err).forEach((key: string) => {\n        const value = err[key];\n        if (typeof value === 'string' || typeof value === 'boolean' || typeof value === 'number') {\n          context[key] = value.toString();\n        }\n      });\n    }\n\n    if (hasData(err)) {\n      if (typeof err.data === 'object' && err.data !== null) {\n        try {\n          context.data = JSON.stringify(err.data);\n        } catch (e) {\n          // do nothing\n        }\n      } else if (typeof err.data === 'string' || typeof err.data === 'boolean' || typeof err.data === 'number') {\n        context.data = err.data.toString();\n      }\n    }\n  }\n}\n\nconst attemptFaroErr = (err: Error | FetchError | unknown, context2: LogContext) => {\n  let context = context2;\n  try {\n    populateFetchErrorContext(err, context);\n\n    if (err instanceof Error) {\n      logError(err, context);\n    } else if (typeof err === 'string') {\n      logError(new Error(err), context);\n    } else if (err && typeof err === 'object') {\n      if (context.msg) {\n        logError(new Error(context.msg), context);\n      } else {\n        logError(new Error('error object'), context);\n      }\n    } else {\n      logError(new Error('unknown error'), context);\n    }\n  } catch (e) {\n    console.error('Failed to log faro error!', { context, err });\n  }\n};\n\nconst hasData = (value: object): value is { data: unknown } => {\n  return 'data' in value;\n};\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { NodeType, SyntaxNode, Tree } from '@lezer/common';\n\nimport { PluginExtensionPanelContext } from '@grafana/data';\nimport {\n  Bytes,\n  Duration,\n  Eq,\n  FilterOp,\n  Gte,\n  Gtr,\n  Identifier,\n  <PERSON>son,\n  LabelFilter,\n  LineFilter,\n  Logfmt,\n  Lss,\n  Lte,\n  Matcher,\n  Neq,\n  Npa,\n  Nre,\n  Number,\n  OrFilter,\n  parser,\n  PipeExact,\n  PipeMatch,\n  PipePattern,\n  Re,\n  Selector,\n  String,\n} from '@grafana/lezer-logql';\n\nimport { LabelType } from './fieldsTypes';\nimport {\n  FieldFilter,\n  FilterOp as FilterOperator,\n  FilterOpType,\n  IndexedLabelFilter,\n  LineFilterCaseSensitive,\n  LineFilterOp,\n  LineFilterType,\n  PatternFilterOp,\n  PatternFilterType,\n} from './filterTypes';\nimport { getLabelType<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Query } from './lokiQuery';\nimport { ParserType } from './variables';\n\nexport class NodePosition {\n  from: number;\n  to: number;\n  type?: NodeType;\n  syntaxNode?: SyntaxNode;\n\n  constructor(from: number, to: number, syntaxNode?: SyntaxNode, type?: NodeType) {\n    this.from = from;\n    this.to = to;\n    this.type = type;\n    this.syntaxNode = syntaxNode;\n  }\n\n  static fromNode(node: SyntaxNode): NodePosition {\n    return new NodePosition(node.from, node.to, node, node.type);\n  }\n\n  contains(position: NodePosition): boolean {\n    return this.from <= position.from && this.to >= position.to;\n  }\n\n  getExpression(query: string): string {\n    return query.substring(this.from, this.to);\n  }\n}\n\nexport function getNodesFromQuery(query: string, nodeTypes?: number[]): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  const tree: Tree = parser.parse(query);\n  tree.iterate({\n    enter: (node): false | void => {\n      if (nodeTypes === undefined || nodeTypes.includes(node.type.id)) {\n        nodes.push(node.node);\n      }\n    },\n  });\n  return nodes;\n}\n\nfunction getAllPositionsInNodeByType(node: SyntaxNode, type: number): NodePosition[] {\n  if (node.type.id === type) {\n    return [NodePosition.fromNode(node)];\n  }\n\n  const positions: NodePosition[] = [];\n  let pos = 0;\n  let child = node.childAfter(pos);\n  while (child) {\n    positions.push(...getAllPositionsInNodeByType(child, type));\n    pos = child.to;\n    child = node.childAfter(pos);\n  }\n  return positions;\n}\n\nfunction parseLabelFilters(query: string, filter: IndexedLabelFilter[]) {\n  // `Matcher` will select field filters as well as indexed label filters\n  const allMatcher = getNodesFromQuery(query, [Matcher]);\n  for (const matcher of allMatcher) {\n    const identifierPosition = getAllPositionsInNodeByType(matcher, Identifier);\n    if (!identifierPosition || identifierPosition.length === 0) {\n      continue;\n    }\n\n    const valuePosition = getAllPositionsInNodeByType(matcher, String);\n    const operator = query.substring(identifierPosition[0].to, valuePosition[0].from);\n    const key = identifierPosition[0].getExpression(query);\n    const value = valuePosition.map((position) => query.substring(position.from + 1, position.to - 1))[0];\n\n    if (\n      !key ||\n      !value ||\n      (operator !== FilterOperator.NotEqual &&\n        operator !== FilterOperator.Equal &&\n        operator !== FilterOperator.RegexEqual &&\n        operator !== FilterOperator.RegexNotEqual)\n    ) {\n      continue;\n    }\n\n    filter.push({\n      key,\n      operator,\n      type: LabelType.Indexed,\n      value,\n    });\n  }\n}\n\nfunction parseNonPatternFilters(\n  lineFilterValue: string,\n  quoteString: string,\n  lineFilters: LineFilterType[],\n  index: number,\n  operator: LineFilterOp\n) {\n  const isRegexSelector = operator === LineFilterOp.regex || operator === LineFilterOp.negativeRegex;\n  const isCaseInsensitive = lineFilterValue.includes('(?i)') && isRegexSelector;\n\n  // If quoteString is `, we shouldn't need to un-escape anything\n  // But if the quoteString is \", we'll need to remove double escape chars, as these values are re-escaped when building the query expression (but not stored in the value/url)\n  if (quoteString === '\"' && isRegexSelector) {\n    // replace \\\\ with \\\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  } else if (quoteString === '\"') {\n    // replace \\\\\\\" => \"\n    const replaceDoubleQuoteEscape = new RegExp(`\\\\\\\\\\\"`, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  }\n\n  if (isCaseInsensitive) {\n    // If `(?i)` exists in a regex it would need to be escaped to match log lines containing `(?i)`, so it should be safe to replace all instances of `(?i)` in the line filter?\n    lineFilterValue = lineFilterValue.replace('(?i)', '');\n  }\n\n  lineFilters.push({\n    key: isCaseInsensitive\n      ? LineFilterCaseSensitive.caseInsensitive.toString()\n      : LineFilterCaseSensitive.caseSensitive.toString() + ',' + index.toString(),\n    operator: operator,\n    value: lineFilterValue,\n  });\n\n  return lineFilterValue;\n}\n\nfunction parsePatternFilters(lineFilterValue: string, patternFilters: PatternFilterType[], operator: PatternFilterOp) {\n  const replaceDoubleQuoteEscape = new RegExp(/\\\\\"/, 'g');\n  lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n  patternFilters.push({\n    operator,\n    value: lineFilterValue,\n  });\n}\n\nfunction parseLineFilters(query: string, lineFilters: LineFilterType[], patternFilters: PatternFilterType[]) {\n  const allLineFilters = getNodesFromQuery(query, [LineFilter]);\n  for (const [index, matcher] of allLineFilters.entries()) {\n    const equal = getAllPositionsInNodeByType(matcher, PipeExact);\n    const pipeRegExp = getAllPositionsInNodeByType(matcher, PipeMatch);\n    const notEqual = getAllPositionsInNodeByType(matcher, Neq);\n    const notEqualRegExp = getAllPositionsInNodeByType(matcher, Nre);\n    const patternInclude = getAllPositionsInNodeByType(matcher, PipePattern);\n    const patternExclude = getAllPositionsInNodeByType(matcher, Npa);\n\n    const lineFilterValueNodes = getStringsFromLineFilter(matcher);\n\n    for (const lineFilterValueNode of lineFilterValueNodes) {\n      const quoteString = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.from);\n\n      // Remove quotes\n      let lineFilterValue = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.to - 1);\n\n      if (lineFilterValue.length) {\n        let operator;\n        if (equal.length) {\n          operator = LineFilterOp.match;\n        } else if (notEqual.length) {\n          operator = LineFilterOp.negativeMatch;\n        } else if (notEqualRegExp.length) {\n          operator = LineFilterOp.negativeRegex;\n        } else if (pipeRegExp.length) {\n          operator = LineFilterOp.regex;\n        } else if (patternInclude.length) {\n          operator = PatternFilterOp.match;\n        } else if (patternExclude.length) {\n          operator = PatternFilterOp.negativeMatch;\n        } else {\n          console.warn('unknown line filter', {\n            query: query.substring(matcher.from, matcher.to),\n          });\n\n          continue;\n        }\n\n        if (!(operator === PatternFilterOp.match || operator === PatternFilterOp.negativeMatch)) {\n          parseNonPatternFilters(lineFilterValue, quoteString, lineFilters, index, operator);\n        } else {\n          parsePatternFilters(lineFilterValue, patternFilters, operator);\n        }\n      }\n    }\n  }\n}\n\nfunction getNumericFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Lte).length) {\n    return FilterOperator.lte;\n  } else if (getAllPositionsInNodeByType(matcher, Lss).length) {\n    return FilterOperator.lt;\n  } else if (getAllPositionsInNodeByType(matcher, Gte).length) {\n    return FilterOperator.gte;\n  } else if (getAllPositionsInNodeByType(matcher, Gtr).length) {\n    return FilterOperator.gt;\n  }\n\n  console.warn('unknown numeric operator');\n\n  return undefined;\n}\n\nfunction getStringFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Eq).length) {\n    return FilterOperator.Equal; // =\n  } else if (getAllPositionsInNodeByType(matcher, Neq).length) {\n    return FilterOperator.NotEqual; // !=\n  } else if (getAllPositionsInNodeByType(matcher, Re).length) {\n    return FilterOperator.RegexEqual; // =~\n  } else if (getAllPositionsInNodeByType(matcher, Nre).length) {\n    return FilterOperator.RegexNotEqual; // !~\n  }\n\n  return undefined;\n}\n\nfunction parseFields(query: string, fields: FieldFilter[], context: PluginExtensionPanelContext, lokiQuery: LokiQuery) {\n  const dataFrame = context.data?.series.find((frame) => frame.refId === lokiQuery.refId);\n  // We do not currently support \"or\" in Grafana Logs Drilldown, so grab the left hand side LabelFilter leaf nodes as this will be the first filter expression in a given pipeline stage\n  const allFields = getNodesFromQuery(query, [LabelFilter]);\n  for (const matcher of allFields) {\n    const position = NodePosition.fromNode(matcher);\n    const expression = position.getExpression(query);\n    const isParentNode = matcher.getChild(LabelFilter);\n\n    // If the Label filter contains other Label Filter nodes, we want to skip this node so we only add the leaf LabelFilter nodes\n    if (isParentNode) {\n      continue;\n    }\n\n    // Skip error expression, it will get added automatically when Grafana Logs Drilldown adds a parser\n    if (expression.substring(0, 9) === `__error__`) {\n      continue;\n    }\n\n    // @todo we need to use detected_fields API to get the \"right\" parser for a specific field\n    // Currently we just check to see if there is a parser before the current node, this means that queries that are placing metadata filters after the parser will query the metadata field as a parsed field, which will lead to degraded performance\n    const logFmtParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Logfmt]);\n    const jsonParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Json]);\n\n    // field filter key\n    const fieldNameNode = getAllPositionsInNodeByType(matcher, Identifier);\n    const fieldName = fieldNameNode[0]?.getExpression(query);\n\n    // field filter value\n    const fieldStringValue = getAllPositionsInNodeByType(matcher, String);\n    const fieldNumberValue = getAllPositionsInNodeByType(matcher, Number);\n    const fieldBytesValue = getAllPositionsInNodeByType(matcher, Bytes);\n    const fieldDurationValue = getAllPositionsInNodeByType(matcher, Duration);\n\n    let fieldValue: string, operator: FilterOpType | undefined;\n    if (fieldStringValue.length) {\n      operator = getStringFieldOperator(matcher);\n      // Strip out quotes\n      fieldValue = query.substring(fieldStringValue[0].from + 1, fieldStringValue[0].to - 1);\n    } else if (fieldNumberValue.length) {\n      fieldValue = fieldNumberValue[0].getExpression(query);\n      operator = getNumericFieldOperator(matcher);\n    } else if (fieldDurationValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldDurationValue[0].getExpression(query);\n    } else if (fieldBytesValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldBytesValue[0].getExpression(query);\n    } else {\n      continue;\n    }\n\n    // Label type\n    let labelType: LabelType | undefined;\n    if (dataFrame) {\n      // @todo if the field label is not in the first line, we'll always add this filter as a field filter\n      // Also negative filters that exclude all values of a field will always fail to get a label type for that exclusion filter?\n      labelType = getLabelTypeFromFrame(fieldName, dataFrame) ?? undefined;\n    }\n\n    if (operator) {\n      let parser: ParserType | undefined;\n      if (logFmtParser.length && jsonParser.length) {\n        parser = 'mixed';\n      } else if (logFmtParser.length) {\n        parser = 'logfmt';\n      } else if (jsonParser.length) {\n        parser = 'json';\n      } else {\n        // If there is no parser in the query, the field would have to be metadata or an invalid query?\n        labelType = LabelType.StructuredMetadata;\n      }\n\n      fields.push({\n        key: fieldName,\n        operator: operator,\n        parser,\n        type: labelType ?? LabelType.Parsed,\n        value: fieldValue,\n      });\n    }\n  }\n}\n\nexport function getMatcherFromQuery(\n  query: string,\n  context: PluginExtensionPanelContext,\n  lokiQuery: LokiQuery\n): {\n  fields?: FieldFilter[];\n  labelFilters: IndexedLabelFilter[];\n  lineFilters?: LineFilterType[];\n  patternFilters?: PatternFilterType[];\n} {\n  const filter: IndexedLabelFilter[] = [];\n  const lineFilters: LineFilterType[] = [];\n  const patternFilters: PatternFilterType[] = [];\n  const fields: FieldFilter[] = [];\n  const selector = getNodesFromQuery(query, [Selector]);\n\n  if (selector.length === 0) {\n    return { labelFilters: filter };\n  }\n\n  // Get the stream selector portion of the query\n  const selectorQuery = getAllPositionsInNodeByType(selector[0], Selector)[0].getExpression(query);\n\n  parseLabelFilters(selectorQuery, filter);\n  parseLineFilters(query, lineFilters, patternFilters);\n  parseFields(query, fields, context, lokiQuery);\n\n  return { fields, labelFilters: filter, lineFilters, patternFilters };\n}\n\nexport function isQueryWithNode(query: string, nodeType: number): boolean {\n  let isQueryWithNode = false;\n  const tree = parser.parse(query);\n  tree.iterate({\n    enter: ({ type }): false | void => {\n      if (type.id === nodeType) {\n        isQueryWithNode = true;\n        return false;\n      }\n    },\n  });\n  return isQueryWithNode;\n}\n\n/**\n * Parses the query and looks for error nodes. If there is at least one, it returns true.\n * Grafana variables are considered errors, so if you need to validate a query\n * with variables you should interpolate it first.\n */\nexport const ErrorId = 0;\nexport function isValidQuery(query: string): boolean {\n  return isQueryWithNode(query, ErrorId) === false;\n}\n\nfunction getStringsFromLineFilter(filter: SyntaxNode): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  let node: SyntaxNode | null = filter;\n  do {\n    const string = node.getChild(String);\n    if (string && !node.getChild(FilterOp)) {\n      nodes.push(string);\n    }\n    node = node.getChild(OrFilter);\n  } while (node != null);\n\n  return nodes;\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { DataFrame, DataSourceJsonData, ScopedVars, TimeRange } from '@grafana/data';\nimport { DataSourceWithBackend } from '@grafana/runtime';\nimport { DataSourceRef } from '@grafana/schema';\n\nimport { LabelType } from './fieldsTypes';\n\nexport enum LokiQueryDirection {\n  Backward = 'backward',\n  Forward = 'forward',\n  Scan = 'scan',\n}\n\nexport type LokiQuery = {\n  datasource?: DataSourceRef;\n  direction?: LokiQueryDirection;\n  editorMode?: string;\n  expr: string;\n  legendFormat?: string;\n  maxLines?: number;\n  queryType?: LokiQueryType;\n  refId: string;\n  splitDuration?: string;\n  step?: string;\n  supportingQueryType?: string;\n};\n\nexport type LokiQueryType = 'instant' | 'range' | 'stream' | string;\n\nexport type LokiDatasource = DataSourceWithBackend<LokiQuery, DataSourceJsonData> & {\n  maxLines?: number;\n} & {\n  getTimeRangeParams: (timeRange: TimeRange) => { end: number; start: number };\n  // @todo delete after min supported grafana is upgraded to >=11.6\n  interpolateString?: (string: string, scopedVars?: ScopedVars) => string;\n};\n\nexport function getLabelTypeFromFrame(labelKey: string, frame: DataFrame, index = 0): null | LabelType {\n  const typeField = frame.fields.find((field) => field.name === 'labelTypes')?.values[index];\n  if (!typeField) {\n    return null;\n  }\n  switch (typeField[labelKey]) {\n    case 'I':\n      return LabelType.Indexed;\n    case 'S':\n      return LabelType.StructuredMetadata;\n    case 'P':\n      return LabelType.Parsed;\n    default:\n      return null;\n  }\n}\n", "import { LogsSortOrder, RawTimeRange } from '@grafana/data';\n\nimport { SelectedTableRow } from '../Components/Table/LogLineCellComponent';\nimport { LabelFilterOp, NumericFilterOp } from './filterTypes';\nimport { LogsVisualizationType } from './store';\nimport { FieldValue, ParserType } from './variables';\n\nconst isObj = (o: unknown): o is object => typeof o === 'object' && o !== null;\n\nexport function hasProp<K extends PropertyKey>(data: object, prop: K): data is Record<K, unknown> {\n  return prop in data;\n}\n\nconst isString = (s: unknown) => (typeof s === 'string' && s) || '';\n\nexport const isRecord = (obj: unknown): obj is Record<string, unknown> => typeof obj === 'object';\n\nexport function unknownToStrings(a: unknown): string[] {\n  let strings: string[] = [];\n  if (Array.isArray(a)) {\n    for (let i = 0; i < a.length; i++) {\n      strings.push(isString(a[i]));\n    }\n  }\n  return strings;\n}\n\nexport function narrowSelectedTableRow(o: unknown): SelectedTableRow | false {\n  const narrowed = isObj(o) && hasProp(o, 'row') && hasProp(o, 'id') && o;\n\n  if (narrowed) {\n    const row = typeof narrowed.row === 'number' && narrowed.row;\n    const id = typeof narrowed.id === 'string' && narrowed.id;\n    if (id !== false && row !== false) {\n      return { id, row };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowLogsVisualizationType(o: unknown): LogsVisualizationType | false {\n  return typeof o === 'string' && (o === 'logs' || o === 'table') && o;\n}\nexport function narrowLogsSortOrder(o: unknown): LogsSortOrder | false {\n  if (typeof o === 'string' && o === LogsSortOrder.Ascending.toString()) {\n    return LogsSortOrder.Ascending;\n  }\n\n  if (typeof o === 'string' && o === LogsSortOrder.Descending.toString()) {\n    return LogsSortOrder.Descending;\n  }\n\n  return false;\n}\n\nexport function narrowFieldValue(o: unknown): FieldValue | false {\n  const narrowed = isObj(o) && hasProp(o, 'value') && hasProp(o, 'parser') && o;\n\n  if (narrowed) {\n    const parser: ParserType | false =\n      typeof narrowed.parser === 'string' &&\n      (narrowed.parser === 'logfmt' ||\n        narrowed.parser === 'json' ||\n        narrowed.parser === 'mixed' ||\n        narrowed.parser === 'structuredMetadata') &&\n      narrowed.parser;\n    const value = typeof narrowed.value === 'string' && narrowed.value;\n\n    if (parser !== false && value !== false) {\n      return { parser, value };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowRecordStringNumber(o: unknown): Record<string, number> | false {\n  const narrowed = isObj(o) && isRecord(o) && o;\n\n  if (narrowed) {\n    const keys = Object.keys(narrowed);\n    const returnRecord: Record<string, number> = {};\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const value = narrowed[keys[i]];\n      if (typeof value === 'number') {\n        returnRecord[key] = value;\n      }\n    }\n\n    return returnRecord;\n  }\n\n  return false;\n}\n\nexport function narrowTimeRange(unknownRange: unknown): RawTimeRange | undefined {\n  const range = isObj(unknownRange) && hasProp(unknownRange, 'to') && hasProp(unknownRange, 'from') && unknownRange;\n  if (range) {\n    const to = isString(range.to);\n    const from = isString(range.from);\n    if (to && from) {\n      return { from, to };\n    }\n  }\n\n  return undefined;\n}\n\nexport function narrowErrorMessage(e: unknown): string | undefined {\n  const msg = isObj(e) && hasProp(e, 'error') && isString(e.error);\n  if (msg) {\n    return msg;\n  }\n  return undefined;\n}\n\nexport function narrowFilterOperator(op: string): LabelFilterOp | NumericFilterOp {\n  switch (op) {\n    case LabelFilterOp.Equal:\n    case LabelFilterOp.NotEqual:\n    case LabelFilterOp.RegexEqual:\n    case LabelFilterOp.RegexNotEqual:\n    case NumericFilterOp.gt:\n    case NumericFilterOp.gte:\n    case NumericFilterOp.lt:\n    case NumericFilterOp.lte:\n      return op;\n    default:\n      throw new NarrowingError('operator is invalid!');\n  }\n}\n\nexport class NarrowingError extends Error {}\n", "import { FilterOp, FilterOpType, NumericFilterOp } from './filterTypes';\nimport { numericOperatorArray } from './operators';\n\nexport const isOperatorInclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.Equal || op === FilterOp.RegexEqual;\n};\nexport const isOperatorExclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.NotEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorRegex = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.RegexEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorNumeric = (op: string | NumericFilterOp): boolean => {\n  return numericOperatorArray.includes(op);\n};\n", "import { FilterOp, FilterOpType } from './filterTypes';\nimport { logger } from './logger';\n\nexport function getOperatorDescription(op: FilterOpType): string {\n  if (op === FilterOp.NotEqual) {\n    return 'Not equal';\n  }\n  if (op === FilterOp.RegexNotEqual) {\n    return 'Does not match regex';\n  }\n  if (op === FilterOp.Equal) {\n    return 'Equals';\n  }\n  if (op === FilterOp.RegexEqual) {\n    return 'Matches regex';\n  }\n  if (op === FilterOp.lt) {\n    return 'Less than';\n  }\n  if (op === FilterOp.gt) {\n    return 'Greater than';\n  }\n  if (op === FilterOp.gte) {\n    return 'Greater than or equal to';\n  }\n  if (op === FilterOp.lte) {\n    return 'Less than or equal to';\n  }\n\n  const error = new Error('Invalid operator!');\n  logger.error(error, { msg: 'Invalid operator', operator: op });\n  throw error;\n}\n", "import { SelectableValue } from '@grafana/data';\n\nimport { FilterOp, LineFilterOp } from './filterTypes';\nimport { getOperatorDescription } from './getOperatorDescription';\n\nexport const operators = [FilterOp.Equal, FilterOp.NotEqual, FilterOp.RegexEqual, FilterOp.RegexNotEqual].map<\n  SelectableValue<string>\n>((value, index, array) => {\n  return {\n    description: getOperatorDescription(value),\n    label: value,\n    value,\n  };\n});\n\nexport const includeOperators = [FilterOp.Equal, FilterOp.RegexEqual].map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const numericOperatorArray = [FilterOp.gt, FilterOp.gte, FilterOp.lt, FilterOp.lte];\n\nexport const numericOperators = numericOperatorArray.map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const lineFilterOperators: SelectableValue[] = [\n  { label: 'match', value: LineFilterOp.match },\n  { label: 'negativeMatch', value: LineFilterOp.negativeMatch },\n  { label: 'regex', value: LineFilterOp.regex },\n  { label: 'negativeRegex', value: LineFilterOp.negativeRegex },\n];\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\n/**\n * Methods copied from scenes that we want in the module (to generate links which cannot be lazy loaded), without including all of scenes.\n * See https://github.com/grafana/scenes/issues/1046\n */\n// based on the openmetrics-documentation, the 3 symbols we have to handle are:\n// - \\n ... the newline character\n// - \\  ... the backslash character\n// - \"  ... the double-quote character\nexport function escapeLabelValueInExactSelector(labelValue: string): string {\n  return labelValue.replace(/\\\\/g, '\\\\\\\\').replace(/\\n/g, '\\\\n').replace(/\"/g, '\\\\\"');\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain many imports to keep that bundle size small. Don't add imports to this file!\nimport { escapeLabelValueInExactSelector } from './extensions/scenesMethods';\nimport { AppliedPattern } from './variables';\n\nexport function renderPatternFilters(patterns: AppliedPattern[]) {\n  const excludePatterns = patterns.filter((pattern) => pattern.type === 'exclude');\n  const excludePatternsLine = excludePatterns\n    .map((p) => `!> \"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n    .join(' ')\n    .trim();\n\n  const includePatterns = patterns.filter((pattern) => pattern.type === 'include');\n  let includePatternsLine = '';\n  if (includePatterns.length > 0) {\n    if (includePatterns.length === 1) {\n      includePatternsLine = `|> \"${escapeLabelValueInExactSelector(includePatterns[0].pattern)}\"`;\n    } else {\n      includePatternsLine = `|> ${includePatterns\n        .map((p) => `\"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n        .join(' or ')}`;\n    }\n  }\n  return `${excludePatternsLine} ${includePatternsLine}`.trim();\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nexport interface FieldValue {\n  parser: ParserType;\n  value: string;\n}\n\nexport interface AdHocFieldValue {\n  parser?: ParserType;\n  value?: string;\n}\nexport interface AppliedPattern {\n  pattern: string;\n  type: 'exclude' | 'include';\n}\n\nexport type ParserType = 'json' | 'logfmt' | 'mixed' | 'structuredMetadata';\nexport type DetectedFieldType = 'boolean' | 'bytes' | 'duration' | 'float' | 'int' | 'string';\nexport type AdHocFilterWithLabelsMeta = { parser?: ParserType; type?: DetectedFieldType };\nexport type AdHocFiltersWithLabelsAndMeta = AdHocFilterWithLabels<AdHocFilterWithLabelsMeta>;\n\nexport type LogsQueryOptions = {\n  fieldExpressionToAdd?: string;\n  fieldType?: DetectedFieldType;\n  jsonParserPropToAdd?: string;\n  labelExpressionToAdd?: string;\n  parser?: ParserType;\n  structuredMetadataToAdd?: string;\n};\n\nexport const VAR_LABELS = 'filters';\nexport const VAR_LABELS_EXPR = '${filters}';\nexport const VAR_LABELS_REPLICA = 'filters_replica';\nexport const VAR_LABELS_REPLICA_EXPR = '${filters_replica}';\nexport const VAR_FIELDS = 'fields';\nexport const VAR_FIELDS_EXPR = '${fields}';\nexport const PENDING_FIELDS_EXPR = '${pendingFields}';\nexport const PENDING_METADATA_EXPR = '${pendingMetadata}';\nexport const VAR_FIELDS_AND_METADATA = 'all-fields';\nexport const VAR_METADATA = 'metadata';\nexport const VAR_METADATA_EXPR = '${metadata}';\nexport const VAR_PATTERNS = 'patterns';\nexport const VAR_PATTERNS_EXPR = '${patterns}';\nexport const VAR_LEVELS = 'levels';\nexport const VAR_LEVELS_EXPR = '${levels}';\nexport const VAR_FIELD_GROUP_BY = 'fieldBy';\nexport const VAR_LABEL_GROUP_BY = 'labelBy';\nexport const VAR_LABEL_GROUP_BY_EXPR = '${labelBy}';\nexport const VAR_PRIMARY_LABEL_SEARCH = 'primary_label_search';\nexport const VAR_PRIMARY_LABEL_SEARCH_EXPR = '${primary_label_search}';\nexport const VAR_PRIMARY_LABEL = 'primary_label';\nexport const VAR_PRIMARY_LABEL_EXPR = '${primary_label}';\nexport const VAR_DATASOURCE = 'ds';\nexport const VAR_DATASOURCE_EXPR = '${ds}';\nexport const VAR_JSON_FIELDS = 'jsonFields';\nexport const VAR_JSON_FIELDS_EXPR = '${jsonFields}';\n\nexport const VAR_LINE_FORMAT = 'lineFormat';\nexport const VAR_LINE_FORMAT_EXPR = '${lineFormat}';\n\nexport const DETECTED_FIELDS_MIXED_FORMAT_EXPR_NO_JSON_FIELDS = `| json | logfmt | drop __error__, __error_details__`;\nexport const DETECTED_FIELDS_MIXED_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__`;\nexport const MIXED_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__`;\nexport const JSON_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | drop __error__, __error_details__`;\n// export const JSON_FORMAT_EXPR_NO_JSON_FIELDS = `| json | drop __error__, __error_details__`;\nexport const LOGS_FORMAT_EXPR = `| logfmt`;\n// This variable is hardcoded to the value of MIXED_FORMAT_EXPR. This is a hack to get logs context working, we don't want to use a variable for a value that doesn't change and cannot be updated by the user.\nexport const VAR_LOGS_FORMAT = 'logsFormat';\nexport const VAR_LOGS_FORMAT_EXPR = '${logsFormat}';\n// The deprecated line filter (custom variable)\nexport const VAR_LINE_FILTER_DEPRECATED = 'lineFilter';\n// The new single value line filter (ad-hoc variable), results are added to VAR_LINE_FILTER_AD_HOC when \"submitted\"\nexport const VAR_LINE_FILTER = 'lineFilterV2';\nexport const VAR_LINE_FILTER_EXPR = '${lineFilterV2}';\n// The new multi value line filter (ad-hoc variable)\nexport const VAR_LINE_FILTERS = 'lineFilters';\nexport const VAR_LINE_FILTERS_EXPR = '${lineFilters}';\nexport const LOG_STREAM_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR} ${VAR_LINE_FORMAT_EXPR}`;\n// Same as the LOG_STREAM_SELECTOR_EXPR, but without the fields as they will need to be built manually to exclude the current filter value\nexport const DETECTED_FIELD_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${MIXED_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_FIELD_AND_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${DETECTED_FIELDS_MIXED_FORMAT_EXPR} ${PENDING_FIELDS_EXPR}`;\nexport const DETECTED_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_FIELDS_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_LEVELS_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${PENDING_FIELDS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const PATTERNS_SAMPLE_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LOGS_FORMAT_EXPR}`;\nexport const PRETTY_LOG_STREAM_SELECTOR_EXPR = `${VAR_LABELS_EXPR} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const EXPLORATION_DS = { uid: VAR_DATASOURCE_EXPR };\nexport const ALL_VARIABLE_VALUE = '$__all';\nexport const LEVEL_VARIABLE_VALUE = 'detected_level';\nexport const SERVICE_NAME = 'service_name';\nexport const SERVICE_UI_LABEL = 'service';\nexport const VAR_AGGREGATED_METRICS = 'var_aggregated_metrics';\nexport const VAR_AGGREGATED_METRICS_EXPR = '${var_aggregated_metrics}';\nexport const EMPTY_VARIABLE_VALUE = '\"\"';\n\n// Delimiter used at the start of a label value to denote user input that should not be escaped\n// @todo we need ad-hoc-filter meta that is persisted in the URL so we can clean this up.\nexport const USER_INPUT_ADHOC_VALUE_PREFIX = '__CVΩ__';\nexport function stripAdHocFilterUserInputPrefix(value = '') {\n  if (value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX)) {\n    return value.substring(USER_INPUT_ADHOC_VALUE_PREFIX.length);\n  }\n  return value;\n}\nexport function isAdHocFilterValueUserInput(value = '') {\n  return value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX);\n}\nexport function addAdHocFilterUserInputPrefix(value = '') {\n  return USER_INPUT_ADHOC_VALUE_PREFIX + value;\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1308__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__200__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1159__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7694__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this.parent, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traversal. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (!depth)\n                    return;\n                if (this.nextSibling())\n                    break;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node.parent, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this._tree, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead, contextAtStart = contextHash;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type, contextAtStart);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end, contextAtStart);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type, contextHash) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead, contextHash);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead, contextHash) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to, contextHash));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead, contextHash, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to) {\n                    let last = overlay.ranges.length - 1;\n                    if (last >= 0 && overlay.ranges[last].to == range.from)\n                        overlay.ranges[last] = { from: overlay.ranges[last].from, to: range.to };\n                    else\n                        overlay.ranges.push(range);\n                }\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n", "import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n", "import { L<PERSON>arser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json$1 = 1,\n  Logfmt$1 = 2,\n  Unpack$1 = 3,\n  Pattern$1 = 4,\n  Regexp$1 = 5,\n  Unwrap$1 = 6,\n  LabelFormat$1 = 7,\n  LineFormat$1 = 8,\n  LabelReplace$1 = 9,\n  Vector$1 = 10,\n  Offset$1 = 11,\n  Bool$1 = 12,\n  On$1 = 13,\n  Ignoring$1 = 14,\n  GroupLeft$1 = 15,\n  GroupRight$1 = 16,\n  Decolorize$1 = 17,\n  Drop$1 = 18,\n  Keep$1 = 19,\n  By$1 = 20,\n  Without$1 = 21,\n  And$1 = 22,\n  Or$1 = 23,\n  Unless$1 = 24,\n  Sum$1 = 25,\n  Avg$1 = 26,\n  Count$1 = 27,\n  Max$1 = 28,\n  Min$1 = 29,\n  Stddev$1 = 30,\n  Stdvar$1 = 31,\n  Bottomk$1 = 32,\n  Topk$1 = 33,\n  Sort$1 = 34,\n  Sort_Desc$1 = 35;\n\nconst keywordTokens = {\n  json: Json$1,\n  logfmt: Logfmt$1,\n  unpack: Unpack$1,\n  pattern: Pattern$1,\n  regexp: Regexp$1,\n  label_format: LabelFormat$1,\n  line_format: LineFormat$1,\n  label_replace: LabelReplace$1,\n  vector: Vector$1,\n  offset: Offset$1,\n  bool: Bool$1,\n  on: On$1,\n  ignoring: Ignoring$1,\n  group_left: GroupLeft$1,\n  group_right: GroupRight$1,\n  unwrap: Unwrap$1,\n  decolorize: Decolorize$1,\n  drop: Drop$1,\n  keep: Keep$1,\n};\n\nconst specializeIdentifier = (value) => {\n  return keywordTokens[value.toLowerCase()] || -1;\n};\n\nconst contextualKeywordTokens = {\n  by: By$1,\n  without: Without$1,\n  and: And$1,\n  or: Or$1,\n  unless: Unless$1,\n  sum: Sum$1,\n  avg: Avg$1,\n  count: Count$1,\n  max: Max$1,\n  min: Min$1,\n  stddev: Stddev$1,\n  stdvar: Stdvar$1,\n  bottomk: Bottomk$1,\n  topk: Topk$1,\n  sort: Sort$1,\n  sort_desc: Sort_Desc$1,\n};\n\nconst extendIdentifier = (value) => {\n  return contextualKeywordTokens[value.toLowerCase()] || -1;\n};\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Identifier = {__proto__:null,ip:295, count_over_time:301, rate:303, rate_counter:305, bytes_over_time:307, bytes_rate:309, avg_over_time:311, sum_over_time:313, min_over_time:315, max_over_time:317, stddev_over_time:319, stdvar_over_time:321, quantile_over_time:323, first_over_time:325, last_over_time:327, absent_over_time:329, bytes:335, duration:337, duration_seconds:339};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EtOYQPOOO#cQPO'#DUOOQO'#ER'#ERO#hQPO'#ERO$}QPO'#DTOYQPO'#DTOOQO'#Ed'#EdO%[QPO'#EcOOQO'#FP'#FPO%aQPO'#FOQ%lQPOOO&mQPO'#F]O&rQPO'#F^OOQO'#Eb'#EbOOQO'#DS'#DSOOQO'#Ee'#EeOOQO'#Ef'#EfOOQO'#Eg'#EgOOQO'#Eh'#EhOOQO'#Ei'#EiOOQO'#Ej'#EjOOQO'#Ek'#EkOOQO'#El'#ElOOQO'#Em'#EmOOQO'#En'#EnOOQO'#Eo'#EoOOQO'#Ep'#EpOOQO'#Eq'#EqOOQO'#Er'#ErOOQO'#Es'#EsO&wQPO'#DWOOQO'#DV'#DVO'VQPO,59pOOQO,5:m,5:mOOQO'#Dc'#DcO'_QPO'#DbO'gQPO'#DaO)lQPO'#D`O*{QPO'#D`OOQO'#D_'#D_O+sQPO,59oO-}QPO,59oO.UQPO,5:|O.]QPO,5:}O.hQPO'#E|O0sQPO,5;jO0zQPO,5;jO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lOYQPO,5;wO3cQPO,5;xO3hQPO,59rO#cQPO,59qOOQO1G/[1G/[OOQO'#Dh'#DhO3mQPO,59|O5^QPO,59|OOQO'#Di'#DiO5cQPO,59{OOQO,59{,59{O5kQPO'#DWO6YQPO'#DlO8PQPO'#DoO9sQPO'#DoOOQO'#Do'#DoOOQO'#Dv'#DvOOQO'#Dt'#DtO+kQPO'#DtO9xQPO,59zO;iQPO'#EVO;nQPO'#EWOOQO'#EZ'#EZO;sQPO'#E[O;xQPO'#E_OOQO,59z,59zOOQO,59y,59yOOQO1G/Z1G/ZOOQO1G0h1G0hO;}QPO'#EtO.`QPO'#EtO<XQPO1G0iO<^QPO1G0iO<cQPO,5;hO=oQPO1G1UO=vQPO1G1UO=}QPO1G1UO>UQPO'#FSO@dQPO'#FRO@nQPO'#FROYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO@xQPO1G1cOAPQPO1G1dOOQO1G/^1G/^OOQO1G/]1G/]O5cQPO1G/hOAUQPO1G/hOAZQPO'#DjOBzQPO'#DjOOQO1G/g1G/gOCbQPO,59rOCPQPO,5:cOOQO'#Dm'#DmOClQPO,5:WOEcQPO'#DrOOQO'#Dq'#DqOGVQPO,5:_OHvQPO,5:[OOQO,5:Z,5:ZOJgQPO,5:`O+kQPO,5:`O+kQPO,5:`OOQO,5:q,5:qOJuQPO'#EYOOQO'#EX'#EXOJzQPO,5:rOLkQPO'#E^OOQO'#E^'#E^OOQO'#E]'#E]ONbQPO,5:vO!!RQPO'#EaOOQO'#Ea'#EaOOQO'#E`'#E`O!#xQPO,5:yO!%iQPO'#D`O;}QPO,5;`O!%pQPO'#EuO!%uQPO,5;`O!%}QPO,5;`O!&[QPO,5;`O!&iQPO,5;`O!&nQPO7+&TO.`QPO7+&TOOQO'#E}'#E}O!(OQPO1G1SOOQO1G1S1G1SOYQPO7+&pO!(WQPO7+&pO!)hQPO7+&pO!)oQPO7+&pO!)vQQO'#FTOOQO,5;n,5;nO!,UQPO,5;mO!,]QPO,5;mO!-nQPO7+&rO!-uQPO7+&rOOQO7+&r7+&rO!.SQPO7+&rO!.ZQPO7+&rO!/`QPO7+&rO!/pQPO7+&}OOQO7+'O7+'OOOQO7+%S7+%SO!/uQPO7+%SO5cQPO,5:UO!/zQPO,5:UO!0PQPO1G/{OOQO1G/}1G/}OOQO1G0U1G0UOOQO1G0W1G0WOOQO,5:X,5:XO!0UQPO1G/yO!1uQPO,5:^O!1zQPO,5:]OOQO1G/z1G/zO!2PQPO1G/zO!3pQPO,5:tO;nQPO,5:sO;sQPO,5:wO;xQPO,5:zO!3xQPO,5;cO!%uQPO1G0zO!4WQPO1G0zO!4`QPO,5;aO+kQPO,5;cO!4eQPO1G0zO!4oQPO'#EvO!4tQPO1G0zO!4eQPO1G0zO!4|QPO1G0zO!5ZQPO1G0zO!%xQPO1G0zOOQO1G0z1G0zOOQO<<Io<<IoO!5fQPO<<IoO!5kQPO,5;iOOQO7+&n7+&nO!5pQPO<<J[OOQO<<J[<<J[OYQPO<<J[OOQO'#FV'#FVO!5wQPO,5;oOOQO'#FU'#FUOOQO,5;o,5;oOOQO1G1X1G1XO!6PQPO1G1XO!8YQPO<<JiOOQO<<Hn<<HnOOQO1G/p1G/pO!8_QPO1G/pO!8dQPO7+%gOOQO1G/x1G/xOOQO1G/w1G/wOOQO1G0`1G0`OOQO1G0_1G0_OOQO1G0c1G0cOOQO1G0f1G0fOOQO'#Ex'#ExOOQO1G0}1G0}O!8iQPO1G0}OOQO'#Ey'#EyOOQO'#Ez'#EzOOQO'#E{'#E{OOQO7+&f7+&fOOQO1G0{1G0{O!8nQPO1G0}O!9SQPO7+&fOOQO,5;b,5;bO!9[QPO7+&fO!%xQPO7+&fO!9fQPO7+&fO!9qQPOAN?ZOOQO1G1T1G1TO!;RQPOAN?vO!<cQPOAN?vO!<jQQO1G1ZOOQO1G1Z1G1ZOOQO7+&s7+&sO!<rQPOAN@TOOQO7+%[7+%[O!<wQPO<<IRO!<|QPO7+&iO!=RQPO<<JQO!=ZQPO<<JQO!=cQPO'#EwO!=hQPO<<JQOOQOG24uG24uOOQOG25bG25bOOQO1G1[1G1[OOQO7+&u7+&uO!=pQPOG25oOOQOAN>mAN>mO!=uQPO<<JTOOQOAN?lAN?lO!=zQPOAN?lO!>SQPOLD+ZOOQOAN?oAN?oOOQO,5:r,5:rO!>XQPO!$'NuO!>^QPO!)9DaO!>cQPO!.K9{OOQO!4//g!4//gO;nQPO'#EWO!>hQPO'#D`O!?`QPO,59oO!@fQPO'#DTOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO!AqQPO7+&rO!AxQPO7+&rO!BVQPO7+&rO!C_QPO7+&rO!CfQPO7+&rO!B^QPO'#FQ\",\n  stateData: \"!Cs~O$TOStOS~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!vQO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O{nO~O!vqO~O!OrO!QrO!WrO!XrO!YrO!ZrOfwXgwXhwX!lwX!nwX!owX!pwX!qwX!wwX!xwX#{wX#|wX#}wX$OwX~O!_vO$RwX$ZwX~P#mO$Y{O~Od|Oe|O$Y}O~Of!QOg!POh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{!SO#|!SO#}!SO$O!TO~O$Y!VO~O$Y!WO~O|!XO!O!XO!P!XO!Q!XO~O$V!YO$W!ZO~O}!]O$X!_O~Og!`Of!TXh!TX!O!TX!Q!TX!W!TX!X!TX!Y!TX!Z!TX!_!TX!l!TX!n!TX!o!TX!p!TX!q!TX!w!TX!x!TX#{!TX#|!TX#}!TX$O!TX$R!TX$Z!TX$k!TX$V!TX~O!OrO!QrO!WrO!XrO!YrO!ZrO~Of!SXg!SXh!SX!_!SX!l!SX!n!SX!o!SX!p!SX!q!SX!w!SX!x!SX#{!SX#|!SX#}!SX$O!SX$R!SX$Z!SX$k!SX$V!SX~P)WOP!dOQ!cOR!fOS!eOT!eOV!lOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_vOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Rwa$Zwa~P)WOfvXgvXhvX!OvX!lvX!nvX!ovX!pvX!qvX!wvX!xvX#{vX#|vX#}vX$OvX~O$Z!rO~P,|O$Z!sO~P,|O!v!wO$UPO$Y!uO~O$Y!xO~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O!v!yO~P.mO$Y!{O~O[#OO]!|O^!|OX#uPY#uPi#uPj#uPk#uPl#uPm#uPn#uPo#uPp#uPq#uPr#uPs#uP!v#uP!w#uP!x#uP$U#uP$Y#uP$[#uP$]#uP$^#uP$_#uP$`#uP$a#uP$b#uP$c#uP$d#uP$e#uP$f#uP$g#uP$h#uP$i#uP$j#uP~O!v#WO~O}#XO~Og#ZOf!Uah!Ua!O!Ua!Q!Ua!W!Ua!X!Ua!Y!Ua!Z!Ua!_!Ua!l!Ua!n!Ua!o!Ua!p!Ua!q!Ua!w!Ua!x!Ua#{!Ua#|!Ua#}!Ua$O!Ua$R!Ua$Z!Ua$k!Ua$V!Ua~O$Y#[O~O}#]O$X!_O~O|#`O!O#`O!P!XO!Q!XO!l#aO!n#aO!o#aO!p#aO!q#aO~O{#dO!b#bOf!`Xg!`Xh!`X!O!`X!Q!`X!W!`X!X!`X!Y!`X!Z!`X!_!`X!l!`X!n!`X!o!`X!p!`X!q!`X!w!`X!x!`X#{!`X#|!`X#}!`X$O!`X$R!`X$Z!`X$k!`X$V!`X~O{#dOf!cXg!cXh!cX!O!cX!Q!cX!W!cX!X!cX!Y!cX!Z!cX!_!cX!l!cX!n!cX!o!cX!p!cX!q!cX!w!cX!x!cX#{!cX#|!cX#}!cX$O!cX$R!cX$Z!cX$k!cX$V!cX~O}#hO~Of#jOg#kO$V#jOh!Sa!O!Sa!Q!Sa!W!Sa!X!Sa!Y!Sa!Z!Sa!_!Sa!l!Sa!n!Sa!o!Sa!p!Sa!q!Sa!w!Sa!x!Sa#{!Sa#|!Sa#}!Sa$O!Sa$R!Sa$Z!Sa$k!Sa~O}#lO~O{#mO~O{#pO~O{#tO~O!_#xO$k#zO~P)WO$Z$PO~O$V$QO~O{$RO$Z$TO~Of!uXg!uXh!uX!O!uX!l!uX!n!uX!o!uX!p!uX!q!uX!w!uX!x!uX#{!uX#|!uX#}!uX$O!uX$Z!uX~O$V$UO~P<kO$Z$VO~P,|O!v$WO~P.mO$Y$YO~OX#uXY#uXi#uXj#uXk#uXl#uXm#uXn#uXo#uXp#uXq#uXr#uXs#uX!v#uX!w#uX!x#uX$U#uX$Y#uX$[#uX$]#uX$^#uX$_#uX$`#uX$a#uX$b#uX$c#uX$d#uX$e#uX$f#uX$g#uX$h#uX$i#uX$j#uX~O_$[O`$[O~P>ZO]!|O^!|O~P>ZO$V$dO~P,|O$Z$eO~O}$gO~Og$hOf!^Xh!^X!O!^X!Q!^X!W!^X!X!^X!Y!^X!Z!^X!_!^X!l!^X!n!^X!o!^X!p!^X!q!^X!w!^X!x!^X#{!^X#|!^X#}!^X$O!^X$R!^X$Z!^X$k!^X$V!^X~O$Y$iO~O!m$kO!s$lO!vQO!wRO!xRO~O}#XO$X!_O~PCPO{#dO!b$nOf!`ag!`ah!`a!O!`a!Q!`a!W!`a!X!`a!Y!`a!Z!`a!_!`a!l!`a!n!`a!o!`a!p!`a!q!`a!w!`a!x!`a#{!`a#|!`a#}!`a$O!`a$R!`a$Z!`a$k!`a$V!`a~O|$pOf!fXg!fXh!fX!O!fX!Q!fX!W!fX!X!fX!Y!fX!Z!fX!_!fX!l!fX!n!fX!o!fX!p!fX!q!fX!w!fX!x!fX#{!fX#|!fX#}!fX$O!fX$R!fX$V!fX$Z!fX$k!fX~O$V$qOf!gag!gah!ga!O!ga!Q!ga!W!ga!X!ga!Y!ga!Z!ga!_!ga!l!ga!n!ga!o!ga!p!ga!q!ga!w!ga!x!ga#{!ga#|!ga#}!ga$O!ga$R!ga$Z!ga$k!ga~O$V$qOf!dag!dah!da!O!da!Q!da!W!da!X!da!Y!da!Z!da!_!da!l!da!n!da!o!da!p!da!q!da!w!da!x!da#{!da#|!da#}!da$O!da$R!da$Z!da$k!da~Of#jOg#kO$V#jO$Z$rO~O|$tO~O$V$uOf!zag!zah!za!O!za!Q!za!W!za!X!za!Y!za!Z!za!_!za!l!za!n!za!o!za!p!za!q!za!w!za!x!za#{!za#|!za#}!za$O!za$R!za$Z!za$k!za~O|!XO!O!XO!P!XO!Q!XOf#QXg#QXh#QX!W#QX!X#QX!Y#QX!Z#QX!_#QX!l#QX!n#QX!o#QX!p#QX!q#QX!w#QX!x#QX#{#QX#|#QX#}#QX$O#QX$R#QX$V#QX$Z#QX$k#QX~O$V$vOf#Oag#Oah#Oa!O#Oa!Q#Oa!W#Oa!X#Oa!Y#Oa!Z#Oa!_#Oa!l#Oa!n#Oa!o#Oa!p#Oa!q#Oa!w#Oa!x#Oa#{#Oa#|#Oa#}#Oa$O#Oa$R#Oa$Z#Oa$k#Oa~O|!XO!O!XO!P!XO!Q!XOf#TXg#TXh#TX!W#TX!X#TX!Y#TX!Z#TX!_#TX!l#TX!n#TX!o#TX!p#TX!q#TX!w#TX!x#TX#{#TX#|#TX#}#TX$O#TX$R#TX$V#TX$Z#TX$k#TX~O$V$wOf#Rag#Rah#Ra!O#Ra!Q#Ra!W#Ra!X#Ra!Y#Ra!Z#Ra!_#Ra!l#Ra!n#Ra!o#Ra!p#Ra!q#Ra!w#Ra!x#Ra#{#Ra#|#Ra#}#Ra$O#Ra$R#Ra$Z#Ra$k#Ra~OU$xO~P*{O!m${O~O!_$|O$k#zO~OZ%OO!_#xO$Z#ha~P)WO!_#xO$Z%TO$k#zO~P)WO$Z%UO~Od|Oe|Of#Vqg#Vqh#Vq!O#Vq!l#Vq!n#Vq!o#Vq!p#Vq!q#Vq!w#Vq!x#Vq#{#Vq#|#Vq#}#Vq$O#Vq$R#Vq$Z#Vq$V#Vq~O$V%XO$Z%YO~Od|Oe|Of#rqg#rqh#rq!O#rq!l#rq!n#rq!o#rq!p#rq!q#rq!w#rq!x#rq#{#rq#|#rq#}#rq$O#rq$R#rq$Z#rq$V#rq~O$V%]O~P<kO$Z%[O~P,|O#z%^O$Z%aO~OX#uaY#uai#uaj#uak#ual#uam#uan#uao#uap#uaq#uar#uas#ua!v#ua!w#ua!x#ua$U#ua$[#ua$]#ua$^#ua$_#ua$`#ua$a#ua$b#ua$c#ua$d#ua$e#ua$f#ua$g#ua$h#ua$i#ua$j#ua~O$Y$YO~P!*OO_%cO`%cO$Y#ua~P!*OOf!QOh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{#tq#|#tq#}#tq$O#tq$R#tq$Z#tq~Og#tq~P!,jOf#tqg#tqh#tq~P!,pOg!PO~P!,jO$R#tq$Z#tq~P%lOf#tqg#tqh#tq!O#tq!l#tq!n#tq!o#tq!p#tq!q#tq#{#tq#|#tq#}#tq$O#tq~O!w!RO!x!RO$R#tq$Z#tq~P!.eO}%dO~O$Z%eO~O}%gO~O$Y%hO~O$V$qOf!gig!gih!gi!O!gi!Q!gi!W!gi!X!gi!Y!gi!Z!gi!_!gi!l!gi!n!gi!o!gi!p!gi!q!gi!w!gi!x!gi#{!gi#|!gi#}!gi$O!gi$R!gi$Z!gi$k!gi~O}%iO~O{#dO~Of#jO$V#jOg!hih!hi!O!hi!Q!hi!W!hi!X!hi!Y!hi!Z!hi!_!hi!l!hi!n!hi!o!hi!p!hi!q!hi!w!hi!x!hi#{!hi#|!hi#}!hi$O!hi$R!hi$Z!hi$k!hi~O{%kO}%kO~O{%pO$m%rO$n%sO$o%tO~OZ%OO$Z#hi~O$l%vO~O!_#xO$Z#hi~P)WO!m%yO~O!_$|O$Z#hi~O!_#xO$Z%{O$k#zO~P)WO!_$|O$Z%{O$k#zO~O$Z%}O~O{&OO~O$Z&PO~P,|O$V&RO$Z&SO~O$Y$YOX#uiY#uii#uij#uik#uil#uim#uin#uio#uip#uiq#uir#uis#ui!v#ui!w#ui!x#ui$U#ui$[#ui$]#ui$^#ui$_#ui$`#ui$a#ui$b#ui$c#ui$d#ui$e#ui$f#ui$g#ui$h#ui$i#ui$j#ui~O$V&UO~O$Z&VO~O}&WO~O$Y&XO~Of#jOg#kO$V#jO!_#ki$k#ki$Z#ki~O!_$|O$Z#hq~O!_#xO$Z#hq~P)WOZ%OO!_&[O$Z#hq~Od|Oe|Of#V!Rg#V!Rh#V!R!O#V!R!l#V!R!n#V!R!o#V!R!p#V!R!q#V!R!w#V!R!x#V!R#{#V!R#|#V!R#}#V!R$O#V!R$R#V!R$Z#V!R$V#V!R~Od|Oe|Of#r!Rg#r!Rh#r!R!O#r!R!l#r!R!n#r!R!o#r!R!p#r!R!q#r!R!w#r!R!x#r!R#{#r!R#|#r!R#}#r!R$O#r!R$R#r!R$Z#r!R$V#r!R~O$Z&_O~P,|O#z%^O$Z&aO~O}&bO~O$Z&cO~O{&dO~O!_$|O$Z#hy~OZ%OO$Z#hy~OU$xO~O!_&[O$Z#hy~O$V&gO~O$Z&hO~O!_$|O$Z#h!R~O}&jO~O$V&kO~O}&lO~O$Z&mO~OP!dOQ!cOR!fOS!eOT!eOV&nOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_&oOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Vwa~P)WO!_&oO$VwX~P#mOf&yOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{#tq#|#tq#}#tq$O#tq$V#tq~Og#tq~P!@pOf#tqg#tqh#tq~P!@vOg&xO~P!@pOf&yOg&xOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{&{O#|&{O#}&{O$O&|O~O$V#tq~P!B^O!w&zO!x&zO$V#tq~P!.eO\",\n  goto: \"1l$RPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP$S%R%j&Y&]PPPPPP&t'W'h'v(XPPPP(h(p(yP)S)XP)S)S)[)e)S)m*O*O*XPPPPPP*XP*O*bPPP)S)S*{+R)S)S+Y+])S+c+f+l,_,t-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-p-y.^.j/S/V/V/V/Y/i,_/l,_0R0w1Y1c1fPPPPP,_,_[YOT}!{$U%]Q$^#PQ$_#QS$`#R&tQ$a#SQ$b#TQ$c#UQ'O&rQ'P&sQ'Q&uQ'R&vQ'S&wR'T!Vt^O}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wRyTjSOT}!V!{#P#Q#R#S#T#U$U%]S!t{$QQ#}!u]&q&r&s&t&u&v&wRpPQoP^!hv!i#j#k#x$|&oQ#Y!YS#q!n$vT#u!o$wQxSQ#y!tQ$}#|Q%R#}Q%z%QR&p&q[wS!t#|#}%Q&q]!qx#y$}%R%z&piuSx!t#y#|#}$}%Q%R%z&p&qhtSx!t#y#|#}$}%Q%R%z&p&qR!auksSux!t#y#|#}$}%Q%R%z&p&qQ!^sV#^!`#Z$hW![s!`#Z$hR$j#`Q#_!`Q$f#ZR%f$hV!pv#x&oR#c!cQ#f!cQ#g!dR$o#cU#e!c!d#cR%j$qU!jv#x&oQ#i!iQ$r#jQ$s#kR%w$|_!hv!i#j#k#x$|&o_!gv!i#j#k#x$|&ov]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wT$m#`#aQ#o!lR&i&nS#n!l&nR%l$uR#s!nQ#r!nR%m$vR#w!oQ#v!oR%n$wj^O#P#Q#R#S#T#U&r&s&t&u&v&wQzTQ!z}Q#V!VQ$X!{Q%Z$UR&Q%]w]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwVOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwUOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ!v{Q$O!uR%W$QS#|!t#}W$z#y#{%R%SQ%u$yQ%|%TR&Z%{Q%Q#|Q%u$zQ&]%|R&e&ZQ#{!tS$y#y%RQ%P#|Q%S#}S%x$}%QS&Y%z%|R&f&]R%q$xR%o$xQ!OXQ%V$PQ%[$VQ&^%}R&_&PR$S!xwXOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ#P!PQ#Q!QQ#R!RQ#S!SQ#T!TQ#U!UQ&r&xQ&s&yQ&t&zQ&u&{Q&v&|R&w&}h!}!P!Q!R!S!T!U&x&y&z&{&|&}R$]#OQ$Z!|Q%b$[R&T%cR%_$YQ%`$YR&`&R\",\n  nodeNames: \"⚠ Json Logfmt Unpack Pattern Regexp Unwrap LabelFormat LineFormat LabelReplace Vector Offset Bool On Ignoring GroupLeft GroupRight Decolorize Drop Keep By Without And Or Unless Sum Avg Count Max Min Stddev Stdvar Bottomk Topk Sort Sort_Desc LineComment LogQL Expr LogExpr Selector Matchers Matcher Identifier Eq String Neq Re Nre PipelineExpr PipelineStage LineFilters LineFilter Filter PipeExact PipeMatch PipePattern Npa FilterOp Ip OrFilter Pipe LogfmtParser LogfmtParserFlags ParserFlag LabelParser JsonExpressionParser LabelExtractionExpressionList LabelExtractionExpression LogfmtExpressionParser LabelFilter IpLabelFilter UnitFilter DurationFilter Gtr Duration Gte Lss Lte Eql BytesFilter Bytes NumberFilter LiteralExpr Number Add Sub LineFormatExpr LabelFormatExpr LabelsFormat LabelFormatMatcher DecolorizeExpr DropLabelsExpr DropLabels DropLabel KeepLabelsExpr KeepLabels KeepLabel MetricExpr RangeAggregationExpr RangeOp CountOverTime Rate RateCounter BytesOverTime BytesRate AvgOverTime SumOverTime MinOverTime MaxOverTime StddevOverTime StdvarOverTime QuantileOverTime FirstOverTime LastOverTime AbsentOverTime LogRangeExpr Range OffsetExpr UnwrapExpr ConvOp BytesConv DurationConv DurationSecondsConv Grouping Labels VectorAggregationExpr VectorOp BinOpExpr BinOpModifier OnOrIgnoringModifier GroupingLabels GroupingLabelList GroupingLabel LabelName Mul Div Mod Pow LabelReplaceExpr VectorExpr\",\n  maxTerm: 169,\n  skippedNodes: [0,36],\n  repeatNodeCount: 0,\n  tokenData: \"<n~RvX^#ipq#iqr$^rs$yst%kuv%vxy%{yz&Qz{&V{|&[|}&a}!O&f!O!P2v!P!Q3v!Q!R3{!R![7^![!]9]!^!_9q!_!`:O!`!a:e!c!}:r!}#O;Y#P#Q;_#Q#R;d#R#S:r#S#T;i#T#o:r#o#p;u#p#q;z#q#r<i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~#nY$T~X^#ipq#i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~$aR!_!`$j!`!a$o#r#s$t~$oO!O~~$tO!Z~~$yO!Q~~$|UOY$yZr$yrs%`s#O$y#O#P%e#P~$y~%eO}~~%hPO~$y~%pQt~OY%kZ~%k~%{O#}~~&QO$Y~~&VO$Z~~&[O#{~~&aO!w~~&fO$V~~&kQ!x~}!O&q!Q![(w~&tQ#_#`&z#g#h(X~&}P#X#Y'Q~'TP#X#Y'W~'ZP#d#e'^~'aP}!O'd~'gP#X#Y'j~'mP#a#b'p~'sP#d#e'v~'yP#h#i'|~(PP#m#n(S~(XO!b~~([P#h#i(_~(bP#f#g(e~(hP#]#^(k~(nP#V#W(q~(tP#h#i(S~(zZ!O!P)m!Q![(w#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~)pP!Q![)s~)vV!Q![)s#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~*bP!m~!Q![*e~*hV!O!P*}!Q![*e#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+QP!Q![+T~+WU!Q![+T#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+oQ!m~!Q![+u#g#h-Q~+xV!O!P,_!Q![+u#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,bP!Q![,e~,hU!Q![,e#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,}P#g#h-Q~-VP!m~!Q![-Y~-]T!O!P-l!Q![-Y#b#c.R#i#j.^${$|.^~-oP!Q![-r~-uS!Q![-r#b#c.R#i#j.^${$|.^~.UP#g#h.X~.^O!m~~.aP#g#h.d~.iP!m~!Q![.l~.oR!O!P.x!Q![.l#b#c.R~.{P!Q![/O~/RQ!Q![/O#b#c.R~/^P!m~!Q![/a~/dU!O!P/v!Q![/a#a#b,z#b#c.R#i#j.^${$|.^~/yP!Q![/|~0PT!Q![/|#a#b,z#b#c.R#i#j.^${$|.^~0eP!m~!Q![0h~0kW!O!P)m!Q![0h#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~1YP!m~!Q![1]~1`X!O!P)m!Q![1]#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~2QP!m~!Q![2T~2WY!O!P)m!Q![2T#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T${$|.^~2yP!Q![2|~3RR!v~!Q![2|!g!h3[#X#Y3[~3_R{|3h}!O3h!Q![3n~3kP!Q![3n~3sP!v~!Q![3n~3{O#|~~4Qe!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#l#m8q#m#n1{${$|.^~5hR!v~!Q![5q!g!h3[#X#Y3[~5v`!v~!Q![5q!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~6}O!s~~7QQ!d!e6x#]#^7W~7ZP!d!e6x~7cd!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~8tR!Q![8}!c!i8}#T#Z8}~9SR!v~!Q![8}!c!i8}#T#Z8}P9bT{P!Q![9]![!]9]!c!}9]#R#S9]#T#o9]~9vP!o~!_!`9y~:OO!p~~:TQ|~!_!`:Z#r#s:`~:`O!q~~:eO!P~~:jP!l~!_!`:m~:rO!n~R:yT{P#zQ!Q![:r![!]9]!c!}:r#R#S:r#T#o:r~;_O$k~~;dO$l~~;iO$O~~;lRO#S;i#S#T%`#T~;i~;zO$U~~<PR!_~!_!`<Y!`!a<_#r#s<d~<_O!W~~<dO!Y~~<iO!X~~<nO$W~\",\n  tokenizers: [0, 1],\n  topRules: {\"LogQL\":[0,37]},\n  specialized: [{term: 43, get: (value, stack) => (specializeIdentifier(value) << 1)},{term: 43, get: (value, stack) => (extendIdentifier(value) << 1) | 1},{term: 43, get: value => spec_Identifier[value] || -1}],\n  tokenPrec: 0\n});\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json = 1,\n  Logfmt = 2,\n  Unpack = 3,\n  Pattern = 4,\n  Regexp = 5,\n  Unwrap = 6,\n  LabelFormat = 7,\n  LineFormat = 8,\n  LabelReplace = 9,\n  Vector = 10,\n  Offset = 11,\n  Bool = 12,\n  On = 13,\n  Ignoring = 14,\n  GroupLeft = 15,\n  GroupRight = 16,\n  Decolorize = 17,\n  Drop = 18,\n  Keep = 19,\n  By = 20,\n  Without = 21,\n  And = 22,\n  Or = 23,\n  Unless = 24,\n  Sum = 25,\n  Avg = 26,\n  Count = 27,\n  Max = 28,\n  Min = 29,\n  Stddev = 30,\n  Stdvar = 31,\n  Bottomk = 32,\n  Topk = 33,\n  Sort = 34,\n  Sort_Desc = 35,\n  LineComment = 36,\n  LogQL = 37,\n  Expr = 38,\n  LogExpr = 39,\n  Selector = 40,\n  Matchers = 41,\n  Matcher = 42,\n  Identifier = 43,\n  Eq = 44,\n  String = 45,\n  Neq = 46,\n  Re = 47,\n  Nre = 48,\n  PipelineExpr = 49,\n  PipelineStage = 50,\n  LineFilters = 51,\n  LineFilter = 52,\n  Filter = 53,\n  PipeExact = 54,\n  PipeMatch = 55,\n  PipePattern = 56,\n  Npa = 57,\n  FilterOp = 58,\n  Ip = 59,\n  OrFilter = 60,\n  Pipe = 61,\n  LogfmtParser = 62,\n  LogfmtParserFlags = 63,\n  ParserFlag = 64,\n  LabelParser = 65,\n  JsonExpressionParser = 66,\n  LabelExtractionExpressionList = 67,\n  LabelExtractionExpression = 68,\n  LogfmtExpressionParser = 69,\n  LabelFilter = 70,\n  IpLabelFilter = 71,\n  UnitFilter = 72,\n  DurationFilter = 73,\n  Gtr = 74,\n  Duration = 75,\n  Gte = 76,\n  Lss = 77,\n  Lte = 78,\n  Eql = 79,\n  BytesFilter = 80,\n  Bytes = 81,\n  NumberFilter = 82,\n  LiteralExpr = 83,\n  Number = 84,\n  Add = 85,\n  Sub = 86,\n  LineFormatExpr = 87,\n  LabelFormatExpr = 88,\n  LabelsFormat = 89,\n  LabelFormatMatcher = 90,\n  DecolorizeExpr = 91,\n  DropLabelsExpr = 92,\n  DropLabels = 93,\n  DropLabel = 94,\n  KeepLabelsExpr = 95,\n  KeepLabels = 96,\n  KeepLabel = 97,\n  MetricExpr = 98,\n  RangeAggregationExpr = 99,\n  RangeOp = 100,\n  CountOverTime = 101,\n  Rate = 102,\n  RateCounter = 103,\n  BytesOverTime = 104,\n  BytesRate = 105,\n  AvgOverTime = 106,\n  SumOverTime = 107,\n  MinOverTime = 108,\n  MaxOverTime = 109,\n  StddevOverTime = 110,\n  StdvarOverTime = 111,\n  QuantileOverTime = 112,\n  FirstOverTime = 113,\n  LastOverTime = 114,\n  AbsentOverTime = 115,\n  LogRangeExpr = 116,\n  Range = 117,\n  OffsetExpr = 118,\n  UnwrapExpr = 119,\n  ConvOp = 120,\n  BytesConv = 121,\n  DurationConv = 122,\n  DurationSecondsConv = 123,\n  Grouping = 124,\n  Labels = 125,\n  VectorAggregationExpr = 126,\n  VectorOp = 127,\n  BinOpExpr = 128,\n  BinOpModifier = 129,\n  OnOrIgnoringModifier = 130,\n  GroupingLabels = 131,\n  GroupingLabelList = 132,\n  GroupingLabel = 133,\n  LabelName = 134,\n  Mul = 135,\n  Div = 136,\n  Mod = 137,\n  Pow = 138,\n  LabelReplaceExpr = 139,\n  VectorExpr = 140;\n\nexport { AbsentOverTime, Add, And, Avg, AvgOverTime, BinOpExpr, BinOpModifier, Bool, Bottomk, By, Bytes, BytesConv, BytesFilter, BytesOverTime, BytesRate, ConvOp, Count, CountOverTime, Decolorize, DecolorizeExpr, Div, Drop, DropLabel, DropLabels, DropLabelsExpr, Duration, DurationConv, DurationFilter, DurationSecondsConv, Eq, Eql, Expr, Filter, FilterOp, FirstOverTime, GroupLeft, GroupRight, Grouping, GroupingLabel, GroupingLabelList, GroupingLabels, Gte, Gtr, Identifier, Ignoring, Ip, IpLabelFilter, Json, JsonExpressionParser, Keep, KeepLabel, KeepLabels, KeepLabelsExpr, LabelExtractionExpression, LabelExtractionExpressionList, LabelFilter, LabelFormat, LabelFormatExpr, LabelFormatMatcher, LabelName, LabelParser, LabelReplace, LabelReplaceExpr, Labels, LabelsFormat, LastOverTime, LineComment, LineFilter, LineFilters, LineFormat, LineFormatExpr, LiteralExpr, LogExpr, LogQL, LogRangeExpr, Logfmt, LogfmtExpressionParser, LogfmtParser, LogfmtParserFlags, Lss, Lte, Matcher, Matchers, Max, MaxOverTime, MetricExpr, Min, MinOverTime, Mod, Mul, Neq, Npa, Nre, Number, NumberFilter, Offset, OffsetExpr, On, OnOrIgnoringModifier, Or, OrFilter, ParserFlag, Pattern, Pipe, PipeExact, PipeMatch, PipePattern, PipelineExpr, PipelineStage, Pow, QuantileOverTime, Range, RangeAggregationExpr, RangeOp, Rate, RateCounter, Re, Regexp, Selector, Sort, Sort_Desc, Stddev, StddevOverTime, Stdvar, StdvarOverTime, String, Sub, Sum, SumOverTime, Topk, UnitFilter, Unless, Unpack, Unwrap, UnwrapExpr, Vector, VectorAggregationExpr, VectorExpr, VectorOp, Without, parser };\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js?_cache=\" + {\"82\":\"55bd52670999d87e8e9b\",\"115\":\"1286fb68d3caa969500f\",\"328\":\"1bae26718376f8fa2e8a\",\"470\":\"3204c8fc47e32152da72\",\"546\":\"6f5ddc8aa7794d51d98a\",\"826\":\"a2cff326ac9c20ddbaf7\",\"854\":\"dfc3af049daeccb14055\",\"919\":\"20d467191de04bdd8fc1\",\"944\":\"f71d4a80dffcd450b1ad\"}[chunkId] + \"\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-lokiexplore-app/\";", "\n__webpack_require__.sriHashes = {\"82\":\"*-*-*-CHUNK-SRI-HASH-EQQjepNKyKKHggNWDI+9nHc8T6ovg=\",\"115\":\"*-*-*-CHUNK-SRI-HASH-W89PumdttBRCN49JbN0uDB5srF6M0=\",\"328\":\"*-*-*-CHUNK-SRI-HASH-HP0RIrpXqzYxxeeTwj5+o/GRsLRxE=\",\"470\":\"*-*-*-CHUNK-SRI-HASH-DLC92l8qdycD4Ua87OV1W1EmkNCnU=\",\"546\":\"*-*-*-CHUNK-SRI-HASH-SGxEKWjXIi8J7KnveGHhrAGvn0JRw=\",\"826\":\"*-*-*-CHUNK-SRI-HASH-TD9ZyoD08pRdcHFno3E1sZN/8NxwE=\",\"854\":\"*-*-*-CHUNK-SRI-HASH-CO8yIHolx/3g3nglKHNgio0QWCR/E=\",\"919\":\"*-*-*-CHUNK-SRI-HASH-E0zoKbm1MOaKexO/750XsMkg7RxhQ=\",\"944\":\"*-*-*-CHUNK-SRI-HASH-9Nvrh568fe5XpRS5Cb19/W6QCSzAA=\"};", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_lokiexplore_app\"] = self[\"webpackChunkgrafana_lokiexplore_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(6709);\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "slice", "lastIndexOf", "OpenInLogsDrilldownButton", "lazy", "exposedComponents", "component", "props", "Suspense", "fallback", "LinkButton", "variant", "disabled", "description", "id", "title", "App", "wasmSupported", "default", "initRuntimeDs", "initChangepoint", "initOutlier", "Promise", "all", "AppConfig", "plugin", "AppPlugin", "setRootPage", "addConfigPage", "body", "icon", "linkConfig", "linkConfigs", "addLink", "exposedComponentConfig", "exposeComponent", "PRODUCT_NAME", "ExtensionPoints", "MetricInvestigation", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "path", "createAppUrl", "configure", "contextToLink", "ExploreToolbarAction", "stringifyValues", "value", "EMPTY_VARIABLE_VALUE", "replaceEscapeChars", "replace", "stringifyAdHocValues", "addAdHocFilterUserInputPrefix", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "target", "datasource", "type", "uid", "expr", "fields", "labelFilters", "lineFilters", "patternFilters", "getMatcherFromQuery", "labelSelector", "selector", "isOperatorInclusive", "operator", "labelValue", "replaceSlash", "split", "labelName", "key", "SERVICE_NAME", "sort", "a", "params", "setUrlParameter", "UrlParameters", "DatasourceId", "URLSearchParams", "TimeRangeFrom", "timeRange", "from", "valueOf", "toString", "TimeRangeTo", "to", "labelFilter", "LabelType", "Indexed", "labelsAdHocFilterURLString", "escapeURLDelimiters", "appendUrlParameter", "Labels", "lineFilter", "LineFilters", "length", "field", "StructuredMetadata", "LEVEL_VARIABLE_VALUE", "Levels", "<PERSON><PERSON><PERSON>", "fieldValue", "parser", "adHocFilterURLString", "JSON", "stringify", "Fields", "patterns", "push", "PatternFilterOp", "match", "pattern", "patternsString", "renderPatternFilters", "Patterns", "PatternsVariable", "urlParams", "pluginJson", "VAR_DATASOURCE", "VAR_LABELS", "VAR_FIELDS", "VAR_METADATA", "VAR_LEVELS", "VAR_LINE_FILTERS", "VAR_PATTERNS", "initalParams", "searchParams", "locationService", "getSearch", "set", "location", "getLocation", "search", "append", "parameter", "stripAdHocFilterUserInputPrefix", "Symbol", "escapeUrlCommaDelimiters", "escapeUrlPipeDelimiters", "LabelFilterOp", "LineFormatFilterOp", "NumericFilterOp", "FilterOp", "LineFilterOp", "LineFilterCaseSensitive", "defaultContext", "app", "version", "logger", "error", "err", "ctx", "console", "attemptFaroErr", "info", "msg", "attemptFaroInfo", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logInfo", "e", "logWarning", "context2", "isRecord", "Object", "keys", "for<PERSON>ach", "hasData", "data", "populateFetchErrorContext", "Error", "logError", "NodePosition", "fromNode", "node", "contains", "position", "this", "getExpression", "query", "substring", "constructor", "syntaxNode", "getNodesFromQuery", "nodeTypes", "nodes", "parse", "iterate", "enter", "undefined", "includes", "getAllPositionsInNodeByType", "positions", "pos", "child", "childAfter", "parseNonPatternFilters", "lineFilterValue", "quoteString", "index", "isRegexSelector", "regex", "negativeRegex", "isCaseInsensitive", "replaceDoubleEscape", "RegExp", "replaceDoubleQuoteEscape", "caseInsensitive", "caseSensitive", "parsePatternFilters", "getNumericFieldOperator", "matcher", "<PERSON><PERSON>", "FilterOperator", "lte", "Lss", "lt", "Gte", "gte", "Gtr", "gt", "getStringFieldOperator", "Eq", "Equal", "Neq", "NotEqual", "Re", "RegexEqual", "Nre", "RegexNotEqual", "filter", "Selector", "allMatcher", "Matcher", "identifierPosition", "Identifier", "valuePosition", "String", "map", "parseLabelFilters", "allLineFilters", "LineFilter", "entries", "equal", "PipeExact", "pipeRegExp", "PipeMatch", "notEqual", "notEqualRegExp", "patternInclude", "PipePattern", "patternExclude", "Npa", "lineFilterValueNodes", "getStringsFromLineFilter", "lineFilterValueNode", "negativeMatch", "parseLineFilters", "dataFrame", "series", "frame", "refId", "allFields", "LabelFilter", "fieldNameNode", "expression", "<PERSON><PERSON><PERSON><PERSON>", "logFmtParser", "Logfmt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Json", "fieldName", "fieldStringValue", "fieldNumberValue", "Number", "fieldBytesValue", "Bytes", "fieldDurationValue", "Duration", "labelType", "getLabelTypeFromFrame", "Parsed", "parseFields", "ErrorId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "isQueryWithNode", "string", "<PERSON><PERSON><PERSON><PERSON>", "LokiQueryDirection", "labelKey", "typeField", "name", "values", "isObj", "o", "hasProp", "prop", "isString", "s", "obj", "unknownToStrings", "strings", "Array", "isArray", "i", "narrowSelectedTableRow", "narrowed", "row", "narrowLogsVisualizationType", "narrowLogsSortOrder", "LogsSortOrder", "Ascending", "Descending", "narrowFieldValue", "narrowRecordStringNumber", "returnRecord", "narrowTimeRange", "<PERSON><PERSON><PERSON><PERSON>", "range", "narrowErrorMessage", "narrowFilterOperator", "op", "NarrowingError", "isOperatorExclusive", "isOperatorRegex", "isOperatorNumeric", "numericOperatorArray", "getOperatorDescription", "operators", "array", "label", "includeOperators", "numericOperators", "lineFilterOperators", "escapeLabelValueInExactSelector", "excludePatternsLine", "p", "join", "trim", "includePatterns", "includePatternsLine", "VAR_LABELS_EXPR", "VAR_LABELS_REPLICA", "VAR_LABELS_REPLICA_EXPR", "VAR_FIELDS_EXPR", "PENDING_FIELDS_EXPR", "PENDING_METADATA_EXPR", "VAR_FIELDS_AND_METADATA", "VAR_METADATA_EXPR", "VAR_PATTERNS_EXPR", "VAR_LEVELS_EXPR", "VAR_FIELD_GROUP_BY", "VAR_LABEL_GROUP_BY", "VAR_LABEL_GROUP_BY_EXPR", "VAR_PRIMARY_LABEL_SEARCH", "VAR_PRIMARY_LABEL", "VAR_PRIMARY_LABEL_EXPR", "VAR_DATASOURCE_EXPR", "VAR_JSON_FIELDS", "VAR_JSON_FIELDS_EXPR", "VAR_LINE_FORMAT", "VAR_LINE_FORMAT_EXPR", "MIXED_FORMAT_EXPR", "JSON_FORMAT_EXPR", "LOGS_FORMAT_EXPR", "VAR_LOGS_FORMAT", "VAR_LOGS_FORMAT_EXPR", "VAR_LINE_FILTER", "VAR_LINE_FILTERS_EXPR", "LOG_STREAM_SELECTOR_EXPR", "DETECTED_FIELD_VALUES_EXPR", "DETECTED_FIELD_AND_METADATA_VALUES_EXPR", "DETECTED_LEVELS_VALUES_EXPR", "PATTERNS_SAMPLE_SELECTOR_EXPR", "PRETTY_LOG_STREAM_SELECTOR_EXPR", "EXPLORATION_DS", "ALL_VARIABLE_VALUE", "SERVICE_UI_LABEL", "VAR_AGGREGATED_METRICS", "USER_INPUT_ADHOC_VALUE_PREFIX", "startsWith", "isAdHocFilterValueUserInput", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__1308__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__200__", "__WEBPACK_EXTERNAL_MODULE__1159__", "__WEBPACK_EXTERNAL_MODULE__7694__", "__WEBPACK_EXTERNAL_MODULE__1269__", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "nextPropID", "Range", "NodeProp", "config", "perNode", "deserialize", "add", "RangeError", "NodeType", "result", "closedBy", "str", "openedBy", "group", "isolate", "contextHash", "lookAhead", "mounted", "MountedTree", "tree", "overlay", "get", "noProps", "create", "flags", "define", "spec", "top", "skipped", "src", "isTop", "isSkipped", "isError", "isAnonymous", "is", "indexOf", "direct", "groups", "found", "none", "NodeSet", "types", "extend", "newTypes", "newProps", "source", "assign", "CachedNode", "WeakMap", "CachedInnerNode", "IterMode", "Tree", "children", "ch", "test", "cursor", "mode", "TreeCursor", "topNode", "cursorAt", "side", "scope", "moveTo", "_tree", "TreeNode", "resolve", "resolveNode", "resolveInner", "resolveStack", "inner", "layers", "scan", "parent", "mount", "root", "iterStack", "stackIterator", "leave", "anon", "IncludeAnonymous", "c", "entered", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "balance", "balanceRange", "makeTree", "build", "_a", "buffer", "nodeSet", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reused", "minRepeatType", "FlatBufferCursor", "takeNode", "parentStart", "minPos", "inRepeat", "depth", "start", "end", "size", "lookAheadAtStart", "contextAtStart", "next", "startPos", "findBufferSize", "Uint16Array", "skip", "endPos", "copyToBuffer", "<PERSON><PERSON><PERSON><PERSON>", "localChildren", "localPositions", "localInRepeat", "lastGroup", "lastEnd", "makeRepeatLeaf", "takeFlatNode", "reverse", "make", "makeBalanced", "nodeCount", "stopAt", "j", "last", "lookAheadProp", "lastI", "base", "pop", "pair", "concat", "maxSize", "fork", "minStart", "nodeSize", "localSkipped", "nodeStart", "bufferStart", "startIndex", "topID", "buildTree", "empty", "childString", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "dir", "pick", "checkSide", "startI", "endI", "b", "copy", "len", "Math", "max", "overlays", "IgnoreOverlays", "BaseNode", "before", "after", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchContext", "matchNodeContext", "enterUnfinishedNodesBefore", "childBefore", "<PERSON><PERSON><PERSON><PERSON>", "prevSibling", "_parent", "super", "<PERSON><PERSON><PERSON><PERSON>", "ExcludeBuffers", "BufferNode", "BufferContext", "<PERSON><PERSON><PERSON><PERSON>", "IgnoreMounts", "rPos", "nextSignificantParent", "val", "toTree", "cur", "externalSibling", "heads", "picked", "newHeads", "splice", "StackIterator", "stack", "bufferNode", "yieldNode", "n", "unshift", "yieldBuf", "yield", "enterChild", "sibling", "d", "atLastNode", "move", "prev", "cache", "mustLeave", "some", "nodeSizeCache", "balanceType", "mkTop", "mkTree", "total", "max<PERSON><PERSON><PERSON>", "ceil", "divide", "offset", "groupFrom", "groupStart", "groupSize", "nextSize", "only", "<PERSON><PERSON><PERSON>", "startParse", "input", "fragments", "ranges", "StringInput", "createParse", "done", "advance", "chunk", "lineChunks", "read", "<PERSON><PERSON>", "state", "reducePos", "score", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "_", "cx", "StackContext", "pushState", "reduce", "action", "lookaheadRecord", "setLookAhead", "dPrec", "dynamicPrecedence", "getGoto", "minRepeatTerm", "storeNode", "reduceContext", "lastBigReductionStart", "bigReductionCount", "lastBigReductionSize", "count", "stateFlag", "baseStateID", "term", "mustSink", "mustMove", "shift", "shiftContext", "maxNode", "nextState", "apply", "nextStart", "nextEnd", "useNode", "updateContext", "tracker", "reuse", "stream", "reset", "off", "recoverByDelete", "isNode", "canShift", "sim", "SimulatedStack", "stateSlot", "hasAction", "recoverByInsert", "nextStates", "best", "v", "forceReduce", "validAction", "backup", "findForcedReduction", "seen", "explore", "allActions", "r<PERSON><PERSON><PERSON>", "forceAll", "deadEnd", "restart", "sameState", "other", "dialectEnabled", "dialectID", "dialect", "emitContext", "hash", "emitLookAhead", "newCx", "close", "strict", "goto", "StackBufferCursor", "maybeNext", "decodeArray", "Type", "out", "charCodeAt", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extended", "mask", "nullToken", "InputStream", "chunkOff", "chunk2", "chunk2Pos", "token", "rangeIndex", "chunkPos", "readNext", "resolveOffset", "assoc", "clipPos", "peek", "idx", "resolved", "acceptToken", "endOffset", "acceptTokenTo", "getChunk", "nextChunk", "setDone", "min", "TokenGroup", "readToken", "tokenPrecTable", "prototype", "contextual", "precTable", "precOffset", "groupMask", "accEnd", "allows", "overrides", "low", "high", "mid", "findOffset", "tableData", "tableOffset", "iPrev", "verbose", "process", "env", "LOG", "stackIDs", "cutAt", "fragment", "safeFrom", "safeTo", "trees", "nextFragment", "fr", "openStart", "openEnd", "nodeAt", "TokenCache", "tokens", "mainToken", "actions", "tokenizers", "getActions", "actionIndex", "main", "tokenizer", "updateCachedToken", "addActions", "eofTerm", "getMainToken", "specialized", "specializers", "putAction", "Parse", "recovering", "nextStackID", "minStackPos", "stoppedAt", "topTerm", "stacks", "bufferLength", "parsedPos", "stopped", "stoppedTokens", "newStacks", "advanceStack", "tok", "finished", "findFinished", "stackToTree", "SyntaxError", "runRecovery", "maxRemaining", "outer", "stackID", "strictCx", "cxHash", "cached", "defaultReduce", "localStack", "advanceFully", "pushStackDedup", "restarted", "tokenEnd", "force", "forceBase", "insert", "fromCodePoint", "Dialect", "<PERSON><PERSON><PERSON><PERSON>", "wrappers", "nodeNames", "repeatNodeCount", "topTerms", "topRules", "nodeProps", "setProp", "nodeID", "propSpec", "skippedNodes", "propSources", "tokenArray", "tokenData", "specializerSpecs", "getSpecializer", "states", "Uint32Array", "stateData", "maxTerm", "dialects", "dynamicPrecedences", "tokenPrec", "termNames", "parseDialect", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "deflt", "t", "external", "contextTracker", "wrap", "hasWrappers", "getName", "prec", "part", "Uint8Array", "keywordTokens", "json", "logfmt", "unpack", "regexp", "label_format", "line_format", "label_replace", "vector", "bool", "on", "ignoring", "group_left", "group_right", "unwrap", "decolorize", "drop", "keep", "contextualKeywordTokens", "by", "without", "and", "or", "unless", "sum", "avg", "stddev", "stdvar", "bottomk", "topk", "sort_desc", "spec_Identifier", "__proto__", "ip", "count_over_time", "rate", "rate_counter", "bytes_over_time", "bytes_rate", "avg_over_time", "sum_over_time", "min_over_time", "max_over_time", "stddev_over_time", "stdvar_over_time", "quantile_over_time", "first_over_time", "last_over_time", "absent_over_time", "bytes", "duration", "duration_seconds", "toLowerCase", "specializeIdentifier", "extendIdentifier", "MetricExpr", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "loaded", "__webpack_modules__", "call", "m", "getter", "__esModule", "getPrototypeOf", "then", "ns", "def", "current", "getOwnPropertyNames", "definition", "defineProperty", "enumerable", "f", "chunkId", "promises", "u", "g", "globalThis", "Function", "window", "hasOwnProperty", "l", "url", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "origin", "crossOrigin", "integrity", "sri<PERSON><PERSON><PERSON>", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "fn", "setTimeout", "bind", "head", "append<PERSON><PERSON><PERSON>", "toStringTag", "nmd", "paths", "baseURI", "self", "href", "installedChunks", "installedChunkData", "promise", "reject", "errorType", "realSrc", "message", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "chunkLoadingGlobal"], "sourceRoot": ""}