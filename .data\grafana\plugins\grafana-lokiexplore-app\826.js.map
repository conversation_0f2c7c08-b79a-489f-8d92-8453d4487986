{"version": 3, "file": "826.js?_cache=a2cff326ac9c20ddbaf7", "mappings": "2oCAsBA,MAwEMA,EAAaC,IAA0B,CAC3CC,UAAWC,EAAAA,GAAG;aACHF,EAAMG,OAAOC,KAAKC;IAE7BC,MAAMJ,EAAAA,EAAAA,KAAI,CACRK,WAAYP,EAAMQ,QAAQ,KAE5BC,OAAOP,EAAAA,EAAAA,KAAI,CACTQ,WAAY,SACZC,QAAS,OACTC,aAAcZ,EAAMQ,QAAQ,OAE9BK,UAAWX,EAAAA,GAAG;kBACEF,EAAMQ,QAAQ;IAE9BM,YAAaZ,EAAAA,GAAG;kBACAF,EAAMQ,QAAQ;MAI1BO,EAAAA,W,MAAwB,aAAOC,EAAkBC,GACrD,UACQC,EAAaF,EAAUC,GAI7BE,EAAAA,gBAAgBC,QAClB,CAAE,MAAOC,GACPC,EAAAA,EAAOC,MAAMF,EAAG,CAAEG,IAAK,mCACzB,CACF,I,gBAVqCR,EAAkBC,G,gCAAjDF,GAYAU,EAAU,CACdC,UAAW,CACTC,UAAW,2BACXC,SAAU,gCACVC,OAAQ,+BAICX,EAAAA,W,MAAe,aAAOF,EAAkBC,GACnD,MAAMa,GAAWC,EAAAA,EAAAA,iBAAgBC,MAAM,CACrCf,OACAgB,OAAQ,OACRC,IAAK,gBAAgBlB,eAKvB,aAF2BmB,EAAAA,EAAAA,eAAcL,IAErBb,IACtB,I,gBAVmCD,EAAkBC,G,gCAAxCC,GAYPkB,EAAWR,IACf,IACE,GAAIA,EAAU,CACZ,MAAMS,EAAUC,EAAAA,UAAUC,kBAAkBX,GAC5C,OAAOY,EAAAA,EAAAA,UAASH,IAAYA,GAhIL,IAiIzB,CAEE,OAAO,CAEX,CAAE,MAAOhB,GAAI,CAEb,OAAO,CAAK,EAGd,EAtIkB,EAAGoB,aACnB,MAAMC,GAASC,EAAAA,EAAAA,YAAW5C,IACpB,QAAE6C,EAAO,SAAEC,EAAQ,OAAEC,GAAWL,EAAOM,K,IAGjCF,EACOA,EAFnB,MAAOG,EAAOC,IAAYC,EAAAA,EAAAA,UAAgB,CACxCtB,SAA4B,QAAlBiB,EAAAA,aAAAA,EAAAA,EAAUjB,gBAAViB,IAAAA,EAAAA,EAAsB,GAChCT,QAASA,EAA0B,QAAlBS,EAAAA,aAAAA,EAAAA,EAAUjB,gBAAViB,IAAAA,EAAAA,EAAsB,MAYzC,OACE,kBAACM,MAAAA,CAAIC,cAAa3B,EAAQC,UAAUC,WAClC,kBAAC0B,EAAAA,SAAQA,CAAC5C,MAAM,YACd,kBAAC6C,EAAAA,MAAKA,CACJC,SAAUnB,EAAQY,EAAMpB,UACxBL,MAAO,2FACPiC,YACE,kBAACC,OAAAA,KAAK,qLAEuE,kBAACC,KAAAA,MAAK,+BAIrFjD,MAAO,+BACPkD,UAAWjB,EAAO7B,WAElB,kBAAC+C,EAAAA,MAAKA,CACJC,MAAO,GACPC,GAAG,WACHV,cAAa3B,EAAQC,UAAUE,SAC/BnB,MAAO,eACPsD,MAAOf,aAAAA,EAAAA,EAAOpB,SACdoC,YAAa,KACbC,SAhCgBC,IACxB,MAAMtC,EAAWsC,EAAMC,OAAOJ,MAAMK,OACpCnB,EAAS,E,sUAAA,IACJD,GAAAA,CACHpB,WACAQ,QAASA,EAAQR,K,KA+Bf,kBAACuB,MAAAA,CAAIQ,UAAWjB,EAAO7B,WACrB,kBAACwD,EAAAA,OAAMA,CACLC,KAAK,SACLlB,cAAa3B,EAAQC,UAAUG,OAC/B0C,QAAS,IACPxD,EAAsB0B,EAAOM,KAAKe,GAAI,CACpClB,UACAC,SAAU,CACRjB,SAAUoB,EAAMpB,UAElBkB,WAGJ0B,UAAWpC,EAAQY,EAAMpB,WAC1B,oB", "sources": ["webpack://grafana-lokiexplore-app/./Components/AppConfig/AppConfig.tsx"], "sourcesContent": ["import React, { ChangeEvent, useState } from 'react';\n\nimport { css } from '@emotion/css';\nimport { isNumber } from 'lodash';\nimport { lastValueFrom } from 'rxjs';\n\nimport { AppPluginMeta, GrafanaTheme2, PluginConfigPageProps, PluginMeta, rangeUtil } from '@grafana/data';\nimport { getBackendSrv, locationService } from '@grafana/runtime';\nimport { Button, Field, FieldSet, Input, useStyles2 } from '@grafana/ui';\n\nimport { logger } from '../../services/logger';\n\nexport type JsonData = {\n  interval?: string;\n};\n\ntype State = {\n  interval: string;\n  isValid: boolean;\n};\n\n// 1 hour minimum\nconst MIN_INTERVAL_SECONDS = 3600;\n\ninterface Props extends PluginConfigPageProps<AppPluginMeta<JsonData>> {}\n\nconst AppConfig = ({ plugin }: Props) => {\n  const styles = useStyles2(getStyles);\n  const { enabled, jsonData, pinned } = plugin.meta;\n\n  const [state, setState] = useState<State>({\n    interval: jsonData?.interval ?? '',\n    isValid: isValid(jsonData?.interval ?? ''),\n  });\n\n  const onChangeInterval = (event: ChangeEvent<HTMLInputElement>) => {\n    const interval = event.target.value.trim();\n    setState({\n      ...state,\n      interval,\n      isValid: isValid(interval),\n    });\n  };\n\n  return (\n    <div data-testid={testIds.appConfig.container}>\n      <FieldSet label=\"Settings\">\n        <Field\n          invalid={!isValid(state.interval)}\n          error={'Interval is invalid. Please enter an interval longer then \"60m\". For example: 3d, 1w, 1m'}\n          description={\n            <span>\n              The maximum interval that can be selected in the time picker within the Grafana Logs Drilldown app. If\n              empty, users can select any time range interval in Grafana Logs Drilldown. <br />\n              Example values: 7d, 24h, 2w\n            </span>\n          }\n          label={'Maximum time picker interval'}\n          className={styles.marginTop}\n        >\n          <Input\n            width={60}\n            id=\"interval\"\n            data-testid={testIds.appConfig.interval}\n            label={`Max interval`}\n            value={state?.interval}\n            placeholder={`7d`}\n            onChange={onChangeInterval}\n          />\n        </Field>\n\n        <div className={styles.marginTop}>\n          <Button\n            type=\"submit\"\n            data-testid={testIds.appConfig.submit}\n            onClick={() =>\n              updatePluginAndReload(plugin.meta.id, {\n                enabled,\n                jsonData: {\n                  interval: state.interval,\n                },\n                pinned,\n              })\n            }\n            disabled={!isValid(state.interval)}\n          >\n            Save settings\n          </Button>\n        </div>\n      </FieldSet>\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  colorWeak: css`\n    color: ${theme.colors.text.secondary};\n  `,\n  icon: css({\n    marginLeft: theme.spacing(1),\n  }),\n  label: css({\n    alignItems: 'center',\n    display: 'flex',\n    marginBottom: theme.spacing(0.75),\n  }),\n  marginTop: css`\n    margin-top: ${theme.spacing(3)};\n  `,\n  marginTopXl: css`\n    margin-top: ${theme.spacing(6)};\n  `,\n});\n\nconst updatePluginAndReload = async (pluginId: string, data: Partial<PluginMeta<JsonData>>) => {\n  try {\n    await updatePlugin(pluginId, data);\n\n    // Reloading the page as the changes made here wouldn't be propagated to the actual plugin otherwise.\n    // This is not ideal, however unfortunately currently there is no supported way for updating the plugin state.\n    locationService.reload();\n  } catch (e) {\n    logger.error(e, { msg: 'Error while updating the plugin' });\n  }\n};\n\nconst testIds = {\n  appConfig: {\n    container: 'data-testid ac-container',\n    interval: 'data-testid ac-interval-input',\n    submit: 'data-testid ac-submit-form',\n  },\n};\n\nexport const updatePlugin = async (pluginId: string, data: Partial<PluginMeta>) => {\n  const response = getBackendSrv().fetch({\n    data,\n    method: 'POST',\n    url: `/api/plugins/${pluginId}/settings`,\n  });\n\n  const dataResponse = await lastValueFrom(response);\n\n  return dataResponse.data;\n};\n\nconst isValid = (interval: string): boolean => {\n  try {\n    if (interval) {\n      const seconds = rangeUtil.intervalToSeconds(interval);\n      return isNumber(seconds) && seconds >= MIN_INTERVAL_SECONDS;\n    } else {\n      // Empty strings are fine\n      return true;\n    }\n  } catch (e) {}\n\n  return false;\n};\n\nexport default AppConfig;\n"], "names": ["getStyles", "theme", "colorWeak", "css", "colors", "text", "secondary", "icon", "marginLeft", "spacing", "label", "alignItems", "display", "marginBottom", "marginTop", "marginTopXl", "updatePluginAndReload", "pluginId", "data", "updatePlugin", "locationService", "reload", "e", "logger", "error", "msg", "testIds", "appConfig", "container", "interval", "submit", "response", "getBackendSrv", "fetch", "method", "url", "lastValueFrom", "<PERSON><PERSON><PERSON><PERSON>", "seconds", "rangeUtil", "intervalToSeconds", "isNumber", "plugin", "styles", "useStyles2", "enabled", "jsonData", "pinned", "meta", "state", "setState", "useState", "div", "data-testid", "FieldSet", "Field", "invalid", "description", "span", "br", "className", "Input", "width", "id", "value", "placeholder", "onChange", "event", "target", "trim", "<PERSON><PERSON>", "type", "onClick", "disabled"], "sourceRoot": ""}