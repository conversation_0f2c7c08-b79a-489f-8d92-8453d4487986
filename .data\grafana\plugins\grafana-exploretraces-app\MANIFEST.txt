
-----<PERSON><PERSON><PERSON> PGP SIGNED MESSAGE-----
Hash: SHA512

{
  "manifestVersion": "2.0.0",
  "signatureType": "grafana",
  "signedByOrg": "grafana",
  "signedByOrgName": "Grafana Labs",
  "plugin": "grafana-exploretraces-app",
  "version": "1.0.0",
  "time": 1745502057581,
  "keyId": "7e4d0c6a708866e7",
  "files": {
    "142.js": "7ac080ce46e82035ca8778b59856c6da36b14ac22955710e7d97f15dee7c041a",
    "142.js.map": "af7c93b627673bafd1478a8ddffadc2e4712014c93a4265b6579d98e0f68b3bc",
    "327.js": "8c59603ace476194410523efda0e32ffadee271fabf9b45d1d1db8804d4b92d2",
    "327.js.map": "574d63da155b7c665ab56bdcfb80296ff003c58b3c4113e715c1c811f8a66d25",
    "389.js": "b7b81ef31f9956209439e955138b94b3c29d7bbd90934ab3a7ead04f176c88c4",
    "389.js.map": "4c21080fce8fbb2c172d7ba6f8c41b28f1e9d2844f4cc7f6f8bb666fca574d5b",
    "67.js": "14744479ef8953996a665372b98636fbe4a9b0800490bd2affcc0dd5d79f8f8b",
    "67.js.map": "7401e451d55f3bc144302728b8a646a604a6dc04e623fbe05644a4f755ae8c02",
    "746.js": "8d26f57e40e34dcb6a5df9038cea39604c8551986d50e0680dd1a4d5a4d897ae",
    "746.js.LICENSE.txt": "3dc73efd43239eafc14f7a7fff74969f5ac081aafb8a87d0d60683fec2ed99cc",
    "746.js.map": "4c5680d11e0b2ddd6aae1b3793ba5e626b871ee7b39dfdb086100a74e2f7e7f8",
    "CHANGELOG.md": "860331ad2804095cf8f45ba1a9d14aa8cd92132580e085597f2e1a80e442ed0d",
    "LICENSE": "20b067f86de375aae6db0f283ab2e65de24d537733b89bd58432c101259d84cf",
    "README.md": "48850d5d55ec4cf786dea095b681e712750a2bda8b33a09d8daa7c282c72dc9d",
    "components/states/EmptyState/img/grot-404-dark.svg": "a0c8acbcf5685a8950ce1c67722579dc745585fb0d668ce965327955e5e829ff",
    "components/states/EmptyState/img/grot-404-light.svg": "89ea40b6dcf2dc8dfe146f8acac42b604e4d3c3dad03e539551d58a21f80654d",
    "img/1382cadfeb81ccdaa67d.svg": "3a23f8cdad37100716aa8a17c065afd0ee1c557b9dbeb5991482373297fe60e2",
    "img/944c737f589d02ecf603.svg": "a0c8acbcf5685a8950ce1c67722579dc745585fb0d668ce965327955e5e829ff",
    "img/e79edcfbe2068fae2364.svg": "89ea40b6dcf2dc8dfe146f8acac42b604e4d3c3dad03e539551d58a21f80654d",
    "img/errors-metric-flow.png": "c10680c301dc72597b2ace18811494f1a330952b77a2e1a7344f99fe2ffd4b41",
    "img/errors-root-cause.png": "599bf867d3411fa05a08c26c63b421c351e2a6c7689fb254bdb949b869204ad0",
    "img/histogram-breakdown.png": "469248a97a7f029b94479c3f89231c1e921957a3b2aad98a6135949540df66a3",
    "img/logo.svg": "3a23f8cdad37100716aa8a17c065afd0ee1c557b9dbeb5991482373297fe60e2",
    "module.js": "1037aa8ba8e5c12f3eab04e73c77c87f2f35200590462b41daf489b124d623a9",
    "module.js.map": "8f373404cfe99a953eb66829d6b1f73f8ad006d508be0f1ffd4b0276ea45efae",
    "plugin.json": "510b69a375bdc8847732ddabc8bd8a63966cfb9ed5257380bc010df9ced696d7",
    "utils/trace-merge/test-responses/service-struct.json": "e00d31b20fec475399a0c4b18bcbb26b9c36f72c7e92283a67b032651b05c870"
  }
}
-----BEGIN PGP SIGNATURE-----
Version: OpenPGP.js v4.10.11
Comment: https://openpgpjs.org

wrkEARMKAAYFAmgKP2kAIQkQfk0ManCIZucWIQTzOyW2kQdOhGNlcPN+TQxq
cIhm5+GtAgkBBM73Y26H0tfJii+GFDrvn1RphAy2m3/LEvj5UYYLHKQdNyRK
f+A6NXFdJxsS/spEncCtMMT2jJ9ZxWpdf2iG2/QCCQD3GZ8nwaGgy/yYVHbs
A7SdzSl5ECLwBhtk/zsswMSFNYT+0NES35v/m5k/lVHrQ8BQ6A0dMu9NNfEJ
rTUuTDllkA==
=A/Ip
-----END PGP SIGNATURE-----
