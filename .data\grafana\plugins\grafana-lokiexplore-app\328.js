"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[328],{7709:(e,t,n)=>{n.d(t,{F:()=>l});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(3571);const l=e=>{const{buttonFill:t,hideExclude:n,isExcluded:r,isIncluded:i,onClear:l,onExclude:u,onInclude:d,titles:p}=e,g=(0,s.useStyles2)(c,i,r,n);return a().createElement("div",{className:g.container},a().createElement(s.<PERSON><PERSON>,{variant:i?"primary":"secondary",fill:t,size:"sm","aria-selected":i,className:g.includeButton,onClick:i?l:d,"data-testid":o.b.exploreServiceDetails.buttonFilterInclude,title:null==p?void 0:p.include},"Include"),!n&&a().createElement(s.<PERSON><PERSON>,{variant:r?"primary":"secondary",fill:t,size:"sm","aria-selected":r,className:g.excludeButton,onClick:r?l:u,title:null==p?void 0:p.exclude,"data-testid":o.b.exploreServiceDetails.buttonFilterExclude},"Exclude"))},c=(e,t,n,r)=>({container:(0,i.css)({display:"flex",justifyContent:"center"}),excludeButton:(0,i.css)({borderLeft:n?void 0:"none",borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`}),includeButton:(0,i.css)({borderRadius:0,borderRight:t||r?void 0:"none"})})},7191:(e,t,n)=>{n.d(t,{R:()=>o});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007);const o=({children:e})=>{const t=(0,s.useStyles2)(l);return a().createElement("div",{className:t.wrap},a().createElement(s.EmptyState,{variant:"not-found",message:e?"":"An error occurred"},e&&e))},l=e=>({wrap:(0,i.css)({margin:"0 auto"})})},2661:(e,t,n)=>{n.d(t,{P:()=>at,y:()=>rt});var r=n(5959),a=n.n(r),i=n(7781),s=n(8531),o=n(9736),l=n(2245),c=n(2007),u=n(6709),d=n(4509),p=n(1532),g=n(4702),h=n(7839),f=n(6838),m=n(8502),v=n(6854),b=n(5700);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let S={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},w={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},O={"!":"1",'"':"'","#":"3",$:"4","%":"5","&":"7","(":"9",")":"0","*":"8","+":"=",":":";","<":",",">":".","?":"/","@":"2","^":"6",_:"-","|":"\\","~":"`"},x={command:"meta",escape:"esc",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl",option:"alt",plus:"+",return:"enter"},E=null;for(let e=1;e<20;++e)S[111+e]="f"+e;for(let e=0;e<=9;++e)S[e+96]=e.toString();function C(e){if("keypress"===e.type){let t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return S[e.which]?S[e.which]:w[e.which]?w[e.which]:String.fromCharCode(e.which).toLowerCase()}function k(e){return"shift"===e||"ctrl"===e||"alt"===e||"meta"===e}function P(e,t,n){return n||(n=function(){if(!E){E={};for(let e in S){const t=parseInt(e,10);t>95&&t<112||S.hasOwnProperty(e)&&(E[S[e]]=e)}}return E}()[e]?"keydown":"keypress"),"keypress"===n&&t.length&&(n="keydown"),n}function j(e,t){let n,r,a,i=[];for(n=function(e){return"+"===e?["+"]:(e=e.replace(/\+{2}/g,"+plus")).split("+")}(e),a=0;a<n.length;++a)r=n[a],x[r]&&(r=x[r]),t&&"keypress"!==t&&O[r]&&(r=O[r],i.push("shift")),k(r)&&i.push(r);if(!r)throw new Error("Unable to get key");return{action:t=P(r,i,t),key:r,modifiers:i}}function F(e,t){return null!==e&&e!==document&&(e===t||F(e.parentNode,t))}const L=new class{constructor(e){y(this,"target",void 0),y(this,"_callbacks",{}),y(this,"_directMap",{}),y(this,"_sequenceLevels",{}),y(this,"_resetTimer",void 0),y(this,"_ignoreNextKeyup",!1),y(this,"_ignoreNextKeypress",!1),y(this,"_nextExpectedAction",!1),y(this,"_globalCallbacks",{}),y(this,"_resetSequences",(e=>{e=e||{};let t,n=!1;for(t in this._sequenceLevels)e[t]?n=!0:this._sequenceLevels[t]=0;n||(this._nextExpectedAction=!1)})),y(this,"_getMatches",((e,t,n,r,a,i)=>{let s,o,l=[],c=n.type;if(!this._callbacks[e])return[];for("keyup"===c&&k(e)&&(t=[e]),s=0;s<this._callbacks[e].length;++s)if(o=this._callbacks[e][s],(r||!o.seq||this._sequenceLevels[o.seq]===o.level)&&c===o.action&&("keypress"===c&&!n.metaKey&&!n.ctrlKey||(u=t,d=o.modifiers,u.sort().join(",")===d.sort().join(",")))){let t=!r&&o.combo===a,n=r&&o.seq===r&&o.level===i;(t||n)&&this._callbacks[e].splice(s,1),l.push(o)}var u,d;return l})),y(this,"_fireCallback",((e,t,n,r)=>{const a=t.target||t.srcElement;var i;a&&a instanceof HTMLElement&&this.stopCallback(t,a,n,r)||!1===e(t,n)&&((i=t).preventDefault?i.preventDefault():i.returnValue=!1,function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}(t))})),y(this,"_handleKey",((e,t,n)=>{let r,a=this._getMatches(e,t,n),i={},s=0,o=!1;for(r=0;r<a.length;++r){var l;if(a[r].seq)s=Math.max(s,null!==(l=a[r].level)&&void 0!==l?l:0)}for(r=0;r<a.length;++r){const t=a[r].seq;if(t){if(a[r].level!==s)continue;o=!0,i[t]=1,this._fireCallback(a[r].callback,n,a[r].combo,t);const l=t.lastIndexOf(e),c=t.slice(0,l);for(const[e,t]of Object.entries(this._sequenceLevels))t>0&&e.startsWith(c)&&(i[e]=1)}else o||this._fireCallback(a[r].callback,n,a[r].combo)}var c;for(const t of null!==(c=this._callbacks[e])&&void 0!==c?c:[])t.action===n.type&&t.seq&&0===t.level&&(i[t.seq]=1);let u="keypress"===n.type&&this._ignoreNextKeypress;n.type!==this._nextExpectedAction||k(e)||u||this._resetSequences(i),this._ignoreNextKeypress=o&&"keydown"===n.type})),y(this,"_handleKeyEvent",(e=>{if(!(e instanceof KeyboardEvent))throw new Error("Didn't get a KeyboardEvent");const t=e;if(t.repeat)return;"number"!=typeof t.which&&(t.which=t.keyCode);let n=C(t);n&&("keyup"!==t.type||this._ignoreNextKeyup!==n?this.handleKey(n,function(e){let t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}(t),t):this._ignoreNextKeyup=!1)})),y(this,"_resetSequenceTimer",(()=>{clearTimeout(this._resetTimer),this._resetTimer=setTimeout(this._resetSequences,1e3)})),y(this,"_bindSequence",((e,t,n,r)=>{this._sequenceLevels[e]=0;const a=t=>()=>{this._nextExpectedAction=t,++this._sequenceLevels[e],this._resetSequenceTimer()},i=t=>{this._fireCallback(n,t,e),"keyup"!==r&&(this._ignoreNextKeyup=C(t)),this._resetSequenceTimer()};for(let n=0;n<t.length;++n){let s=n+1===t.length?i:a(r||j(t[n+1]).action);this._bindSingle(t[n],s,r,e,n)}})),y(this,"_bindSingle",((e,t,n,r,a)=>{this._directMap[e+":"+n]=t;let i,s=(e=e.replace(/\s+/g," ")).split(" ");if(s.length>1)return void this._bindSequence(e,s,t,n);i=j(e,n),this._callbacks[i.key]=this._callbacks[i.key]||[];const o={ctrlKey:!1,metaKey:!1,type:i.action};this._getMatches(i.key,i.modifiers,o,r,e,a);const l={action:i.action,callback:t,combo:e,level:a,modifiers:i.modifiers,seq:r};this._callbacks[i.key][r?"unshift":"push"](l)})),y(this,"_bindMultiple",((e,t,n)=>{for(let r=0;r<e.length;++r)this._bindSingle(e[r],t,n)})),y(this,"bind",((e,t,n)=>(e=e instanceof Array?e:[e],this._bindMultiple(e,t,n),self))),y(this,"unbind",((e,t)=>this.bind(e,(function(){}),t))),y(this,"bindGlobal",((e,t,n)=>{if(this.bind(e,t,n),e instanceof Array)for(let t=0;t<e.length;t++)this._globalCallbacks[e[t]]=!0;else this._globalCallbacks[e]=!0})),y(this,"unbindGlobal",((e,t)=>{if(this.unbind(e,t),e instanceof Array)for(let t=0;t<e.length;t++)this._globalCallbacks[e[t]]=!1;else this._globalCallbacks[e]=!1})),y(this,"trigger",((e,t)=>{let n=this;return n._directMap[e+":"+t]&&n._directMap[e+":"+t]({},e),n})),y(this,"reset",(()=>(this._callbacks={},this._directMap={},this))),y(this,"stopCallback",((e,t,n,r)=>{if(this._globalCallbacks[n]||r&&this._globalCallbacks[r])return!1;if((" "+t.className+" ").indexOf(" mousetrap ")>-1)return!1;if(F(t,this.target))return!1;if("composedPath"in e&&"function"==typeof e.composedPath){let n=e.composedPath()[0];n!==e.target&&n instanceof HTMLElement&&(t=n)}return Boolean("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||"isContentEditable"in t&&t.isContentEditable)})),y(this,"handleKey",((...e)=>this._handleKey(...e))),y(this,"addKeycodes",(e=>{for(let t in e)e.hasOwnProperty(t)&&(S[t]=e[t]);E=null})),this.target=e,this.target.addEventListener("keypress",(e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)})),this.target.addEventListener("keydown",(e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)})),this.target.addEventListener("keyup",(e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)}))}}(document);class _{addBinding(e){L.bind(e.key,(t=>{t.preventDefault(),t.stopPropagation(),t.returnValue=!1,e.onTrigger()}),"keydown"),this._binds.push(e)}removeAll(){this._binds.forEach((e=>{L.unbind(e.key,e.type)})),this._binds=[]}constructor(){var e,t,n;n=[],(t="_binds")in(e=this)?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}}var T=n(8428),D=n(5719);function N(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function $(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){N(i,r,a,s,o,"next",e)}function o(e){N(i,r,a,s,o,"throw",e)}s(void 0)}))}}function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const B=(0,s.getAppEvents)();function A(e){const t=new _;let n=null;const r=B.subscribe(i.SetPanelAttentionEvent,(e=>{"string"==typeof e.payload.panelId&&(n=e.payload.panelId)}));function a(e,t){return()=>{const r=o.jh.findObject(e,(e=>e.state.key===n&&e.isActive));r&&r instanceof o.Eb&&t(r)}}return t.addBinding({key:"p l",onTrigger:a(e,V)}),t.addBinding({key:"a l",onTrigger:function(e,t){return()=>{o.jh.findAllObjects(e,(e=>e instanceof o.Eb&&e.isActive)).forEach((e=>{e&&e instanceof o.Eb&&t(e)}))}}(e,V)}),t.addBinding({key:"p x",onTrigger:a(e,function(){var e=$((function*(e){const t=(0,b.iD)(e);t&&s.locationService.push(t)}));return function(t){return e.apply(this,arguments)}}())}),t.addBinding({key:"t c",onTrigger:()=>{!function(e){const t=window.__grafanaSceneContext;window.__grafanaSceneContext=e}(o.jh.getTimeRange(e)),B.publish(new W)}}),t.addBinding({key:"t v",onTrigger:()=>{const t=new z({updateUrl:!1});e.publishEvent(t),B.publish(t)}}),t.addBinding({key:"d r",onTrigger:()=>o.jh.getTimeRange(e).onRefresh()}),t.addBinding({key:"t z",onTrigger:()=>{M(e)}}),t.addBinding({key:"ctrl+z",onTrigger:()=>{M(e)}}),t.addBinding({key:"t a",onTrigger:()=>{const t=(0,D.m0)(e);null==t||t.toAbsolute()}}),t.addBinding({key:"t left",onTrigger:()=>{R(e,"left")}}),t.addBinding({key:"t right",onTrigger:()=>{R(e,"right")}}),()=>{t.removeAll(),r.unsubscribe()}}function M(e){const t=(0,D.m0)(e);null==t||t.onZoom()}function R(e,t){const n=(0,D.m0)(e);n&&("left"===t&&n.onMoveBackward(),"right"===t&&n.onMoveForward())}function V(e){const t=e.state.options;var n;null!=(n=t)&&"object"==typeof n&&"legend"in n&&"boolean"==typeof t.legend.showLegend&&e.onOptionsChange({legend:{showLegend:!t.legend.showLegend}})}class W extends i.BusEventBase{}I(W,"type","copy-time");class z extends i.BusEventWithPayload{}function K(){return(K=$((function*(){const e=yield navigator.clipboard.readText();let t;try{t=JSON.parse(e);const n=(0,T.OK)(t);if(n)return{isError:!1,range:n}}catch(e){}return{isError:!0,range:e}}))).apply(this,arguments)}I(z,"type","paste-time");var H=n(5953),U=n(708),G=n(4532),Q=n(2499),q=n(9683),J=n(6464),Y=n(20);function X(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function Z(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){X(i,r,a,s,o,"next",e)}function o(e){X(i,r,a,s,o,"throw",e)}s(void 0)}))}}function ee(e){return te.apply(this,arguments)}function te(){return(te=Z((function*(e){const t=yield(0,s.getDataSourceSrv)().get((0,D.U4)(e));if(!(t instanceof s.DataSourceWithBackend))throw H.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const n=t;if(n&&n.getTagKeys){const t={filters:new J.K(e.state.filters).getJoinedLabelsFilters()},r=yield n.getTagKeys(t);return{replace:!0,values:(Array.isArray(r)?r:[]).filter((e=>!m.rm.includes(e.text)))}}return H.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}}))).apply(this,arguments)}function ne(e){return re.apply(this,arguments)}function re(){return re=Z((function*({expr:e,limit:t,sceneRef:n,scopedVars:r,timeRange:a,variableType:i}){const o=yield(0,s.getDataSourceSrv)().get((0,D.U4)(n));if(!(o instanceof s.DataSourceWithBackend))throw H.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const l=o,c=l.languageProvider,u={expr:e,limit:t,sceneRef:n,scopedVars:r,timeRange:a,variableType:i},d=l&&"function"==typeof c.fetchDetectedFields&&c.fetchDetectedFields.bind(c)||function(e){return function(e,t,n){return ae.apply(this,arguments)}(l,e)};if(d&&"function"==typeof d){const e=yield d(u);if(e instanceof Error)throw H.v.error(e,{msg:"Failed to fetch detected fields"}),e;const t=e.filter((e=>i===Y._Y?e.label===Y.e4:i===Y.sL&&e.label!==Y.e4||null!==e.parsers)).map((e=>{if(i===Y.sL){var t;let n=1===(null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers[0]:"mixed";null===e.parsers&&(n="structuredMetadata");return{group:n,meta:{parser:n,type:e.type},text:e.label,value:e.label}}return{text:e.label,value:e.label}}));return t.sort(((e,t)=>"structuredMetadata"===e.group&&"structuredMetadata"!==t.group?-1:"structuredMetadata"!==e.group&&"structuredMetadata"===t.group?1:0)),{replace:!0,values:t}}return H.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}})),re.apply(this,arguments)}function ae(){return ae=Z((function*(e,t,n){if(!("interpolateString"in e)||"function"!=typeof(null==e?void 0:e.interpolateString))throw new Error("Datasource missing interpolateString method");const r=t.expr&&"{}"!==t.expr?e.interpolateString(t.expr,t.scopedVars):void 0;if(!r)throw new Error("fetchDetectedFields requires query expression");var a;const s=null!==(a=null==t?void 0:t.timeRange)&&void 0!==a?a:(0,i.getDefaultTimeRange)(),o=e.getTimeRangeParams(s),{end:l,start:c}=o;var u;const d={end:l,limit:null!==(u=null==t?void 0:t.limit)&&void 0!==u?u:1e3,start:c};return d.query=r,new Promise(function(){var t=Z((function*(t,r){try{t((yield e.getResource("detected_fields",d,n)).fields)}catch(e){console.error("error",e),r(e)}}));return function(e,n){return t.apply(this,arguments)}}())})),ae.apply(this,arguments)}var ie=n(3241),se=n(4351),oe=n(5553);function le(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function ce(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){le(i,r,a,s,o,"next",e)}function o(e){le(i,r,a,s,o,"throw",e)}s(void 0)}))}}const ue=function(){var e=ce((function*(e,t,n,r,a,i){const o=yield(0,s.getDataSourceSrv)().get((0,D.U4)(r));if(!(o instanceof s.DataSourceWithBackend))throw H.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const l=o.languageProvider;let c=[];if(l&&l.fetchDetectedLabelValues){const r={expr:n,limit:1e3,throwError:!0,timeRange:a},s={showErrorAlert:!1};try{let n=yield l.fetchDetectedLabelValues(e.key,r,s);if(n&&(0,ie.isArray)(n)){var u;if(i===Y._Y)return{replace:!0,values:n.map((e=>({text:e})))};const r=t.state.filters;let a=[];r.forEach((e=>{var t,n;const r=null!==(n=null===(t=e.valueLabels)||void 0===t?void 0:t[0])&&void 0!==n?n:e.value;(0,U.SM)(e.operator)?r.split("|").forEach((e=>a.push(e))):a.push(r)}));const s=n.filter((e=>!a.includes(e)));if("structuredMetadata"!==(null===(u=e.meta)||void 0===u?void 0:u.parser)){if(e.value){const t=(0,oe.bu)(e,i);return{replace:!0,values:s.map((e=>({text:e,value:JSON.stringify({parser:t.parser,value:e})})))}}return{replace:!0,values:s.map((t=>{var n,r;return{text:t,value:JSON.stringify({parser:null!==(r=null===(n=e.meta)||void 0===n?void 0:n.parser)&&void 0!==r?r:"mixed",value:t})}}))}}c=s.map((e=>({text:e})))}else c=[],H.v.error(n,{msg:"fetchDetectedLabelValues error!"})}catch(e){H.v.error(e,{msg:"getDetectedFieldValuesTagValuesProvider: loki missing detected_field/.../values endpoint. Upgrade to Loki 3.3.0 or higher."}),c=[]}}else H.v.warn("getDetectedFieldValuesTagValuesProvider: fetchDetectedLabelValues is not defined in Loki datasource. Upgrade to Grafana 11.4 or higher."),c=[];return{replace:!0,values:c}}));return function(t,n,r,a,i,s){return e.apply(this,arguments)}}();function de(e,t){return pe.apply(this,arguments)}function pe(){return(pe=ce((function*(e,t){const n=yield(0,s.getDataSourceSrv)().get((0,D.U4)(e));if(!(n instanceof s.DataSourceWithBackend))throw H.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const r=n;if(r&&r.getTagValues){const n=function(e,t){let n=e.filter((e=>!((0,U.BG)(t.operator)&&e.key===t.key)));return n.some((e=>(0,U.BG)(e.operator)))||(n=[]),n}(new J.K(e.state.filters).getJoinedLabelsFilters(),t),i={filters:n,key:t.key};let s=yield r.getTagValues(i);if((0,ie.isArray)(s)){var a;s=s.filter((n=>!e.state.filters.filter((e=>e.key===t.key)).some((e=>{if((0,U.SM)(e.operator)){return e.value.split("|").some((e=>e===n.text))}return e.operator===v.w7.Equal&&e.value===n.text}))));const n=(0,se.eT)(null===(a=(0,oe.S9)(e).getValue())||void 0===a?void 0:a.toString(),t.key),r=new Set(n);n.length&&s.sort(((e,t)=>(r.has(t.text)?1:-1)-(r.has(e.text)?1:-1)))}return{replace:!0,values:s}}return H.v.error(new Error("getTagValuesProvider: missing or invalid datasource!")),{replace:!0,values:[]}}))).apply(this,arguments)}var ge=n(5548),he=n(6089),fe=n(1792);const me=()=>{const e=(0,c.useStyles2)(ve),t=(0,c.useTheme2)();return a().createElement("div",{className:e.wrap},a().createElement("div",{className:e.graphicContainer},a().createElement(fe.A,{src:(t.isDark,"/public/plugins/grafana-lokiexplore-app/img/grot_loki.svg")})),a().createElement("div",{className:e.text},a().createElement("h3",{className:e.title},"Welcome to Grafana Logs Drilldown"),a().createElement("p",null,"We noticed there is no Loki datasource configured.",a().createElement("br",null),"Add a"," ",a().createElement("a",{className:"external-link",href:i.locationUtil.assureBaseUrl("/connections/datasources/new")},"Loki datasource")," ","to view logs."),a().createElement("br",null),a().createElement("p",null,"Click"," ",a().createElement("a",{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/",target:"_blank",className:"external-link",rel:"noreferrer"},"here")," ","to learn more...")))},ve=e=>({graphicContainer:(0,he.css)({[e.breakpoints.up("md")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"300px"},[e.breakpoints.up("lg")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"400px"},display:"flex",height:"250px",justifyContent:"center",margin:"0 auto",padding:e.spacing(1),width:"200px"}),text:(0,he.css)({alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center"}),title:(0,he.css)({marginBottom:"1.5rem"}),wrap:(0,he.css)({[e.breakpoints.up("md")]:{flexDirection:"row",margin:"4rem auto auto auto"},alignItems:"center",display:"flex",flexDirection:"column",margin:"0 auto auto auto",padding:"2rem",textAlign:"center"})});var be,ye,Se,we=n(6991),Oe=n(7352),xe=n(173);class Ee extends o.Bs{}Se=function({model:e}){var t,n;const r=o.jh.getVariables(e).useState();let i=r.variables;return(null===(t=e.state.include)||void 0===t?void 0:t.length)&&(i=r.variables.filter((t=>{var n,r;return null===(n=e.state.include)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:"")}))),(null===(n=e.state.exclude)||void 0===n?void 0:n.length)&&(i=r.variables.filter((t=>{var n,r;return!(null===(n=e.state.exclude)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:""))}))),a().createElement(a().Fragment,null,i.map((t=>a().createElement(o.Lp,{key:t.state.key,variable:t,layout:e.state.layout}))))},(ye="Component")in(be=Ee)?Object.defineProperty(be,ye,{value:Se,enumerable:!0,configurable:!0,writable:!0}):be[ye]=Se;var Ce=n(9598);function ke(e){const t=(0,c.useStyles2)(Pe);return a().createElement(a().Fragment,null,a().createElement(c.Alert,{className:t.alert,severity:"info",title:"Welcome to Grafana Logs Drilldown!",onRemove:e.onRemove},a().createElement("div",null,"Check out our"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/",rel:"noreferrer"},"Get started doc"),", or see"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/releases",rel:"noreferrer"},"recent changes"),".",a().createElement("br",null),"Help us shape the future of the app."," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://forms.gle/1sYWCTPvD72T1dPH9",rel:"noreferrer"},"Send us feedback")," ","or engage with us on"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/?tab=readme-ov-file#explore-logs",rel:"noreferrer"},"GitHub"),".")))}function Pe(e){return{alert:(0,he.css)({flex:"none"})}}var je=n(2085),Fe=n(1459);const Le=()=>{const e=(0,c.useStyles2)(_e);return a().createElement("div",{className:e.wrapper},a().createElement("a",{href:"https://forms.gle/1sYWCTPvD72T1dPH9",className:e.feedback,title:"Share your thoughts about Logs in Grafana.",target:"_blank",rel:"noreferrer noopener"},a().createElement(c.Icon,{name:"comment-alt-message"})," Give feedback"))},_e=e=>({feedback:(0,he.css)({"&:hover":{color:e.colors.text.link},alignSelf:"center",color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize}),wrapper:(0,he.css)({display:"flex",gap:e.spacing(1),marginLeft:"auto",position:"relative",top:e.spacing(-1)})});var Te=n(7478),De=n(3571);const Ne=({onRemove:e,pattern:t,size:n="lg"})=>{const i=(0,c.useStyles2)(Be),[s,o]=(0,r.useState)(!1);return a().createElement("div",{className:i.pattern,onClick:()=>o(!s),onMouseLeave:()=>o(!1),onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||o(!s)},role:"button",tabIndex:0},a().createElement(c.Tag,{title:t,key:t,name:s?t:Ie(t,n),className:i.tag}),a().createElement(c.Button,{"aria-label":"Remove pattern","data-testid":De.b.exploreServiceDetails.buttonRemovePattern,variant:"secondary",size:"sm",className:i.removeButton,onClick:e},a().createElement(c.Icon,{name:"times"})))},$e={lg:Math.round(window.innerWidth/8),sm:50};function Ie(e,t){const n=e.length;if(n<$e[t])return e;const r=Math.round(.4*$e[t]);return`${e.substring(0,r)} … ${e.substring(n-r)}`}const Be=e=>({pattern:(0,he.css)({cursor:"pointer",display:"flex",fontFamily:"monospace",gap:e.spacing(.25),overflow:"hidden"}),removeButton:(0,he.css)({paddingLeft:2.5,paddingRight:2.5}),tag:(0,he.css)({backgroundColor:e.colors.secondary.main,border:`solid 1px ${e.colors.secondary.border}`,borderBottomRightRadius:0,borderTopRightRadius:0,boxSizing:"border-box",color:e.colors.secondary.text,overflow:"hidden",padding:e.spacing(.25,.75),textOverflow:"ellipsis"})}),Ae=({onRemove:e,patterns:t})=>{const n=(0,c.useStyles2)(Me);if(!t||0===t.length)return null;const r=t.filter((e=>"include"===e.type)),i=t.filter((e=>"include"!==e.type)),s=n=>{(0,Te.bN)(),e(t.filter((e=>e!==n))),(0,d.EE)(d.NO.service_details,d.ir.service_details.pattern_removed,{excludePatternsLength:i.length-("include"!==(null==n?void 0:n.type)?1:0),includePatternsLength:r.length-("include"===(null==n?void 0:n.type)?1:0),type:n.type})};return a().createElement("div",null,r.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(c.Text,{variant:"bodySmall",weight:"bold","data-testid":De.b.patterns.buttonIncludedPattern},"Included pattern",t.length>1?"s":""),a().createElement("div",{className:n.patterns},r.map((e=>a().createElement(Ne,{key:e.pattern,pattern:e.pattern,size:"lg",onRemove:()=>s(e)}))))),i.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(c.Text,{variant:"bodySmall",weight:"bold","data-testid":De.b.patterns.buttonExcludedPattern},"Excluded pattern",i.length>1?"s":"",":"),a().createElement("div",{className:n.patterns},i.map((e=>a().createElement(Ne,{key:e.pattern,pattern:e.pattern,size:i.length>1?"sm":"lg",onRemove:()=>s(e)}))))))};function Me(e){return{patterns:(0,he.css)({alignItems:"center",display:"flex",flexWrap:"wrap",gap:e.spacing(1)}),patternsContainer:(0,he.css)({overflow:"hidden"})}}class Re extends o.Bs{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Re,"Component",(({model:e})=>{const t=o.jh.getAncestor(e,at),{controls:n,patterns:r}=t.useState(),i=o.jh.getAncestor(e,Qe),{levelsRenderer:s,lineFilterRenderer:l}=i.useState(),u=(0,c.useStyles2)((t=>function(e){return{controlsContainer:(0,he.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),label:"controlsContainer",padding:e.spacing(2)}),controlsFirstRowContainer:(0,he.css)({[e.breakpoints.down("md")]:{flexDirection:"column-reverse"},alignItems:"flex-start",display:"flex",gap:e.spacing(2),justifyContent:"space-between",label:"controls-first-row"}),controlsRowContainer:(0,he.css)({[e.breakpoints.down("lg")]:{flexDirection:"column"},"&:empty":{display:"none"},alignItems:"flex-start",display:"flex",gap:e.spacing(2),label:"controls-row"}),controlsWrapper:(0,he.css)({display:"flex",flexDirection:"column",label:"controlsWrapper",marginTop:e.spacing(.375)}),filters:(0,he.css)({display:"flex",label:"filters"}),filtersWrap:(0,he.css)({alignItems:"flex-end",display:"flex",flexWrap:"wrap",gap:e.spacing(2),label:"filtersWrap",width:"calc(100% - 450)"}),firstRowWrapper:(0,he.css)({"& > div > div":{[e.breakpoints.down("lg")]:{flexDirection:"column"},gap:"16px",label:"first-row-wrapper"}}),stickyControlsContainer:(0,he.css)({background:e.colors.background.canvas,boxShadow:e.shadows.z1,gap:e.spacing(0),left:0,position:"sticky",top:Ve,zIndex:e.zIndex.navbarFixed}),timeRange:(0,he.css)({display:"flex",flexDirection:"row",gap:e.spacing(1),label:"timeRange"}),timeRangeDatasource:(0,he.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(1),justifyContent:"flex-end",label:"timeRangeDatasource"})}}(t,e.state.position)));return a().createElement("div",{className:(0,he.cx)(u.controlsContainer,"sticky"===e.state.position?u.stickyControlsContainer:void 0)},a().createElement(a().Fragment,null,n&&a().createElement("div",{className:u.controlsFirstRowContainer},a().createElement("div",{className:u.filtersWrap},a().createElement("div",{className:(0,he.cx)(u.filters,u.firstRowWrapper)},n.map((e=>e instanceof o.G1?a().createElement(e.Component,{key:e.state.key,model:e}):null)))),a().createElement("div",{className:u.controlsWrapper},a().createElement(Le,null),a().createElement("div",{className:u.timeRangeDatasource},n.map((e=>e.state.key===Ge?a().createElement(e.Component,{key:e.state.key,model:e}):null)),a().createElement("div",{className:u.timeRange},n.map((e=>e instanceof Ee||e instanceof o.G1?null:a().createElement(e.Component,{key:e.state.key,model:e}))))))),a().createElement("div",{className:u.controlsRowContainer},s&&a().createElement(s.Component,{model:s}),n&&a().createElement("div",{className:u.filtersWrap},a().createElement("div",{className:u.filters},n.map((e=>e instanceof Ee&&e.state.key===He?a().createElement(e.Component,{key:e.state.key,model:e}):null))))),(0,se.Qi)()&&a().createElement("div",{className:u.controlsRowContainer},n&&a().createElement("div",{className:u.filtersWrap},a().createElement("div",{className:u.filters},n.map((e=>e instanceof Ee&&e.state.key===Ue?a().createElement(e.Component,{key:e.state.key,model:e}):null))))),a().createElement("div",{className:u.controlsRowContainer},a().createElement(Ae,{patterns:r,onRemove:e=>t.setState({patterns:e})})),a().createElement("div",{className:u.controlsRowContainer},l&&a().createElement(l.Component,{model:l}))))}));const Ve=40;function We(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ze(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Ke=`${Ce.s_}.interceptBannerStorageKey`,He="vars-fields-metadata",Ue="vars-json-fields",Ge="vars-ds";class Qe extends o.Bs{onActivate(){const e=(0,q.FT)();this.setState({levelsRenderer:new je.qV({}),lineFilterRenderer:new Fe.Y({}),variableLayout:new Re({position:e===h.G3.explore?"sticky":"relative"})})}dismiss(){this.setState({interceptDismissed:!0}),localStorage.setItem(Ke,"true")}constructor(e){super(ze(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){We(e,t,n[t])}))}return e}({},e),{interceptDismissed:!!localStorage.getItem(Ke)})),this.addActivationHandler(this.onActivate.bind(this))}}function qe(e){return{body:(0,he.css)({display:"flex",flexDirection:"column",flexGrow:1,gap:e.spacing(1),label:"body-wrapper",padding:`0 ${e.spacing(2)} ${e.spacing(2)}`}),bodyContainer:(0,he.css)({display:"flex",flexDirection:"column",flexGrow:1,minHeight:"100%"}),container:(0,he.css)({display:"flex",flexDirection:"column",flexGrow:1,maxWidth:"100vw",minHeight:"100%"}),controlsContainer:(0,he.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),label:"controlsContainer"})}}We(Qe,"Component",(({model:e})=>{const t=o.jh.getAncestor(e,at),{contentScene:n}=t.useState(),{interceptDismissed:r,variableLayout:i}=e.useState();if(!n)return H.v.warn("content scene not defined"),null;const s=(0,c.useStyles2)(qe);return a().createElement("div",{className:s.bodyContainer},a().createElement("div",{className:s.container},!r&&a().createElement(ke,{onRemove:()=>{e.dismiss()}}),i&&a().createElement(i.Component,{model:i}),a().createElement("div",{className:s.body},n&&a().createElement(n.Component,{model:n}))))}));var Je=n(7796),Ye=n(9731),Xe=n(7985);function Ze(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function et(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){Ze(i,r,a,s,o,"next",e)}function o(e){Ze(i,r,a,s,o,"throw",e)}s(void 0)}))}}function tt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function nt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const rt="showLogsButtonScene";class at extends o.Bs{onActivate(){const e={};this.setVariableProviders();var t;(o.jh.findByKeyAndType(this,rt,Je.H).setState({hidden:!1}),this.state.contentScene)||(e.contentScene=function(e){if((0,q.FT)()===h.G3.explore)return new xe.y({});return new Oe.Mn({drillDownLabel:e})}(null===(t=this.state.routeMatch)||void 0===t?void 0:t.params.breakdownLabel));this.setTagProviders(),this.setState(e),this.updatePatterns(this.state,(0,oe.Ku)(this)),this.resetVariablesIfNotInUrl((0,oe.ir)(this),(0,oe.n5)(Y.mB)),this.resetVariablesIfNotInUrl((0,oe.iw)(this),(0,oe.n5)(Y._Y)),this._subs.add(this.subscribeToState((e=>{this.updatePatterns(e,(0,oe.Ku)(this))})));const n=o.jh.getTimeRange(this);this._subs.add(n.subscribeToState(this.limitMaxInterval(n))),this._subs.add(this.subscribeToEvent(z,this.subscribeToPasteTimeEvent));const r=(0,oe.ir)(this).state.filters,a=(0,oe.oY)(this).state.filters,i=(0,oe.YS)(this);i.updateFilters([...a,...r]),this._subs.add(i.subscribeToState(this.subscribeToCombinedFieldsVariable));const s=A(this);return()=>{s()}}setTagProviders(){this.setLabelsProviders()}setLabelsProviders(){const e=(0,oe.cR)(this);e._getOperators=()=>(0,ge.Ht)(e),e.setState({getTagKeysProvider:ee,getTagValuesProvider:de})}limitMaxInterval(e){return(t,n)=>{const{jsonData:r}=u.plugin.meta;if(null==r?void 0:r.interval)try{var a;const l=i.rangeUtil.intervalToSeconds(null!==(a=null==r?void 0:r.interval)&&void 0!==a?a:"");if(!l)return;const c=t.value.to.diff(t.value.from,"seconds");if(c>l){if(c<=n.value.to.diff(n.value.from,"seconds"))e.setState({from:n.from,to:n.to,value:n.value});else{const t=new o.JZ(we.sp);e.setState({from:t.state.from,to:t.state.to,value:t.state.value})}(0,s.getAppEvents)().publish({payload:["Time range interval exceeds maximum interval configured by the administrator."],type:i.AppEvents.alertWarning.name}),(0,d.EE)("all","interval_too_long",{attempted_duration_seconds:c,configured_max_interval:l})}}catch(e){console.error(e)}}}setVariableProviders(){const e=(0,oe.iw)(this),t=(0,oe.YS)(this);t._getOperators=()=>(0,ge.Ht)(t),e.setState({getTagKeysProvider:this.getLevelsTagKeysProvider(),getTagValuesProvider:this.getLevelsTagValuesProvider()}),t.setState({getTagKeysProvider:this.getCombinedFieldsTagKeysProvider(),getTagValuesProvider:this.getCombinedFieldsTagValuesProvider()})}getCombinedFieldsTagKeysProvider(){return(e,t)=>{const n=(0,oe.oY)(this),r=(0,oe.ir)(this),a=(0,f.O)(Y.sL),i=n.state.filters.filter((e=>e.key!==t)),s=r.state.filters.filter((e=>e.key!==t)),l=this.renderVariableFilters(Y.mB,s),c=this.renderVariableFilters(Y._P,i),u=a.replace(Y.Gd,l).replace(Y.w0,c);return ne({expr:o.jh.interpolate(this,u),sceneRef:this,timeRange:o.jh.getTimeRange(this).state.value,variableType:Y.sL})}}getCombinedFieldsTagValuesProvider(){return(e,t)=>{const n=(0,f.O)(Y.sL),r=(0,oe.oY)(this),a=(0,oe.ir)(this),i=r.state.filters.filter((e=>e.key!==t.key&&(0,U.BG)(e.operator))),s=a.state.filters.filter((e=>e.key!==t.key&&(0,U.BG)(e.operator))),l=this.renderVariableFilters(Y.mB,s),c=this.renderVariableFilters(Y._P,i),u=n.replace(Y.Gd,l).replace(Y.w0,c),d=(0,Xe.Sh)(this,u);return ue(t,e,d,this,o.jh.getTimeRange(this).state.value,Y.sL)}}getLevelsTagKeysProvider(){return(e,t)=>{const n=e.state.filters.filter((e=>e.key!==t)),r=this.renderVariableFilters(Y._Y,n),a=(0,f.O)(Y._Y).replace(Y.Gd,r);return ne({expr:o.jh.interpolate(this,a),sceneRef:this,timeRange:o.jh.getTimeRange(this).state.value,variableType:Y._Y})}}getLevelsTagValuesProvider(){return(e,t)=>{const n=e.state.filters.filter((e=>e.key!==t.key&&e.operator===v.w7.Equal)),r=this.renderVariableFilters(Y._Y,n),a=(0,f.O)(Y._Y).replace(Y.Gd,r),i=(0,Xe.Sh)(this,a);return ue(t,e,i,this,o.jh.getTimeRange(this).state.value,Y._Y)}}renderVariableFilters(e,t){if(e===Y.mB)return(0,Xe.ZX)(t);if(e===Y._P)return(0,Xe.E3)(t);if(e===Y._Y)return(0,Xe.E3)(t);{const e=new Error("getFieldsTagValuesProvider only supports fields, metadata, and levels");throw H.v.error(e),e}}resetVariablesIfNotInUrl(e,t){const n=s.locationService.getLocation();null===new URLSearchParams(n.search).get(t)&&e.setState({filters:[]})}updatePatterns(e,t){var n;const r=(0,Q.M)(null!==(n=e.patterns)&&void 0!==n?n:[]);t.changeValueTo(r)}getUrlState(){return{patterns:JSON.stringify(this.state.patterns)}}updateFromUrl(e){const t={};e.patterns&&"string"==typeof e.patterns&&(t.patterns=JSON.parse(e.patterns)),this.setState(t)}constructor(e){var t,n;const{unsub:r,variablesScene:a}=function(e,t){const n=new o.H9({allowCustomValue:!0,datasource:Y.eL,expressionBuilder:Xe.VW,filters:null!=t?t:[],hide:l.zL.dontHide,key:"adhoc_service_filter",label:"Labels",layout:"combobox",name:Y.MB,onAddCustomValue:Xe.c0});n._getOperators=function(){return G.II};const r=new o.H9({allowCustomValue:!0,applyMode:"manual",expressionBuilder:Xe.ZX,hide:l.zL.hideVariable,label:"Detected fields",layout:"combobox",name:Y.mB});r._getOperators=()=>G.II;const a=new o.H9({allowCustomValue:!0,applyMode:"manual",expressionBuilder:e=>(0,Xe.E3)(e),hide:l.zL.hideVariable,label:"Metadata",layout:"combobox",name:Y._P});a._getOperators=()=>G.II;const i=new o.H9({allowCustomValue:!0,applyMode:"manual",hide:l.zL.hideVariable,label:"Fields",layout:"combobox",name:Y.sL,onAddCustomValue:Xe.PP,skipUrlSync:!0}),s=new o.H9({applyMode:"manual",expressionBuilder:Xe._q,hide:l.zL.hideVariable,label:"Error levels",layout:"vertical",name:Y._Y,supportsMultiValueOperators:!0}),c=new o.H9({expressionBuilder:Xe.CY,getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),hide:l.zL.hideVariable,layout:"horizontal",name:Y.NW});c._getOperators=()=>G.eb;const u=new o.mI({label:"Data source",name:Y.EY,pluginId:"loki",value:e}),d=u.subscribeToState((e=>{const t=`${e.value}`;e.value&&(0,se.ke)(t)})),p=new o.H9({allowCustomValue:!0,expressionBuilder:(0,Xe.Hs)(),getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),name:Y.lV}),h=new o.H9({allowCustomValue:!0,expressionBuilder:(0,Xe.tR)(),getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),layout:"horizontal",name:Y.pw});return{unsub:d,variablesScene:new o.Pj({variables:[h,u,n,r,s,a,p,i,new o.yP({hide:l.zL.hideVariable,name:Y.uw,value:""}),new o.H9({expressionBuilder:Xe.CY,hide:l.zL.hideVariable,name:Y.WM}),c,new g.m({hide:l.zL.hideVariable,name:Y.QE,options:[{label:Y.YN,value:Y.YN}],skipUrlSync:!0,value:Y.YN})]})}}(null!==(n=(0,se.QB)())&&void 0!==n?n:"grafanacloud-logs",e.initialFilters),c=[new o.G1({children:[new o.vA({body:new Ee({include:[Y.MB],key:"vars-labels",layout:"vertical"})}),new Je.H({disabled:!0,key:rt})],direction:"row",key:"vars-row__datasource-labels-timepicker-button"}),new Ee({include:[Y._P],key:"vars-metadata",layout:"vertical"}),new Ee({include:[Y.mB],key:"vars-fields",layout:"vertical"}),new Ee({include:[Y.EY],key:Ge,layout:"horizontal"}),new Ee({include:[Y.sL],key:He,layout:"vertical"}),new Ee({include:[Y.lV,Y.pw],key:Ue,layout:"vertical"}),new o.KE({key:"vars-timepicker"}),new o.WM({key:"vars-refresh"})];var u,d,h;"explore"===(0,q.FT)()&&s.config.featureToggles.exploreLogsAggregatedMetrics&&c.push(new Ye.s({isOpen:!1,key:"vars-toolbar"})),super(nt(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){tt(e,t,n[t])}))}return e}({$timeRange:null!==(u=e.$timeRange)&&void 0!==u?u:new o.JZ({}),$variables:null!==(d=e.$variables)&&void 0!==d?d:a,controls:null!==(h=e.controls)&&void 0!==h?h:c,patterns:[]},e),{body:new Qe({})})),t=this,tt(this,"_urlSync",new o.So(this,{keys:["patterns"]})),tt(this,"subscribeToCombinedFieldsVariable",((e,t)=>{if(!(0,p.B)(e.filters,null==t?void 0:t.filters)){const t=e.filters.filter((e=>(0,m.OH)(e))),n=e.filters.filter((e=>!(0,m.OH)(e)));(0,oe.ir)(this).updateFilters(n),(0,oe.oY)(this).updateFilters(t)}})),tt(this,"subscribeToPasteTimeEvent",et((function*(){const e=yield function(){return K.apply(this,arguments)}();if(e.isError)return;const n=o.jh.getTimeRange(t),r="string"==typeof e.range.to?e.range.to:void 0,a="string"==typeof e.range.from?e.range.from:void 0,s=i.rangeUtil.convertRawToRange(e.range);n&&s?n.setState({from:a,to:r,value:s}):H.v.error(new Error("Invalid time range from clipboard"),{from:null!=a?a:"",msg:"Invalid time range from clipboard",sceneTimeRange:typeof n,to:null!=r?r:""})}))),this._subs.add(r),this.addActivationHandler(this.onActivate.bind(this)),(0,D.hJ)(this).then((e=>{this.setState({ds:e})}))}}tt(at,"Component",(({model:e})=>{const{body:t}=e.useState();return(0,oe.S9)(e).state.options.length?t?a().createElement(t.Component,{model:t}):a().createElement(c.LoadingPlaceholder,{text:"Loading..."}):a().createElement(me,null)}))},2085:(e,t,n)=>{n.d(t,{dm:()=>b,kz:()=>m,qV:()=>v});var r=n(5959),a=n.n(r),i=n(6089),s=n(9736),o=n(2007),l=n(6854),c=n(7478),u=n(3571),d=n(5553),p=n(20);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const m="levels-var-custom-renderer";class v extends s.Bs{onActivate(){this.onFilterChange(),this._subs.add((0,d.iw)(this).subscribeToEvent(s.oh,(()=>{this.onFilterChange()})))}onFilterChange(){const e=(0,d.iw)(this);this.setState({options:e.state.filters.map((e=>{var t,n;return{selected:!0,text:null!==(n=null===(t=e.valueLabels)||void 0===t?void 0:t[0])&&void 0!==n?n:e.value,value:e.value}}))})}constructor(e){super(f(h({},e),{isLoading:!1,isOpen:!1,key:m,visible:!1})),g(this,"getTagValues",(()=>{var e,t;this.setState({isLoading:!0});const n=(0,d.iw)(this);var r;const a=null==n||null===(t=n.state)||void 0===t||null===(e=t.getTagValuesProvider)||void 0===e?void 0:e.call(t,n,null!==(r=n.state.filters[0])&&void 0!==r?r:{key:p.e4});null==a||a.then((e=>{Array.isArray(e.values)&&this.setState({isLoading:!1,options:e.values.map((e=>{var t;return{selected:n.state.filters.some((t=>t.value===e.text)),text:e.text,value:null!==(t=e.value)&&void 0!==t?t:e.text}}))})}))})),g(this,"updateFilters",((e,t)=>{var n;const r=(0,d.iw)(this),a=null===(n=this.state.options)||void 0===n?void 0:n.filter((e=>e.selected));var i;r.updateFilters(null!==(i=null==a?void 0:a.map((e=>({key:p.e4,operator:l.w7.Equal,value:e.text}))))&&void 0!==i?i:[],{forcePublish:t,skipPublish:e})})),g(this,"onChangeOptions",(e=>{var t;(0,c.bN)(),this.setState({options:null===(t=this.state.options)||void 0===t?void 0:t.map((t=>e.some((e=>e.value===t.value))?f(h({},t),{selected:!0}):f(h({},t),{selected:!1})))}),this.state.isOpen?this.updateFilters(!0):this.updateFilters(!1)})),g(this,"openSelect",(e=>{this.setState({isOpen:e})})),g(this,"onCloseMenu",(()=>{this.openSelect(!1),this.updateFilters(!1,!0)})),this.addActivationHandler(this.onActivate.bind(this))}}function b(e){const t=s.jh.findObject(e,(e=>e instanceof v));t instanceof v&&t.onFilterChange()}g(v,"Component",(({model:e})=>{const{isLoading:t,isOpen:n,options:r,visible:i}=e.useState(),l=(0,o.useStyles2)(y);return(0,d.iw)(e).useState(),i?a().createElement("div",{"data-testid":u.b.variables.levels.inputWrap,className:l.wrapper},a().createElement(s.Zx,{layout:"vertical",label:"Log levels"}),a().createElement(o.MultiSelect,{"aria-label":"Log level filters",prefix:a().createElement(o.Icon,{size:"lg",name:"filter"}),placeholder:"All levels",className:l.control,onChange:e.onChangeOptions,onCloseMenu:()=>e.onCloseMenu(),onOpenMenu:e.getTagValues,onFocus:()=>e.openSelect(!0),menuShouldPortal:!0,isOpen:n,isLoading:t,isClearable:!0,blurInputOnSelect:!1,closeMenuOnSelect:!1,openMenuOnFocus:!0,showAllSelectedWhenOpen:!0,hideSelectedOptions:!1,value:null==r?void 0:r.filter((e=>e.selected)),options:null==r?void 0:r.map((e=>({label:e.text,value:e.value})))})):null}));const y=()=>({control:(0,i.css)({flex:"1"}),wrapper:(0,i.css)({flex:"0 0 auto",whiteSpace:"nowrap"})})},1459:(e,t,n)=>{n.d(t,{Y:()=>w,F:()=>O});var r=n(5959),a=n.n(r),i=n(6089),s=n(3241),o=n(9736),l=n(2007),c=n(4509),u=n(6854),d=n(7478),p=n(5553),g=n(72);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function m({onClick:e,props:t}){const[n,i]=(0,r.useState)(!1),s=(0,l.useStyles2)(v);return a().createElement(a().Fragment,null,a().createElement("span",null,a().createElement("div",{className:s.titleWrap},a().createElement("span",null,"Line filter"),a().createElement(l.IconButton,{onClick:e,name:"times",size:"xs","aria-label":"Remove line filter"})),a().createElement("span",{className:s.collapseWrap},a().createElement(g._,f(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){h(e,t,n[t])}))}return e}({},t),{focus:n,setFocus:i,type:"variable"})),n&&a().createElement(l.IconButton,{className:s.collapseBtn,tooltip:"Collapse",size:"lg","aria-label":"Collapse filter",onClick:()=>i(!1),name:"table-collapse-all"}))))}const v=e=>({collapseBtn:(0,i.css)({marginLeft:e.spacing(1)}),collapseWrap:(0,i.css)({display:"flex"}),titleWrap:(0,i.css)({display:"flex",fontSize:e.typography.bodySmall.fontSize,gap:e.spacing(1),marginBottom:e.spacing(.5)})});function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}function S(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class w extends o.Bs{isFilterExclusive({operator:e}){return e===u.cK.negativeMatch||e===u.cK.negativeRegex}updateFilter(e,t,n=!0){n?(this.updateVariableLineFilter(e,t,!0),this.updateVariableDebounced(e,t,!1,!0)):this.updateVariableLineFilter(e,t)}constructor(...e){super(...e),b(this,"handleEnter",((e,t,n)=>{"Enter"===e.key&&((0,d.bN)(),this.updateVariableLineFilter(n,S(y({},n),{value:t})))})),b(this,"onRegexToggle",(e=>{let t;switch(e.operator){case u.cK.match:t=u.cK.regex;break;case u.cK.negativeMatch:t=u.cK.negativeRegex;break;case u.cK.regex:t=u.cK.match;break;case u.cK.negativeRegex:t=u.cK.negativeMatch;break;default:throw new Error("Invalid operator!")}this.updateFilter(e,S(y({},e),{operator:t}),!1)})),b(this,"onToggleExclusive",(e=>{let t;switch(e.operator){case u.cK.match:t=u.cK.negativeMatch;break;case u.cK.negativeMatch:t=u.cK.match;break;case u.cK.regex:t=u.cK.negativeRegex;break;case u.cK.negativeRegex:t=u.cK.regex;break;default:throw new Error("Invalid operator!")}this.updateFilter(e,S(y({},e),{operator:t}),!1)})),b(this,"onCaseSensitiveToggle",(e=>{const t=e.key===u.ld.caseSensitive?u.ld.caseInsensitive:u.ld.caseSensitive;this.updateFilter(e,S(y({},e),{key:t}),!1)})),b(this,"onInputChange",((e,t)=>{this.updateFilter(t,S(y({},t),{value:e.target.value}),!0)})),b(this,"removeFilter",(e=>{(0,d.bN)();const t=(0,p.Gk)(this),n=t.state.filters.filter((t=>void 0!==t.keyLabel&&t.keyLabel!==e.keyLabel));t.setState({filters:n})})),b(this,"updateVariableLineFilter",((e,t,n=!1,r=!1)=>{const a=(0,p.Gk)(this),i=a.state.filters.filter((t=>void 0!==t.keyLabel&&t.keyLabel!==e.keyLabel));a.updateFilters([{key:t.key,keyLabel:e.keyLabel,operator:t.operator,value:t.value},...i],{forcePublish:r,skipPublish:n}),(0,c.EE)(c.NO.service_details,c.ir.service_details.search_string_in_variables_changed,{caseSensitive:t.key,containsLevel:e.value.toLowerCase().includes("level"),operator:t.operator,searchQueryLength:e.value.length})})),b(this,"updateVariableDebounced",(0,s.debounce)(((e,t,n=!1,r=!1)=>{this.updateVariableLineFilter(e,t,n,r)}),1e3))}}function O(e){e.sort(((e,t)=>{var n,r;return parseInt(null!==(n=e.keyLabel)&&void 0!==n?n:"0",10)-parseInt(null!==(r=t.keyLabel)&&void 0!==r?r:"0",10)}))}function x(e){return{lineFiltersWrap:(0,i.css)({display:"flex",flexWrap:"wrap",gap:`${e.spacing(.25)} ${e.spacing(2)}`,label:"lineFiltersWrap"})}}b(w,"Component",(({model:e})=>{const t=(0,p.Gk)(e),{filters:n}=t.useState(),r=(0,l.useStyles2)(x);return O(n),n.length?a().createElement("div",{className:r.lineFiltersWrap},n.map((t=>{const n={caseSensitive:t.key===u.ld.caseSensitive,exclusive:e.isFilterExclusive(t),handleEnter:(n,r)=>e.handleEnter(n,t.value,t),lineFilter:t.value,onCaseSensitiveToggle:()=>e.onCaseSensitiveToggle(t),onInputChange:n=>e.onInputChange(n,t),onRegexToggle:()=>e.onRegexToggle(t),regex:t.operator===u.cK.regex||t.operator===u.cK.negativeRegex,setExclusive:()=>e.onToggleExclusive(t),updateFilter:(n,r)=>e.updateFilter(t,S(y({},t),{value:n}),r)};return a().createElement(m,{key:t.keyLabel,onClick:()=>e.removeFilter(t),props:n})}))):null}))},7796:(e,t,n)=>{n.d(t,{H:()=>g});var r=n(5959),a=n.n(r),i=n(6089),s=n(9736),o=n(2007),l=n(7478),c=n(708),u=n(3571),d=n(5553);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends s.Bs{onActivate(){const e=(0,d.cR)(this),t=e.state.filters.some((e=>(0,c.BG)(e.operator)));this.setState({disabled:!t}),e.subscribeToState((e=>{const t=e.filters.some((e=>(0,c.BG)(e.operator)));this.setState({disabled:!t})}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}({},e)),p(this,"getLink",(()=>{const e=(0,d.cR)(this).state.filters.find((e=>(0,c.BG)(e.operator)));return e?(0,l.k9)(e.key,e.value):""})),this.addActivationHandler(this.onActivate.bind(this))}}function h(e){return{button:(0,i.css)({[e.breakpoints.down("lg")]:{alignSelf:"flex-end"},[e.breakpoints.down("md")]:{alignSelf:"flex-start",marginTop:e.spacing(1)},alignSelf:"flex-start",marginTop:"22px"})}}p(g,"Component",(({model:e})=>{const{disabled:t,hidden:n}=e.useState(),r=(0,o.useStyles2)(h);if(!0===n)return null;const i=e.getLink();return a().createElement(o.LinkButton,{"data-testid":u.b.index.header.showLogsButton,disabled:t||!i,fill:"outline",className:r.button,href:i},"Show logs")}))},9731:(e,t,n)=>{n.d(t,{s:()=>f});var r=n(5959),a=n.n(r),i=n(6089),s=n(8531),o=n(9736),l=n(2007),c=n(2533),u=n(4509),d=n(3571),p=n(173);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const h=`${c.id}.serviceSelection.aggregatedMetrics`;class f extends o.Bs{constructor(e){const t=localStorage.getItem(h),n=s.config.featureToggles.exploreLogsAggregatedMetrics&&"false"!==t;var r;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({isOpen:!1,options:{aggregatedMetrics:{active:null!=n&&n,disabled:!1,userOverride:null!==(r="true"===t)&&void 0!==r&&r}}},e)),g(this,"toggleAggregatedMetricsOverride",(()=>{const e=!this.state.options.aggregatedMetrics.active;(0,u.EE)(u.NO.service_selection,u.ir.service_selection.aggregated_metrics_toggled,{enabled:e}),localStorage.setItem(h,e.toString()),this.setState({options:{aggregatedMetrics:{active:e,disabled:this.state.options.aggregatedMetrics.disabled,userOverride:e}}})})),g(this,"onToggleOpen",(e=>{this.setState({isOpen:e})}))}}function m(e){return{heading:(0,i.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,i.css)({alignItems:"center",columnGap:e.spacing(2),display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1)}),popover:(0,i.css)({background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,display:"flex",flexDirection:"column",marginRight:e.spacing(2),padding:e.spacing(2)})}}g(f,"Component",(({model:e})=>{const{isOpen:t,options:n}=e.useState(),r=(0,l.useStyles2)(m),i=()=>a().createElement("div",{className:r.popover,role:"dialog","aria-modal":"true","aria-label":"Query options",onClick:e=>e.stopPropagation()},a().createElement("div",{className:r.heading},"Query options"),a().createElement("div",{className:r.options},a().createElement("div",{title:"Aggregated metrics will return service queries results much more quickly, but with lower resolution"},"Aggregated metrics"),a().createElement("span",{title:n.aggregatedMetrics.disabled?`Aggregated metrics can only be enabled for queries starting after ${p.X.toLocaleString()}`:""},a().createElement(l.Switch,{label:"Toggle aggregated metrics","data-testid":d.b.index.aggregatedMetricsToggle,value:n.aggregatedMetrics.active,disabled:n.aggregatedMetrics.disabled,onChange:e.toggleAggregatedMetricsOverride}))));return n.aggregatedMetrics?a().createElement(l.Dropdown,{overlay:i,placement:"bottom",onVisibleChange:e.onToggleOpen},a().createElement(l.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:t,"data-testid":d.b.index.aggregatedMetricsMenu})):a().createElement(a().Fragment,null)}))},6991:(e,t,n)=>{n.d(t,{Oo:()=>f,c:()=>h,sp:()=>p});var r=n(7781),a=n(9736),i=n(7839),s=n(5953),o=n(7478),l=n(9598),c=n(9683),u=n(5002),d=n(2661);const p={from:"now-15m",to:"now"};function g(e){return new a.P1({body:new d.P({$timeRange:new a.JZ(p),routeMatch:e})})}function h(){return new a.jD({drilldowns:[{defaultRoute:!0,getPage:(e,t)=>m(e,t,i.G3.logs),routePath:c.HU.logs},{getPage:(e,t)=>m(e,t,i.G3.labels),routePath:c.HU.labels},{getPage:(e,t)=>m(e,t,i.G3.patterns),routePath:c.HU.patterns},{getPage:(e,t)=>m(e,t,i.G3.fields),routePath:c.HU.fields},{getPage:(e,t)=>v(e,t,i._J.label),routePath:c.KL.label},{getPage:(e,t)=>v(e,t,i._J.field),routePath:c.KL.field},{getPage:()=>f(),routePath:"*"}],getScene:e=>g(e),layout:r.PageLayoutType.Custom,preserveUrlKeys:c.Zt,routePath:`${i.G3.explore}/*`,title:"Grafana Logs Drilldown",url:(0,l._F)(i.G3.explore)})}function f(){return new a.jD({$behaviors:[()=>{(0,o.Ns)()}],getScene:()=>new a.P1({body:new a.G1({children:[],direction:"column"})}),hideFromBreadcrumbs:!0,routePath:"*",title:"",url:r.urlUtil.renderUrl(l.Gy,void 0)})}function m(e,t,n){const{labelName:i,labelValue:s}=(0,c.XJ)(e);return new a.jD({getParentPage:()=>t,getScene:e=>g(e),layout:r.PageLayoutType.Custom,preserveUrlKeys:c.tm,routePath:c.HU[n],title:(0,u.Zr)(n),url:c.bw[n](s,i)})}function v(e,t,n){const{breakdownLabel:i,labelName:o,labelValue:l}=(0,c.XJ)(e);if(!i){const e=new Error("Breakdown value missing!");throw s.v.error(e,{breakdownLabel:null!=i?i:"",labelName:o,labelValue:l,msg:"makeBreakdownValuePage: Breakdown value missing!"}),e}return new a.jD({getParentPage:()=>t,getScene:e=>g(e),layout:r.PageLayoutType.Custom,preserveUrlKeys:c.tm,routePath:c.KL[n],title:(0,u.Zr)(i),url:c.mC[n](l,o,i)})}},5700:(e,t,n)=>{n.d(t,{ls:()=>M,Ci:()=>R,GD:()=>V,iD:()=>W,K_:()=>G});var r=n(5959),a=n.n(r),i=n(6089),s=n(1269),o=n(8531),l=n(9736),c=n(4509),u=n(7389),d=n(5953),p=n(1475),g=n(7985),h=n(5719),f=n(4351),m=n(2661),v=n(2007);const b=n.p+"3d96a93cfcb32df74eef.svg";function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){y(e,t,n[t])}))}return e}function w(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class O extends l.Bs{constructor(e){super(w(S({},e),{queries:[]})),y(this,"onActivate",(()=>{(0,h.hJ)(this).then((e=>{this.setState({ds:e})})),this._subs.add(this.subscribeToState(((e,t)=>{this.state.queries.length||this.getQueries(),!this.state.context&&this.state.queries.length&&this.getContext()})))})),y(this,"getQueries",(()=>{const e=l.jh.getData(this),t=(0,h.UX)(e,(e=>e instanceof l.dt),l.dt);if(t){const e=this.state.frame?x(this.state.frame):null,n=t.state.queries.map((n=>{var r;return w(S({},n),{datasource:null!==(r=n.datasource)&&void 0!==r?r:void 0,expr:(0,g.Sh)(t,n.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:l.jh.interpolate(t,n.legendFormat)})}));JSON.stringify(n)!==JSON.stringify(this.state.queries)&&this.setState({queries:n})}})),y(this,"getFieldConfig",(()=>{var e;const t=(0,h.UX)(this,(e=>e instanceof l.Eb),l.Eb),n=l.jh.getData(this),r=null==n||null===(e=n.state.data)||void 0===e?void 0:e.series;let a=null==t?void 0:t.state.fieldConfig;if(a&&(null==r?void 0:r.length))for(const e of r)for(const t of e.fields){const e=Object.keys(t.config).map((e=>({id:e,value:t.config[e]}))),n=a.overrides.find((e=>{var n,r;return e.matcher.options===(null!==(r=null!==(n=t.config.displayNameFromDS)&&void 0!==n?n:t.config.displayName)&&void 0!==r?r:t.name)&&"byName"===e.matcher.id}));var i,s;if(!n)a.overrides.unshift({matcher:{id:"byName",options:null!==(s=null!==(i=t.config.displayNameFromDS)&&void 0!==i?i:t.config.displayName)&&void 0!==s?s:t.name},properties:e});n&&JSON.stringify(n.properties)!==JSON.stringify(e)&&(n.properties=e)}return a})),y(this,"getContext",(()=>{const e=this.getFieldConfig(),{ds:t,fieldName:n,labelName:r,queries:a,type:i}=this.state,s=l.jh.getTimeRange(this);if(!s||!a||!(null==t?void 0:t.uid))return;const o={datasource:{uid:t.uid},drillDownLabel:n,fieldConfig:e,id:`${JSON.stringify(a)}${r}${n}`,logoPath:b,origin:"Grafana Logs Drilldown",queries:a,timeRange:S({},s.state.value),title:`${r}${n?` > ${n}`:""}`,type:null!=i?i:"timeseries",url:window.location.href};JSON.stringify(o)!==JSON.stringify(this.state.context)&&this.setState({context:o})})),this.addActivationHandler(this.onActivate)}}y(O,"Component",(({model:e})=>{const{context:t}=e.useState(),{links:n}=(0,o.usePluginLinks)({context:t,extensionPointId:u.R6.MetricInvestigation});return a().createElement(a().Fragment,null,n.filter((e=>"grafana-investigations-app"===e.pluginId&&e.onClick)).map((e=>{var t;return a().createElement(v.IconButton,{tooltip:e.description,"aria-label":"extension-link-to-open-exploration",key:e.id,name:null!==(t=e.icon)&&void 0!==t?t:"panel-add",onClick:t=>{e.onClick&&e.onClick(t)}})})))}));const x=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return;const a=Object.keys(r)[0];return{name:a,value:r[a]}};var E=n(7813),C=n(2969),k=n(6887),P=n(5865),j=n(7781),F=n(2165),L=n(8428);n(3571);const _=(e,t,n=!1)=>{t||(t=(0,h.u9)(e)),t=t.replace(/\s+/g," ").trimEnd();const r=(0,h.U4)(e),a=l.jh.getTimeRange(e).state.value,i=(0,f.N$)(e),s=(0,f.k5)(),c=function(){const e=new URLSearchParams(window.location.search).get("urlColumns");if(e)try{const t=(0,L.aJ)(JSON.parse(e));let n={};for(const e in t)n[e]=t[e];return n}catch(e){console.error(e)}return}(),u=JSON.stringify({"loki-explore":{range:(0,j.toURLRange)(a.raw),queries:[{refId:"logs",expr:t,datasource:r}],panelsState:{logs:{displayedFields:i,visualisationType:"json"===s?"logs":s,columns:c,labelFieldName:"table"===s?F.bz:void 0}},datasource:r}});var d;const p=null!==(d=o.config.appSubUrl)&&void 0!==d?d:"",g=j.urlUtil.renderUrl(`${p}/explore`,{panes:u,schemaVersion:1});return n&&window.open(g,"_blank"),g};function T(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function D(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){T(i,r,a,s,o,"next",e)}function o(e){T(i,r,a,s,o,"throw",e)}s(void 0)}))}}function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const I="Add to investigation",B="investigations_divider",A="Investigations";var M=function(e){return e.timeseries="timeseries",e.histogram="histogram",e}({}),R=function(e){return e.collapsed="Collapse",e.expanded="Expand",e}({});class V extends l.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t;super($(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){N(e,t,n[t])}))}return e}({},e),{addInvestigationsLink:null===(t=e.addInvestigationsLink)||void 0===t||t})),this.addActivationHandler((()=>{var e,t,n,r,a,i,s;const o=[{text:"Navigation",type:"group"},{href:W(this),iconClassName:"compass",onClick:()=>z(),shortcut:"p x",text:"Explore"}];let c;try{c=l.jh.getAncestor(this,l.Eb)}catch(e){return void this.setState({body:new l.Lw({items:o})})}var u;(this.setState({investigationsButton:new O({fieldName:null===(e=this.state.investigationOptions)||void 0===e?void 0:e.fieldName,frame:null===(t=this.state.investigationOptions)||void 0===t?void 0:t.frame,labelName:(null===(n=this.state.investigationOptions)||void 0===n?void 0:n.getLabelName)?null===(r=this.state.investigationOptions)||void 0===r?void 0:r.getLabelName():null===(a=this.state.investigationOptions)||void 0===a?void 0:a.labelName,type:null===(i=this.state.investigationOptions)||void 0===i?void 0:i.type})}),this.state.addInvestigationsLink)&&(null===(u=this.state.investigationsButton)||void 0===u||u.activate());(this.state.panelType||(null==c?void 0:c.state.collapsible))&&function(e){e.push({text:"",type:"divider"}),e.push({text:"Visualization",type:"group"})}(o),(null==c?void 0:c.state.collapsible)&&function(e,t){const n=l.jh.getAncestor(t,l.Eb);e.push({iconClassName:n.state.collapsed?"table-collapse-all":"table-expand-all",onClick:()=>{const e=n.state.collapsed?"Expand":"Collapse",r=l.jh.getAncestor(t,l.G1);(0,P.Zb)(r,e),n.setState({collapsed:!n.state.collapsed}),(0,f.IW)("collapsed",e)},text:n.state.collapsed?"Expand":"Collapse"})}(o,this),this.state.panelType&&function(e,t){e.push({iconClassName:"histogram"!==t.state.panelType?"graph-bar":"chart-line",onClick:()=>{const e=l.jh.getAncestor(t,l.xK),n=l.jh.getAncestor(t,l.Eb).clone(),r=l.jh.getData(t).clone(),a=t.clone(),i=Array.isArray(n.state.headerActions)?n.state.headerActions.map((e=>e.clone())):n.state.headerActions;let s;s="histogram"!==t.state.panelType?l.d0.timeseries().setOverrides(p.jC):l.d0.histogram(),e.setState({body:s.setMenu(a).setTitle(n.state.title).setHeaderActions(i).setData(r).build()});const o="timeseries"!==t.state.panelType?"timeseries":"histogram";(0,f.IW)("panelType",o),a.setState({panelType:o});const c=(0,h.UX)(e,(e=>e instanceof E.E),E.E);c&&c.rebuildAvgFields(),K(o)},text:"histogram"!==t.state.panelType?"Histogram":"Time series"})}(o,this),this.setState({body:new l.Lw({items:o})});var d=this;this._subs.add(null===(s=this.state.investigationsButton)||void 0===s?void 0:s.subscribeToState(D((function*(){yield function(e){return U.apply(this,arguments)}(d)}))))}))}}N(V,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(a().Fragment,null)}));const W=e=>{const t=l.jh.getAncestor(e,m.P),n=l.jh.getData(e);let r=n instanceof l.dt?n:(0,h.oh)(n)[0];if(!r){const t=l.jh.findObject(e,(e=>e instanceof C.u||e instanceof k.u));if(t){const e=l.jh.getData(t);r=e instanceof l.dt?e:(0,h.oh)(e)[0]}else d.v.error(new Error("Unable to locate query runner!"),{msg:"PanelMenu - getExploreLink: Unable to locate query runner!"})}const a=r.state.queries[0].expr,i=(0,g.Sh)(e,a);return _(t,i)},z=()=>{(0,c.EE)(c.NO.all,c.ir.all.open_in_explore_menu_clicked)},K=e=>{(0,c.EE)(c.NO.service_details,c.ir.service_details.change_viz_type,{newVizType:e})},H=function(){var e=D((function*(e){const t=u.R6.MetricInvestigation,n=e.state.context;if(void 0!==o.getPluginLinkExtensions){return(0,o.getPluginLinkExtensions)({context:n,extensionPointId:t}).extensions[0]}if(void 0!==o.getObservablePluginLinks){return(yield(0,s.firstValueFrom)((0,o.getObservablePluginLinks)({context:n,extensionPointId:t})))[0]}}));return function(t){return e.apply(this,arguments)}}();function U(){return(U=D((function*(e){const t=e.state.investigationsButton;if(t){var n;const l=yield H(t);var r;const c=null!==(r=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==r?r:[],u=c.find((e=>e.text===I));var a,i,s,o;if(l)if(u){if(u)null===(a=e.state.body)||void 0===a||a.setItems(c.filter((e=>!1===[B,A,I].includes(e.text))))}else null===(i=e.state.body)||void 0===i||i.addItem({text:B,type:"divider"}),null===(s=e.state.body)||void 0===s||s.addItem({text:A,type:"group"}),null===(o=e.state.body)||void 0===o||o.addItem({iconClassName:"plus-square",onClick:e=>l.onClick&&l.onClick(e),text:I})}}))).apply(this,arguments)}const G=e=>({panelWrapper:(0,i.css)({display:"flex",height:"100%",label:"panel-wrapper",position:"absolute",width:"100%"})})},9405:(e,t,n)=>{n.d(t,{Of:()=>S,Qt:()=>P,XI:()=>O,hi:()=>x,oR:()=>L,ts:()=>j,u7:()=>C,vn:()=>k});var r=n(5959),a=n.n(r),i=n(7781),s=n(9736),o=n(1532),l=n(696),c=n(376),u=n(8502),d=n(6854),p=n(5953),g=n(7478),h=n(5553),f=n(7352),m=n(7709),v=n(4509),b=n(20);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class S extends i.BusEventBase{constructor(e,t,n,r){super(),y(this,"source",void 0),y(this,"operator",void 0),y(this,"key",void 0),y(this,"value",void 0),this.source=e,this.operator=t,this.key=n,this.value=r}}y(S,"type","add-filter");class w extends i.BusEventBase{constructor(e,t,n){super(),y(this,"key",void 0),y(this,"value",void 0),y(this,"operator",void 0),this.key=e,this.value=t,this.operator=n}}function O(e,t,n){const r="="===e.operator?"include":"exclude";P(e.key,e.value,r,t,n)}function x(e,t,n,r,a){const i=T(n,e,t);let s=i.state.filters.filter((t=>{const i=(0,h.z2)(n,t);return r&&a?!(t.key===e&&i.value===r&&t.operator===a):r?!(t.key===e&&i.value===r):a?!(t.key===e&&t.operator===a):!(t.key===e)}));t.publishEvent(new w(e,r,a),!0),i.setState({filters:s})}y(w,"type","add-filter");const E=e=>e===d.w7.gt||e===d.w7.gte?"greater":e===d.w7.lt||e===d.w7.lte?"lesser":void 0;function C(e,t,n,r){r||(r=F(e,t));const a=T(r,e,t),i=n?E(n):void 0;let s=a.state.filters.filter((t=>!(t.key===e&&(E(t.operator)===i||t.operator===d.w7.NotEqual))));a.setState({filters:s})}function k(e,t,n,r,a){const i=E(n);a||(a=F(e,r));const s=T(a,e,r);let o;a===b.mB&&(o=JSON.stringify({parser:(0,c.Ri)(e,r),value:t}));let l=s.state.filters.filter((t=>!(t.key===e&&(E(t.operator)===i||t.operator===d.w7.NotEqual))));l=[...l,{key:e,operator:n,value:o||t,valueLabels:[t]}],s.setState({filters:l}),r.publishEvent(new S("filterButton",n,e,t),!0)}function P(e,t,n,r,a,i=!0,s=!1){i&&(0,g.bN)(),a===b.MB&&(0,l._J)(e,t,r);const o=T(a,e,r);let u,p=t;a===b.mB?u=JSON.stringify({parser:s?"json":(0,c.Ri)(e,r),value:t}):a===b._Y&&"exclude"===n&&(p=`!${t}`);let f=o.state.filters.filter((r=>{const i=(0,h.z2)(a,r);return"include"===n?!(r.key===e&&r.operator===d.w7.NotEqual):"exclude"===n?!(r.key===e&&r.operator===d.w7.Equal):!(r.key===e&&i.value===t)}));const m=f.length!==o.state.filters.length;("include"===n||"exclude"===n||!m&&"toggle"===n)&&(f=[...f,{key:e,operator:"exclude"===n?d.w7.NotEqual:d.w7.Equal,value:u||t,valueLabels:[p]}]),o.setState({filters:f}),r.publishEvent(new S("filterButton",n,e,t),!0)}function j(e,t){return e===b.e4?b._Y:t}function F(e,t){var n,r;return(null===(r=(0,f.TG)(t))||void 0===r||null===(n=r.fields)||void 0===n?void 0:n.find((t=>t.name===e)))?b.MB:b.mB}class L extends s.Bs{onActivate(){const e=_(this.state.frame);if(e){const t=T(this.state.variableName,e.name,this);this.setFilterState(t),this._subs.add(t.subscribeToState(((e,n)=>{(0,o.B)(e.filters,n.filters)||this.setFilterState(t)})))}}setFilterState(e){const t=_(this.state.frame);if(!t)return void this.setState({isExcluded:!1,isIncluded:!1});const n=e.state.filters.find((e=>{const n=(0,u.OH)(t),r=(0,h.z2)(n?b._P:b.mB,e);return e.key===t.name&&r.value===t.value}));n?this.setState({isExcluded:n.operator===d.w7.NotEqual,isIncluded:n.operator===d.w7.Equal}):this.setState({isExcluded:!1,isIncluded:!1})}constructor(e){super(e),y(this,"onClick",(e=>{const t=_(this.state.frame);if(!t)return;P(t.name,t.value,e,this,this.state.variableName);const n=T(this.state.variableName,t.name,this);(0,v.EE)(v.NO.service_details,v.ir.service_details.add_to_filters_in_breakdown_clicked,{action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0,filterType:this.state.variableName,key:t.name})})),this.addActivationHandler(this.onActivate.bind(this))}}y(L,"Component",(({model:e})=>{const{hideExclude:t,isExcluded:n,isIncluded:r}=e.useState();return a().createElement(m.F,{buttonFill:"outline",isIncluded:null!=r&&r,isExcluded:null!=n&&n,onInclude:()=>e.onClick("include"),onClear:()=>e.onClick("clear"),onExclude:()=>e.onClick("exclude"),hideExclude:t})}));const _=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return void p.v.warn("getFilter: unexpected empty labels");const a=Object.keys(r)[0];return{name:a,value:r[a]}},T=(e,t,n)=>e===b.mB||e===b._P?(0,h.YS)(n):(0,h.bY)(j(t,e),n)},8313:(e,t,n)=>{n.d(t,{G:()=>f,x:()=>g});var r=n(5959),a=n.n(r),i=n(7781),s=n(9736),o=n(5953),l=n(158),c=n(6830),u=n(8072),d=n(9284);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends i.BusEventBase{}p(g,"type","breakdown-search-reset");const h={};class f extends s.Bs{filterValues(e){const t=s.jh.findObject(this,(e=>e instanceof u.O||e instanceof c.J6));if(t instanceof u.O||t instanceof c.J6){h[this.cacheKey]=e;const n=s.jh.findDescendents(t,l.h);null==n||n.forEach((t=>{t.state.body.isActive&&t.filterByString(e)}))}else o.v.warn("unable to find Breakdown scene",{filter:e,typeofBody:typeof t})}constructor(e){var t;super({filter:null!==(t=h[e])&&void 0!==t?t:""}),p(this,"cacheKey",void 0),p(this,"onValueFilterChange",(e=>{this.setState({filter:e.target.value}),this.filterValues(e.target.value)})),p(this,"clearValueFilter",(()=>{this.setState({filter:""}),this.filterValues("")})),p(this,"reset",(()=>{this.setState({filter:""}),h[this.cacheKey]=""})),this.cacheKey=e}}p(f,"Component",(({model:e})=>{const{filter:t}=e.useState();return a().createElement(d.D,{value:t,onChange:e.onValueFilterChange,onClear:e.clearValueFilter,placeholder:"Search for value"})}))},158:(e,t,n)=>{n.d(t,{h:()=>y});var r=n(5959),a=n.n(r),i=n(6089),s=n(1269),o=n(7781),l=n(9736),c=n(2007),u=n(5953),d=n(9193),p=n(8313),g=n(6081),h=n(5865),f=n(1049),m=n(2601);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}class y extends l.Bs{performRepeat(e){const t=[],n=(0,m.sortSeries)(e.series,this.sortBy,this.direction);for(let e=0;e<n.length;e++){const r=this.state.getLayoutChild(n[e],e);t.push(r)}this.sortedSeries=n,this.unfilteredChildren=t,this.getFilter()?(this.state.body.setState({children:[]}),this.filterByString(this.getFilter())):this.state.body.setState({children:t})}filterSummaryChart(e){const t=l.jh.getAncestor(this,g.U);if(t){const n=l.jh.findAllObjects(t,(e=>e.isActive&&e.state.key===h.s$));if(n[0]instanceof l.G1){const t=l.jh.findDescendents(n[0],l.Eb)[0];t instanceof l.Eb?t.setState({$data:new l.Es({transformations:[()=>{return t=e[0],e=>e.pipe((0,s.map)((e=>{if(!t||!t.length)return e;let n=[];return e.forEach((e=>{const r=(0,f.ee)(e);t.includes(r)&&n.push(e)})),n})));var t}]})}):u.v.warn("filterSummaryChart: VizPanel not found",{typeofPanel:typeof t})}else u.v.warn("filterSummaryChart: SceneFlexItem not found",{typeofGraphParent:typeof n})}}constructor(e){var{direction:t,getFilter:n,sortBy:r}=e;super(b(e,["direction","getFilter","sortBy"])),v(this,"unfilteredChildren",[]),v(this,"sortBy",void 0),v(this,"direction",void 0),v(this,"sortedSeries",[]),v(this,"getFilter",void 0),v(this,"sort",((e,t)=>{const n=l.jh.getData(this);this.sortBy=e,this.direction=t,n.state.data&&this.performRepeat(n.state.data)})),v(this,"iterateFrames",(e=>{if(l.jh.getData(this).state.data)for(let t=0;t<this.sortedSeries.length;t++)e(this.sortedSeries,t)})),v(this,"filterByString",(e=>{let t=[];this.iterateFrames(((e,n)=>{const r=(0,f.ee)(e[n]);t.push(r)})),(0,d.X)(t,e,(e=>{e&&e[0]?this.filterFrames((t=>{const n=(0,f.ee)(t);return e[0].includes(n)})):this.filterFrames((()=>!0)),this.filterSummaryChart(e)}))})),v(this,"filterFrames",(e=>{const t=[];if(this.iterateFrames(((n,r)=>{e(n[r])&&t.push(this.unfilteredChildren[r])})),0===t.length){const e=this.getFilter();this.state.body.setState({children:[S(e,this.clearFilter)]})}else this.state.body.setState({children:t})})),v(this,"clearFilter",(()=>{this.publishEvent(new p.x,!0)})),this.sortBy=r,this.direction=t,this.getFilter=n,this.addActivationHandler((()=>{const e=l.jh.getData(this);this._subs.add(e.subscribeToState(((e,t)=>{var n,r,a,i;((null===(n=e.data)||void 0===n?void 0:n.state)===o.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===o.LoadingState.Streaming&&e.data.series.length>(null!==(i=null===(a=t.data)||void 0===a?void 0:a.series.length)&&void 0!==i?i:0))&&this.performRepeat(e.data)}))),e.state.data&&this.performRepeat(e.state.data)}))}}function S(e,t){return new l.G1({children:[new l.vA({body:new l.dM({reactNode:a().createElement("div",{className:w.alertContainer},a().createElement(c.Alert,{title:"",severity:"info",className:w.noResultsAlert},"No values found matching “",e,"”",a().createElement(c.Button,{className:w.clearButton,onClick:t},"Clear filter")))})})],direction:"row"})}v(y,"Component",(({model:e})=>{const{body:t}=e.useState();return a().createElement(t.Component,{model:t})}));const w={alertContainer:(0,i.css)({alignItems:"center",display:"flex",flexGrow:1,justifyContent:"center"}),clearButton:(0,i.css)({marginLeft:"1.5rem"}),noResultsAlert:(0,i.css)({flexGrow:0,minWidth:"30vw"})}},713:(e,t,n)=>{n.d(t,{a:()=>c});var r=n(5959),a=n.n(r),i=n(9736),s=n(2007),o=n(7191),l=n(6830);class c extends i.Bs{static Component({model:e}){const{type:t}=e.useState();return a().createElement(o.R,null,a().createElement(s.Alert,{title:"",severity:"warning"},"We did not find any ",t," for the given timerange. Please"," ",a().createElement("a",{className:l.ZI.link,href:"https://forms.gle/1sYWCTPvD72T1dPH9",target:"_blank",rel:"noopener noreferrer"},"let us know")," ","if you think this is a mistake."))}}},7243:(e,t,n)=>{n.d(t,{f:()=>p,u:()=>d});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(7985),l=n(3571);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function d({label:e,onChange:t,options:n,value:i}){const o=(0,s.useStyles2)(g),[l,c]=(0,r.useState)(!1),u=n.map((e=>({label:e.label,value:e.value})));return a().createElement(s.InlineField,{className:o.selectWrapper,label:e},a().createElement(s.Select,{options:u,value:i,onOpenMenu:()=>c(!0),onCloseMenu:()=>c(!1),onChange:e=>t(e.value),className:o.select,prefix:l?void 0:a().createElement(s.Icon,{name:"search"})}))}function p({initialFilter:e,isLoading:t,label:n,onChange:i,options:d,selectOption:p,value:h}){var f;const m=(0,s.useStyles2)(g),[v,b]=(0,r.useState)(!1),[y,S]=(0,r.useState)(e),w=d.map((e=>({label:e.label,value:e.value}))),O=y&&h&&(null===(f=y.value)||void 0===f?void 0:f.includes(h))?[y,...w]:w,x=null==O?void 0:O.find((e=>e.value===h));return a().createElement(s.InlineField,{className:m.serviceSceneSelectWrapper,label:n},a().createElement(s.Select,{isLoading:t,"data-testid":l.b.exploreServiceSearch.search,placeholder:"Search values",options:O,isClearable:!0,value:h,onOpenMenu:()=>b(!0),onCloseMenu:()=>b(!1),allowCustomValue:!0,prefix:v||(null==x?void 0:x.__isNew__)?void 0:a().createElement(s.Icon,{name:"search"}),onChange:(e,t)=>(null==e?void 0:e.__isNew__)||(null==e?void 0:e.icon)?(S(u(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}({},e),{icon:"filter"})),i(e.value)):"clear"===t.action?i(""):void("select-option"===t.action&&e.value&&!e.__isNew__&&p(e.value)),onInputChange:(e,t)=>{const n=t;return"input-change"===n.action?i(e):"menu-close"===n.action&&n.prevInputValue?(S({__isNew__:!0,icon:"filter",label:n.prevInputValue,value:(0,o.vC)(n.prevInputValue)}),i(n.prevInputValue)):void 0}}))}function g(e){return{input:(0,i.css)({marginBottom:0}),select:(0,i.css)({maxWidth:e.spacing(64),minWidth:e.spacing(20)}),selectWrapper:(0,i.css)({label:"field-selector-select-wrapper",marginBottom:0,marginRight:e.spacing.x1,maxWidth:e.spacing(62.5),minWidth:e.spacing(20)}),serviceSceneSelectWrapper:(0,i.css)({label:"service-select-wrapper",marginBottom:0,marginRight:e.spacing.x1,maxWidth:e.spacing(62.5),minWidth:e.spacing(20)})}}},2969:(e,t,n)=>{n.d(t,{u:()=>F});var r,a,i,s=n(5959),o=n.n(s),l=n(7781),c=n(9736),u=n(2007),d=n(1532),p=n(376),g=n(5953),h=n(1475),f=n(7985),m=n(2601),v=n(4351),b=n(5553),y=n(20),S=n(5700),w=n(7352),O=n(158),x=n(6830),E=n(6081),C=n(5865),k=n(1049);function P(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function j(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){P(i,r,a,s,o,"next",e)}function o(e){P(i,r,a,s,o,"throw",e)}s(void 0)}))}}class F extends c.Bs{static Selector({model:e}){const{body:t}=e.useState();return t instanceof E.U?o().createElement(o().Fragment,null,t&&o().createElement(E.U.Selector,{model:t})):o().createElement(o().Fragment,null)}getTagKey(){const e=(0,b.Hj)(this);return String(e.state.value)}onActivate(){var e;const t=this.buildQuery();this.setState({$data:this.buildQueryRunner(),body:this.build(t)}),this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{this.onValuesDataQueryChange(e,t)}))),this.runQuery(),this.setSubscriptions()}buildQueryRunner(){const e=this.buildQuery();return(0,h.rS)([e],{runQueriesMode:"manual"})}buildQuery(){const e=this.getTagKey(),t=(0,b.ir)(this),n=(0,w.rD)(this),r=(0,b.WA)(this),a=(0,p.Jl)(e,t,n,r),{filterExpression:i,variableName:s}=this.removeFieldLabelFromVariableInterpolation(),o=c.jh.interpolate(this,a.replace(`\${${s}}`,i));return(0,f.l)(o,{legendFormat:`{{${e}}}`,refId:e})}setSubscriptions(){this._subs.add(c.jh.getTimeRange(this).subscribeToState((()=>{this.runQuery()}))),this._subs.add((0,b.Gk)(this).subscribeToState(((e,t)=>{(0,d.B)(e.filters,t.filters)||this.runQuery()}))),this._subs.add((0,b.Ku)(this).subscribeToState(((e,t)=>{e.value!==t.value&&this.runQuery()}))),this._subs.add((0,b.cR)(this).subscribeToState(((e,t)=>{(0,d.B)(e.filters,t.filters)||this.runQuery()}))),this._subs.add((0,b.iw)(this).subscribeToState(((e,t)=>{(0,d.B)(e.filters,t.filters)||this.runQuery()})));const{parser:e}=this.getParserForThisField();"structuredMetadata"!==e?this.setFieldParserSubscriptions():this.setMetadataParserSubscriptions()}setMetadataParserSubscriptions(){const e=this.getTagKey();var t=this;this._subs.add((0,b.ir)(this).subscribeToState(function(){var e=j((function*(e,n){(0,d.B)(e.filters,n.filters)||t.runQuery()}));return function(t,n){return e.apply(this,arguments)}}()));var n=this;this._subs.add((0,b.oY)(this).subscribeToState(function(){var t=j((function*(t,r){(0,d.B)(t.filters.filter((t=>t.key!==e)),r.filters.filter((t=>t.key!==e)))||n.runQuery()}));return function(e,n){return t.apply(this,arguments)}}()))}setFieldParserSubscriptions(){const e=this.getTagKey();var t=this;this._subs.add((0,b.oY)(this).subscribeToState(function(){var e=j((function*(e,n){(0,d.B)(e.filters,n.filters)||t.runQuery()}));return function(t,n){return e.apply(this,arguments)}}()));var n=this;this._subs.add((0,b.ir)(this).subscribeToState(function(){var t=j((function*(t,r){(0,d.B)(t.filters.filter((t=>t.key!==e)),r.filters.filter((t=>t.key!==e)))||n.runQuery()}));return function(e,n){return t.apply(this,arguments)}}()))}rebuildQuery(){var e;const t=this.buildQuery();null===(e=this.getSceneQueryRunner())||void 0===e||e.setState({queries:[t]})}runQuery(){this.rebuildQuery();const e=this.getSceneQueryRunner();null==e||e.runQueries()}getSceneQueryRunner(){if(this.state.$data){const e=c.jh.findDescendents(this.state.$data,c.dt);if(1!==e.length){const e=new Error("Unable to find query runner in value breakdown!");throw g.v.error(e,{msg:"FieldValuesBreakdownScene: Unable to find query runner in value breakdown!"}),e}return e[0]}g.v.warn("FieldValuesBreakdownScene: Query is attempting to execute, but query runner is undefined!")}removeFieldLabelFromVariableInterpolation(){const e=this.getTagKey();let t,n;if("structuredMetadata"===this.getQueryParser()){const r=(0,b.oY)(this);n=y._P,t=(0,f.E3)(r.state.filters,[e])}else{n=y.mB;const r=(0,b.ir)(this);t=(0,f.ZX)(r.state.filters,[e])}return{filterExpression:t,variableName:n}}onValuesDataQueryChange(e,t){var n,r;(null===(n=e.data)||void 0===n?void 0:n.state)===l.LoadingState.Done&&this.state.body instanceof c.dM&&this.setState({body:this.build(t)}),(null===(r=e.data)||void 0===r?void 0:r.state)===l.LoadingState.Error&&this.setErrorState(e.data.errors)}setErrorState(e){this.setState({body:new c.dM({reactNode:o().createElement(u.Alert,{title:"Something went wrong with your request",severity:"error"},null==e?void 0:e.map(((e,t)=>o().createElement("div",{key:t},e.status&&o().createElement(o().Fragment,null,o().createElement("strong",null,"Status"),": ",e.status," ",o().createElement("br",null)),e.message&&o().createElement(o().Fragment,null,o().createElement("strong",null,"Message"),": ",e.message," ",o().createElement("br",null)),e.traceId&&o().createElement(o().Fragment,null,o().createElement("strong",null,"TraceId"),": ",e.traceId)))))})})}build(e){const{optionValue:t,parser:n}=this.getParserForThisField(),{direction:r,sortBy:a}=(0,v.vs)("fields",m.DEFAULT_SORT_BY,"desc"),i=c.jh.getAncestor(this,x.J6),s=()=>{var e;return null!==(e=i.state.search.state.filter)&&void 0!==e?e:""};return new E.U({active:"grid",layouts:[new c.G1({children:[new c.dM({reactNode:o().createElement(x.J6.LabelsMenu,{model:i})}),new c.vA({body:c.d0.timeseries().setTitle(t).setShowMenuAlways(!0).setMenu(new S.GD({})).build(),minHeight:300})],direction:"column"}),new c.G1({children:[new c.dM({reactNode:o().createElement(x.J6.LabelsMenu,{model:i})}),new C.s7({tagKey:this.getTagKey(),title:t,type:"field"}),new c.dM({reactNode:o().createElement(x.J6.ValuesMenu,{model:i})}),new O.h({body:new c.gF({autoRows:"200px",children:[new c.vA({body:new c.dM({reactNode:o().createElement(u.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0,templateColumns:x.OK}),direction:r,getFilter:s,getLayoutChild:(0,p.Zp)(k.ee,(null==e?void 0:e.expr.includes("count_over_time"))?u.DrawStyle.Bars:u.DrawStyle.Line,"structuredMetadata"===n?y._P:y.mB,c.jh.getAncestor(this,x.J6).state.sort,t),sortBy:a})],direction:"column"}),new c.G1({children:[new c.dM({reactNode:o().createElement(x.J6.LabelsMenu,{model:i})}),new C.s7({tagKey:this.getTagKey(),title:t,type:"field"}),new c.dM({reactNode:o().createElement(x.J6.ValuesMenu,{model:i})}),new O.h({body:new c.gF({autoRows:"200px",children:[new c.vA({body:new c.dM({reactNode:o().createElement(u.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0,templateColumns:"1fr"}),direction:r,getFilter:s,getLayoutChild:(0,p.Zp)(k.ee,(null==e?void 0:e.expr.includes("count_over_time"))?u.DrawStyle.Bars:u.DrawStyle.Line,"structuredMetadata"===n?y._P:y.mB,c.jh.getAncestor(this,x.J6).state.sort,t),sortBy:a})],direction:"column"})],options:[{label:"Single",value:"single"},{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}getParserForThisField(){const e=(0,b.Hj)(this),t=String(e.state.value);return{optionValue:t,parser:(0,p.Ri)(t,this)}}getParserForFields(){return(0,p.k$)((0,b.ir)(this))}getQueryParser(){const{parser:e}=this.getParserForThisField(),t=this.getParserForFields();return e===t?t:void 0===e?"mixed":"structuredMetadata"===e?t:"structuredMetadata"===t?e:"mixed"}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}i=({model:e})=>{const{body:t}=e.useState(),n=(0,u.useStyles2)(S.K_);return t?o().createElement("span",{className:n.panelWrapper},t&&o().createElement(t.Component,{model:t})):o().createElement(u.LoadingPlaceholder,{text:"Loading..."})},(a="Component")in(r=F)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},7813:(e,t,n)=>{n.d(t,{E:()=>x});var r=n(5959),a=n.n(r),i=n(7781),s=n(9736),o=n(2007),l=n(7839),c=n(376),u=n(5953),d=n(1475),p=n(7985),g=n(4351),h=n(5553),f=n(20),m=n(5700),v=n(7352),b=n(6830),y=n(6081),S=n(2606),w=n(4059);function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class x extends s.Bs{updateChildren(e,t=void 0){var n;const r=(0,v.UO)(e),a=(0,v.nU)(e),i=(0,v.dB)(e),o=this.calculateCardinalityMap(e);null===(n=this.state.body)||void 0===n||n.state.layouts.forEach((e=>{if(e instanceof s.gF){const n=new Set(null==a?void 0:a.values),l=e.state.children;for(let o=0;o<l.length;o++){const d=e.state.children[o];if(d instanceof s.xK){const e=d.state.body;if(e instanceof s.Eb){if(t){const n=null==a?void 0:a.values.indexOf(e.state.title);if((n&&-1!==n?null==i?void 0:i.values[n]:void 0)!==t){const t=(0,c.ph)(e.state.title,r),n=this.getQueryRunnerForPanel(e.state.title,r,t);e.setState({$data:n})}}n.has(e.state.title)?n.delete(e.state.title):(l.splice(o,1),o--)}else u.v.warn("panel is not VizPanel!")}else u.v.warn("gridItem is not SceneCSSGridItem")}const d=Array.from(n).map((e=>e));l.push(...this.buildChildren(d)),l.sort(this.sortChildren(o)),l.map((e=>{this.subscribeToPanel(e)})),e.setState({children:l})}else u.v.warn("Layout is not SceneCSSGridLayout")}))}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;var i;const s=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var o;return(null!==(o=e.get(a.state.title))&&void 0!==o?o:0)-s}}calculateCardinalityMap(e){const t=(0,v.UO)(e),n=new Map;if(null==t?void 0:t.length)for(let e=0;e<(null==t?void 0:t.length);e++){const r=t.fields[0].values[e],a=t.fields[1].values[e];n.set(r,a)}return n}onActivate(){var e;this.setState({body:this.build()});const t=s.jh.getAncestor(this,v.Mn);void 0===t.state.fieldsCount&&this.updateFieldCount(),this._subs.add(null===(e=t.state.$detectedFieldsData)||void 0===e?void 0:e.subscribeToState(this.onDetectedFieldsChange)),this._subs.add(this.subscribeToFieldsVar())}subscribeToFieldsVar(){return(0,h.ir)(this).subscribeToState(((e,t)=>{const n=s.jh.getAncestor(this,v.Mn),r=e.filters.map((e=>(0,h.bu)(e).parser)),a=t.filters.map((e=>(0,h.bu)(e).parser)),i=(0,c.Qg)(r);if(i!==(0,c.Qg)(a)){var o;const e=null===(o=n.state.$detectedFieldsData)||void 0===o?void 0:o.state;e&&this.updateChildren(e,i)}}))}build(){var e;const t=(0,h.Hj)(this).state.options.map((e=>String(e.value)));s.jh.getAncestor(this,b.J6).state.search.reset();const n=this.buildChildren(t),r=s.jh.getAncestor(this,v.Mn),a=this.calculateCardinalityMap(null===(e=r.state.$detectedFieldsData)||void 0===e?void 0:e.state);n.sort(this.sortChildren(a));const i=n.map((e=>e.clone()));return[...n,...i].map((e=>{this.subscribeToPanel(e)})),new y.U({active:"grid",layouts:[new s.gF({autoRows:"200px",children:n,isLazy:!0,templateColumns:b.OK}),new s.gF({autoRows:"200px",children:i,isLazy:!0,templateColumns:"1fr"})],options:[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}subscribeToPanel(e){const t=e.state.body;var n;t&&this._subs.add(null==t||null===(n=t.state.$data)||void 0===n?void 0:n.getResultsStream().subscribe((t=>{t.data.errors&&t.data.errors.length>0&&(e.setState({isHidden:!0}),this.updateFieldCount())})))}rebuildAvgFields(){const e=(0,v.rD)(this),t=this.getActiveGridLayouts(),n=[];var r;const a=null!==(r=(0,g.ex)("panelType",[m.ls.histogram,m.ls.timeseries]))&&void 0!==r?r:m.ls.timeseries;null==t||t.state.children.forEach((t=>{if(t instanceof s.xK&&!t.state.isHidden){const r=s.jh.findDescendents(t,s.Eb);if(r.length){const i=r[0].state.title,s=(0,c.ph)(i,e);if((0,c.JI)(s)){const t=this.buildChild(i,e,a);t&&n.push(t)}else n.push(t)}}})),n.length&&(null==t||t.setState({children:n}))}buildChildren(e){const t=[],n=(0,v.rD)(this);var r;const a=null!==(r=(0,g.ex)("panelType",[m.ls.timeseries,m.ls.histogram]))&&void 0!==r?r:m.ls.timeseries;for(const r of e){if(r===f.To||!r)continue;const e=this.buildChild(r,n,a);e&&t.push(e)}return t}buildChild(e,t,n){if(e===f.To||!e)return;const r=(0,c.ph)(e,t),a=this.getQueryRunnerForPanel(e,t,r);let i;const u=[];(0,c.JI)(r)?(i="histogram"===n?s.d0.histogram():s.d0.timeseries(),i.setTitle(e).setData(a).setMenu(new m.GD({investigationOptions:{labelName:e},panelType:n})),u.push(new S.X({fieldType:l._J.field,hideValueDrilldown:!0,labelName:String(e)}))):(i=s.d0.timeseries().setTitle(e).setData(a).setMenu(new m.GD({investigationOptions:{labelName:e}})).setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars).setOverrides(d.jC),u.push(new S.X({fieldType:l._J.field,hasNumericFilters:"int"===r,labelName:String(e)}))),i.setHeaderActions(u),i.setSeriesLimit(w.l),i.setShowMenuAlways(!0);const p=i.build();return new s.xK({body:p})}getQueryRunnerForPanel(e,t,n){const r=(0,h.ir)(this),a=(0,h.WA)(this),i=(0,c.Jl)(e,r,t,a),s=(0,p.l)(i,{legendFormat:(0,c.JI)(n)?e:`{{${e}}}`,refId:e});return(0,d.rS)([s])}getActiveGridLayouts(){var e,t,n;return null!==(n=null===(e=this.state.body)||void 0===e?void 0:e.state.layouts.find((e=>e.isActive)))&&void 0!==n?n:null===(t=this.state.body)||void 0===t?void 0:t.state.layouts[0]}updateFieldCount(){var e,t;const n=this.getActiveGridLayouts(),r=null==n?void 0:n.state.children,a=null==r?void 0:r.filter((e=>!e.state.isHidden));var i;null===(e=(t=s.jh.getAncestor(this,b.J6).state).changeFieldCount)||void 0===e||e.call(t,null!==(i=null==a?void 0:a.length)&&void 0!==i?i:0)}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&a().createElement(y.U.Selector,{model:t}))}constructor(e){super(e),O(this,"onDetectedFieldsChange",(e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&this.updateChildren(e)})),this.addActivationHandler(this.onActivate.bind(this))}}O(x,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,o.useStyles2)(m.K_);return t?a().createElement("span",{className:n.panelWrapper},t&&a().createElement(t.Component,{model:t})):a().createElement(o.LoadingPlaceholder,{text:"Loading..."})}))},6830:(e,t,n)=>{n.d(t,{J6:()=>$,OK:()=>N,ZI:()=>I});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(9736),l=n(2007),c=n(1532),u=n(4702),d=n(7839),p=n(7478),g=n(9683),h=n(2601),f=n(5553),m=n(5548),v=n(2661),b=n(7352),y=n(8313),S=n(158),w=n(713),O=n(7813),x=n(7243),E=n(2969),C=n(6081),k=n(6779),P=n(1049),j=n(5659),F=n(4509),L=n(8502),_=n(4351),T=n(20);function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const N="repeat(auto-fit, minmax(400px, 1fr))";class $ extends o.Bs{onActivate(){var e,t,n;const r=(0,f.Hj)(this),a=o.jh.getAncestor(this,b.Mn);this.setState({loading:(null===(t=a.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done}),this._subs.add(this.subscribeToEvent(y.x,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent(P.gf,this.handleSortByChange)),this._subs.add(r.subscribeToState(this.variableChanged)),this._subs.add((0,f.cR)(this).subscribeToState(((e,t)=>{const n=(0,f.Hj)(this);let{labelName:r}=(0,g.W6)();const a=e.filters.find((e=>e.key===r)),i=t.filters.find((e=>e.key===r));n.state.value===T.To&&a!==i&&this.setState({body:void 0,loading:!0})}))),this._subs.add(null===(n=a.state.$detectedFieldsData)||void 0===n?void 0:n.subscribeToState(((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&((null===(r=e.data.series)||void 0===r?void 0:r[0])&&this.updateOptions(null===(a=e.data.series)||void 0===a?void 0:a[0]))})));const i=(0,b.rD)(this);i&&this.updateOptions(i),(0,g.NX)(this)}updateOptions(e){if(!e||!e.length){const e=o.jh.getAncestor(this,v.P);let r;var t,n;if((0,m.mE)(e).length>1)null===(t=(n=this.state).changeFieldCount)||void 0===t||t.call(n,0),r=new k.W({clearCallback:()=>(0,m.rA)(this)});else r=new w.a({type:"fields"});return void this.setState({body:r,loading:!1})}const r=o.jh.getAncestor(this,b.Mn);var a;(0,f.Hj)(this).setState({loading:!1,options:(0,L.rd)(e.fields[0].values.map((e=>String(e)))),value:null!==(a=r.state.drillDownLabel)&&void 0!==a?a:T.To}),this.setState({loading:!1})}updateBody(e){const t=(0,f.Hj)(this);if(!t.state.options||!t.state.options.length)return;const n={};if(t.state.options&&t.state.options.length<=1){const e=o.jh.getAncestor(this,v.P);var r,a;if((0,m.mE)(e).length>1)null===(r=(a=this.state).changeFieldCount)||void 0===r||r.call(a,0),n.body=new k.W({clearCallback:()=>(0,m.rA)(this)});else n.body=new w.a({type:"fields"})}else e.value===T.To&&this.state.body instanceof E.u?n.body=new O.E({}):e.value!==T.To&&this.state.body instanceof O.E?n.body=new E.u({}):(void 0===this.state.body||this.state.body instanceof w.a||this.state.body instanceof k.W)&&(n.body=e.value===T.To?new O.E({}):new E.u({}));this.setState(n)}constructor(e){var t,n,r,a;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){D(e,t,n[t])}))}return e}({$variables:null!==(r=e.$variables)&&void 0!==r?r:new o.Pj({variables:[new u.m({defaultToAll:!1,includeAll:!0,name:T.LI,options:null!==(t=e.options)&&void 0!==t?t:[],value:null!==(n=e.value)&&void 0!==n?n:T.To})]}),loading:!0,search:new y.G("fields"),sort:new P.wd({target:"fields"}),value:null!==(a=e.value)&&void 0!==a?a:T.To},e)),D(this,"_variableDependency",new o.Sh(this,{variableNames:[T.MB]})),D(this,"variableChanged",((e,t)=>{(e.value!==t.value||!(0,c.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof w.a||this.state.body instanceof k.W)&&this.updateBody(e)})),D(this,"handleSortByChange",(e=>{if("fields"!==e.target)return;const t=this.state.body;var n;t instanceof E.u&&t.state.body instanceof C.U&&(null===(n=t.state.body)||void 0===n||n.state.layouts.forEach((n=>{o.jh.findDescendents(t,S.h).forEach((t=>t.sort(e.sortBy,e.direction)))})));(0,F.EE)(F.NO.service_details,F.ir.service_details.value_breakdown_sort_change,{criteria:e.sortBy,direction:e.direction,target:"fields"})})),D(this,"onFieldSelectorChange",(e=>{if(!e)return;const t=(0,f.Hj)(this),{direction:n,sortBy:r}=(0,_.vs)("fields",h.DEFAULT_SORT_BY,"desc");(0,F.EE)(F.NO.service_details,F.ir.service_details.select_field_in_breakdown_clicked,{field:e,previousField:t.getValueText(),sortBy:r,sortByDirection:n,view:"fields"});const a=o.jh.getAncestor(this,b.Mn);(0,p.fg)(d._J.field,e,a)})),this.addActivationHandler(this.onActivate.bind(this))}}D($,"LabelsMenu",(({model:e})=>{const{body:t,loading:n,search:r}=e.useState(),i=(0,l.useStyles2)(B),s=(0,f.Hj)(e),{options:o,value:c}=s.useState();return a().createElement("div",{className:i.labelsMenuWrapper},t instanceof O.E&&a().createElement(O.E.Selector,{model:t}),t instanceof E.u&&a().createElement(E.u.Selector,{model:t}),t instanceof E.u&&a().createElement(r.Component,{model:r}),!n&&o.length>1&&a().createElement(x.u,{label:"Field",options:o,value:String(c),onChange:e.onFieldSelectorChange}))})),D($,"ValuesMenu",(({model:e})=>{const{loading:t,sort:n}=e.useState(),r=(0,l.useStyles2)(B),i=(0,f.Hj)(e),{value:s}=i.useState();return a().createElement("div",{className:r.valuesMenuWrapper},!t&&s!==T.To&&a().createElement(a().Fragment,null,a().createElement(n.Component,{model:n})))})),D($,"Component",(({model:e})=>{const{blockingMessage:t,body:n,loading:r}=e.useState(),i=(0,l.useStyles2)(B);return a().createElement("div",{className:i.container},a().createElement(j.O,{blockingMessage:t,isLoading:r},n instanceof O.E&&e&&a().createElement($.LabelsMenu,{model:e}),a().createElement("div",{className:i.content},n&&a().createElement(n.Component,{model:n}))))}));const I={button:(0,i.css)({marginLeft:"1.5rem"}),link:(0,i.css)({textDecoration:"underline"})};function B(e){return{container:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1,gap:e.spacing(1),minHeight:"100%"}),content:(0,i.css)({display:"flex",flexGrow:1,paddingTop:e.spacing(0)}),labelsMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row-reverse",flexGrow:0,gap:e.spacing(2),justifyContent:"space-between"}),valuesMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row",flexGrow:0,gap:e.spacing(2)})}}},8072:(e,t,n)=>{n.d(t,{O:()=>M});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(9736),l=n(2007),c=n(1532),u=n(4702),d=n(7839),p=n(7478),g=n(2601),h=n(5553),f=n(7352),m=n(8313),v=n(158),b=n(713),y=n(7243),S=n(42),w=n(1475),O=n(20),x=n(5700),E=n(6081),C=n(2606),k=n(4059);function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class j extends o.Bs{onActivate(){var e;const t=(0,h.ir)(this),n=o.jh.getAncestor(this,f.Mn).state.$detectedLabelsData;this.state.body?(null==n||null===(e=n.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Done&&this.update(null==n?void 0:n.state.data.series[0]):this.setState({body:this.build()}),this._subs.add(null==n?void 0:n.subscribeToState(((e,t)=>{var n;(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&this.update(e.data.series[0])}))),this._subs.add(t.subscribeToState((()=>{this.updateQueriesOnFieldsVariableChange()})))}getPanelByIndex(e,t){const n=e.state.children[t].state.body;return{panel:n,title:n.state.title}}update(e){var t;const n=(0,h.P4)(this).state.options.filter((e=>e.value!==O.To)).map((e=>e.label));null===(t=this.state.body)||void 0===t||t.state.layouts.forEach((t=>{let r=[];const a=t,i=new Set(n),s=a.state.children;for(let e=0;e<s.length;e++){const{title:t}=this.getPanelByIndex(a,e);i.has(t)?i.delete(t):(s.splice(e,1),e--),r.push(t)}const o=Array.from(i).map((e=>({label:e,value:e})));s.push(...this.buildChildren(o));const l=this.calculateCardinalityMap(e);s.sort(this.sortChildren(l)),a.setState({children:s})}))}calculateCardinalityMap(e){const t=new Map;if(null==e?void 0:e.length)for(let n=0;n<(null==e?void 0:e.fields.length);n++){const r=e.fields[n].name,a=e.fields[n].values[0];t.set(r,a)}return t}build(){var e;const t=(0,h.P4)(this);o.jh.getAncestor(this,M).state.search.reset();const n=this.buildChildren(t.state.options),r=o.jh.getAncestor(this,f.Mn).state.$detectedLabelsData;if((null==r||null===(e=r.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Done){const e=this.calculateCardinalityMap(null==r?void 0:r.state.data.series[0]);n.sort(this.sortChildren(e))}const a=n.map((e=>e.clone()));return new E.U({active:"grid",layouts:[new o.gF({autoRows:"200px",children:n,isLazy:!0,templateColumns:S.di}),new o.gF({autoRows:"200px",children:a,isLazy:!0,templateColumns:"1fr"})],options:[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}buildChildren(e){const t=[];for(const n of e){const{value:e}=n,r=String(e);if(e===O.To||!e)continue;const a=(0,S.oj)(this,String(n.value),String(n.value)),i=(0,w.rS)([a]);t.push(new o.xK({body:o.d0.timeseries().setTitle(r).setData(i).setHeaderActions([new C.X({fieldType:d._J.label,labelName:r})]).setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars).setHoverHeader(!1).setShowMenuAlways(!0).setOverrides(w.jC).setMenu(new x.GD({investigationOptions:{labelName:r}})).setSeriesLimit(k.l).build()}))}return t}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;if(r.state.title===O.e4)return-1;if(a.state.title===O.e4)return 1;var i;const s=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var o;return(null!==(o=e.get(a.state.title))&&void 0!==o?o:0)-s}}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&a().createElement(E.U.Selector,{model:t}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){P(e,t,n[t])}))}return e}({},e)),P(this,"updateQueriesOnFieldsVariableChange",(()=>{var e;null===(e=this.state.body)||void 0===e||e.state.layouts.forEach((e=>{const t=e;for(let e=0;e<t.state.children.length;e++){const{panel:a,title:i}=this.getPanelByIndex(t,e),s=a.state.$data,l=(0,S.oj)(this,i,i);var n,r;if(s instanceof o.dt)if(l.expr===(null==s||null===(r=s.state.queries)||void 0===r||null===(n=r[0])||void 0===n?void 0:n.expr))break;a.setState({$data:(0,w.rS)([l])})}}))})),this.addActivationHandler(this.onActivate.bind(this))}}P(j,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,l.useStyles2)(x.K_);return t?a().createElement("span",{className:n.panelWrapper},t&&a().createElement(t.Component,{model:t})):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})}));var F=n(6887),L=n(1049),_=n(5659),T=n(4509),D=n(8502),N=n(9683),$=n(4351);function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){I(e,t,n[t])}))}return e}function A(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class M extends o.Bs{onActivate(){var e,t,n,r,a;const i=o.jh.getAncestor(this,f.Mn),l=(0,h.P4)(this);this.setState({error:(null===(t=i.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Error,loading:(null===(r=i.state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)!==s.LoadingState.Done}),this._subs.add(this.subscribeToEvent(m.x,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent(L.gf,this.handleSortByChange)),this._subs.add(null===(a=i.state.$detectedLabelsData)||void 0===a?void 0:a.subscribeToState(this.onDetectedLabelsDataChange)),this._subs.add((0,h.cR)(this).subscribeToState(((e,t)=>{this.onLabelsVariableChange(e,t)}))),this._subs.add(l.subscribeToState(((e,t)=>{this.onGroupByVariableChange(e,t)})));const c=(0,f.TG)(this);c&&this.updateOptions(c),(0,N.NX)(this)}onGroupByVariableChange(e,t){(e.value!==t.value||!(0,c.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof b.a)&&this.updateBody()}onLabelsVariableChange(e,t){let{labelName:n}=(0,N.W6)();n===O.ky&&(n=O.OX);const r=(0,h.P4)(this),a=e.filters.find((e=>e.key===n)),i=t.filters.find((e=>e.key===n));r.state.value===O.To&&a!==i&&this.setState({body:void 0,error:void 0,loading:!0})}updateOptions(e){if(!e||!e.length)return void this.setState({body:new b.a({type:"labels"}),loading:!1});const t=(0,h.P4)(this),n=(0,D.dD)(e.fields.map((e=>e.name)));var r;t.setState({loading:!1,options:n,value:null!==(r=this.state.value)&&void 0!==r?r:O.To})}updateBody(){const e=(0,h.P4)(this);if(!e.state.options||!e.state.options.length)return;const t={blockingMessage:void 0,error:!1,loading:!1};e.hasAllValue()&&this.state.body instanceof F.u?t.body=new j({}):!e.hasAllValue()&&this.state.body instanceof j?t.body=new F.u({}):void 0===this.state.body?e.state.options.length>0?t.body=e.hasAllValue()?new j({}):new F.u({}):t.body=new b.a({type:"labels"}):this.state.body instanceof b.a&&e.state.options.length>0&&(t.body=e.hasAllValue()?new j({}):new F.u({})),this.setState(B({},t))}constructor(e){var t,n,r;super(A(B({},e),{$variables:null!==(r=e.$variables)&&void 0!==r?r:new o.Pj({variables:[new u.m({defaultToAll:!1,includeAll:!0,name:O.Jg,options:null!==(t=e.options)&&void 0!==t?t:[],value:null!==(n=e.value)&&void 0!==n?n:O.To})]}),loading:!0,search:new m.G("labels"),sort:new L.wd({target:"labels"}),value:e.value})),I(this,"_variableDependency",new o.Sh(this,{variableNames:[O.MB]})),I(this,"onDetectedLabelsDataChange",((e,t)=>{var n,r,a,i,o,l,u,d,p;if((null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&(null===(r=e.data.series)||void 0===r?void 0:r[0])&&!(0,c.B)(null===(i=e.data.series)||void 0===i||null===(a=i[0])||void 0===a?void 0:a.fields,null===(u=t.data)||void 0===u||null===(l=u.series)||void 0===l||null===(o=l[0])||void 0===o?void 0:o.fields))this.updateOptions(null===(p=e.data.series)||void 0===p?void 0:p[0]);else if((null===(d=e.data)||void 0===d?void 0:d.state)===s.LoadingState.Done){(0,h.P4)(this).setState({loading:!1})}})),I(this,"handleSortByChange",(e=>{if("labels"!==e.target)return;const t=this.state.body;if(t instanceof F.u){o.jh.findDescendents(t,v.h).forEach((t=>{t.sort(e.sortBy,e.direction)}))}(0,T.EE)(T.NO.service_details,T.ir.service_details.value_breakdown_sort_change,{criteria:e.sortBy,direction:e.direction,target:"labels"})})),I(this,"onChange",(e=>{if(!e)return;const t=(0,h.P4)(this);t.changeValueTo(e);const{direction:n,sortBy:r}=(0,$.vs)("labels",g.DEFAULT_SORT_BY,"desc");(0,T.EE)(T.NO.service_details,T.ir.service_details.select_field_in_breakdown_clicked,{label:e,previousLabel:t.getValueText(),sortBy:r,sortByDirection:n,view:"labels"});const a=o.jh.getAncestor(this,f.Mn);(0,p.fg)(d._J.label,e,a)})),this.addActivationHandler(this.onActivate.bind(this))}}function R(e){return{container:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1,gap:e.spacing(1),minHeight:"100%"}),content:(0,i.css)({display:"flex",flexGrow:1,paddingTop:e.spacing(0)}),labelsMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row-reverse",flexGrow:0,gap:e.spacing(2),justifyContent:"space-between"}),valuesMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row",flexGrow:0,gap:e.spacing(2)})}}I(M,"LabelsMenu",(({model:e})=>{const{body:t,loading:n,search:r}=e.useState(),i=(0,h.P4)(e),{options:s,value:o}=i.useState(),c=(0,l.useStyles2)(R);return a().createElement("div",{className:c.labelsMenuWrapper},t instanceof F.u&&a().createElement(F.u.Selector,{model:t}),t instanceof j&&a().createElement(j.Selector,{model:t}),t instanceof F.u&&a().createElement(r.Component,{model:r}),!n&&s.length>0&&a().createElement(y.u,{label:"Label",options:s,value:String(o),onChange:e.onChange}))})),I(M,"ValuesMenu",(({model:e})=>{const{loading:t,sort:n}=e.useState(),r=(0,h.P4)(e),{value:i}=r.useState(),s=(0,l.useStyles2)(R);return a().createElement("div",{className:s.valuesMenuWrapper},!t&&i!==O.To&&a().createElement(a().Fragment,null,a().createElement(n.Component,{model:n})))})),I(M,"Component",(({model:e})=>{const{blockingMessage:t,body:n,error:r,loading:i}=e.useState(),s=(0,l.useStyles2)(R);return a().createElement("div",{className:s.container},a().createElement(_.O,{blockingMessage:t,isLoading:i},r&&a().createElement(l.Alert,{title:"",severity:"warning"},"The labels are not available at this moment. Try using a different time range or check again later."),n instanceof j&&e&&a().createElement(M.LabelsMenu,{model:e}),a().createElement("div",{className:s.content},n&&a().createElement(n.Component,{model:n}))))}))},6887:(e,t,n)=>{n.d(t,{u:()=>N});var r=n(5959),a=n.n(r),i=n(7781),s=n(8531),o=n(9736),l=n(2007),c=n(1532),u=n(376),d=n(42),p=n(5953),g=n(1475),h=n(7985),f=n(2601),m=n(4351),v=n(5553),b=n(5548),y=n(20),S=n(2661),w=n(5700),O=n(158),x=n(713),E=n(8072),C=n(6081),k=n(6779),P=n(5865),j=n(1049);function F(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function L(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){F(i,r,a,s,o,"next",e)}function o(e){F(i,r,a,s,o,"throw",e)}s(void 0)}))}}function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){_(e,t,n[t])}))}return e}function D(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class N extends o.Bs{onActivate(){this.setState({$data:this.buildQueryRunner(),body:this.build()}),this.runQuery(),this.setSubscriptions()}buildQueryRunner(){const e=this.buildQuery();return(0,g.rS)([e],{runQueriesMode:"manual"})}buildQuery(){const e=(0,d.oj)(this,y.zp,String((0,v.P4)(this).state.value)),{filterExpression:t,variableName:n}=this.removeValueLabelFromVariableInterpolation();return e.expr=e.expr.replace(`\${${n}}`,t),e}setSubscriptions(){var e;this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(((e,t)=>{this.onValuesDataQueryChange(e)}))),this._subs.add((0,v.P4)(this).subscribeToState((e=>{e.value===y.To&&this.setState({$data:void 0,body:void 0})}))),this._subs.add(o.jh.getTimeRange(this).subscribeToState((()=>{this.runQuery()}))),this._subs.add((0,v.ir)(this).subscribeToState(((e,t)=>{(0,c.B)(e.filters,t.filters)||this.runQuery()}))),this._subs.add((0,v.oY)(this).subscribeToState(((e,t)=>{(0,c.B)(e.filters,t.filters)||this.runQuery()}))),this._subs.add((0,v.Gk)(this).subscribeToState(((e,t)=>{(0,c.B)(e.filters,t.filters)||this.runQuery()}))),this._subs.add((0,v.Ku)(this).subscribeToState(((e,t)=>{e.value!==t.value&&this.runQuery()})));const t=this.getTagKey();var n=this;this._subs.add((0,v.cR)(this).subscribeToState(function(){var e=L((function*(e,r){(0,c.B)(e.filters.filter((e=>t===y.e4&&e.key!==t)),r.filters.filter((e=>t===y.e4&&e.key!==t)))||n.runQuery()}));return function(t,n){return e.apply(this,arguments)}}()));var r=this;this._subs.add((0,v.iw)(this).subscribeToState(function(){var e=L((function*(e,n){(0,c.B)(e.filters.filter((e=>t!==y.e4&&e.key!==t)),n.filters.filter((e=>t!==y.e4&&e.key!==t)))||r.runQuery()}));return function(t,n){return e.apply(this,arguments)}}()))}rebuildQuery(){var e;null===(e=this.getSceneQueryRunner())||void 0===e||e.setState({queries:[this.buildQuery()]})}runQuery(){this.rebuildQuery();const e=this.getSceneQueryRunner();null==e||e.runQueries()}getSceneQueryRunner(){if(this.state.$data){const e=o.jh.findDescendents(this.state.$data,o.dt);if(1!==e.length){const e=new Error("Unable to find query runner in value breakdown!");throw p.v.error(e,{msg:"LabelValuesBreakdownScene: Unable to find query runner in value breakdown!"}),e}return e[0]}p.v.warn("LabelValuesBreakdownScene: Query is attempting to execute, but query runner is undefined!")}removeValueLabelFromVariableInterpolation(){const e=this.getTagKey();let t,n;if(e===y.e4){const r=(0,v.iw)(this);n=y._Y,t=(0,h._q)(r.state.filters,[e])}else{const r=(0,v.cR)(this);n=y.MB,t=(0,h.VW)(r.state.filters,[e])}return{filterExpression:t,variableName:n}}getTagKey(){const e=(0,v.P4)(this);return String(e.state.value)}onValuesDataQueryChange(e){this.setEmptyStates(e),this.setErrorStates(e)}setErrorStates(e){var t,n;if((null==e||null===(t=e.data)||void 0===t?void 0:t.errors)&&(null===(n=e.data)||void 0===n?void 0:n.state)!==i.LoadingState.Done){var r;const t=this.state.errors;null==e||null===(r=e.data)||void 0===r||r.errors.forEach((e=>{const n=`${e.status}_${e.traceId}_${e.message}`;void 0===t[n]&&(t[n]=D(T({},e),{displayed:!1}))})),this.setState({errors:t}),this.showErrorToast(this.state.errors)}}setEmptyStates(e){var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done)if(e.data.series.length>0&&!(this.state.body instanceof C.U))this.setState({body:this.build()});else if(0===e.data.series.length){const e=o.jh.getAncestor(this,S.P);(0,b.mE)(e).length>1?this.setState({body:new k.W({clearCallback:()=>(0,b.rA)(this)})}):this.setState({body:new x.a({type:"fields"})})}}getActiveLayout(){const e=this.state.body;if(e instanceof C.U){const t=null==e?void 0:e.state.layouts.find((e=>e.isActive));if(t instanceof o.G1)return t}}activeLayoutContainsNoPanels(){const e=this.getActiveLayout();if(e){return o.jh.findDescendents(e,O.h).some((e=>{const t=e.state.body.state.children[0];return t instanceof o.vA||t instanceof o.dM}))}return!1}build(){const e=(0,v.P4)(this).state,t=String(null==e?void 0:e.value),n=o.jh.getAncestor(this,E.O);let r=o.d0.timeseries();r=r.setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars).setShowMenuAlways(!0).setOverrides(g.jC).setMenu(new w.GD({})).setTitle(t);const i=r.build(),{direction:s,sortBy:c}=(0,m.vs)("labels",f.DEFAULT_SORT_BY,"desc"),p=()=>{var e;return null!==(e=n.state.search.state.filter)&&void 0!==e?e:""};return new C.U({active:"grid",layouts:[new o.G1({children:[new o.dM({reactNode:a().createElement(E.O.LabelsMenu,{model:n})}),new o.vA({body:i,minHeight:300})],direction:"column"}),new o.G1({children:[new o.dM({reactNode:a().createElement(E.O.LabelsMenu,{model:n})}),new P.s7({levelColor:!0,tagKey:this.getTagKey(),title:t,type:"label"}),new o.dM({reactNode:a().createElement(E.O.ValuesMenu,{model:n})}),new O.h({body:new o.gF({autoRows:"200px",children:[new o.vA({body:new o.dM({reactNode:a().createElement(l.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0,templateColumns:d.di}),direction:s,getFilter:p,getLayoutChild:(0,u.Zp)(j.ee,l.DrawStyle.Bars,y.MB,o.jh.getAncestor(this,E.O).state.sort,t),sortBy:c})],direction:"column"}),new o.G1({children:[new o.dM({reactNode:a().createElement(E.O.LabelsMenu,{model:n})}),new P.s7({levelColor:!0,tagKey:this.getTagKey(),title:t,type:"label"}),new o.dM({reactNode:a().createElement(E.O.ValuesMenu,{model:n})}),new O.h({body:new o.gF({autoRows:"200px",children:[new o.vA({body:new o.dM({reactNode:a().createElement(l.LoadingPlaceholder,{text:"Loading..."})})})],templateColumns:"1fr"}),direction:s,getFilter:p,getLayoutChild:(0,u.Zp)(j.ee,l.DrawStyle.Bars,y.MB,o.jh.getAncestor(this,E.O).state.sort,t),sortBy:c})],direction:"column"})],options:[{label:"Single",value:"single"},{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}showErrorToast(e){const t=(0,s.getAppEvents)();let n=[];for(const t in e){const r=e[t];r.displayed||(n.push(r),r.displayed=!0)}n.length&&(this.activeLayoutContainsNoPanels()||t.publish({payload:null==n?void 0:n.map(((e,t)=>this.renderError(t,e))),type:i.AppEvents.alertError.name}),this.setState({errors:e}))}renderError(e,t){return a().createElement("div",{key:e},t.status&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Status"),": ",t.status," ",a().createElement("br",null)),t.message&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Message"),": ",t.message," ",a().createElement("br",null)),t.traceId&&a().createElement(a().Fragment,null,a().createElement("strong",null,"TraceId"),": ",t.traceId))}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&t instanceof C.U&&a().createElement(C.U.Selector,{model:t}))}constructor(e){super(D(T({},e),{errors:{}})),this.addActivationHandler(this.onActivate.bind(this))}}_(N,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,l.useStyles2)(w.K_);return t?a().createElement("span",{className:n.panelWrapper},t&&a().createElement(t.Component,{model:t})):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})}))},6081:(e,t,n)=>{n.d(t,{U:()=>g});var r=n(5959),a=n.n(r),i=n(6089),s=n(9736),o=n(2007),l=n(9683),c=n(4509),u=n(4351);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class g extends s.Bs{constructor(e){var t;super(p(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}({},e),{active:null!==(t=e.active)&&void 0!==t?t:"grid"})),d(this,"isTopLevelLayoutType",(()=>this.state.options.every((e=>"single"!==e.value)))),d(this,"updateLayout",(()=>{const e=(0,u.Dy)();e&&("single"===e&&this.isTopLevelLayoutType()?this.setState({active:"grid"}):this.setState({active:"grid"===e||"rows"===e||"single"===e?e:"grid"}))})),d(this,"onLayoutChange",(e=>{(0,c.EE)(c.NO.service_details,c.ir.service_details.layout_type_changed,{layout:e,view:(0,l.FT)()}),(0,u.zu)(e),this.setState({active:e})})),d(this,"onActivate",(()=>{this.updateLayout()})),this.addActivationHandler(this.onActivate.bind(this))}}d(g,"Selector",(function({model:e}){const{active:t,options:n}=e.useState(),r=(0,o.useStyles2)(h);return a().createElement(o.Field,{className:r.field},a().createElement(o.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))})),d(g,"Component",(({model:e})=>{const{active:t,layouts:n,options:r}=e.useState(),i=r.findIndex((e=>e.value===t));if(-1===i)return null;const s=n[i];return a().createElement(s.Component,{model:s})}));const h=e=>({field:(0,i.css)({marginBottom:0})})},6779:(e,t,n)=>{n.d(t,{W:()=>p});var r,a,i,s=n(5959),o=n.n(s),l=n(9736),c=n(2007),u=n(7191),d=n(6830);class p extends l.Bs{}i=({model:e})=>{const{clearCallback:t}=e.useState();return o().createElement(u.R,null,o().createElement(c.Alert,{title:"",severity:"info"},"No labels match these filters."," ",o().createElement(c.Button,{className:d.ZI.button,onClick:()=>t()},"Clear filters")," "))},(a="Component")in(r=p)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},5865:(e,t,n)=>{n.d(t,{Zb:()=>y,s$:()=>w,s7:()=>b});var r=n(5959),a=n.n(r),i=n(7781),s=n(9736),o=n(2007),l=n(4509),c=n(42),u=n(5570),d=n(5953),p=n(1475),g=n(4351),h=n(5553),f=n(20),m=n(5700);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class b extends s.Bs{onActivate(){var e;const t=null!==(e=(0,g.ex)("collapsed",[m.Ci.collapsed,m.Ci.expanded]))&&void 0!==e?e:m.Ci.expanded,n=function(e,t){var n;const r=null!==(n=(0,g.ex)("collapsed",[m.Ci.collapsed,m.Ci.expanded]))&&void 0!==n?n:m.Ci.expanded,a=s.d0.timeseries().setTitle(e).setMenu(new m.GD({})).setCollapsible(!0).setCollapsed(r===m.Ci.collapsed).setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars).setShowMenuAlways(!0).setSeriesLimit(100);(null==t?void 0:t.levelColor)&&a.setOverrides(p.jC);return a.build()}(this.state.title,{levelColor:this.state.levelColor}),r=S(t);n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(t)}),this.setState({body:new s.G1({children:[new s.vA({body:n})],height:r,key:w,maxHeight:r,minHeight:r,wrap:"nowrap"})}),this._subs.add(n.subscribeToState(((e,t)=>{if(e.collapsed!==t.collapsed){y(s.jh.getAncestor(n,s.G1),e.collapsed?m.Ci.collapsed:m.Ci.expanded),(0,g.IW)("collapsed",e.collapsed?m.Ci.collapsed:m.Ci.expanded)}})))}initLegendOptions(e,t,n){e&&("label"===this.state.type?t===f.e4?(0,p.C6)(n,e,this):(0,p.dO)(t,n,e,this):(0,p.Nr)(t,n,e,this))}getQuerySubscription(e,t,n){return t.subscribeToState(((t,r)=>{var a;(null===(a=t.data)||void 0===a?void 0:a.state)===i.LoadingState.Done&&("label"===this.state.type?e===f.e4?(0,p.C6)(n,t.data.series,this):(0,p.dO)(e,n,t.data.series,this):(0,p.Nr)(e,n,t.data.series,this))}))}getFieldsVariableLegendSyncSubscription(e,t){return null==t?void 0:t.subscribeToState((()=>{var t,n;const r=null===(t=this.state.body)||void 0===t?void 0:t.state.children[0];if(!(r instanceof s.vA))throw new Error("Cannot find sceneFlexItem");const a=r.state.body;if(!(a instanceof s.Eb))throw new Error("ValueSummary - getFieldsVariableLegendSyncSubscription: Cannot find VizPanel");const i=null===(n=s.jh.getData(this).state.data)||void 0===n?void 0:n.series;i?(0,p.Nr)(e,a,i,this):d.v.warn("ValueSummary - getFieldsVariableLegendSyncSubscription: missing dataframe!")}))}getLabelsVariableLegendSyncSubscription(e){const t=e===f.e4,n=t?(0,h.iw)(this):(0,h.cR)(this);return null==n?void 0:n.subscribeToState((()=>{var n,r;const a=null===(n=this.state.body)||void 0===n?void 0:n.state.children[0];if(!(a instanceof s.vA))throw new Error("Cannot find sceneFlexItem");const i=a.state.body;if(!(i instanceof s.Eb))throw new Error("ValueSummary - getLabelsVariableLegendSyncSubscription: Cannot find VizPanel");const o=null===(r=s.jh.getData(this).state.data)||void 0===r?void 0:r.series;o?t?(0,p.C6)(i,o,this):(0,p.dO)(e,i,o,this):d.v.warn("ValueSummary - getLabelsVariableLegendSyncSubscription: missing dataframe!")}))}constructor(e){super(e),v(this,"extendTimeSeriesLegendBus",(e=>{var t,n;const r=s.jh.getData(this),a=null===(t=r.state.data)||void 0===t?void 0:t.series,i=this.state.tagKey,o=null===(n=this.state.body)||void 0===n?void 0:n.state.children[0];if(!(o instanceof s.vA))throw new Error("Cannot find sceneFlexItem");const d=o.state.body;if(!(d instanceof s.Eb))throw new Error("Cannot find VizPanel");this.initLegendOptions(a,i,d),"label"===this.state.type?this._subs.add(this.getLabelsVariableLegendSyncSubscription(i)):(this._subs.add(this.getFieldsVariableLegendSyncSubscription(i,(0,h.ir)(this))),this._subs.add(this.getFieldsVariableLegendSyncSubscription(i,(0,h.oY)(this)))),this._subs.add(this.getQuerySubscription(i,r,d)),e.onToggleSeriesVisibility=(e,t)=>{let n;n="label"===this.state.type?i===f.e4?(0,u.PE)(e,this):(0,c.R7)(i,e,this):(0,c.zr)(i,e,this),(0,l.EE)(l.NO.service_details,l.ir.service_details.label_in_panel_summary_clicked,{action:n,label:e})}})),this.addActivationHandler(this.onActivate.bind(this))}}function y(e,t){const n=S(t);e.setState({height:n,maxHeight:n,minHeight:n})}function S(e){return e===m.Ci.collapsed?35:300}v(b,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement("div",null,a().createElement(t.Component,{model:t})):null}));const w="value_summary_panel"},9284:(e,t,n)=>{n.d(t,{D:()=>c});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const c=e=>{var{onChange:t,onClear:n,placeholder:r,suffix:i,value:c}=e,d=l(e,["onChange","onClear","placeholder","suffix","value"]);const p=(0,s.useStyles2)(u);return a().createElement(s.Input,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){o(e,t,n[t])}))}return e}({value:c,onChange:t,suffix:a().createElement("span",{className:p.suffixWrapper},n&&c?a().createElement(s.IconButton,{"aria-label":"Clear search",tooltip:"Clear search",onClick:n,name:"times",className:p.clearIcon}):void 0,i&&i),prefix:a().createElement(s.Icon,{name:"search"}),placeholder:r},d))},u=e=>({clearIcon:(0,i.css)({cursor:"pointer"}),suffixWrapper:(0,i.css)({display:"inline-flex",gap:e.spacing(.5)})})},2606:(e,t,n)=>{n.d(t,{X:()=>I});var r=n(5959),a=n.n(r),i=n(6089),s=n(3241),o=n(7781),l=n(9736),c=n(2007),u=n(7839),d=n(376),p=n(6854),g=n(5953),h=n(7478),f=n(9683),m=n(5719),v=n(3571),b=n(5553),y=n(20),S=n(2085),w=n(7352),O=n(9405);function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var E=function(e){return e.ns="ns",e.us="µs",e.ms="ms",e.s="s",e.m="m",e.h="h",e}(E||{});var C=function(e){return e.B="B",e.KB="KB",e.MB="MB",e.GB="GB",e.TB="TB",e}(C||{});class k extends l.Bs{onActivate(){const e=(0,b.bY)((0,O.ts)(this.state.labelName,this.state.variableType),this).state.filters.filter((e=>e.key===this.state.labelName)),t=e.find((e=>e.operator===p.w7.gte||e.operator===p.w7.gt)),n=e.find((e=>e.operator===p.w7.lte||e.operator===p.w7.lt));let r={};if("duration"===this.state.fieldType||"bytes"===this.state.fieldType){if(t){const e=P((0,b.bu)(t).value,this.state.fieldType);e&&(r.gt=e.value,r.gtu=e.unit,r.gte=t.operator===p.w7.gte)}if(n){const e=P((0,b.bu)(n).value,this.state.fieldType);e&&(r.lt=e.value,r.ltu=e.unit,r.lte=n.operator===p.w7.lte)}}else{if(t){const e=(0,b.bu)(t).value;r.gt=Number(e),r.gtu="",r.gte=t.operator===p.w7.gte}if(n){const e=(0,b.bu)(n).value;r.lt=Number(e),r.ltu="",r.lte=n.operator===p.w7.lte}}0!==Object.keys(r).length&&(r.hasExistingFilter=!0),this.setState(r)}onSubmit(){this.state.gt?(0,O.vn)(this.state.labelName,this.state.gt.toString()+this.state.gtu,this.state.gte?p.w7.gte:p.w7.gt,this,this.state.variableType):(0,O.u7)(this.state.labelName,this,this.state.gte?p.w7.gte:p.w7.gt,this.state.variableType),this.state.lt?(0,O.vn)(this.state.labelName,this.state.lt.toString()+this.state.ltu,this.state.lte?p.w7.lte:p.w7.lt,this,this.state.variableType):(0,O.u7)(this.state.labelName,this,this.state.lte?p.w7.lte:p.w7.lt,this.state.variableType);l.jh.getAncestor(this,I).togglePopover()}constructor(e){let t;const n=e.fieldType;if("bytes"===n)t={gtu:"B",ltu:"B"};else if("duration"===n)t={gtu:"s",ltu:"s"};else{if("float"!==n&&"int"!==n)throw new Error(`field type incorrectly defined: ${n}`);t={gtu:"",ltu:""}}super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){x(e,t,n[t])}))}return e}({},e,t)),x(this,"onInputKeydown",(e=>{const t=void 0===this.state.gt&&void 0===this.state.lt;"Enter"!==e.key||t||this.onSubmit()})),this.addActivationHandler(this.onActivate.bind(this))}}function P(e,t){if("duration"===t){const t=Object.values(E).find((t=>{const n=t.length;return e.slice(-1*n)===t}));if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}if("bytes"===t){const t=Object.values(C).sort(((e,t)=>t.length-e.length)).find((t=>{const n=t.length;return e.slice(-1*n)===t}));if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}}function j(e){if("duration"===e){return Object.keys(E).map((e=>({label:e,text:e,value:E[e]})))}if("bytes"===e){return Object.keys(C).map((e=>({label:e,text:e,value:C[e]})))}const t=new Error(`invalid field type: ${e}`);throw g.v.error(t,{msg:"getUnitOptions, invalid field type"}),t}x(k,"Component",(({model:e})=>{const t=(0,c.useStyles2)(F),{fieldType:n,gt:r,gte:s,gtu:o,hasExistingFilter:u,labelName:d,lt:p,lte:g,ltu:h}=e.useState(),f="float"!==n&&"int"!==n&&n!==d?`(${n})`:void 0,m=l.jh.getAncestor(e,I),b=void 0===r&&void 0===p;return a().createElement(c.ClickOutsideWrapper,{useCapture:!0,onClick:()=>m.togglePopover()},a().createElement(c.Stack,{direction:"column",gap:0,role:"tooltip"},a().createElement("div",{className:t.card.body},a().createElement("div",{className:t.card.title},d," ",f),a().createElement("div",{className:t.card.fieldWrap},a().createElement(c.FieldSet,{className:t.card.fieldset},a().createElement(c.Field,{"data-testid":v.b.breakdowns.common.filterNumericPopover.inputGreaterThanInclusive,horizontal:!0,className:(0,i.cx)(t.card.field,t.card.inclusiveField)},a().createElement(c.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==s?s.toString():"false",options:[{label:"Greater than",value:"false"},{label:"Greater than or equal",value:"true"}],onChange:t=>e.setState({gte:"true"===t.value})})),a().createElement(c.Field,{"data-testid":v.b.breakdowns.common.filterNumericPopover.inputGreaterThan,horizontal:!0,className:t.card.field},a().createElement(c.Input,{onKeyDownCapture:e.onInputKeydown,autoFocus:!0,onChange:t=>{e.setState({gt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0})},className:t.card.numberInput,value:r,type:"number"})),"float"!==n&&"int"!==n&&a().createElement(c.Label,null,a().createElement(c.Field,{"data-testid":v.b.breakdowns.common.filterNumericPopover.inputGreaterThanUnit,horizontal:!0,className:t.card.field,label:a().createElement("span",{className:t.card.unitFieldLabel},"Unit")},a().createElement(c.Select,{onChange:t=>{e.setState({gtu:t.value})},menuShouldPortal:!1,options:j(n),className:t.card.selectInput,value:o})))),a().createElement(c.FieldSet,{className:t.card.fieldset},a().createElement(c.Field,{"data-testid":v.b.breakdowns.common.filterNumericPopover.inputLessThanInclusive,horizontal:!0,className:(0,i.cx)(t.card.field,t.card.inclusiveField)},a().createElement(c.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==g?g.toString():"false",options:[{label:"Less than",value:"false"},{label:"Less than or equal",value:"true"}],onChange:t=>e.setState({lte:"true"===t.value})})),a().createElement(c.Field,{"data-testid":v.b.breakdowns.common.filterNumericPopover.inputLessThan,horizontal:!0,className:t.card.field},a().createElement(c.Input,{onKeyDownCapture:e.onInputKeydown,onChange:t=>e.setState({lt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0}),className:t.card.numberInput,value:p,type:"number"})),"float"!==n&&"int"!==n&&a().createElement(c.Label,null,a().createElement(c.Field,{"data-testid":v.b.breakdowns.common.filterNumericPopover.inputLessThanUnit,horizontal:!0,className:t.card.field,label:a().createElement("span",{className:t.card.unitFieldLabel},"Unit")},a().createElement(c.Select,{onChange:t=>{e.setState({ltu:t.value})},menuShouldPortal:!1,options:j(n),className:t.card.selectInput,value:h}))))),a().createElement("div",{className:t.card.buttons},u&&a().createElement(c.Button,{"data-testid":v.b.breakdowns.common.filterNumericPopover.removeButton,disabled:!u,onClick:()=>{e.setState({gt:void 0,lt:void 0}),e.onSubmit()},size:"sm",variant:"destructive",fill:"outline"},"Remove"),a().createElement(c.Button,{"data-testid":v.b.breakdowns.common.filterNumericPopover.submitButton,disabled:b,onClick:()=>e.onSubmit(),size:"sm",variant:"primary",fill:"outline",type:"submit"},"Add"),a().createElement(c.Button,{"data-testid":v.b.breakdowns.common.filterNumericPopover.cancelButton,onClick:()=>m.togglePopover(),size:"sm",variant:"secondary",fill:"outline"},"Cancel")))))}));const F=e=>({card:{body:(0,i.css)({padding:e.spacing(2)}),buttons:(0,i.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(1.5),justifyContent:"flex-end",marginTop:e.spacing(1)}),field:(0,i.css)({alignItems:"center",display:"flex",marginBottom:e.spacing(1)}),fieldset:(0,i.css)({alignItems:"center",display:"flex",justifyContent:"space-between",marginBottom:0,width:"100%"}),fieldWrap:(0,i.css)({display:"flex",flexDirection:"column",paddingBottom:0,paddingTop:e.spacing(2)}),inclusiveField:(0,i.css)({marginRight:e.spacing(1)}),inclusiveInput:(0,i.css)({minWidth:"185px"}),numberFieldLabel:(0,i.css)({width:"100px"}),numberInput:(0,i.css)({width:"75px"}),p:(0,i.css)({maxWidth:300}),selectInput:(0,i.css)({minWidth:"65px"}),switchFieldLabel:(0,i.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1)}),title:(0,i.css)({}),unitFieldLabel:(0,i.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1.5)})}});function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){L(e,t,n[t])}))}return e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const D="Include",N="Exclude",$="Add to filter";class I extends l.Bs{onChange(e){const t=this.getVariable(),n=t.state.name,r=this.getExistingFilter(t),a=(0,b.z2)(n,r);(null==r?void 0:r.operator)===p.w7.NotEqual&&a.value===y.ZO&&e.value===D?this.clearFilter(n):e.value===D?this.onClickExcludeEmpty(n):e.value===N?this.onClickIncludeEmpty(n):e.value===$&&this.onClickNumericFilter(n),this.setState({selectedValue:e})}getExistingFilter(e){let{labelName:t}=(0,f.W6)();if(this.state.labelName!==t)return null==e?void 0:e.state.filters.find((e=>e.key===this.state.labelName))}onActivate(){var e,t;const n=l.jh.getAncestor(this,w.Mn);(null===(t=n.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)===o.LoadingState.Done&&this.calculateSparsity(),this._subs.add(l.jh.getData(this).subscribeToState((e=>{var t,r,a,i;(null===(t=e.data)||void 0===t?void 0:t.state)===o.LoadingState.Done&&((null===(a=n.state.$data)||void 0===a||null===(r=a.state.data)||void 0===r?void 0:r.state)===o.LoadingState.Done&&this.calculateSparsity(),this._subs.add(null===(i=n.state.$data)||void 0===i?void 0:i.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===o.LoadingState.Done&&this.calculateSparsity()}))))})))}togglePopover(){this.setState({showPopover:!this.state.showPopover})}calculateSparsity(){var e;const t=l.jh.getAncestor(this,w.Mn),n=(0,w.tn)(null===(e=t.state.$data)||void 0===e?void 0:e.state.data),r=null==n?void 0:n.fields.find((e=>"labels"===e.name)),a=l.jh.getData(this),i=(0,m.UX)(a,(e=>e instanceof l.dt),l.dt);if(i){const e=i.state.queries[0];(null==e?void 0:e.expr.includes("avg_over_time"))&&this.setState({hasNumericFilters:!0})}if(!r||!n)return void this.setState({hasSparseFilters:!1});const s=this.getVariable(),o=r.values.reduce(((e,t)=>((null==t?void 0:t[this.state.labelName])&&e++,e)),0),c=l.jh.getAncestor(this,l.Eb);if(void 0!==o&&n.length>0){const e=(o/n.length*100).toLocaleString(),t=`${this.state.labelName} exists on ${e}% of ${n.length} sampled log lines`;c.setState({description:t})}else c.setState({description:void 0});const u=this.getExistingFilter(s),d=u&&s.state.name===y.mB?(0,b.bu)(u):void 0;o<n.length||(null==d?void 0:d.value)===y.ZO?this.setState({hasSparseFilters:!0}):this.setState({hasSparseFilters:!1})}getVariable(){return this.state.fieldType===u._J.field?(0,b.ir)(this):this.state.labelName===y.e4?(0,b.iw)(this):(0,b.cR)(this)}constructor(e){super(T(_({},e),{showPopover:!1})),L(this,"onClickNumericFilter",(e=>{const t=(0,w.rD)(this),n=(0,d.ph)(this.state.labelName,t);if(!n||"string"===n||"boolean"===n){const e=new Error(`Incorrect field type: ${n}`);throw g.v.error(e,{msg:`onClickNumericFilter invalid field type ${n}`}),e}this.setState({popover:new k({fieldType:n,labelName:this.state.labelName,variableType:e})}),this.togglePopover()})),L(this,"getViewValuesLink",(()=>{const e=l.jh.getAncestor(this,w.Mn);return(0,h.FB)(this.state.fieldType,this.state.labelName,e)})),L(this,"onClickExcludeEmpty",(e=>{(0,O.Qt)(this.state.labelName,y.ZO,"exclude",this,e)})),L(this,"onClickIncludeEmpty",(e=>{(0,O.Qt)(this.state.labelName,y.ZO,"include",this,e)})),L(this,"clearFilter",(e=>{(0,O.Qt)(this.state.labelName,y.ZO,"clear",this,e)})),L(this,"clearFilters",(e=>{(0,O.hi)(this.state.labelName,this,e),this.state.labelName===y.e4&&(0,S.dm)(this)})),this.addActivationHandler(this.onActivate.bind(this))}}function B(e){const t=(0,c.useStyles2)(A);return a().createElement("span",{className:t.description},e.selected&&a().createElement("span",{className:t.selected}),e.text)}L(I,"Component",(({model:e})=>{const{fieldType:t,hasNumericFilters:n,hasSparseFilters:i,hideValueDrilldown:o,labelName:l,popover:d,selectedValue:g,showPopover:h}=e.useState(),f=e.getVariable(),m=f.useState().name,S=e.getExistingFilter(f),w=(0,b.z2)(m,S),O=(0,c.useStyles2)(M),x=(0,r.useRef)(null),E=t===u._J.label&&f.state.name===y.mB&&0===f.state.filters.filter((e=>e.key!==l&&e.operator===p.w7.Equal)).length,C=(null==S?void 0:S.operator)===p.w7.NotEqual&&w.value===y.ZO,k=!!S;var P;const j=null!==(P=null==g?void 0:g.value)&&void 0!==P?P:C?D:n?$:D,F=!!(null==S?void 0:S.operator)&&[p.w7.gte,p.w7.gt,p.w7.lte,p.w7.lt].includes(S.operator),L=j===$||F,I=j===D&&!L,A={component:()=>a().createElement(B,{selected:I,text:`Include all log lines with ${l}`}),value:D},R={component:()=>a().createElement(B,{selected:!1,text:`Exclude all log lines with ${l}`}),value:N},V={component:()=>a().createElement(B,{selected:L,text:`Add an expression, i.e. ${l} > 30`}),value:$},W=[];n&&W.push(V),i&&(F||W.push(A),W.push(R));const z=C?A:n?V:A;var K;return a().createElement(a().Fragment,null,k&&a().createElement(c.IconButton,{disabled:E,name:"filter",tooltip:`Clear ${l} filters`,onClick:()=>e.clearFilters(m)}),(n||i)&&a().createElement(a().Fragment,null,a().createElement(c.ButtonGroup,{"data-testid":v.b.breakdowns.common.filterButtonGroup},a().createElement(c.Button,{"data-testid":v.b.breakdowns.common.filterButton,ref:x,onClick:()=>e.onChange(null!=g?g:z),size:"sm",fill:"outline",variant:"secondary"},null!==(K=null==g?void 0:g.value)&&void 0!==K?K:z.value),a().createElement(c.ButtonSelect,{"data-testid":v.b.breakdowns.common.filterSelect,className:O.buttonSelect,variant:"default",options:W,onChange:t=>{e.onChange(t)}}))),!0!==o&&a().createElement(c.LinkButton,{title:`View breakdown of values for ${l}`,variant:"primary",fill:"outline",size:"sm","aria-label":`Select ${l}`,href:e.getViewValuesLink()},"Select"),d&&a().createElement(c.PopoverController,{content:a().createElement(d.Component,{model:d})},((e,t,n)=>{const r={onBlur:t,onFocus:e};return a().createElement(a().Fragment,null,x.current&&a().createElement(a().Fragment,null,a().createElement(c.Popover,_(T(_({},n,s.rest),{show:h,wrapperClassName:O.popover,referenceElement:x.current,renderArrow:!0}),r))))})))}));const A=e=>({description:(0,i.css)({fontSize:e.typography.pxToRem(12),textAlign:"left"}),selected:(0,i.css)({"&:before":{backgroundColor:e.colors.warning.main,content:'""',height:"calc(100% - 8px)",left:0,position:"absolute",top:"4px",width:"2px"},label:"selectable-value-selected"})}),M=e=>({buttonSelect:(0,i.css)({border:`1px solid ${e.colors.border.strong}`,borderBottomLeftRadius:0,borderLeft:"none",borderTopLeftRadius:0,height:"24px",padding:1}),description:(0,i.css)({fontSize:e.typography.pxToRem(12),textAlign:"left"}),popover:(0,i.css)({background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3})})},1049:(e,t,n)=>{n.d(t,{ee:()=>v,gf:()=>g,wd:()=>h});var r=n(5959),a=n.n(r),i=n(7781),s=n(9736),o=n(2007),l=n(2601),c=n(3571),u=n(5570),d=n(4351);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends i.BusEventBase{constructor(e,t,n){super(),p(this,"target",void 0),p(this,"sortBy",void 0),p(this,"direction",void 0),this.target=e,this.sortBy=t,this.direction=n}}p(g,"type","sort-criteria-changed");class h extends s.Bs{constructor(e){const{direction:t,sortBy:n}=(0,d.vs)(e.target,l.DEFAULT_SORT_BY,"desc");super({direction:t,sortBy:n,target:e.target}),p(this,"sortingOptions",[{label:"",options:[{description:"Smart ordering of graphs based on the most significant spikes in the data",label:"Most relevant",value:"changepoint"},{description:"Order by the amount of outlying values in the data",label:"Outlying values",value:"outliers"},{description:"Sort graphs by deviation from the average value",label:"Widest spread",value:i.ReducerID.stdDev},{description:"Alphabetical order",label:"Name",value:"alphabetical"},{description:"Sort graphs by total number of logs",label:"Count",value:i.ReducerID.sum},{description:"Sort graphs by the highest values (max)",label:"Highest spike",value:i.ReducerID.max},{description:"Sort graphs by the smallest values (min)",label:"Lowest dip",value:i.ReducerID.min}]},{label:"Percentiles",options:[...i.fieldReducers.selectOptions([],m).options]}]),p(this,"onCriteriaChange",(e=>{e.value&&(this.setState({sortBy:e.value}),(0,d.fq)(this.state.target,e.value,this.state.direction),this.publishEvent(new g(this.state.target,e.value,this.state.direction),!0))})),p(this,"onDirectionChange",(e=>{e.value&&(this.setState({direction:e.value}),(0,d.fq)(this.state.target,this.state.sortBy,e.value),this.publishEvent(new g(this.state.target,this.state.sortBy,e.value),!0))}))}}p(h,"Component",(({model:e})=>{const{direction:t,sortBy:n}=e.useState(),r=e.sortingOptions.find((e=>e.options.find((e=>e.value===n)))),i=null==r?void 0:r.options.find((e=>e.value===n));return a().createElement(a().Fragment,null,a().createElement(o.InlineField,{label:"Sort by",htmlFor:"sort-by-criteria",tooltip:"Calculate a derived quantity from the values in your time series and sort by this criteria. Defaults to standard deviation."},a().createElement(o.Select,{"data-testid":c.b.breakdowns.common.sortByFunction,value:i,width:20,isSearchable:!0,options:e.sortingOptions,placeholder:"Choose criteria",onChange:e.onCriteriaChange,inputId:"sort-by-criteria"})),a().createElement(o.InlineField,null,a().createElement(o.Select,{"data-testid":c.b.breakdowns.common.sortByDirection,onChange:e.onDirectionChange,"aria-label":"Sort direction",placeholder:"",value:t,options:[{label:"Asc",value:"asc"},{label:"Desc",value:"desc"}]})))}));const f=["p10","p25","p75","p90","p99"];function m(e){return e.id>="p1"&&e.id<="p99"&&f.includes(e.id)}function v(e){var t;return null!==(t=(0,u.H7)(e))&&void 0!==t?t:"No labels"}},5659:(e,t,n)=>{n.d(t,{O:()=>o});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007);function o({blockingMessage:e,children:t,isLoading:n}){const r=(0,s.useStyles2)(l);return n&&!e&&(e="Loading..."),n?a().createElement(s.LoadingPlaceholder,{className:r.statusMessage,text:e}):e?a().createElement("div",{className:r.statusMessage},e):a().createElement(a().Fragment,null,t)}function l(e){return{statusMessage:(0,i.css)({fontStyle:"italic",marginTop:e.spacing(7),textAlign:"center"})}}},4059:(e,t,n)=>{n.d(t,{l:()=>r});const r=20},72:(e,t,n)=>{n.d(t,{_:()=>_});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(3571),l=n(8428);function c(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}let p;const g=e=>{var{onChange:t,onClear:n,placeholder:i,regex:o,suffix:c,value:g,width:m}=e,v=d(e,["onChange","onClear","placeholder","regex","suffix","value","width"]);const b=(0,s.useStyles2)(f),[y,S]=(0,r.useState)(!1),[w,O]=(0,r.useState)(""),x=(0,r.useCallback)((e=>{if(!e||!o)return O(""),void S(!1);if(void 0!==p)try{null==p||p.compile(e),S(!1),O("")}catch(e){const t=(0,l.DU)(e);S(!0),t&&O(t)}else h().then((()=>x(e)))}),[o]);return(0,r.useEffect)((()=>{x(g)}),[x,g]),a().createElement(s.Tooltip,{placement:"auto-start",show:!!w&&y,content:w},a().createElement(s.Input,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){u(e,t,n[t])}))}return e}({invalid:y,"aria-invalid":y,rows:2,width:m,onFocusCapture:e=>{v.onFocus&&v.onFocus(e)},value:g,onChange:t,suffix:a().createElement("span",{className:b.suffixWrapper},n&&g?a().createElement(s.IconButton,{"aria-label":"Clear line filter",tooltip:"Clear line filter",onClick:n,name:"times",className:b.clearIcon}):void 0,c&&c),prefix:a().createElement(s.Icon,{name:"search"}),placeholder:i},v)))},h=function(){var e,t=(e=function*(){p=null,p=(yield n.e(470).then(n.bind(n,4470))).RE2JS},function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){c(i,r,a,s,o,"next",e)}function o(e){c(i,r,a,s,o,"throw",e)}s(void 0)}))});return function(){return t.apply(this,arguments)}}(),f=e=>({clearIcon:(0,i.css)({cursor:"pointer"}),suffixWrapper:(0,i.css)({display:"inline-flex",gap:e.spacing(.5)})});var m=n(7781),v=n(6854);function b(e){return{boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,outline:"2px dotted transparent",outlineOffset:"2px",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow",transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)"}}function y(e,t){return{[t.transitions.handleMotion("no-preference","reduce")]:{transitionDuration:"0.2s",transitionProperty:"opacity",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)"},borderRadius:t.shape.radius.default,content:'""',height:`${e}px`,opacity:"0",position:"absolute",width:`${e}px`,zIndex:"-1"}}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){S(e,t,n[t])}))}return e}function O(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const x=e=>{const t=(0,s.useTheme2)(),n=e.caseSensitive?t.colors.text.maxContrast:t.colors.text.disabled,r=E(t),o=(e.caseSensitive?"Disable":"Enable")+" case match";return a().createElement(s.Tooltip,{content:o},a().createElement("button",{onClick:()=>e.onCaseSensitiveToggle(e.caseSensitive?v.ld.caseInsensitive:v.ld.caseSensitive),className:(0,i.cx)(r.button,e.caseSensitive?r.active:null),"aria-label":o},a().createElement("svg",{fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},"Aa"))))},E=(e,t="secondary")=>{const n=16+e.spacing.gridSize;return{active:(0,i.css)({"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:m.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1},"&:hover":{"&:before":{backgroundColor:"none",opacity:0}}}),button:(0,i.css)({"&:before":O(w({},y(n,e)),{position:"absolute"}),"&:focus, &:focus-visible":b(e),"&:focus:not(:focus-visible)":{boxShadow:"none",outline:"none"},"&:hover":{"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:m.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1}},alignItems:"center",background:"transparent",border:"none",boxShadow:"none",color:e.colors.text.primary,display:"inline-flex",justifyContent:"center",margin:`0 ${e.spacing.x0_5} 0 ${e.spacing.x0_5}`,padding:0,position:"relative",zIndex:0})}};function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){C(e,t,n[t])}))}return e}function P(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const j=e=>{const t=(0,s.useTheme2)(),n=e.regex?t.colors.text.maxContrast:t.colors.text.disabled,r=F(t),o=(e.regex?"Disable":"Enable")+" regex";return a().createElement(s.Tooltip,{content:o},a().createElement("button",{onClick:()=>e.onRegexToggle(e.regex?"match":"regex"),className:(0,i.cx)(r.button,e.regex?r.active:null),"aria-label":o},a().createElement("svg",{fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},".*"))))},F=(e,t="secondary")=>{const n=16+e.spacing.gridSize;return{active:(0,i.css)({"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:m.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1},"&:hover":{"&:before":{backgroundColor:"none",opacity:0}}}),button:(0,i.css)({"&:before":P(k({},y(n,e)),{position:"absolute"}),"&:focus, &:focus-visible":b(e),"&:focus:not(:focus-visible)":{boxShadow:"none",outline:"none"},"&:hover":{"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:m.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1}},alignItems:"center",background:"transparent",border:"none",boxShadow:"none",color:e.colors.text.primary,display:"inline-flex",justifyContent:"center",margin:`0 ${e.spacing.x0_5} 0 ${e.spacing.x0_5}`,padding:0,position:"relative",zIndex:0})}},L=30;function _({caseSensitive:e,exclusive:t,focus:n,handleEnter:l,lineFilter:c,onCaseSensitiveToggle:u,onClearLineFilter:d,onInputChange:p,onRegexToggle:h,onSubmitLineFilter:f,regex:m,setExclusive:v,setFocus:b,type:y}){const S=(0,s.useStyles2)((e=>T(e,y))),[w,O]=(0,r.useState)(L);function E(e){var t;const n=Math.max(null!==(t=null==e?void 0:e.length)&&void 0!==t?t:0,L);O(n+9)}return(0,r.useEffect)((()=>{E(c)}),[c,n]),a().createElement("div",{className:S.wrapper},!f&&a().createElement(s.Select,{prefix:null,className:S.select,value:t?"exclusive":"inclusive",options:[{label:"Exclude",value:"exclusive"},{label:"Include",value:"inclusive"}],onChange:()=>v(!t)}),a().createElement(s.Field,{className:S.field},a().createElement(g,{regex:m,width:n?w:void 0,onFocus:()=>b(!0),"data-testid":o.b.exploreServiceDetails.searchLogs,value:null!=c?c:"",className:(0,i.cx)(f?S.inputNoBorderRight:void 0,S.input),onChange:p,suffix:a().createElement("span",{className:`${S.suffix} input-suffix`},a().createElement(x,{caseSensitive:e,onCaseSensitiveToggle:u}),a().createElement(j,{regex:m,onRegexToggle:h})),prefix:null,placeholder:"Search in log lines",onClear:d,onKeyUp:e=>{l(e,c),E(c)}})),f&&a().createElement("span",{className:S.buttonWrap},a().createElement(s.Button,{onClick:()=>{v(!1),f()},className:S.includeButton,variant:"secondary",fill:"outline",disabled:!c},"Include"),a().createElement(s.Button,{onClick:()=>{v(!0),f()},className:S.excludeButton,variant:"secondary",fill:"outline",disabled:!c},"Exclude")))}const T=(e,t)=>({buttonWrap:(0,i.css)({display:"flex",justifyContent:"center"}),excludeButton:(0,i.css)({"&[disabled]":{borderLeft:"none"},borderLeft:"none",borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`}),exclusiveBtn:(0,i.css)({marginRight:"1rem"}),field:(0,i.css)({flex:"0 1 auto",label:"field",marginBottom:0}),includeButton:(0,i.css)({"&[disabled]":{borderRight:"none"},borderLeft:"none",borderRadius:0,borderRight:"none"}),input:(0,i.css)({input:{borderBottomLeftRadius:0,borderTopLeftRadius:0,fontFamily:"monospace",fontSize:e.typography.bodySmall.fontSize,width:"100%"},label:"line-filter-input-wrapper",maxWidth:"editor"===t?"calc(100vw - 198px)":"calc(100vw - 288px)",minWidth:"200px"}),inputNoBorderRight:(0,i.css)({input:{borderBottomRightRadius:0,borderTopRightRadius:0}}),removeBtn:(0,i.css)({borderBottomLeftRadius:0,borderTopLeftRadius:0}),select:(0,i.css)({borderBottomRightRadius:"0",borderRight:"none",borderTopRightRadius:"0",height:"auto",label:"line-filter-exclusion",marginLeft:0,maxWidth:"95px",minHeight:"30px",minWidth:"95px",outline:"none",paddingLeft:0}),submit:(0,i.css)({borderBottomLeftRadius:0,borderTopLeftRadius:0}),suffix:(0,i.css)({display:"inline-flex",gap:e.spacing(.5)}),wrapper:(0,i.css)({display:"flex",width:"100%"})})},2649:(e,t,n)=>{n.d(t,{P:()=>S,Z:()=>y});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(8531),l=n(9736),c=n(2007),u=n(5953),d=n(8428),p=n(9641),g=n(7902),h=n(8996),f=n(4509),m=n(1475),v=n(4351);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class y extends l.Bs{constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}({},e)),b(this,"handleWrapLinesChange",(e=>{this.getLogsPanelScene().setState({prettifyLogMessage:e,wrapLogMessage:e}),(0,v.YK)("wrapLogMessage",e),(0,v.YK)("prettifyLogMessage",e),this.getLogsListScene().setLogsVizOption({prettifyLogMessage:e,wrapLogMessage:e})})),b(this,"onChangeLogsSortOrder",(e=>{this.getLogsPanelScene().setState({sortOrder:e}),(0,v.YK)("sortOrder",e),this.getLogsListScene().setLogsVizOption({sortOrder:e})})),b(this,"getLogsListScene",(()=>l.jh.getAncestor(this,g.i))),b(this,"getLogsPanelScene",(()=>l.jh.getAncestor(this,h.o))),b(this,"clearDisplayedFields",(()=>{this.getLogsListScene().clearDisplayedFields(),(0,f.EE)(f.NO.service_details,f.ir.service_details.logs_clear_displayed_fields)}))}}function S(){const e=o.locationService.getLocation(),t=new URLSearchParams(e.search).get("sortOrder");try{if("string"==typeof t){const e=(0,d.FH)(JSON.parse(t));if(e)return e}}catch(e){u.v.error(e,{msg:"LogOptionsScene(getLogsPanelSortOrderFromURL): unable to parse sortOrder"})}return!1}b(y,"Component",(function({model:e}){const{onChangeVisualizationType:t,visualizationType:n}=e.useState(),{sortOrder:r,wrapLogMessage:i}=e.getLogsPanelScene().useState(),{displayedFields:o}=e.getLogsListScene().useState(),l=(0,c.useStyles2)(w),u=null!=i&&i;return a().createElement("div",{className:l.container},o.length>0&&a().createElement(c.Tooltip,{content:`Clear displayed fields: ${o.join(", ")}`},a().createElement(c.Button,{size:"sm",variant:"secondary",fill:"outline",onClick:e.clearDisplayedFields},"Show original log line")),!m.CT&&a().createElement(a().Fragment,null,a().createElement(c.InlineField,{className:l.buttonGroupWrapper,transparent:!0},a().createElement(c.RadioButtonGroup,{size:"sm",options:[{description:"Show results newest to oldest",label:"Newest first",value:s.LogsSortOrder.Descending},{description:"Show results oldest to newest",label:"Oldest first",value:s.LogsSortOrder.Ascending}],value:r,onChange:e.onChangeLogsSortOrder})),a().createElement(c.InlineField,{className:l.buttonGroupWrapper,transparent:!0},a().createElement(c.RadioButtonGroup,{size:"sm",value:u,onChange:e.handleWrapLinesChange,options:[{description:"Enable wrapping of long log lines",label:"Wrap",value:!0},{description:"Disable wrapping of long log lines",label:"No wrap",value:!1}]}))),a().createElement(p.C,{vizType:n,onChange:t}))}));const w=e=>({buttonGroupWrapper:(0,i.css)({alignItems:"center",margin:0}),container:(0,i.css)({alignItems:"center",display:"flex",gap:e.spacing(1),marginTop:e.spacing(.5)})})},7902:(e,t,n)=>{n.d(t,{i:()=>Jt});var r=n(5959),a=n.n(r),i=n(6089),s=n(8531),o=n(9736),l=n(4509),c=n(5953),u=n(8428),d=n(2165);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}function h(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const f=`${n(9598).s_}.tableColumnWidths`;var m=function(e){return e.text="text",e.labels="labels",e.auto="auto",e}({});const v=(0,r.createContext)({bodyState:"auto",clearSelectedLine:()=>{},columns:{},columnWidthMap:{},filteredColumns:{},setBodyState:()=>{},setColumns:()=>{},setColumnWidthMap:()=>{},setFilteredColumns:()=>{},setVisible:()=>!1,visible:!1});const b=({children:e,clearSelectedLine:t,initialColumns:n,isColumnManagementActive:i,logsFrame:s,setUrlColumns:o,setUrlTableBodyState:l,showColumnManagementDrawer:p,urlTableBodyState:h})=>{const[m,b]=(0,r.useState)(y(n)),[S,w]=(0,r.useState)(null!=h?h:"auto"),[O,x]=(0,r.useState)(void 0),E=function(){let e={};const t=localStorage.getItem(f);if(t)try{return e=(0,u.Zt)(JSON.parse(t)),!1===e&&c.v.error(new u.QX("getColumnWidthsFromLocalStorage: unable to validate values in local storage"),{msg:"NarrowingError: error parsing table column widths from local storage"}),e}catch(e){c.v.error(e,{msg:"error parsing table column widths from local storage"})}return e}(),[C,k]=(0,r.useState)(E),P=(0,r.useCallback)((e=>{if(e){const t=y(e);b(t),o((e=>{let t=[];return Object.keys(e).forEach((n=>{e[n].active&&void 0!==e[n].index&&t.push(n)})),t.sort(((t,n)=>{const r=e[t],a=e[n];return r.index-a.index})),t})(t))}}),[o]),j=(0,r.useCallback)((e=>{w(e),l(e)}),[l]),F=(0,r.useCallback)((e=>{p(e)}),[p]);return(0,r.useEffect)((()=>{n&&P(n)}),[n,P]),(0,r.useEffect)((()=>{h&&w(h)}),[h]),(0,r.useEffect)((()=>{const e=function(e,t){if(!t)return void c.v.warn("missing dataframe, cannot set url state");const n=Object.keys(e).filter((t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active})).sort(((t,n)=>{const r=e[t],a=e[n];return void 0!==r.index&&void 0!==a.index?r.index-a.index:0})),r=t.timeField,a=t.bodyField;if(r&&a||n.length){const e=[];return(null==r?void 0:r.name)&&e.push(r.name),(null==a?void 0:a.name)&&e.push(a.name),n.length?n:e}return[]}(m,s);if(null==e?void 0:e.length){0===Object.keys(m).filter((e=>m[e].active)).length&&function(e,t,n){const r=g({},e);r[(0,d.fF)(n)]={active:!0,cardinality:1/0,index:0,percentOfLinesWithLabel:100,type:"TIME_FIELD"},r[(0,d.Il)(n)]={active:!0,cardinality:1/0,index:1,percentOfLinesWithLabel:100,type:"BODY_FIELD"},t(r)}(m,P,s),x(void 0)}}),[m,s,x,P]),a().createElement(v.Provider,{value:{bodyState:S,clearSelectedLine:()=>{t()},columns:m,columnWidthMap:C,filteredColumns:O,setBodyState:j,setColumns:P,setColumnWidthMap:e=>{localStorage.setItem(f,JSON.stringify(e)),k(e)},setFilteredColumns:x,setVisible:F,visible:i}},e)},y=e=>{if("labelTypes"in e){const t=g({},e),{labelTypes:n}=t;return h(t,["labelTypes"])}return e};const S=()=>(0,r.useContext)(v);var w=n(6177),O=n.n(w),x=n(6854),E=n(7478),C=n(4351),k=n(5553),P=n(72);function j(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class F extends o.Bs{clearVariable(){(0,k.Rr)(this).updateFilters([],{skipPublish:!0}),this.setState({lineFilter:""})}getOperator(){if(this.state.regex&&this.state.exclusive)return x.cK.negativeRegex;if(this.state.regex&&!this.state.exclusive)return x.cK.regex;if(!this.state.regex&&this.state.exclusive)return x.cK.negativeMatch;if(!this.state.regex&&!this.state.exclusive)return x.cK.match;throw new Error("getOperator: failed to determine operation")}getFilterKey(){return this.state.caseSensitive?x.ld.caseSensitive:x.ld.caseInsensitive}getFilter(){return(0,k.Rr)(this).state.filters[0]}updateFilter(e,t=!0){this.updateInputState(e),t?this.updateVariableDebounced(e):this.updateVariable(e)}updateInputState(e){this.setState({lineFilter:e})}constructor(e){var t,n,r,a;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){j(e,t,n[t])}))}return e}({caseSensitive:null!==(t=null==e?void 0:e.caseSensitive)&&void 0!==t?t:(0,C.hp)(!1),exclusive:null!==(n=null==e?void 0:e.exclusive)&&void 0!==n?n:(0,C.Zs)(!1),lineFilter:null!==(r=null==e?void 0:e.lineFilter)&&void 0!==r?r:"",regex:null!==(a=null==e?void 0:e.regex)&&void 0!==a?a:(0,C.og)(!1)},e)),j(this,"onActivate",(()=>{const e=this.getFilter();if(e)return this.setState({caseSensitive:e.key===x.ld.caseSensitive,exclusive:e.operator===x.cK.negativeMatch||e.operator===x.cK.negativeRegex,lineFilter:e.value,regex:e.operator===x.cK.regex||e.operator===x.cK.negativeRegex}),()=>{this.clearFilter()}})),j(this,"clearFilter",(()=>{this.updateVariableDebounced.cancel(),this.updateFilter("",!1)})),j(this,"onToggleExclusive",(e=>{(0,C.Bq)(e),this.setState({exclusive:e}),this.updateFilter(this.state.lineFilter,!1)})),j(this,"onSubmitLineFilter",(()=>{(0,E.bN)(),this.updateFilter(this.state.lineFilter,!1),this.updateVariableDebounced.flush();const e=(0,k.Gk)(this),t=e.state.filters,n=this.getFilter();e.updateFilters([...t,n]),this.clearVariable()})),j(this,"handleChange",(e=>{this.updateInputState(e.target.value)})),j(this,"handleEnter",(e=>{"Enter"===e.key&&this.state.lineFilter&&this.onSubmitLineFilter()})),j(this,"onCaseSensitiveToggle",(e=>{const t=e===x.ld.caseSensitive;this.setState({caseSensitive:t}),(0,C.Xo)(t),this.updateFilter(this.state.lineFilter,!1)})),j(this,"onRegexToggle",(e=>{const t="regex"===e;this.setState({regex:t}),(0,C.GL)(t),this.updateFilter(this.state.lineFilter,!1)})),j(this,"updateVariableDebounced",O()((e=>{this.updateVariable(e)}),1e3)),j(this,"updateVariable",(e=>{this.updateVariableDebounced.flush();const t=(0,k.Rr)(this),n=(0,k.Gk)(this),r={key:this.getFilterKey(),keyLabel:n.state.filters.length.toString(),operator:this.getOperator(),value:e};t.updateFilters([r]),(0,l.EE)(l.NO.service_details,l.ir.service_details.search_string_in_logs_changed,{caseSensitive:r.key,containsLevel:e.toLowerCase().includes("level"),operator:r.operator,searchQueryLength:e.length})})),this.addActivationHandler(this.onActivate)}}j(F,"Component",(function({model:e}){const{caseSensitive:t,exclusive:n,lineFilter:a,regex:i}=e.useState(),[s,o]=(0,r.useState)(!1);return(0,P._)({caseSensitive:t,exclusive:n,focus:s,handleEnter:e.handleEnter,lineFilter:a,onCaseSensitiveToggle:e.onCaseSensitiveToggle,onClearLineFilter:e.clearFilter,onInputChange:e.handleChange,onRegexToggle:e.onRegexToggle,onSubmitLineFilter:e.onSubmitLineFilter,regex:i,setExclusive:e.onToggleExclusive,setFocus:o,type:"editor",updateFilter:e.updateFilter})}));var L=n(3241),_=n(7781),T=n(2007),D=n(1532),N=n(376),$=n(8502);function I(e,t){if(1===t.length)return e[t[0]];const n=t.shift();return void 0!==n?I(e[n],t):void 0}const B=(e,t)=>(0,_.dateTimeFormat)(e,{defaultWithMS:!0,timeZone:t}),A=e=>({labelButtonsWrap:(0,i.css)({color:"var(--json-tree-label-color)",display:"inline-flex"}),labelWrap:M}),M=(0,i.css)({alignItems:"center",color:"var(--json-tree-label-color)",display:"inline-flex"}),R=(0,i.css)({alignItems:"center",display:"flex",overflowX:"auto"}),V=(0,i.css)({marginLeft:"0.5em",marginRight:"0.5em"}),W=(0,i.css)({marginLeft:"0.5em"}),z=(0,i.css)({display:"flex",flexWrap:"nowrap",fontSize:"12px",textWrap:"nowrap"});var K=n(42),H=n(5719),U=n(5548),G=n(20),Q=n(5700),q=n(9641),J=n(9405),Y=n(6779);const X=(0,r.memo)((({active:e,addFilter:t,jsonKey:n,keyPath:r,type:i})=>a().createElement(T.IconButton,{tooltip:`${"include"===i?"Include":"Exclude"} log lines that contain ${r[0]}`,onClick:a=>{a.stopPropagation(),t(r,n,G.ZO,e?"toggle":"include"===i?"exclude":"include")},"aria-selected":e,variant:e?"primary":"secondary",size:"md",name:"include"===i?"search-plus":"search-minus","aria-label":`${i} filter`})));X.displayName="JSONFilterNestedNodeButton";const Z=X,ee=(0,r.memo)((({addFilter:e,existingFilter:t,fullKey:n,fullKeyPath:r,label:i,type:s,value:o})=>{const l="include"===s?x.w7.Equal:x.w7.NotEqual;return a().createElement(T.IconButton,{tooltip:`${"include"===s?"Include":"Exclude"} log lines containing ${i}="${o}"`,onClick:a=>{a.stopPropagation(),e(r,n,o,(null==t?void 0:t.operator)===l?"toggle":s)},"aria-selected":(null==t?void 0:t.operator)===l,variant:(null==t?void 0:t.operator)===l?"primary":"secondary",size:"md",name:"include"===s?"search-plus":"search-minus","aria-label":`${s} filter`})}));ee.displayName="JSONFilterValueButton";const te=ee,ne=(0,r.memo)((({keyPath:e,setNewRootNode:t})=>a().createElement(T.IconButton,{tooltip:`Set ${e[0]} as root node`,onClick:n=>{n.stopPropagation(),t(e)},size:"md",name:"eye","aria-label":`drilldown into ${e[0]}`})));ne.displayName="DrilldownButton";const re=ne,ae=({lineState:e,onLineStateClick:t,onManageColumnsClick:n,onScrollToBottomClick:i,onScrollToTopClick:s,onSortOrderChange:o,sortOrder:l})=>{const c=(0,T.useStyles2)(ie),u=(0,r.useCallback)((()=>{o(l===_.LogsSortOrder.Ascending?_.LogsSortOrder.Descending:_.LogsSortOrder.Ascending)}),[o,l]);return a().createElement("div",{className:c.navContainer},i&&a().createElement(T.IconButton,{name:"arrow-down",className:c.controlButton,variant:"secondary",onClick:i,tooltip:"Scroll to bottom",size:"lg"}),a().createElement(T.IconButton,{name:l===_.LogsSortOrder.Descending?"sort-amount-up":"sort-amount-down",className:c.controlButton,onClick:u,tooltip:l===_.LogsSortOrder.Descending?"Newest logs first":"Oldest logs first",size:"lg"}),n&&a().createElement(T.IconButton,{name:"columns",className:c.controlButton,onClick:n,tooltip:"Manage columns",size:"lg"}),t&&e&&a().createElement(T.IconButton,{name:e===m.text?"brackets-curly":"text-fields",className:c.controlButton,onClick:t,tooltip:e===m.text?"Show labels":"Show log text",size:"lg"}),s&&a().createElement(T.IconButton,{name:"arrow-up","data-testid":"scrollToTop",className:c.scrollToTopButton,variant:"secondary",onClick:s,tooltip:"Scroll to top",size:"lg"}))},ie=e=>({controlButton:(0,i.css)({color:e.colors.text.secondary,height:e.spacing(2),margin:0}),divider:(0,i.css)({borderTop:`solid 1px ${e.colors.border.medium}`,height:1,marginBottom:e.spacing(-1.75),marginTop:e.spacing(-.25)}),navContainer:(0,i.css)({borderLeft:`solid 1px ${e.colors.border.medium}`,display:"flex",flexDirection:"column",gap:e.spacing(3),justifyContent:"flex-start",maxHeight:"100%",overflow:"hidden",paddingLeft:e.spacing(1),paddingTop:e.spacing(.75),width:e.spacing(4)}),scrollToTopButton:(0,i.css)({color:e.colors.text.secondary,height:e.spacing(2),margin:0,marginTop:"auto"})});var se=n(7352),oe=n(2051),le=n(1475);function ce(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ce(e,t,n[t])}))}return e}function de(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const pe="Time",ge="Line",he="root";class fe extends o.Bs{setStateFromUrl(){const e=new URLSearchParams(s.locationService.getLocation().search);this.updateFromUrl({sortOrder:e.get("sortOrder")})}getUrlState(){return{sortOrder:JSON.stringify(this.state.sortOrder)}}updateFromUrl(e){try{if("string"==typeof e.sortOrder&&e.sortOrder){const t=(0,u.FH)(JSON.parse(e.sortOrder));t&&this.setState({sortOrder:t})}}catch(e){c.v.error(e,{msg:"LogsJsonScene: updateFromUrl unexpected error"})}}onActivate(){var e,t,n,r;this.setStateFromUrl();const a=o.jh.getAncestor(this,se.Mn);this.setState({emptyScene:new Y.W({clearCallback:()=>(0,U.rA)(this)}),menu:new Q.GD({investigationOptions:{getLabelName:()=>`Logs: ${(0,H.Mq)(a)}`,type:"logs"}})});const i=o.jh.getData(this);(null===(e=i.state.data)||void 0===e?void 0:e.state)===_.LoadingState.Done&&this.transformDataFrame(i.state),this._subs.add(i.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===_.LoadingState.Done&&this.transformDataFrame(e)}))),(0,N.sg)(this);const s=(0,se.UO)(null===(n=a.state)||void 0===n||null===(t=n.$detectedFieldsData)||void 0===t?void 0:t.state);var l,c;s&&s.length&&(void 0===!a.state.fieldsCount||a.state.fieldsCount!==(null==s?void 0:s.length)?null===(c=a.state)||void 0===c||null===(l=c.$detectedFieldsData)||void 0===l||l.runQueries():(this.setVizFlags(s),this.syncFieldsAndJsonProps(s)));this._subs.add(null===(r=a.state.$detectedFieldsData)||void 0===r?void 0:r.subscribeToState((e=>{const t=(0,se.UO)(e);var n;t&&(this.setVizFlags(t),(null===(n=e.data)||void 0===n?void 0:n.state)===_.LoadingState.Done&&this.syncFieldsAndJsonProps(t))})));const u=(0,k.ir)(this);this._subs.add(u.subscribeToState(((e,t)=>{(0,D.B)(e.filters,t.filters)||this.manageJsonParserProps(e,t)})))}syncFieldsAndJsonProps(e){const t=null==e?void 0:e.fields[0],n=null==e?void 0:e.fields[2],r=null==e?void 0:e.fields[4];if(e&&(null==r?void 0:r.values.some((e=>e)))){const a=(0,k.ir)(this),i=(0,k.WA)(this);let s=[];for(let o=0;o<e.length;o++){const e=(0,N.sB)(null==n?void 0:n.values[o]),l=r.values[o],c=null==t?void 0:t.values[o];if(("json"===e||"mixed"===e)&&a.state.filters.some((e=>e.key===c))&&!i.state.filters.some((e=>e.key===c))){const e=(0,N.Mz)(l);s.push({key:c,operator:x.w7.Equal,value:e})}}s.length&&i.setState({filters:[...i.state.filters,...s]})}}manageJsonParserProps(e,t){const n=(0,k.U2)(this);if(e.filters.length||n.state.filters.length){if(e.filters.length<t.filters.length){t.filters.filter((t=>!e.filters.find((e=>e.key===t.key)))).length&&(0,$.AY)(this)}else if(e.filters.length>t.filters.length){const n=(0,k.WA)(this),r=e.filters.filter((e=>!t.filters.find((t=>e.key===t.key)))),a=[];r.forEach((e=>{if(!n.state.filters.some((t=>t.key===e.key))){const{parser:t,path:n}=(0,N.$E)(e.key,this);"json"!==t&&"mixed"!==t||!n||a.push({key:e.key,operator:x.w7.Equal,value:n})}})),a.length&&n.setState({filters:[...n.state.filters,...a]})}}else(0,N.sg)(this)}setVizFlags(e){var t,n;(null===(t=(0,N.$1)(e))||void 0===t?void 0:t.values.some((e=>"json"===e||"mixed"===e)))?this.setState({hasJsonFields:!0,jsonFiltersSupported:null===(n=(0,N.XK)(e))||void 0===n?void 0:n.values.some((e=>void 0!==e))}):this.setState({hasJsonFields:!1})}getValue(e,t){const n=[...e],r=[];for(;n.length;){const e=n.pop();e!==he&&void 0!==e&&r.push(e)}return I(t,r)}getFullKeyPath(e){const t=[...(0,k.U2)(this).state.filters,...e.filter((e=>"string"==typeof e&&!(0,N.Z6)(e)&&e!==he)).reverse().map((e=>({key:e.toString(),operator:x.h8.Empty,value:$.uE})))];return{fullKeyPath:[...t.map((e=>e.key)).reverse(),...e.slice(-3)],fullPathFilters:t}}getKeyPathString(e,t=":"){return e[0]!==pe?e[0]+t:e[0]}transformDataFrame(e){const t=(0,se.tn)(e.data),n=null==t?void 0:t.fields.find((e=>e.type===_.FieldType.time)),r=(0,_.getTimeZone)();if(e.data){const t=de(ue({},e.data),{series:e.data.series.map((e=>de(ue({},e),{fields:e.fields.map((e=>(0,N.Z6)(e.name)?de(ue({},e),{values:e.values.map(((e,t)=>{var a;let i;try{i=JSON.parse(e)}catch(t){i=e}return{[ge]:i,[pe]:B(null==n||null===(a=n.values)||void 0===a?void 0:a[t],r)}})).filter((e=>e))}):e))})))});this.setState({data:t})}}constructor(e){super(de(ue({},e),{sortOrder:(0,C.YM)("sortOrder",_.LogsSortOrder.Descending)})),ce(this,"_urlSync",new o.So(this,{keys:["sortOrder"]})),ce(this,"addDrillUp",(e=>{(0,E.bN)();const t=(0,k.U2)(this),n=(0,k.WA)(this),r=(0,k.ir)(this),a=t.state.filters,i=a.findIndex((t=>t.key===e)),s=a.filter(((e,t)=>t<=i)),o=[];for(let e=0;e<a.length;e++)o.push(`${o.length?`${a.map((e=>e.key)).slice(0,e).join("_")}_`:""}${a[e].key}`);const l=o.slice(i+1),c=new Set;r.state.filters.forEach((e=>c.add(e.key)));const u=n.state.filters.filter((e=>!l.includes(e.key)||c.has(e.key)));n.setState({filters:u}),t.setState({filters:s}),this.lineFormatEvent("remove",e)})),ce(this,"setNewRootNode",(e=>{(0,E.bN)();const{fullKeyPath:t,fullPathFilters:n}=this.getFullKeyPath(e);if(e.length>3){(0,$.gR)(this,t);(0,k.U2)(this).setState({filters:n.map((e=>de(ue({},e),{key:e.key.replace(K.HO,"_")})))}),this.lineFormatEvent("add",e[0].toString())}else(0,$.X)(this),(0,N.sg)(this),this.lineFormatEvent("remove",he)})),ce(this,"lineFormatEvent",((e,t)=>{(0,l.EE)(l.NO.service_details,l.ir.service_details.change_line_format_in_json_panel,{key:t,type:e})})),ce(this,"addFilter",((e,t,n,r)=>{(0,E.bN)(),t=t.replace(K.HO,"_"),(0,$.gR)(this,e);const a=o.jh.getAncestor(this,Jt);(0,J.Qt)(t,n,r,a,G.mB,!1,!0),(0,l.EE)(l.NO.service_details,l.ir.service_details.add_to_filters_in_json_panel,{action:r,filterType:"json",key:t})})),ce(this,"handleSortChange",(e=>{if(e===this.state.sortOrder)return;(0,C.YK)("sortOrder",e);const t=o.jh.getData(this),n=t instanceof o.dt?t:o.jh.findDescendents(t,o.dt)[0];n&&n.runQueries(),this.setState({sortOrder:e})})),ce(this,"renderNestedNodeButtons",((e,t)=>{const n=(0,k.U2)(this).state.filters,r=[ge,0,he];return a().createElement(a().Fragment,null,a().createElement("span",{className:R,key:he},a().createElement(T.Button,{size:"sm",onClick:()=>t&&this.setNewRootNode(r),variant:"secondary",fill:"outline",disabled:!n.length,name:e[0].toString()},this.getKeyPathString(e,n.length?"":":")),n.length>0&&a().createElement(T.Icon,{className:V,name:"angle-right"})),n.map(((e,r)=>{const i=e.key===n[n.length-1].key;return a().createElement("span",{className:R,key:e.key},a().createElement(T.Button,{size:"sm",disabled:i,onClick:()=>t&&this.addDrillUp(e.key),variant:"secondary",fill:"outline"},e.key),r<n.length-1&&a().createElement(T.Icon,{className:V,name:"angle-right"}),r===n.length-1&&a().createElement(T.Icon,{className:W,name:"angle-right"}))})))})),ce(this,"renderNestedNodeFilterButtons",((e,t,n,r)=>{const{fullKeyPath:i}=this.getFullKeyPath(e),s=(0,$.eO)(i),o=n.get(s),l=o&&t.state.filters.find((e=>e.key===(null==o?void 0:o.key)&&(0,k.bu)(e).value===G.ZO));return a().createElement("span",{className:M},r&&a().createElement(a().Fragment,null,a().createElement(re,{keyPath:e,setNewRootNode:this.setNewRootNode}),a().createElement(Z,{type:"include",jsonKey:s,addFilter:this.addFilter,keyPath:i,active:(null==l?void 0:l.operator)===x.w7.NotEqual}),a().createElement(Z,{type:"exclude",jsonKey:s,addFilter:this.addFilter,keyPath:i,active:(null==l?void 0:l.operator)===x.w7.Equal})),a().createElement("strong",null,this.getKeyPathString(e)))})),ce(this,"renderValueLabel",((e,t,n,r,i)=>{var s;const o=(0,T.useStyles2)(A),l=null===(s=this.getValue(e,t.values))||void 0===s?void 0:s.toString(),c=e[0],{fullKeyPath:u}=this.getFullKeyPath(e),d=(0,$.eO)(u),p=r.get(d),g=p&&n.state.filters.find((e=>e.key===(null==p?void 0:p.key)&&(0,k.bu)(e).value===l));return a().createElement("span",{className:o.labelButtonsWrap},i&&a().createElement(a().Fragment,null,a().createElement(te,{label:c,value:l,fullKeyPath:u,fullKey:d,addFilter:this.addFilter,existingFilter:g,type:"include"}),a().createElement(te,{label:c,value:l,fullKeyPath:u,fullKey:d,addFilter:this.addFilter,existingFilter:g,type:"exclude"})),a().createElement("strong",{className:o.labelWrap},this.getKeyPathString(e)))})),this.addActivationHandler(this.onActivate.bind(this))}}ce(fe,"Component",(({model:e})=>{var t,n,i;const{data:s,emptyScene:l,hasJsonFields:c,jsonFiltersSupported:d,menu:p,sortOrder:g}=e.useState(),h=o.jh.getData(e);h.useState();const f=o.jh.getAncestor(e,Jt),{visualizationType:m}=f.useState(),v=(0,T.useStyles2)(me),b=(0,k.ir)(e),y=(0,k.WA)(e),S=(0,se.tn)(s),w=null==S?void 0:S.fields.find((e=>e.type===_.FieldType.string&&(0,N.Z6)(e.name))),O=new Map;y.state.filters.forEach((e=>{const t=e.value.substring(3,e.value.length-3).split('\\"][\\"').join("_");O.set(t,e)}));const x=(0,r.useRef)(null),E=(0,r.useCallback)((()=>{var e;null===(e=x.current)||void 0===e||e.scrollTo(0,x.current.scrollHeight)}),[]),C=(0,r.useCallback)((()=>{var e;null===(e=x.current)||void 0===e||e.scrollTo(0,0)}),[]);return a().createElement(T.PanelChrome,{padding:"none",showMenuAlways:!0,statusMessage:null===(n=h.state.data)||void 0===n||null===(t=n.errors)||void 0===t?void 0:t[0].message,loadingState:null===(i=h.state.data)||void 0===i?void 0:i.state,title:a().createElement(a().Fragment,null,"JSON ",a().createElement(T.Badge,{color:"blue",text:"Experimental"})),menu:p?a().createElement(p.Component,{model:p}):void 0,actions:a().createElement(q.C,{vizType:m,onChange:f.setVisualizationType})},a().createElement("div",{className:v.container},le.CT&&(null==w?void 0:w.values)&&(null==w?void 0:w.values.length)>0&&a().createElement(ae,{sortOrder:g,onSortOrderChange:e.handleSortChange,onScrollToBottomClick:E,onScrollToTopClick:C}),S&&(null==w?void 0:w.values)&&(null==w?void 0:w.values.length)>0&&a().createElement("div",{className:v.JSONTreeWrap,ref:x},!1===d&&a().createElement(T.Alert,{severity:"warning",title:"JSON filtering requires Loki 3.5.0."},"This view will be read only until Loki is upgraded to 3.5.0"),w.values.length>0&&!1===c&&a().createElement(a().Fragment,null,a().createElement(T.Alert,{severity:"info",title:"No JSON fields detected"},"This view is built for JSON log lines, but none were detected. Switch to the Logs or Table view for a better experience.")),a().createElement(oe.d,{data:w.values,hideRootExpand:!0,valueWrap:"",getItemString:(e,t,n,r,i)=>t&&(0,u.cK)(t,pe)&&"string"==typeof t.Time?null:i[0]===he?a().createElement("span",{className:z},n," ",r):a().createElement("span",null,n),valueRenderer:(e,t,n)=>n===pe?null:a().createElement(a().Fragment,null,null==e?void 0:e.toString()),shouldExpandNodeInitially:(e,t,n)=>n<=2,labelRenderer:(t,n)=>{const r=n;if(t[0]===he)return e.renderNestedNodeButtons(t,d);if("Object"!==r&&"Array"!==r&&t[0]!==pe&&!(0,N.Z6)(t[0].toString())&&t[0]!==he&&!(0,L.isNumber)(t[0]))return e.renderValueLabel(t,w,b,O,d);if(!("Object"!==r&&"Array"!==r||(0,N.Z6)(t[0].toString())||t[0]===he||(0,L.isNumber)(t[0])))return e.renderNestedNodeFilterButtons(t,b,O,d);if((0,L.isNumber)(t[0])&&t[1]===he){var i;const e=null===(i=w.values[t[0]])||void 0===i?void 0:i[pe];return a().createElement("strong",null,e)}return t[0]===pe?null:a().createElement("strong",null,t[0],":")}})),l&&0===(null==w?void 0:w.values.length)&&a().createElement(Y.W.Component,{model:l})))}));const me=e=>({container:(0,i.css)({display:"flex",flexDirection:"row-reverse",height:"100%",paddingBottom:e.spacing(1),paddingRight:e.spacing(1)}),JSONTreeWrap:i.css`
    // override css variables
    --json-tree-align-items: flex-start;
    --json-tree-label-color: ${e.colors.text.secondary};
    --json-tree-label-value-color: ${e.colors.text.primary};
    --json-tree-arrow-color: ${e.colors.secondary.contrastText};
    --json-tree-ul-root-padding: 0 0 ${e.spacing(2)} 0;

    overflow: auto;
    height: 100%;
    width: 100%;
    // first nested node padding
    > ul > li > ul {
      // Hackery to keep elements from under the sticky header from being in the scrollable area
      padding: 0 0 0 ${e.spacing(2)};
    }

    // Root node styles
    > ul > li > span {
      position: sticky;
      top: 0;
      left: 0;
      background: ${e.colors.background.primary};
      padding-bottom: ${e.spacing(.5)};
      margin-bottom: ${e.spacing(.5)};
      box-shadow: 0 1px 7px rgba(1, 4, 9, 0.75);
      z-index: 2;
      padding-left: ${e.spacing(1)};
      align-items: center;
      overflow-x: auto;
      overflow-y: hidden;
    }

    > ul > li > ul > li > span {
      position: sticky;
      top: 26px;
      left: 0;
      background: ${e.colors.background.primary};
      z-index: 1;
    }
  `});var ve=n(8996);const be="detected_level",ye={addFilter:e=>{},logsFrame:null,selectedLine:void 0,timeRange:void 0},Se=(0,r.createContext)(ye),we=({addFilter:e,children:t,logsFrame:n,selectedLine:r,timeRange:i})=>a().createElement(Se.Provider,{value:{addFilter:e,logsFrame:n,selectedLine:r,timeRange:i}},t),Oe=()=>(0,r.useContext)(Se);var xe=n(5540),Ee=n(3367),Ce=n(1269),ke=n(1625),Pe=n(3571),je=n(9193);function Fe({searchValue:e,setSearchValue:t}){const{columns:n,setFilteredColumns:r}=S(),o=e=>{const t=e[0];let a={},i=0;var o;t.forEach((e=>{e in n&&(a[e]=n[e],i++)})),r(a),o=i,(0,s.reportInteraction)("grafana_logs_app_table_text_search_result_count",{resultCount:o})},l=function(e){return{searchWrap:(0,i.css)({padding:`${e.spacing(.4)} 0 ${e.spacing(.4)} ${e.spacing(.4)}`})}}((0,T.useTheme2)());return a().createElement(T.Field,{className:l.searchWrap},a().createElement(T.Input,{value:e,type:"text",placeholder:"Search fields by name",onChange:e=>{var a;const i=null===(a=e.currentTarget)||void 0===a?void 0:a.value;var s;t(i),i?(s=i,(0,je.E)(Object.keys(n),s,o)):r(void 0)}}))}var Le=n(9902);function _e(){const e=function(e){return{empty:(0,i.css)({fontSize:e.typography.fontSize,marginBottom:e.spacing(2),marginLeft:e.spacing(1.75)})}}((0,T.useTheme2)());return a().createElement("div",{className:e.empty},"No fields")}function Te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Ne(e){var t=function(e,t){if("object"!==$e(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==$e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===$e(t)?t:String(t)}function $e(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}function Ie(e){const t=function(e){return{checkboxLabel:(0,i.css)({"> span":{display:"block",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}),contentWrap:(0,i.css)({alignItems:"center",display:"flex",justifyContent:"space-between",width:"100%"}),customWidthWrap:(0,i.css)({cursor:"pointer",fontSize:e.typography.bodySmall.fontSize}),dragIcon:(0,i.css)({cursor:"drag",marginLeft:e.spacing(1),opacity:.4}),labelCount:(0,i.css)({alignItems:"self-end",appearance:"none",background:"none",border:"none",display:"flex",flexDirection:"column",fontSize:e.typography.pxToRem(11),marginLeft:e.spacing(.5),marginRight:e.spacing(.5),opacity:.6})}}((0,T.useTheme2)());var n,r,s,o,l,c;return e.labels[e.label]?a().createElement(a().Fragment,null,a().createElement("div",{className:t.contentWrap},a().createElement(T.Checkbox,{className:t.checkboxLabel,label:e.label,onChange:e.onChange,checked:null!==(c=null===(n=e.labels[e.label])||void 0===n?void 0:n.active)&&void 0!==c&&c}),e.showCount&&a().createElement("div",{className:t.labelCount},a().createElement("div",null,null===(r=e.labels[e.label])||void 0===r?void 0:r.percentOfLinesWithLabel,"%"),a().createElement("div",null,null===(s=e.labels[e.label])||void 0===s?void 0:s.cardinality," ",1===(null===(o=e.labels[e.label])||void 0===o?void 0:o.cardinality)?"value":"values")),e.columnWidthMap&&e.setColumnWidthMap&&void 0!==(null===(l=e.columnWidthMap)||void 0===l?void 0:l[e.label])&&a().createElement("button",{onClick:()=>{var t;const n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Te(e,t,n[t])}))}return e}({},e.columnWidthMap),r=e.label,{[r]:a}=n,i=De(n,[r].map(Ne));null===(t=e.setColumnWidthMap)||void 0===t||t.call(e,i)},title:"Clear column width override",className:t.customWidthWrap},"Reset column width",a().createElement(T.Icon,{name:"x"}))),e.draggable&&a().createElement(T.Icon,{"aria-label":"Drag and drop icon",title:"Drag and drop to reorder",name:"draggabledots",size:"lg",className:t.dragIcon})):null}function Be(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ae(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Be(e,t,n[t])}))}return e}function Me(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Re=e=>{const{columnWidthMap:t,setColumnWidthMap:n}=S(),{labels:r,reorderColumn:s,toggleColumn:o,valueFilter:l}=e,c=(0,T.useTheme2)(),{columns:u}=S(),d=function(e){return{columnWrapper:(0,i.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)}),dragging:(0,i.css)({background:e.colors.background.secondary}),wrap:(0,i.css)({background:e.colors.background.primary,display:"flex",marginBottom:e.spacing(1),marginTop:e.spacing(1)})}}(c),p=Object.keys(r).filter((e=>l(e))),g=e=>{e.destination&&s(u,e.source.index,e.destination.index)},h=e=>{const t=r[e];if(t)return`${e} appears in ${null==t?void 0:t.percentOfLinesWithLabel}% of log lines`};return p.length?a().createElement(Le.JY,{onDragEnd:g},a().createElement(Le.gL,{droppableId:"order-fields",direction:"vertical"},(e=>a().createElement("div",Me(Ae({className:d.columnWrapper},e.droppableProps),{ref:e.innerRef}),p.sort(function(e){return(t,n)=>{const r=e[t],a=e[n];return null!=r.index&&null!=a.index?r.index-a.index:0}}(r)).map(((e,s)=>a().createElement(Le.sx,{draggableId:e,key:e,index:s},((s,l)=>a().createElement("div",Me(Ae({className:(0,i.cx)(d.wrap,l.isDragging?d.dragging:void 0),ref:s.innerRef},s.draggableProps,s.dragHandleProps),{title:h(e)}),a().createElement(Ie,{setColumnWidthMap:n,columnWidthMap:t,label:e,onChange:()=>o(e),labels:r,draggable:!0})))))),e.placeholder)))):a().createElement(_e,null)},Ve=new Intl.Collator(void 0,{sensitivity:"base"});const We=e=>{const{labels:t,toggleColumn:n,valueFilter:r}=e,s=function(e){return{columnWrapper:(0,i.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)}),dragging:(0,i.css)({background:e.colors.background.secondary}),wrap:(0,i.css)({background:e.colors.background.primary,borderBottom:`1px solid ${e.colors.background.canvas}`,display:"flex",marginBottom:e.spacing(.25),marginTop:e.spacing(.25)})}}((0,T.useTheme2)()),o=Object.keys(t).filter((e=>r(e)));return o.length?a().createElement("div",{className:s.columnWrapper},o.sort(function(e){return(t,n)=>{const r=e[t],a=e[n];return null!=r&&null!=a?Number("TIME_FIELD"===a.type)-Number("TIME_FIELD"===r.type)||Number("BODY_FIELD"===a.type)-Number("BODY_FIELD"===r.type)||Ve.compare(t,n):0}}(t)).map((e=>{var r;return a().createElement("div",{key:e,className:s.wrap,title:`${e} appears in ${null===(r=t[e])||void 0===r?void 0:r.percentOfLinesWithLabel}% of log lines`},a().createElement(Ie,{showCount:!0,label:e,onChange:()=>n(e),labels:t}))}))):a().createElement(_e,null)};const ze=e=>{const t=function(e){return{columnHeader:(0,i.css)({background:e.colors.background.secondary,display:"flex",fontSize:e.typography.h6.fontSize,justifyContent:"space-between",left:0,marginBottom:e.spacing(2),paddingBottom:e.spacing(.75),paddingLeft:e.spacing(1.5),paddingRight:e.spacing(.75),paddingTop:e.spacing(.75),position:"sticky",top:0,zIndex:3}),columnHeaderButton:(0,i.css)({appearance:"none",background:"none",border:"none",fontSize:e.typography.pxToRem(11)}),sidebarWrap:(0,i.css)({"&::-webkit-scrollbar":{display:"none"},height:"calc(100% - 50px)",overflowY:"scroll",scrollbarWidth:"none"})}}((0,T.useTheme2)());var n,r;return a().createElement("div",{className:t.sidebarWrap},a().createElement(a().Fragment,null,a().createElement("div",{className:t.columnHeader},"Selected fields",a().createElement("button",{onClick:e.clear,className:t.columnHeaderButton},"Reset")),a().createElement(Re,{reorderColumn:e.reorderColumn,toggleColumn:e.toggleColumn,labels:null!==(n=e.filteredColumnsWithMeta)&&void 0!==n?n:e.columnsWithMeta,valueFilter:t=>{var n,r;return null!==(r=null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)&&void 0!==r&&r},id:"selected-fields"}),a().createElement("div",{className:t.columnHeader},"Fields"),a().createElement(We,{toggleColumn:e.toggleColumn,labels:null!==(r=e.filteredColumnsWithMeta)&&void 0!==r?r:e.columnsWithMeta,valueFilter:t=>{var n;return!(null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)}})))};function Ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function He(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ke(e,t,n[t])}))}return e}function Ue(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function Ge(e){return(t,n,r)=>{if(n===r)return;const a=He({},t),i=Object.keys(a).filter((e=>a[e].active)).map((e=>{var t;return{fieldName:e,index:null!==(t=a[e].index)&&void 0!==t?t:0}})).sort(((e,t)=>e.index-t.index)),[s]=i.splice(n,1);i.splice(r,0,s),i.filter((e=>void 0!==e)).forEach(((e,t)=>{a[e.fieldName].index=t})),e(a)}}function Qe(){const{columns:e,filteredColumns:t,setColumns:n,setFilteredColumns:i,setVisible:o}=S(),[l,u]=(0,r.useState)(""),d=Ge(n);return a().createElement(T.ClickOutsideWrapper,{onClick:()=>{o(!1),i(e),u("")},useCapture:!0},a().createElement(Fe,{searchValue:l,setSearchValue:u}),a().createElement(ze,{toggleColumn:r=>{if(!e||!(r in e))return void function(e,t){let n;try{n={columnName:e,columns:JSON.stringify(t)}}catch(t){n={columnName:e,msg:"Table: ColumnSelectionDrawerWrap failed to encode context"}}c.v.warn("failed to get column",n)}(r,e);const a=Object.keys(e).filter((t=>e[t].active)).length,o=!e[r].active||void 0;let l;if(l=Ue(He({},e),o?{[r]:Ue(He({},e[r]),{active:o,index:a})}:{[r]:Ue(He({},e[r]),{active:!1,index:void 0})}),function(t){if(e){var n,r;const a=!(null===(n=e[t])||void 0===n?void 0:n.active),i=null===(r=Object.keys(e).filter((t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active})))||void 0===r?void 0:r.length,o={columnAction:a?"add":"remove",columnCount:a?i+1:i-1};(0,s.reportInteraction)("grafana_logs_app_table_column_filter_clicked",o)}}(r),n(l),t){var d;const e=!(null===(d=t[r])||void 0===d?void 0:d.active);let n;n=Ue(He({},t),e?{[r]:Ue(He({},t[r]),{active:e,index:a})}:{[r]:Ue(He({},t[r]),{active:!1,index:void 0})}),i(n),u("")}},filteredColumnsWithMeta:t,columnsWithMeta:e,clear:()=>{const t=He({},e);let r=0;Object.keys(t).forEach((e=>{const n="BODY_FIELD"===t[e].type||"TIME_FIELD"===t[e].type;t[e].active=n,t[e].index=n?r++:void 0})),n(t),i(t),u("")},reorderColumn:d}))}const qe=(0,r.createContext)({cellIndex:{index:null,numberOfMenuItems:3},setActiveCellIndex:e=>!1}),Je=({children:e})=>{const[t,n]=(0,r.useState)({index:null}),i=(0,r.useCallback)((e=>{n(e)}),[]);return a().createElement(qe.Provider,{value:{cellIndex:t,setActiveCellIndex:i}},e)},Ye=()=>(0,r.useContext)(qe),Xe=(0,r.createContext)({isHeaderMenuActive:!1,setHeaderMenuActive:e=>!1}),Ze=({children:e})=>{const[t,n]=(0,r.useState)(!1),i=(0,r.useCallback)((e=>{n(e)}),[]);return a().createElement(Xe.Provider,{value:{isHeaderMenuActive:t,setHeaderMenuActive:i}},e)},et=()=>(0,r.useContext)(Xe),tt=e=>a().createElement(nt,{onMouseOut:e.onMouseOut,onMouseIn:e.onMouseIn,onClick:e.onClick,field:e.field,rowIndex:e.rowIndex},e.children),nt=e=>{var t;const n=(0,T.useTheme2)(),r=Ye(),s=((e,t)=>({active:(0,i.css)({background:"transparent",height:"calc(100% + 36px)",zIndex:e.zIndex.tooltip}),wrap:(0,i.css)({background:null!=t?t:"transparent",height:"100%",left:0,margin:"auto",overflowX:"hidden",position:"absolute",top:0,whiteSpace:"nowrap",width:"100%"})}))(n,void 0,null===(t=r.cellIndex)||void 0===t||t.numberOfMenuItems);return a().createElement("div",{onMouseLeave:e.onMouseOut,onMouseEnter:e.onMouseIn,onClick:e.onClick,className:r.cellIndex.index===e.rowIndex&&r.cellIndex.fieldName===e.field.name?(0,i.cx)(s.wrap,s.active):s.wrap,onKeyDown:t=>{var n;"Enter"!==t.key&&" "!==t.key||(null===(n=e.onClick)||void 0===n||n.call(e))},role:"button",tabIndex:0},e.children)},rt=e=>{const t=((e,t)=>({menu:(0,i.css)({display:"flex",justifyContent:"flex-start",minWidth:"60px",paddingRight:"5px",position:"relative"}),menuItem:(0,i.css)({alignItems:"center",cursor:"pointer",display:"flex",overflow:"auto",paddingLeft:"5px",paddingRight:"5px",textOverflow:"ellipsis"}),menuItemsWrap:(0,i.css)({background:e.colors.background.secondary,boxShadow:e.shadows.z3,display:"flex",marginLeft:"column"===t?"5px":void 0,padding:"5px 0"})}))((0,T.useTheme2)(),e.pillType),{addFilter:n}=Oe();return a().createElement("span",{className:t.menu},a().createElement("span",{className:t.menuItemsWrap},"derived"!==e.fieldType&&a().createElement(a().Fragment,null,a().createElement("div",{className:t.menuItem,role:"button",tabIndex:0,onClick:()=>{n({key:e.label,operator:x.w7.Equal,value:e.value})},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||n({key:e.label,operator:x.w7.Equal,value:e.value})}},a().createElement(T.Icon,{title:"Add to search",size:"md",name:"plus-circle"})),a().createElement("div",{className:t.menuItem,role:"button",tabIndex:0,onClick:()=>{n({key:e.label,operator:x.w7.NotEqual,value:e.value})},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||n({key:e.label,operator:x.w7.NotEqual,value:e.value})}},a().createElement(T.Icon,{title:"Exclude from search",size:"md",name:"minus-circle"}))),e.showColumn&&a().createElement("div",{title:"Add column",role:"button",tabIndex:0,className:t.menuItem,onClick:e.showColumn,onKeyDown:t=>{var n;"Enter"!==t.key&&" "!==t.key||(null===(n=e.showColumn)||void 0===n||n.call(e))}},a().createElement("svg",{width:"18",height:"16",viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.38725 1.33301H13.3872C13.5641 1.33301 13.7336 1.40325 13.8587 1.52827C13.9837 1.65329 14.0539 1.82286 14.0539 1.99967V2.33333C14.0539 2.70152 13.7554 3 13.3872 3H13.0542C12.87 3 12.7206 2.85062 12.7206 2.66634H8.05391V13.333H12.7206C12.7206 13.1491 12.8697 13 13.0536 13H13.3872C13.7554 13 14.0539 13.2985 14.0539 13.6667V13.9997C14.0539 14.1765 13.9837 14.3461 13.8587 14.4711C13.7336 14.5961 13.5641 14.6663 13.3872 14.6663H1.38725C1.21044 14.6663 1.04087 14.5961 0.915843 14.4711C0.790819 14.3461 0.720581 14.1765 0.720581 13.9997V1.99967C0.720581 1.82286 0.790819 1.65329 0.915843 1.52827C1.04087 1.40325 1.21044 1.33301 1.38725 1.33301ZM2.05391 13.333H6.72058V2.66634H2.05391V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),a().createElement("path",{d:"M13.8538 7.19999H16.2538C16.466 7.19999 16.6695 7.28429 16.8195 7.4343C16.9696 7.58432 17.0538 7.78783 17.0538 7.99999C17.0538 8.21214 16.9696 8.41566 16.8195 8.56567C16.6695 8.71569 16.466 8.79999 16.2538 8.79999H13.8538V11.2C13.8538 11.4121 13.7696 11.6156 13.6195 11.7657C13.4695 11.9157 13.266 12 13.0538 12C12.8416 12 12.6382 11.9157 12.4881 11.7657C12.3381 11.6156 12.2538 11.4121 12.2538 11.2V8.79999H9.85384C9.64165 8.79999 9.43819 8.71569 9.28815 8.56567C9.13811 8.41566 9.05383 8.21214 9.05383 7.99999C9.05383 7.78783 9.13811 7.58432 9.28815 7.4343C9.43819 7.28429 9.64165 7.19999 9.85384 7.19999H12.2538V4.8C12.2538 4.58784 12.3381 4.38433 12.4881 4.23431C12.6382 4.0843 12.8416 4 13.0538 4C13.266 4 13.4695 4.0843 13.6195 4.23431C13.7696 4.38433 13.8538 4.58784 13.8538 4.8V7.19999Z",fill:"#CCCCDC",fillOpacity:"1"}))),e.links&&e.links.map((e=>{var n;return a().createElement("div",{className:t.menuItem,role:"button",tabIndex:0,onClick:()=>{window.open(e.href,"_blank")},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||window.open(e.href,"_blank")},key:e.href},a().createElement(T.Icon,{title:null!==(n=e.title)&&void 0!==n?n:"Link",key:e.href,size:"md",name:"link"}))}))))},at=e=>{const{label:t,value:n}=e,r=(0,T.useTheme2)(),{cellIndex:s}=Ye();let o;if(t===be){const e=$t().options;"string"==typeof n&&n in e&&(o=e[n].color)}const l=s.index===e.rowIndex&&e.field.name===s.fieldName,c=((e,t)=>({activePillWrap:(0,i.css)({}),menu:(0,i.css)({width:"100%"}),menuItem:(0,i.css)({overflow:"auto",textOverflow:"ellipsis"}),menuItemText:(0,i.css)({display:"inline-block",width:"65px"}),pill:(0,i.css)({"&:before":{backgroundColor:t,content:'""',height:"100%",left:0,position:"absolute",top:0,width:`${e.spacing(.25)}`},"&:hover":{border:`1px solid ${e.colors.border.strong}`},backgroundColor:"transparent",border:`1px solid ${e.colors.border.weak}`,display:"inline-flex",flexDirection:"row-reverse",marginLeft:"5px",marginRight:"5px",marginTop:"4px",padding:"2px 5px",paddingLeft:t?`${e.spacing(.75)}`:"2px",position:"relative"}),pillWrap:(0,i.css)({width:"100%"})}))(r,o);return a().createElement("div",{className:(0,i.cx)(c.pillWrap,l?c.activePillWrap:void 0)},!!n&&a().createElement(a().Fragment,null,a().createElement("span",{className:c.pill},a().createElement(a().Fragment,null,n)),l&&"string"==typeof n&&e.field.type!==_.FieldType.time&&a().createElement(rt,{label:e.label,value:n,pillType:"column"})))};var it=n(5002);function st(e){var t;const n=(e=>({clipboardButton:(0,i.css)({height:"100%",lineHeight:"1",padding:0,width:"20px"}),iconWrapper:(0,i.css)({background:e.colors.background.secondary,boxShadow:e.shadows.z2,display:"flex",height:"35px",left:0,padding:`0 ${e.spacing(.5)}`,position:"sticky",zIndex:1}),inspect:(0,i.css)({"&:hover":{color:e.colors.text.link,cursor:"pointer"},padding:"5px 3px"}),inspectButton:(0,i.css)({borderRadius:"5px",display:"inline-flex",margin:0,overflow:"hidden",verticalAlign:"middle"})}))((0,T.useTheme2)()),{logsFrame:s,timeRange:o}=Oe(),l=null==s||null===(t=s.idField)||void 0===t?void 0:t.values[e.rowIndex],c=null==s?void 0:s.bodyField.values[e.rowIndex],[u,d]=(0,r.useState)(!1),p=(0,r.useCallback)((()=>o?(0,it.gW)("selectedLine",{id:l,row:e.rowIndex},o):""),[l,e.rowIndex,o]);return a().createElement(a().Fragment,null,a().createElement("div",{className:n.iconWrapper},a().createElement("div",{className:n.inspect},a().createElement(T.IconButton,{"data-testid":Pe.b.table.inspectLine,className:n.inspectButton,tooltip:"View log line",variant:"secondary","aria-label":"View log line",tooltipPlacement:"top",size:"md",name:"eye",onClick:()=>d(!0),tabIndex:0})),a().createElement("div",{className:n.inspect},a().createElement(T.ClipboardButton,{className:n.clipboardButton,icon:"share-alt",variant:"secondary",fill:"text",size:"md",tooltip:"Copy link to log line",tooltipPlacement:"top",tabIndex:0,getText:p}))),a().createElement(a().Fragment,null,u&&a().createElement(T.Modal,{onDismiss:()=>d(!1),isOpen:!0,title:"Inspect value"},a().createElement("pre",null,c),a().createElement(T.Modal.ButtonRow,null,a().createElement(T.ClipboardButton,{icon:"copy",getText:()=>e.value},"Copy to Clipboard")))))}const ot=e=>{var t;let n=e.value;const r=e.field,s=r.display(n),o=((e,t)=>({content:(0,i.css)({display:"flex",height:"100%",overflow:"hidden",position:"relative"}),flexWrap:(0,i.css)({alignItems:"flex-start",display:"flex",flexDirection:t===_.FieldType.number?"row-reverse":"row",textAlign:t===_.FieldType.number?"right":"left"}),linkWrapper:(0,i.css)({"&:hover":{textDecoration:"underline"},color:e.colors.text.link,marginLeft:"7px",marginTop:"7px"})}))((0,T.useTheme2)(),e.field.type),{setVisible:l}=S(),{cellIndex:c,setActiveCellIndex:u}=Ye(),d={index:e.rowIndex},p=Boolean(null===(t=(0,T.getCellLinks)(e.field,d))||void 0===t?void 0:t.length);if(null===n)return a().createElement(a().Fragment,null);n=a().isValidElement(e.value)?e.value:"object"==typeof n?JSON.stringify(e.value):(0,_.formattedValueToString)(s);return a().createElement(tt,{onClick:()=>e.rowIndex===c.index&&e.field.name===c.fieldName?u({index:null}):u({fieldName:e.field.name,index:e.rowIndex,numberOfMenuItems:3}),field:e.field,rowIndex:e.rowIndex},a().createElement("div",{className:o.content},0===e.fieldIndex&&a().createElement(st,{value:n,rowIndex:e.rowIndex}),a().createElement("div",{className:o.flexWrap}),!p&&((t,n)=>a().createElement(at,{field:e.field,rowIndex:e.rowIndex,showColumns:()=>l(!0),label:n,value:t}))(n,r.name),p&&r.getLinks&&a().createElement(T.DataLinksContextMenu,{links:()=>{var e;return null!==(e=(0,T.getCellLinks)(r,d))&&void 0!==e?e:[]}},(e=>e.openMenu?a().createElement("button",{className:o.linkWrapper,onClick:e.openMenu},a().createElement(a().Fragment,null,n)):a().createElement("div",{className:o.linkWrapper},a().createElement(a().Fragment,null,n))))))},lt=()=>(0,T.useStyles2)((e=>({linkButton:(0,i.css)({"&:focus":{outline:"none"},appearance:"none",background:"none",border:"none",color:"inherit",cursor:"pointer",font:"inherit",lineHeight:"normal",margin:0,MozOsxFontSmoothing:"inherit",padding:0,textAlign:"inherit",WebkitAppearance:"none",WebkitFontSmoothing:"inherit"})})));function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ct(e,t,n[t])}))}return e}function dt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function pt(e){const t=(0,T.useTheme2)(),{linkButton:n}=lt();let r;if(e.label===be){const t=$t().options;e.value in t&&(r=t[e.value].color)}const s=((e,t)=>({activePill:(0,i.css)({}),pill:(0,i.css)({display:"inline-flex",flex:"0 1 auto",flexDirection:"column",marginLeft:e.spacing(.5),marginRight:e.spacing(.5),marginTop:e.spacing(.5),padding:`${e.spacing(.25)} ${e.spacing(.25)}`,position:"relative"}),valueWrap:(0,i.css)({"&:before":{backgroundColor:t,content:'""',height:"100%",left:0,position:"absolute",top:0,width:`${e.spacing(.25)}`},"&:hover":{border:`1px solid ${e.colors.border.strong}`},backgroundColor:"transparent",border:`1px solid ${e.colors.background.secondary}`,boxShadow:`-2px 2px 5px 0px ${e.colors.background.secondary}`,cursor:"pointer",paddingLeft:t?`${e.spacing(.75)}`:`${e.spacing(.5)}`,paddingRight:`${e.spacing(.5)}`,position:"relative"})}))(t,r);return a().createElement("button",{className:(0,i.cx)(n,s.pill,e.menuActive?s.activePill:void 0),onClick:e.onClick},a().createElement("span",{className:s.valueWrap},e.label,"=",e.value),e.menuActive&&a().createElement(rt,{pillType:"logPill",fieldType:e.fieldType,links:e.links,label:e.label,value:e.value,showColumn:e.onClickAdd}))}const gt=e=>{const{label:t}=e,{cellIndex:n,setActiveCellIndex:i}=Ye(),{columns:o,setColumns:l}=S(),c=e.value,u=(0,s.getTemplateSrv)(),d=(0,r.useMemo)((()=>u.replace.bind(u)),[u]),p=e.field;if(!p||(null==p?void 0:p.type)===_.FieldType.other)return null;const g={index:e.rowIndex};e.originalField&&e.isDerivedField&&e.originalFrame&&(e.originalField.getLinks=(0,_.getLinksSupplier)(e.originalFrame,e.originalField,{},d));const h=e.originalField&&(0,T.getCellLinks)(e.originalField,g);return a().createElement(pt,{onClick:()=>e.rowIndex===n.index&&p.name===n.fieldName&&t===n.subFieldName?i({index:null}):i({fieldName:p.name,index:e.rowIndex,numberOfMenuItems:e.isDerivedField?2:3,subFieldName:t}),menuActive:n.index===e.rowIndex&&n.fieldName===p.name&&n.subFieldName===t,fieldType:e.isDerivedField?"derived":void 0,label:t,value:c,onClickAdd:()=>(e=>{const t=ut({},o),n=Object.keys(o).filter((e=>o[e].active)).length;t[e].active?t[e]=dt(ut({},t[e]),{active:!1,index:void 0}):t[e]=dt(ut({},t[e]),{active:!0,index:n}),l(t)})(t),links:h})};function ht(e){const t=(0,T.useTheme2)(),n=ft(t);return a().createElement("div",{"data-testid":Pe.b.table.rawLogLine,className:n.rawLogLine},a().createElement(a().Fragment,null,e.value))}const ft=(e,t)=>({rawLogLine:(0,i.css)({fontFamily:e.typography.fontFamilyMonospace,fontSize:e.typography.bodySmall.fontSize,height:"35px",lineHeight:"35px",paddingLeft:e.spacing(1),paddingRight:e.spacing(1.5)})}),mt=e=>{var t,n;null==e||null===(n=e.current)||void 0===n||n.scrollTo({left:null===(t=e.current)||void 0===t?void 0:t.scrollLeft})};function vt({scrollerRef:e}){const t=(e=>({scroller:i.css`
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 20px;
    top: 32px;
    margin-top: -24px;
    // For some reason clicking on this button causes text to be selected in the following row
    user-select: none;
  `,scrollLeft:i.css`
    cursor: pointer;
    background: ${e.colors.background.primary};

    &:hover {
      background: ${e.colors.background.secondary};
    }
  `,scrollRight:i.css`
    cursor: pointer;
    background: ${e.colors.background.primary};

    &:hover {
      background: ${e.colors.background.secondary};
    }
  `}))((0,T.useTheme2)());return a().createElement("div",{className:t.scroller},a().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({behavior:"smooth",left:0,top:0})},onPointerUp:()=>mt(e),className:t.scrollLeft},a().createElement(T.Icon,{name:"arrow-left"})),a().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({behavior:"smooth",left:t.current.scrollWidth,top:0})},onPointerUp:()=>mt(e),className:t.scrollRight},a().createElement(T.Icon,{name:"arrow-right"})))}const bt=e=>{let t=e.value;const n=e.field,i=n.display(t),s=(0,T.useTheme2)(),o=yt(s),{bodyState:l,columns:c,setVisible:u}=S(),{logsFrame:p}=Oe(),[g,h]=(0,r.useState)(!1),f=(0,r.useRef)(null);t=a().isValidElement(e.value)?e.value:"object"==typeof t?JSON.stringify(e.value):(0,_.formattedValueToString)(i);const v=(t=>Object.keys(c).filter((e=>e!==(0,d.Il)(p))).sort(((e,t)=>e===be?-1:t===be?1:"LINK_FIELD"===c[e].type?-1:"LINK_FIELD"===c[t].type?1:c[e].cardinality>c[t].cardinality?-1:1)).filter((e=>!c[e].active&&c[e].cardinality>1)).map((r=>{var i;const s=t[r],o=null==p||null===(i=p.raw)||void 0===i?void 0:i.fields.find((e=>e.name===r)),l=null==n?void 0:n.values[e.rowIndex],d=!s&&!!l;if(s)return a().createElement(gt,{originalFrame:void 0,field:n,columns:c,rowIndex:e.rowIndex,frame:e.frame,showColumns:()=>u(!0),key:r,label:r,isDerivedField:!1,value:s});if(d&&(null==o?void 0:o.name)){const t=null==o?void 0:o.values[e.rowIndex];if((null==o?void 0:o.type)===_.FieldType.string&&t)return a().createElement(gt,{originalFrame:null==p?void 0:p.raw,originalField:o,field:n,value:t,columns:c,rowIndex:e.rowIndex,frame:e.frame,showColumns:()=>u(!0),key:o.name,label:o.name,isDerivedField:!0})}return null})).filter((e=>e)))(e.labels),b=l===m.auto,y=v.length>0;return a().createElement(tt,{onMouseIn:()=>{h(!0)},onMouseOut:()=>{h(!1)},rowIndex:e.rowIndex,field:e.field},a().createElement(Ee.ScrollSyncPane,{innerRef:f,group:"horizontal"},a().createElement("div",{className:o.content},0===e.fieldIndex&&a().createElement(st,{rowIndex:e.rowIndex,value:t}),b&&y&&a().createElement(a().Fragment,null,v),l===m.labels&&y&&a().createElement(a().Fragment,null,v),l===m.labels&&!y&&a().createElement(ht,{value:t}),b&&!y&&a().createElement(ht,{value:t}),l===m.text&&a().createElement(ht,{value:t}),g&&a().createElement(vt,{scrollerRef:f}))))},yt=e=>({content:i.css`
    white-space: nowrap;
    overflow-x: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    padding-right: 30px;
    display: flex;
    align-items: flex-start;
    height: 100%;
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }

    &:after {
      pointer-events: none;
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      // Fade out text in last 10px to background color to add affordance to horiziontal scroll
      background: linear-gradient(to right, transparent calc(100% - 10px), ${e.colors.background.primary});
    }
  `});function St(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function wt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Ot(e){var t=function(e,t){if("object"!==xt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==xt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===xt(t)?t:String(t)}function xt(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}const Et=e=>{const{isHeaderMenuActive:t,setHeaderMenuActive:n}=et(),{logsFrame:s}=Oe(),o=(0,r.useRef)(null),l=((e,t,n)=>({clearButton:(0,i.css)({marginLeft:"5px"}),defaultContentWrapper:(0,i.css)({borderLeft:t?`1px solid ${e.colors.border.weak}`:"none",display:"flex",marginLeft:t?"-6px":0,paddingLeft:t?"12px":0}),leftAlign:(0,i.css)({display:"flex",label:"left-align",width:"calc(100% - 20px)"}),logLineButton:(0,i.css)({marginLeft:"5px"}),rightAlign:(0,i.css)({display:"flex",label:"right-align",marginRight:"5px"}),tableHeaderMenu:(0,i.css)({backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,height:"100%",label:"tableHeaderMenu",margin:e.spacing(1,0),maxHeight:"400px",minWidth:"250px",padding:e.spacing(2),width:"100%"}),wrapper:(0,i.css)({borderRight:`1px solid ${e.colors.border.weak}`,display:"flex",label:"wrapper",marginLeft:t?"56px":"6px",marginRight:"-6px",width:n?"calc(100% + 6px)":"100%"})}))((0,T.useTheme2)(),0===e.fieldIndex,e.field.name===(0,d.Il)(s)),{bodyState:c,columnWidthMap:u,setBodyState:p,setColumnWidthMap:g}=S(),h=e.field.name===(0,d.Il)(s),f=()=>{p(c===m.text?m.labels:m.text)};return a().createElement("span",{className:l.wrapper},a().createElement("span",{className:l.leftAlign},a().createElement("span",{className:l.defaultContentWrapper},e.defaultContent),u&&g&&void 0!==(null==u?void 0:u[e.field.name])&&a().createElement(T.IconButton,{tooltip:"Reset column width",tooltipPlacement:"top",className:l.clearButton,"aria-label":"Reset column width",name:"x",onClick:()=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){St(e,t,n[t])}))}return e}({},u),n=e.field.name,{[n]:r}=t,a=wt(t,[n].map(Ot));null==g||g(a)}}),h&&a().createElement(a().Fragment,null,c===m.text?a().createElement(T.IconButton,{tooltipPlacement:"top",tooltip:"Show log labels","aria-label":"Show log labels",onClick:f,className:l.logLineButton,name:"brackets-curly",size:"md"}):a().createElement(T.IconButton,{tooltipPlacement:"top",tooltip:"Show log text","aria-label":"Show log text",onClick:f,className:l.logLineButton,name:"text-fields",size:"md"}))),a().createElement("span",{className:l.rightAlign},a().createElement(T.IconButton,{tooltip:`Show ${e.field.name} menu`,tooltipPlacement:"top",ref:o,"aria-label":`Show ${e.field.name} menu`,onClick:e=>{n(!t)},name:"ellipsis-v"})),o.current&&a().createElement(T.Popover,{show:t,content:a().createElement(T.ClickOutsideWrapper,{onClick:()=>n(!1),useCapture:!0},a().createElement("div",{className:l.tableHeaderMenu},e.children)),referenceElement:o.current}))};function Ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kt(e){const{setHeaderMenuActive:t}=et(),{bodyState:n,columns:s,setBodyState:o,setColumns:l}=S(),{logsFrame:c}=Oe(),u=Pt(),{linkButton:p}=lt(),g=(0,r.useCallback)((e=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ct(e,t,n[t])}))}return e}({},s);Object.keys(t).filter((n=>{const r=t[n].index,a=t[e.name].index;return t[n].active&&a&&r&&r>a})).map((e=>t[e])).forEach((e=>{void 0!==e.index&&e.index--})),t[e.name].active=!1,t[e.name].index=void 0,l(t)}),[s,l]),h=e.headerProps.field.name===(0,d.Il)(c);return a().createElement(Et,e.headerProps,a().createElement("div",{className:u.linkWrap},a().createElement("button",{className:(0,i.cx)(p,u.link),onClick:()=>{e.openColumnManagementDrawer(),t(!1)}},a().createElement(T.Icon,{className:u.icon,name:"columns",size:"md"}),"Manage columns")),a().createElement("div",{className:u.linkWrap},a().createElement("button",{className:(0,i.cx)(p,u.link),onClick:()=>g(e.headerProps.field)},a().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17 16",width:"17",height:"16",className:"css-q2u0ig-Icon"},a().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.73446 1.33301H12.2345C12.3892 1.33301 12.5375 1.40325 12.6469 1.52827C12.7563 1.65329 12.8178 1.82286 12.8178 1.99967V4.74967C12.8178 5.07184 12.5566 5.33301 12.2345 5.33301C11.9123 5.33301 11.6511 5.07184 11.6511 4.74967V2.66634H7.56779V13.333H11.6511V10.9163C11.6511 10.5942 11.9123 10.333 12.2345 10.333C12.5566 10.333 12.8178 10.5942 12.8178 10.9163V13.9997C12.8178 14.1765 12.7563 14.3461 12.6469 14.4711C12.5375 14.5961 12.3892 14.6663 12.2345 14.6663H1.73446C1.57975 14.6663 1.43137 14.5961 1.32198 14.4711C1.21258 14.3461 1.15112 14.1765 1.15112 13.9997V1.99967C1.15112 1.82286 1.21258 1.65329 1.32198 1.52827C1.43137 1.40325 1.57975 1.33301 1.73446 1.33301ZM2.31779 13.333H6.40112V2.66634H2.31779V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),a().createElement("path",{d:"M15.9893 10.6315C15.9498 10.7263 15.8919 10.8123 15.819 10.8846C15.7467 10.9575 15.6607 11.0154 15.5659 11.0549C15.4712 11.0943 15.3695 11.1147 15.2668 11.1147C15.1641 11.1147 15.0625 11.0943 14.9677 11.0549C14.8729 11.0154 14.7869 10.9575 14.7146 10.8846L12.9335 9.09573L11.1524 10.8846C11.0801 10.9575 10.9941 11.0154 10.8993 11.0549C10.8045 11.0943 10.7028 11.1147 10.6002 11.1147C10.4975 11.1147 10.3958 11.0943 10.301 11.0549C10.2063 11.0154 10.1202 10.9575 10.0479 10.8846C9.97504 10.8123 9.91717 10.7263 9.87769 10.6315C9.8382 10.5367 9.81787 10.4351 9.81787 10.3324C9.81787 10.2297 9.8382 10.1281 9.87769 10.0333C9.91717 9.9385 9.97504 9.85248 10.0479 9.78017L11.8368 7.99906L10.0479 6.21795C9.90148 6.07149 9.8192 5.87285 9.8192 5.66573C9.8192 5.4586 9.90148 5.25996 10.0479 5.1135C10.1944 4.96705 10.393 4.88477 10.6002 4.88477C10.8073 4.88477 11.0059 4.96705 11.1524 5.1135L12.9335 6.90239L14.7146 5.1135C14.8611 4.96705 15.0597 4.88477 15.2668 4.88477C15.4739 4.88477 15.6726 4.96705 15.819 5.1135C15.9655 5.25996 16.0478 5.4586 16.0478 5.66573C16.0478 5.87285 15.9655 6.07149 15.819 6.21795L14.0302 7.99906L15.819 9.78017C15.8919 9.85248 15.9498 9.9385 15.9893 10.0333C16.0288 10.1281 16.0491 10.2297 16.0491 10.3324C16.0491 10.4351 16.0288 10.5367 15.9893 10.6315Z",fill:"#CCCCDC",fillOpacity:"1"})),"Remove column")),e.slideLeft&&a().createElement("div",{className:u.linkWrap},a().createElement("button",{className:(0,i.cx)(p,u.link),onClick:()=>{var t;return null===(t=e.slideLeft)||void 0===t?void 0:t.call(e,s)}},a().createElement(T.Icon,{className:(0,i.cx)(u.icon,u.reverse),name:"arrow-from-right",size:"md"}),"Move left")),e.slideRight&&a().createElement("div",{className:u.linkWrap},a().createElement("button",{className:(0,i.cx)(p,u.link),onClick:()=>{var t;return null===(t=e.slideRight)||void 0===t?void 0:t.call(e,s)}},a().createElement(T.Icon,{className:u.icon,name:"arrow-from-right",size:"md"}),"Move right")),h&&a().createElement("div",{className:u.linkWrap},a().createElement("button",{className:(0,i.cx)(p,u.link),onClick:()=>{n===m.text?o(m.labels):o(m.text)}},n===m.text?a().createElement(T.Icon,{className:u.icon,name:"brackets-curly",size:"md"}):a().createElement(T.Icon,{className:u.icon,name:"text-fields",size:"md"}),n===m.text?"Show labels":"Show log text")),e.autoColumnWidths&&a().createElement("div",{className:u.linkWrap},a().createElement("button",{className:(0,i.cx)(p,u.link),onClick:()=>{var t;return null===(t=e.autoColumnWidths)||void 0===t?void 0:t.call(e)}},a().createElement(T.Icon,{className:u.icon,name:"arrows-h",size:"md"}),"Reset column widths")))}const Pt=()=>({icon:(0,i.css)({marginRight:"10px"}),link:(0,i.css)({paddingBottom:"5px",paddingTop:"5px"}),linkWrap:(0,i.css)({}),reverse:(0,i.css)({transform:"scaleX(-1)"})});function jt(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function Ft(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ft(e,t,n[t])}))}return e}function _t(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function Tt(e){return a().createElement(T.Table,{onColumnResize:e.onResize,initialRowIndex:e.selectedLine,cellHeight:ke.qM.Sm,data:e.data,height:e.height,width:e.width,footerOptions:{countRows:!0,reducer:["count"],show:!0}})}const Dt=e=>{const{height:t,labels:n,logsFrame:o,timeZone:l,width:c}=e,u=(0,T.useTheme2)(),p={section:(0,i.css)({position:"relative"}),tableWrap:(0,i.css)({".cellActions":{display:"none !important"}})},[g,h]=(0,r.useState)(void 0),{clearSelectedLine:f,columns:m,columnWidthMap:v,setColumns:b,setColumnWidthMap:y,setFilteredColumns:w,setVisible:O,visible:x}=S(),{selectedLine:E}=Oe(),[C]=(0,r.useState)(E),k=Ge(b),P=(0,s.getTemplateSrv)(),j=(0,r.useMemo)((()=>P.replace.bind(P)),[P]),F=(0,r.useCallback)((e=>{if(!e.length)return e;const[t]=(0,_.applyFieldOverrides)({data:[e],fieldConfig:{defaults:{custom:{}},overrides:[]},replaceVariables:j,theme:u,timeZone:l});for(const[s,l]of t.fields.entries()){var r,i;l.type=l.type===_.FieldType.string?null!==(r=Nt(l))&&void 0!==r?r:_.FieldType.string:l.type,l.config=_t(Lt({},l.config),{custom:Lt({cellOptions:It(l,s,n,o),filterable:!0,headerComponent:t=>a().createElement(Ze,null,a().createElement(kt,{headerProps:_t(Lt({},t),{fieldIndex:s}),openColumnManagementDrawer:()=>O(!0),slideLeft:0!==s?e=>k(e,s,s-1):void 0,slideRight:s!==e.fields.length-1?e=>k(e,s,s+1):void 0,autoColumnWidths:Object.keys(v).length>0?()=>{y({})}:void 0})),inspect:!0,width:null!==(i=v[l.name])&&void 0!==i?i:Bt(l,s,m,c,t.fields.length,o)},l.config.custom),filterable:!0})}return t}),[l,u,n,c,j,O,v]);(0,r.useEffect)((()=>{const e=function(){var e,t=(e=function*(){const e=(t=o.raw).fields.filter((e=>{var n,r,a;const i="json.RawMessage"===(null===(n=e.typeInfo)||void 0===n?void 0:n.frame)&&"labels"===e.name&&(null==t||null===(r=t.meta)||void 0===r?void 0:r.type)!==_.DataFrameType.LogLines,s="labels"===e.name&&e.type===_.FieldType.other&&(null==t||null===(a=t.meta)||void 0===a?void 0:a.type)===_.DataFrameType.LogLines;return i||s})).flatMap((e=>[{id:"extractFields",options:{format:"json",keepTime:!1,replace:!1,source:e.name}}]));var t;const n=function(e){let t={};for(const n in e)t[n]=!0;return Object.keys(e).length>0?{id:"organize",options:{includeByName:t,indexByName:e}}:null}(function(e){let t={};return Object.keys(e).filter((t=>e[t].active)).forEach((n=>{const r=e[n].index;void 0!==r&&(t[n]=r)})),t}(m));if(n)e.push(n);else{const t={body:o.bodyField,extraFields:o.extraFields,time:o.timeField};t&&void 0!==t.body&&void 0!==t.time&&e.push(function(e){return{id:"organize",options:{includeByName:{[e.body.name]:!0,[e.time.name]:!0},indexByName:{[e.time.name]:0,[e.body.name]:1}}}}(t))}if(e.length>0){const t=yield(0,Ce.lastValueFrom)((0,_.transformDataFrame)(e,[o.raw])),n=F(t[0]);h(n)}else h(F(o.raw))},function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){jt(i,r,a,s,o,"next",e)}function o(e){jt(i,r,a,s,o,"throw",e)}s(void 0)}))});return function(){return t.apply(this,arguments)}}();e()}),[o.raw,o.bodyField,o.timeField,o.extraFields,F,m]),(0,r.useEffect)((()=>{C&&E&&f()}),[C,f,E]);const D=o.raw.fields.find((e=>e.name===(0,d.po)(o))),N=null==D?void 0:D.values.findIndex((e=>e===(null==C?void 0:C.id))),$=N&&-1!==N?N:void 0;if(!g)return a().createElement(a().Fragment,null);return a().createElement("div",{"data-testid":Pe.b.table.wrapper,className:p.section},x&&a().createElement(T.Drawer,{size:"sm",onClose:()=>{O(!1),w(m)}},a().createElement(Qe,null)),a().createElement("div",{className:p.tableWrap},a().createElement(Je,null,a().createElement(Ee.ScrollSync,{horizontal:!0,vertical:!1,proportional:!1},a().createElement(Tt,{logsFrame:o,selectedLine:$,data:g,height:t,width:c,onResize:(0,L.debounce)(((e,t)=>{const n=Object.keys(m).filter((e=>m[e].active)).find((t=>t===e));if(n&&t>0){const e=Lt({},v);e[n]=t,y(e)}}),100),logsSortOrder:e.logsSortOrder})))))};function Nt(e){if(e.name){const t=e.name.toLowerCase();if("date"===t||"time"===t)return _.FieldType.time}for(let t=0;t<e.values.length;t++){const n=e.values[t];if(null!=n)return Vt(n)}}const $t=()=>({options:{crit:{color:"#705da0",index:1},critical:{color:"#705da0",index:0},debug:{color:"#1f78c1",index:8},eror:{color:"#e24d42",index:4},err:{color:"#e24d42",index:3},error:{color:"#e24d42",index:2},info:{color:"#7eb26d",index:7},trace:{color:"#6ed0e0",index:9},warn:{color:"#FF9900",index:6},warning:{color:"#FF9900",index:5}},type:_.MappingType.ValueToText});function It(e,t,n,r){return e.name===(0,d.Il)(r)?{cellComponent:e=>a().createElement(bt,_t(Lt({},e),{fieldIndex:t,labels:n[e.rowIndex]})),type:T.TableCellDisplayMode.Custom}:{cellComponent:e=>a().createElement(ot,_t(Lt({},e),{fieldIndex:t})),type:T.TableCellDisplayMode.Custom}}function Bt(e,t,n,r,a,i){var s,o;const l=a<=2?r:Math.min(r/2),c=0===t?50:0;if(e.type===_.FieldType.time)return 200+c;const u=n[e.name];if(void 0===u)return;var p;const g=Math.max(null!==(p=u.maxLength)&&void 0!==p?p:0,e.name.length);return u.maxLength?Math.min(Math.max(6.5*g+95+c,90+c),l):e.name!==(0,d.Il)(i)?Math.min(Math.max(6.5*(null!==(h=null===(o=e.values)||void 0===o||null===(s=o[0])||void 0===s?void 0:s.length)&&void 0!==h?h:80)+95+c,90+c),l):void 0;var h}const At=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3,})?(?:Z|[-+]\d{2}:?\d{2})$/,Mt=e=>{const{logsFrame:t}=Oe(),[n,s]=(0,r.useState)({height:0,width:0});(0,xe.w)({onResize:()=>{const t=e.panelWrap.current;t&&(n.width===t.clientWidth&&n.height===t.clientHeight||s({height:t.clientHeight,width:t.clientWidth}))},ref:e.panelWrap});const o={section:(0,i.css)({position:"relative"})},l=(0,_.getTimeZone)(),c=(0,r.useCallback)((t=>{const n=e.urlColumns;return(null==n?void 0:n.length)&&Object.values(n).forEach(((e,n)=>{t[e]&&(t[e].active=!0,t[e].index=n)})),t}),[e.urlColumns]);if(!t||!t.raw.length)return null;var u;const d=null!==(u=t.getLogFrameLabelsAsLabels())&&void 0!==u?u:[],p=t?t.raw.length:0;let g=function(e,t){let n={};const r=new Map,a=function(e){const t=new Map;return e.forEach((e=>{Object.keys(e).forEach((n=>{if(t.has(n)){const r=t.get(n),a=null==r?void 0:r.valueSet,i=null==r?void 0:r.maxLength;a&&!(null==a?void 0:a.has(e[n]))&&(null==a||a.add(e[n]),i&&e[n].length>i&&t.set(n,{maxLength:e[n].length,valueSet:a}))}else t.set(n,{maxLength:e[n].length,valueSet:new Set([e[n]])})}))})),t}(t),i=e?e.length:0;(null==t?void 0:t.length)&&i&&(t.forEach((e=>{Object.keys(e).forEach((e=>{var t;const n=a.get(e);var i;const s=null!==(i=null==n||null===(t=n.valueSet)||void 0===t?void 0:t.size)&&void 0!==i?i:0;if(r.has(e)){const t=r.get(e);t&&((null==t?void 0:t.active)?r.set(e,{active:!0,cardinality:s,index:t.index,maxLength:null==n?void 0:n.maxLength,percentOfLinesWithLabel:t.percentOfLinesWithLabel+1}):r.set(e,{active:!1,cardinality:s,index:void 0,maxLength:null==n?void 0:n.maxLength,percentOfLinesWithLabel:t.percentOfLinesWithLabel+1}))}else r.set(e,{active:!1,cardinality:s,index:void 0,maxLength:null==n?void 0:n.maxLength,percentOfLinesWithLabel:1})}))})),n=Object.fromEntries(r),Object.keys(n).forEach((e=>{n[e].percentOfLinesWithLabel=Rt(n[e].percentOfLinesWithLabel,i)})));return n}(t.raw,d);const h={body:t.bodyField,extraFields:t.extraFields,time:t.timeField};if(h){!function(e,t,n){e.forEach((e=>{var r,a;if(!e)return;const i=null===(r=t[e.name])||void 0===r?void 0:r.active,s=null===(a=t[e.name])||void 0===a?void 0:a.index;t[e.name]=i&&void 0!==s?{active:!0,cardinality:n,index:s,percentOfLinesWithLabel:Rt(e.values.filter((e=>null!=e)).length,n)}:{active:!1,cardinality:n,index:void 0,percentOfLinesWithLabel:Rt(e.values.filter((e=>null!=e)).length,n)}}))}([h.time,h.body,...h.extraFields],g,p),g=c(g);!function(e,t,n){var r,a;if(0===e.length){var i,s,o,l,c,u;if(null===(i=t.body)||void 0===i?void 0:i.name)n[null===(o=t.body)||void 0===o?void 0:o.name].active=!0,n[null===(l=t.body)||void 0===l?void 0:l.name].index=1;if(null===(s=t.time)||void 0===s?void 0:s.name)n[null===(c=t.time)||void 0===c?void 0:c.name].active=!0,n[null===(u=t.time)||void 0===u?void 0:u.name].index=0}if((null===(r=t.time)||void 0===r?void 0:r.name)&&(null===(a=t.body)||void 0===a?void 0:a.name)){var d,p;n[null===(d=t.body)||void 0===d?void 0:d.name].type="BODY_FIELD",n[null===(p=t.time)||void 0===p?void 0:p.name].type="TIME_FIELD"}t.extraFields.length&&t.extraFields.forEach((e=>{var t;(null===(t=e.config.links)||void 0===t?void 0:t.length)&&(n[e.name].type="LINK_FIELD")}))}(Object.keys(g).filter((e=>g[e].active)),h,g)}return a().createElement("section",{className:o.section},a().createElement(b,{setUrlTableBodyState:e.setUrlTableBodyState,logsFrame:t,initialColumns:g,setUrlColumns:e.setUrlColumns,clearSelectedLine:e.clearSelectedLine,urlTableBodyState:e.urlTableBodyState,showColumnManagementDrawer:e.showColumnManagementDrawer,isColumnManagementActive:e.isColumnManagementActive},a().createElement(Dt,{logsFrame:t,timeZone:l,height:n.height-50,width:n.width-25+(le.CT?-32:0),labels:d,logsSortOrder:e.logsSortOrder})))},Rt=(e,t)=>Math.ceil(100*e/t);function Vt(e){let t=(0,_.guessFieldTypeFromValue)(e);return"string"===t&&At.test(e)&&(t=_.FieldType.time),t}const Wt=({addFilter:e,clearSelectedLine:t,dataFrame:n,isColumnManagementActive:i,logsSortOrder:s,panelWrap:o,selectedLine:l,setUrlColumns:c,setUrlTableBodyState:u,showColumnManagementDrawer:p,timeRange:g,urlColumns:h,urlTableBodyState:f})=>{const m=(0,r.useMemo)((()=>{if(!n)return null;const e=n.fields.findIndex((e=>e.type===_.FieldType.time)),t=(0,_.sortDataFrame)(n,e,s===_.LogsSortOrder.Descending);return(0,d.Os)(t)}),[n,s]);return m?a().createElement(we,{addFilter:e,selectedLine:l,timeRange:g,logsFrame:m},a().createElement(Mt,{urlTableBodyState:f,setUrlColumns:c,setUrlTableBodyState:u,urlColumns:h,panelWrap:o,clearSelectedLine:t,showColumnManagementDrawer:p,isColumnManagementActive:i,logsSortOrder:s})):null};function zt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Kt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}let Ht=["timestamp","body","Time","Line"];class Ut extends o.Bs{setStateFromUrl(){const e=new URLSearchParams(s.locationService.getLocation().search);this.updateFromUrl({sortOrder:e.get("sortOrder")})}getUrlState(){return{sortOrder:JSON.stringify(this.state.sortOrder)}}updateFromUrl(e){try{if("string"==typeof e.sortOrder&&e.sortOrder){const t=(0,u.FH)(JSON.parse(e.sortOrder));t&&this.setState({sortOrder:t})}}catch(e){c.v.error(e,{msg:"LogsTableScene: updateFromUrl unexpected error"})}}onActivate(){this.setState({emptyScene:new Y.W({clearCallback:()=>(0,U.rA)(this)}),menu:new Q.GD({addInvestigationsLink:!1})}),this.onActivateSyncDisplayedFieldsWithUrlColumns(),this.setStateFromUrl()}getParentScene(){return o.jh.getAncestor(this,Jt)}constructor(e){super(Kt(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){zt(e,t,n[t])}))}return e}({},e),{isColumnManagementActive:!1,sortOrder:(0,C.YM)("sortOrder",_.LogsSortOrder.Descending)})),zt(this,"_urlSync",new o.So(this,{keys:["sortOrder"]})),zt(this,"showColumnManagementDrawer",(e=>{this.setState({isColumnManagementActive:e})})),zt(this,"onActivateSyncDisplayedFieldsWithUrlColumns",(()=>{const e=new URLSearchParams(s.locationService.getLocation().search);let t=[];try{var n;t=(0,u.aJ)(JSON.parse(decodeURIComponent(null!==(n=e.get("urlColumns"))&&void 0!==n?n:"")))}catch(e){console.error(e)}const r=this.getParentScene();Ht=t&&this.urlHasDefaultUrlColumns(t)?this.updateDefaultUrlColumns(t):Ht,Ht.length,r.setState({urlColumns:Array.from(new Set([...Ht,...r.state.displayedFields]))})})),zt(this,"updateDisplayedFields",(e=>{const t=this.getParentScene();Ht=this.updateDefaultUrlColumns(e);const n=Array.from(new Set([...e||[]])).filter((e=>!Ht.includes(e)));t.setState({displayedFields:n}),(0,C.ZF)(this,t.state.displayedFields)})),zt(this,"urlHasDefaultUrlColumns",(e=>Ht.some((t=>e.includes(t))))),zt(this,"updateDefaultUrlColumns",(e=>(Ht=Ht.reduce(((t,n)=>{if(e.includes(n)){t[e.indexOf(n)]=n}return t}),[]),Ht))),zt(this,"handleSortChange",(e=>{if(e===this.state.sortOrder)return;(0,C.YK)("sortOrder",e);const t=o.jh.getData(this),n=t instanceof o.dt?t:o.jh.findDescendents(t,o.dt)[0];n&&n.runQueries(),this.setState({sortOrder:e})})),zt(this,"onManageColumnsClick",(()=>{this.showColumnManagementDrawer(!0)})),zt(this,"onLineStateClick",(()=>{const e=o.jh.getAncestor(this,Jt),{tableLogLineState:t}=e.state;e.setState({tableLogLineState:t===m.text?m.labels:m.text})})),this.addActivationHandler(this.onActivate.bind(this))}}zt(Ut,"Component",(({model:e})=>{const t=(0,T.useStyles2)(Gt),n=o.jh.getAncestor(e,Jt),{data:i}=o.jh.getData(e).useState(),{selectedLine:s,tableLogLineState:l,urlColumns:c,visualizationType:u}=n.useState(),{emptyScene:d,isColumnManagementActive:p,menu:g,sortOrder:h}=e.useState(),f=o.jh.getTimeRange(e),{value:v}=f.useState(),b=(0,se.tn)(i),y=(0,r.useRef)(null);return a().createElement("div",{className:t.panelWrapper,ref:y},a().createElement(T.PanelChrome,{loadingState:null==i?void 0:i.state,title:"Logs",menu:g?a().createElement(g.Component,{model:g}):void 0,showMenuAlways:!0,actions:a().createElement(a().Fragment,null,!le.CT&&a().createElement(T.Button,{onClick:e.onManageColumnsClick,variant:"secondary",size:"sm"},"Manage columns"),a().createElement(q.C,{vizType:u,onChange:n.setVisualizationType}))},a().createElement("div",{className:t.container},le.CT&&b&&b.length>0&&a().createElement(ae,{sortOrder:h,onSortOrderChange:e.handleSortChange,onManageColumnsClick:e.onManageColumnsClick,onLineStateClick:e.onLineStateClick,lineState:null!=l?l:m.labels}),b&&a().createElement(Wt,{panelWrap:y,addFilter:t=>{const r=(0,N.OE)(b,t.key,e);(0,J.XI)(t,n,r)},timeRange:v,selectedLine:s,urlColumns:null!=c?c:[],setUrlColumns:t=>{(0,D.n)(t,n.state.urlColumns)||(n.setState({urlColumns:t}),e.updateDisplayedFields(t))},dataFrame:b,clearSelectedLine:()=>{n.state.selectedLine&&n.clearSelectedLine()},setUrlTableBodyState:e=>{n.setState({tableLogLineState:e})},urlTableBodyState:l,showColumnManagementDrawer:e.showColumnManagementDrawer,isColumnManagementActive:p,logsSortOrder:h}),d&&b&&0===b.length&&a().createElement(Y.W.Component,{model:d}))))}));const Gt=e=>({container:(0,i.css)({display:"flex",flexDirection:"row-reverse"}),panelWrapper:(0,i.css)({height:"100%",label:"panel-wrapper-table",width:"100%"})});function Qt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class Jt extends o.Bs{getUrlState(){var e;const t=null!==(e=this.state.urlColumns)&&void 0!==e?e:[],n=this.state.selectedLine,r=this.state.visualizationType;var a,i;const s=null!==(i=null!==(a=this.state.displayedFields)&&void 0!==a?a:(0,C.N$)(this))&&void 0!==i?i:[];return{displayedFields:JSON.stringify(s),selectedLine:JSON.stringify(n),tableLogLineState:JSON.stringify(this.state.tableLogLineState),urlColumns:JSON.stringify(t),visualizationType:JSON.stringify(r)}}updateFromUrl(e){const t={};try{if("string"==typeof e.urlColumns){const n=(0,u.aJ)(JSON.parse(e.urlColumns));n!==this.state.urlColumns&&(t.urlColumns=n)}if("string"==typeof e.selectedLine){const n=(0,u.lb)(JSON.parse(e.selectedLine));if(n){const e=n;e!==this.state.selectedLine&&(t.selectedLine=e)}}if("string"==typeof e.visualizationType){const n=(0,u.v_)(JSON.parse(e.visualizationType));n&&n!==this.state.visualizationType&&(t.visualizationType=n)}if("string"==typeof e.displayedFields){const n=(0,u.aJ)(JSON.parse(e.displayedFields));n&&n.length&&(t.displayedFields=n)}if("string"==typeof e.tableLogLineState){const n=JSON.parse(e.tableLogLineState);n!==m.labels&&n!==m.text||(t.tableLogLineState=n)}}catch(e){c.v.error(e,{msg:"LogsListScene: updateFromUrl unexpected error"})}Object.keys(t).length&&this.setState(t)}clearSelectedLine(){this.setState({selectedLine:void 0})}onActivate(){const e=new URLSearchParams(s.locationService.getLocation().search);this.setStateFromUrl(e),this.state.panel||this.updateLogsPanel(),this._subs.add(this.subscribeToState(((e,t)=>{e.visualizationType!==t.visualizationType&&this.updateLogsPanel()})))}setStateFromUrl(e){const t=e.get("selectedLine"),n=e.get("urlColumns"),r=e.get("visualizationType");var a;const i=null!==(a=e.get("displayedFields"))&&void 0!==a?a:JSON.stringify((0,C.N$)(this)),s=e.get("tableLogLineState");this.updateFromUrl({displayedFields:i,selectedLine:t,tableLogLineState:s,urlColumns:n,vizType:r})}getVizPanel(){this.logsPanelScene=new ve.o({});const e="logs"===this.state.visualizationType?[new o.G1({children:[new o.vA({body:new F({lineFilter:this.state.lineFilter}),xSizing:"fill"})]}),new o.vA({body:this.logsPanelScene,height:"calc(100vh - 220px)"})]:"json"===this.state.visualizationType?[new o.vA({body:new F({lineFilter:this.state.lineFilter}),xSizing:"fill"}),new o.vA({body:new fe({}),height:"calc(100vh - 220px)"})]:[new o.vA({body:new F({lineFilter:this.state.lineFilter}),xSizing:"fill"}),new o.vA({body:new Ut({}),height:"calc(100vh - 220px)"})];return new o.G1({children:e,direction:"column"})}constructor(e){super(qt(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Qt(e,t,n[t])}))}return e}({},e),{displayedFields:[],visualizationType:(0,C.k5)()})),Qt(this,"_urlSync",new o.So(this,{keys:["urlColumns","selectedLine","visualizationType","displayedFields","tableLogLineState"]})),Qt(this,"logsPanelScene",void 0),Qt(this,"clearDisplayedFields",(()=>{this.setState({displayedFields:[]}),this.logsPanelScene&&this.logsPanelScene.clearDisplayedFields()})),Qt(this,"setLogsVizOption",((e={})=>{this.logsPanelScene&&this.logsPanelScene.setLogsVizOption(e)})),Qt(this,"updateLogsPanel",(()=>{if(this.setState({panel:this.getVizPanel()}),this.state.panel){const e=o.jh.findDescendents(this.state.panel,F);if(e.length){const t=e[0];this._subs.add(t.subscribeToState(((e,t)=>{e.lineFilter!==t.lineFilter&&this.setState({lineFilter:e.lineFilter})})))}}})),Qt(this,"setVisualizationType",(e=>{this.setState({visualizationType:e}),(0,l.EE)(l.NO.service_details,l.ir.service_details.logs_visualization_toggle,{visualisationType:e}),(0,C.o5)(e)})),this.addActivationHandler(this.onActivate.bind(this))}}Qt(Jt,"Component",(({model:e})=>{const{panel:t}=e.useState();if(t)return a().createElement("div",{className:Yt.panelWrapper},a().createElement(t.Component,{model:t}))}));const Yt={panelWrapper:(0,i.css)({'section > div[class$="panel-content"]':(0,i.css)({contain:"none",overflow:"auto"})})}},8996:(e,t,n)=>{n.d(t,{o:()=>I});var r=n(5959),a=n.n(r),i=n(7781),s=n(8531),o=n(9736),l=n(1625),c=n(2007),u=n(4509),d=n(376),p=n(6854),g=n(5953),h=n(8428),f=n(4351),m=n(5553),v=n(20),b=n(5700),y=n(9405);const S=({onClick:e})=>{const[t,n]=(0,r.useState)(!1);(0,r.useEffect)((()=>{let e;return t&&(e=setTimeout((()=>{n(!1)}),2e3)),()=>{clearTimeout(e)}}),[t]);const i=(0,r.useCallback)(((t,r)=>{e(t,r),n(!0)}),[e]);return a().createElement(c.IconButton,{"aria-label":t?"Copied":"Copy link to log line",tooltip:t?"Copied":"Copy link to log line",tooltipPlacement:"top",variant:t?"primary":"secondary",size:"md",name:t?"check":"share-alt",onClick:i})};var w=n(2649),O=n(7902),x=n(7191);const E=({clearFilters:e,error:t})=>a().createElement(x.R,null,a().createElement("div",null,a().createElement("p",null,t),a().createElement(c.Button,{variant:"secondary",onClick:e},"Clear filters")));var C=n(71),k=n(7352),P=n(8839),j=n(2165),F=n(1475),L=n(5719),_=n(5002),T=n(5548);function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){D(e,t,n[t])}))}return e}function $(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class I extends o.Bs{setStateFromUrl(){const e=new URLSearchParams(s.locationService.getLocation().search);this.updateFromUrl({prettifyLogMessage:e.get("prettifyLogMessage"),sortOrder:e.get("sortOrder"),wrapLogMessage:e.get("wrapLogMessage")})}getUrlState(){return{prettifyLogMessage:JSON.stringify(this.state.prettifyLogMessage),sortOrder:JSON.stringify(this.state.sortOrder),wrapLogMessage:JSON.stringify(this.state.wrapLogMessage)}}updateFromUrl(e){const t={};try{if("string"==typeof e.sortOrder&&e.sortOrder){const n=(0,h.FH)(JSON.parse(e.sortOrder));n&&(t.sortOrder=n)}if("string"==typeof e.prettifyLogMessage&&e.prettifyLogMessage){const n=JSON.parse(e.prettifyLogMessage);"boolean"==typeof n&&(t.prettifyLogMessage=n)}if("string"==typeof e.wrapLogMessage&&e.wrapLogMessage){const n=JSON.parse(e.wrapLogMessage);"boolean"==typeof n&&(t.wrapLogMessage=n,F.CT||(t.prettifyLogMessage=n))}}catch(e){g.v.error(e,{msg:"LogsPanelScene: updateFromUrl unexpected error"})}Object.keys(t).length&&(this.setState(N({},t)),this.setLogsVizOption(N({},t)))}onActivate(){this.setStateFromUrl(),(0,f.sB)(this)&&this.setState({dedupStrategy:(0,f.sB)(this)}),this.state.body||this.setState({body:this.getLogsPanel()});const e=o.jh.getAncestor(this,k.Mn);this._subs.add(e.subscribeToState(((e,t)=>{var n,r,a,s,o,l;(null===(r=e.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)===i.LoadingState.Error?this.handleLogsError(null===(l=e.$data)||void 0===l?void 0:l.state.data):(null===(s=e.$data)||void 0===s||null===(a=s.state.data)||void 0===a?void 0:a.state)===i.LoadingState.Done&&(0,j.y2)(null===(o=e.$data)||void 0===o?void 0:o.state.data.series)?this.handleNoData():this.state.error&&this.clearLogsError();e.logsCount!==t.logsCount&&(this.state.body?this.state.body.setState({title:this.getTitle(e.logsCount)}):this.setState({body:this.getLogsPanel()}))})))}handleLogsError(e){var t;const n=(null===(t=e.errors)||void 0===t?void 0:t.length)?e.errors[0]:e.error,r=null==n?void 0:n.message;var a,i;r&&g.v.error(new Error("Logs Panel error"),{msg:r,status:null!==(a=n.statusText)&&void 0!==a?a:"N/A",type:null!==(i=n.type)&&void 0!==i?i:"N/A"});let s="Unexpected error response. Please review your filters or try a different time range.";(null==r?void 0:r.includes("parse error"))?s="Logs could not be retrieved due to invalid filter parameters. Please review your filters and try again.":(null==r?void 0:r.includes("response larger than the max message size"))&&(s="The response is too large to process. Try narrowing your search or using filters to reduce the data size."),this.showLogsError(s)}handleNoData(){this.showLogsError("No logs match your search. Please review your filters or try a different time range.")}showLogsError(e){var t;const n=null!==(t=this.state.logsVolumeCollapsedByError)&&void 0!==t?t:!(0,f.Rf)("collapsed");if(this.setState({error:e,logsVolumeCollapsedByError:n}),n){var r;null===(r=o.jh.findByKeyAndType(this,C.b,C._).state.panel)||void 0===r||r.setState({collapsed:!0})}}clearLogsError(){if(this.state.logsVolumeCollapsedByError){var e;null===(e=o.jh.findByKeyAndType(this,C.b,C._).state.panel)||void 0===e||e.setState({collapsed:!1})}this.setState({error:void 0,logsVolumeCollapsedByError:void 0})}setLogsVizOption(e={}){if(this.state.body){if("sortOrder"in e&&e.sortOrder!==this.state.body.state.options.sortOrder){const e=o.jh.getData(this),t=e instanceof o.dt?e:o.jh.findDescendents(e,o.dt)[0];t&&t.runQueries()}this.state.body.onOptionsChange(e)}}getParentScene(){return o.jh.getAncestor(this,O.i)}getTitle(e){var t;const n=(0,i.getValueFormat)("short"),r=void 0!==e?n(e,0):void 0;return void 0!==r?`Logs (${r.text}${null===(t=r.suffix)||void 0===t?void 0:t.trim()})`:"Logs"}handleLabelFilter(e,t,n,r){const a=(0,d.OE)(n,e,this);(0,y.Qt)(e,t,r,this,a),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_detail_filter_applied,{action:r,filterType:a,key:e})}constructor(e){super(N({dedupStrategy:l.fY.none,error:void 0,prettifyLogMessage:(0,f.IL)("prettifyLogMessage",!1),sortOrder:(0,f.YM)("sortOrder",l.uH.Descending),wrapLogMessage:(0,f.IL)("wrapLogMessage",!1)},e)),D(this,"_urlSync",new o.So(this,{keys:["sortOrder","wrapLogMessage","prettifyLogMessage"]})),D(this,"onClickShowField",(e=>{const t=this.getParentScene();if(-1===t.state.displayedFields.indexOf(e)&&this.state.body){const n=[...t.state.displayedFields,e];this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,f.ZF)(this,t.state.displayedFields),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_toggle_displayed_field)}})),D(this,"onClickHideField",(e=>{const t=this.getParentScene();if(t.state.displayedFields.indexOf(e)>=0&&this.state.body){const n=t.state.displayedFields.filter((t=>e!==t));this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,f.ZF)(this,t.state.displayedFields),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_toggle_displayed_field)}})),D(this,"clearDisplayedFields",(()=>{this.state.body&&(this.setLogsVizOption({displayedFields:[]}),(0,f.ZF)(this,[]))})),D(this,"getLogsPanel",(()=>{const e=this.getParentScene(),t=e.state.visualizationType,n=o.jh.getAncestor(this,k.Mn),r=o.d0.logs().setTitle(this.getTitle(n.state.logsCount)).setOption("onClickFilterLabel",this.handleLabelFilterClick).setOption("onClickFilterOutLabel",this.handleLabelFilterOutClick).setOption("isFilterLabelActive",this.handleIsFilterLabelActive).setOption("onClickFilterString",this.handleFilterStringClick).setOption("onClickFilterOutString",this.handleFilterOutStringClick).setOption("onClickShowField",this.onClickShowField).setOption("onClickHideField",this.onClickHideField).setOption("displayedFields",e.state.displayedFields).setMenu(new b.GD({investigationOptions:{getLabelName:()=>`Logs: ${(0,L.Mq)(n)}`,type:"logs"}})).setOption("showLogContextToggle",!0).setShowMenuAlways(!0).setOption("enableInfiniteScrolling",!0).setOption("onNewLogsReceived",this.updateVisibleRange).setOption("logRowMenuIconsAfter",[a().createElement(S,{onClick:this.handleShareLogLineClick,key:0})]).setHeaderActions(new w.Z({onChangeVisualizationType:e.setVisualizationType,visualizationType:t})).setOption("sortOrder",this.state.sortOrder).setOption("wrapLogMessage",this.state.wrapLogMessage).setOption("prettifyLogMessage",this.state.prettifyLogMessage).setOption("dedupStrategy",this.state.dedupStrategy);return F.CT?r.setOption("showTime",(0,f.IL)("showTime",!0)).setOption("showControls",!0).setOption("controlsStorageKey",f.vR).setOption("onLogOptionsChange",this.handleLogOptionsChange):r.setOption("showTime",!0),r.build()})),D(this,"handleLogOptionsChange",((e,t)=>{"sortOrder"===e&&(0,P.Q)(t)?(this.setState({sortOrder:t}),this.setLogsVizOption({sortOrder:t})):"wrapLogMessage"===e&&"boolean"==typeof t?(this.setState({wrapLogMessage:t}),this.setLogsVizOption({wrapLogMessage:t})):"prettifyLogMessage"===e&&"boolean"==typeof t?(this.setState({prettifyLogMessage:t}),this.setLogsVizOption({prettifyLogMessage:t})):"dedupStrategy"===e&&(0,P.K)(t)&&((0,f.WO)(this,t),this.setState({dedupStrategy:t}),this.setLogsVizOption({dedupStrategy:t}))})),D(this,"updateVisibleRange",(e=>{var t,n;const r=o.jh.getAncestor(this,k.Mn);r.setState({logsCount:e[0].length}),(null===(n=r.state.$data)||void 0===n||null===(t=n.state.data)||void 0===t?void 0:t.series)&&r.state.$data.setState($(N({},r.state.$data.state),{data:$(N({},r.state.$data.state.data),{series:e})}));o.jh.findByKeyAndType(this,C.b,C._).updateVisibleRange(e)})),D(this,"handleShareLogLineClick",((e,t)=>{if((null==t?void 0:t.rowId)&&this.state.body){const e=this.getParentScene(),n=(0,_.Ki)(t);(0,_.Dk)((0,_.gW)("panelState",{logs:{displayedFields:e.state.displayedFields,id:t.uid}},n))}})),D(this,"handleLabelFilterClick",((e,t,n)=>{this.handleLabelFilter(e,t,n,"toggle")})),D(this,"handleLabelFilterOutClick",((e,t,n)=>{this.handleLabelFilter(e,t,n,"exclude")})),D(this,"handleIsFilterLabelActive",((e,t)=>{const n=(0,m.bY)(v.MB,this),r=(0,m.bY)(v.mB,this),a=(0,m.bY)(v._Y,this),i=(0,m.bY)(v._P,this),s=n=>n&&n.state.filters.findIndex((n=>"="===n.operator&&n.key===e&&n.value===t))>=0;return s(n)||(n=>{if(n){const r=n.state.filters.find((t=>"="===t.operator&&t.key===e));if(r){return(0,m.bu)(r,e).value===t}}return!1})(r)||s(a)||s(i)})),D(this,"handleFilterOutStringClick",(e=>{const t=(0,m.Gk)(this);t&&(t.setState({filters:[...t.state.filters,{key:p.ld.caseSensitive,keyLabel:t.state.filters.length.toString(),operator:p.cK.negativeMatch,value:e}]}),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))})),D(this,"handleFilterStringClick",(e=>{const t=(0,m.Gk)(this);t&&(t.setState({filters:[...t.state.filters,{key:p.ld.caseSensitive,keyLabel:t.state.filters.length.toString(),operator:p.cK.match,value:e}]}),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))})),this.addActivationHandler(this.onActivate.bind(this))}}D(I,"Component",(({model:e})=>{const{body:t,error:n}=e.useState(),r=(0,c.useStyles2)(b.K_);return t?a().createElement("span",{className:r.panelWrapper},!n&&a().createElement(t.Component,{model:t}),n&&a().createElement(E,{error:n,clearFilters:()=>(0,T.rA)(t)})):a().createElement(c.LoadingPlaceholder,{text:"Loading..."})}))},71:(e,t,n)=>{n.d(t,{_:()=>D,b:()=>T});var r,a,i,s=n(5959),o=n.n(s),l=n(7781),c=n(9736),u=n(2007),d=n(1532),p=n(6838),g=n(5553),h=n(2661),f=n(2085),m=n(5700),v=n(9405),b=n(8531),y=n(5719),S=n(20);class w extends c.Bs{}i=function({model:e}){const{component:t,isLoading:n}=(0,b.usePluginComponent)("grafana-adaptivelogs-app/temporary-exemptions/v1"),r=(0,g.bY)(S.MB,e),{filters:a}=r.useState(),i=a.map((({key:e,operator:t,value:n})=>({key:e,operator:t,value:n}))),s=(0,y.U4)(e);return n||!t?null:o().createElement(t,{dataSourceUid:s,streamSelector:i,contextHints:["explorelogs","logvolumepanel","headeraction"]})},(a="Component")in(r=w)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i;var O=n(7352),x=n(4509),E=n(5570),C=n(2165),k=n(1475),P=n(7985),j=n(4351);function F(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){F(e,t,n[t])}))}return e}function _(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const T="logs-volume-panel";class D extends c.Bs{onActivate(){if(!this.state.panel){const e=this.getVizPanel();this.setState({panel:e}),this.updateContainerHeight(e)}const e=(0,g.cR)(this),t=(0,g.ir)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,d.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})}))),this._subs.add(t.subscribeToState(((e,t)=>{(0,d.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})}))),this._subs.add(this.subscribeToEvent(v.Of,(e=>{if(e.key===S.e4){const e=c.jh.findObject(this,(e=>e instanceof f.qV));if(e instanceof f.qV){const e=(0,g.iw)(this);e.setState({filters:e.state.filters})}}})))}getTitle(e,t){var n,r;var a;const i=null!==(a=null===(n=c.jh.getAncestor(this,h.P).state.ds)||void 0===n?void 0:n.maxLines)&&void 0!==a?a:P.by,s=(0,l.getValueFormat)("short"),o=void 0!==e?s(e,0):void 0;if(void 0===e&&void 0!==t&&t<i){var u;const e=s(t,0);return void 0!==e?`Log volume (${e.text}${null===(u=e.suffix)||void 0===u?void 0:u.trim()})`:"Log volume"}return void 0!==o?`Log volume (${o.text}${null===(r=o.suffix)||void 0===r?void 0:r.trim()})`:"Log volume"}getVizPanel(){var e,t;const n=c.jh.getAncestor(this,O.Mn),r=c.d0.timeseries().setTitle(this.getTitle(n.state.totalLogsCount,n.state.logsCount)).setOption("legend",{calcs:["sum"],displayMode:u.LegendDisplayMode.List,showLegend:!0}).setUnit("short").setMenu(new m.GD({investigationOptions:{labelName:"level"}})).setCollapsible(!0).setCollapsed((0,j.Rf)("collapsed")).setHeaderActions(new w({})).setShowMenuAlways(!0).setData((0,k.rS)([(0,P.l)((0,p.m)(this,S.e4,!1),{legendFormat:`{{${S.e4}}}`})]));(0,k.ZC)(r);const a=r.build();return a.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(t)}),this._subs.add(a.subscribeToState(((e,t)=>{e.collapsed!==t.collapsed&&(this.updateContainerHeight(a),(0,j.RN)("collapsed",e.collapsed?"true":void 0))}))),this._subs.add(null===(e=a.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{var t,r,i,s;if((null===(t=e.data)||void 0===t?void 0:t.state)===l.LoadingState.Done){var o,c;if((null===(i=n.state.$data)||void 0===i||null===(r=i.state.data)||void 0===r?void 0:r.state)!==l.LoadingState.Done||(null===(s=e.data.annotations)||void 0===s?void 0:s.length))this.displayVisibleRange();else this.updateVisibleRange(null===(c=n.state.$data)||void 0===c||null===(o=c.state.data)||void 0===o?void 0:o.series);(0,k.C6)(a,e.data.series,this)}}))),this._subs.add(null===(t=n.state.$data)||void 0===t?void 0:t.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===l.LoadingState.Done&&this.updateVisibleRange(e.data.series)}))),this._subs.add(n.subscribeToState(((e,t)=>{e.totalLogsCount===t.totalLogsCount&&void 0===e.logsCount||(this.state.panel?this.state.panel.setState({title:this.getTitle(e.totalLogsCount,e.logsCount)}):this.setState({panel:this.getVizPanel()}))}))),a}updateContainerHeight(e){const t=c.jh.getAncestor(e,c.G1),n=e.state.collapsed?35:Math.max(Math.round(.2*window.innerHeight),100);t.setState({height:n,maxHeight:n,minHeight:n})}updateVisibleRange(e=[]){this.updatedLogSeries=e,this.displayVisibleRange()}displayVisibleRange(){var e,t;const n=this.state.panel;if(!n||!(null===(e=n.state.$data)||void 0===e?void 0:e.state.data)||(null===(t=n.state.$data)||void 0===t?void 0:t.state.data.state)!==l.LoadingState.Done||!this.updatedLogSeries)return;const r=(0,C.z5)(this.updatedLogSeries);this.updatedLogSeries=null,n.state.$data.setState({data:_(L({},n.state.$data.state.data),{annotations:[(0,C.hy)(r.start,r.end)]})})}constructor(e){super(_(L({},e),{key:T})),F(this,"updatedLogSeries",null),F(this,"extendTimeSeriesLegendBus",(e=>{const t=(0,g.iw)(this);this._subs.add(null==t?void 0:t.subscribeToState((()=>{var e,t,n,r;const a=this.state.panel;(null==a||null===(t=a.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.series)&&(0,k.C6)(a,null==a||null===(r=a.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.series,this)}))),e.onToggleSeriesVisibility=(e,t)=>{const n=(0,E.PE)(e,this);this.publishEvent(new v.Of("legend","include",S.e4,e),!0),(0,x.EE)(x.NO.service_details,x.ir.service_details.level_in_logs_volume_clicked,{action:n,level:e})}})),this.addActivationHandler(this.onActivate.bind(this))}}F(D,"Component",(({model:e})=>{const{panel:t}=e.useState();if(!t)return;const n=(0,u.useStyles2)(m.K_);return o().createElement("span",{className:n.panelWrapper},o().createElement(t.Component,{model:t}))}))},7352:(e,t,n)=>{n.d(t,{AA:()=>Je,DS:()=>qe,Mn:()=>rt,rD:()=>Ze,UO:()=>et,nU:()=>tt,dB:()=>nt,TG:()=>Xe,tn:()=>Ye});var r=n(5959),a=n.n(r),i=n(7781),s=n(9736),o=n(2245),l=n(2007),c=n(1532),u=n(7839),d=n(7389),p=n(376),g=n(8502),h=n(5953),f=n(2152),m=n(8531),v=n(2661),b=n(6854),y=n(5553);function S(e,t){const n=["^","$",".","*","+","?","(",")","[","]","{","}","|"];return t||n.push("\\"),e.split("").filter(((e,t,r)=>{const a=r[t+1],i=n.includes(a);return!("\\"===e&&i)})).join("")}var w=n(7478),O=n(708),x=n(9683),E=n(2085),C=n(7796),k=n(6089),P=n(4509),j=n(7985),F=n(5002);function L(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function _(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){L(i,r,a,s,o,"next",e)}function o(e){L(i,r,a,s,o,"throw",e)}s(void 0)}))}}function T(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class D extends s.Bs{setIsOpen(e){this.setState({isOpen:e})}onCopyLink(e,t,r){e?(B(r||n.g.location.href),(0,m.reportInteraction)("grafana_explore_shortened_link_clicked",{isAbsoluteTime:t})):((0,F.Dk)(void 0!==r?`${window.location.protocol}//${window.location.host}${m.config.appSubUrl}${r}`:n.g.location.href),this.state.onCopyLink&&this.state.onCopyLink(e,t,r))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){T(e,t,n[t])}))}return e}({isOpen:!1,lastSelected:N},e))}}T(D,"MenuActions",(({model:e})=>{const t=[{items:[{absTime:!1,getUrl:()=>{},icon:"link",key:"copy-shortened-link",label:"Copy shortened URL",shorten:!0},{absTime:!1,getUrl:()=>{},icon:"link",key:"copy-link",label:"Copy URL",shorten:!1}],key:"normal",label:"Normal URL links"},{items:[{absTime:!0,getUrl:()=>A(void 0!==e.state.getSceneTimeRange?e.state.getSceneTimeRange():s.jh.getTimeRange(e)),icon:"clock-nine",key:"copy-short-link-abs-time",label:"Copy absolute shortened URL",shorten:!0},{absTime:!0,getUrl:()=>A(void 0!==e.state.getSceneTimeRange?e.state.getSceneTimeRange():s.jh.getTimeRange(e)),icon:"clock-nine",key:"copy-link-abs-time",label:"Copy absolute URL",shorten:!1}],key:"timesync",label:"Time-sync URL links (share with time range intact)"}];return a().createElement(l.Menu,null,t.map((t=>a().createElement(l.MenuGroup,{key:t.key,label:t.label},t.items.map((t=>a().createElement(l.Menu.Item,{key:t.key,label:t.label,icon:t.icon,onClick:()=>{const n=t.getUrl();e.onCopyLink(t.shorten,t.absTime,n),e.setState({lastSelected:t})}})))))))})),T(D,"Component",(({model:e})=>{const{isOpen:t,lastSelected:n}=e.useState();return a().createElement(l.ButtonGroup,null,a().createElement(l.ToolbarButton,{tooltip:n.label,icon:n.icon,variant:"canvas",narrow:!0,onClick:()=>{const t=n.getUrl();e.onCopyLink(n.shorten,n.absTime,t)},"aria-label":"Copy shortened URL"},a().createElement("span",null,"Share")),a().createElement(l.Dropdown,{overlay:a().createElement(D.MenuActions,{model:e}),placement:"bottom-end",onVisibleChange:e.setIsOpen.bind(e)},a().createElement(l.ToolbarButton,{narrow:!0,variant:"canvas",isOpen:t,"aria-label":"Open copy link options"})))}));const N={absTime:!1,getUrl:()=>{},icon:"share-alt",key:"copy-link",label:"Copy shortened URL",shorten:!0};function $(e){let t=e.replace(`${window.location.protocol}//${window.location.host}${m.config.appSubUrl}`,"");return t.startsWith("/")?t.substring(1,t.length):t}const I=function(){var e=_((function*(e){const t=(0,m.getAppEvents)();try{return(yield(0,m.getBackendSrv)().post("/api/short-urls",{path:$(e)})).url}catch(e){console.error("Error when creating shortened link: ",e),t.publish({payload:["Error generating shortened link"],type:i.AppEvents.alertError.name})}}));return function(t){return e.apply(this,arguments)}}(),B=function(){var e=_((function*(e){const t=(0,m.getAppEvents)(),n=yield I(e);n?((0,F.Dk)(n),t.publish({payload:["Shortened link copied to clipboard"],type:i.AppEvents.alertSuccess.name})):t.publish({payload:["Error generating shortened link"],type:i.AppEvents.alertError.name})}));return function(t){return e.apply(this,arguments)}}(),A=e=>{const t=(0,i.toUtc)(e.state.value.from),n=(0,i.toUtc)(e.state.value.to),r=m.locationService.getLocation(),a=i.urlUtil.getUrlSearchParams();return a.from=t.toISOString(),a.to=n.toISOString(),i.urlUtil.renderUrl(r.pathname,a)};var M=n(6145),R=n(3571),V=n(6830),W=n(8072);function z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function K(e){var t,n;const{indexScene:r,pattern:a,type:i}=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){z(e,t,n[t])}))}return e}({},e),o=s.jh.getAncestor(r,v.P);if(!o)return void h.v.warn("logs exploration scene not found");(0,w.bN)();const{patterns:l=[]}=o.state,c=l.filter((e=>e.pattern!==a));var u;const d=null!==(u=null===(t=c.filter((e=>"include"===e.type)))||void 0===t?void 0:t.length)&&void 0!==u?u:0;var p;const g=null!==(p=null===(n=c.filter((e=>"exclude"===e.type)))||void 0===n?void 0:n.length)&&void 0!==p?p:0;(0,P.EE)(P.NO.service_details,P.ir.service_details.pattern_selected,{excludePatternsLength:g+("exclude"===i?1:0),includePatternsLength:d+("include"===i?1:0),type:i}),"undo"===i?o.setState({patterns:c}):o.setState({patterns:[...c,{pattern:a,type:i}]})}var H=n(7709);const U=e=>({logsStatsRow:(0,k.css)({margin:`${e.spacing(1.15)}px 0`}),logsStatsRowActive:(0,k.css)({color:e.colors.primary.text,position:"relative"}),logsStatsRowBar:(0,k.css)({background:e.colors.text.disabled,height:e.spacing(.5),overflow:"hidden"}),logsStatsRowCount:(0,k.css)({marginLeft:e.spacing(.75),textAlign:"right"}),logsStatsRowInnerBar:(0,k.css)({background:e.colors.primary.main,height:e.spacing(.5),overflow:"hidden"}),logsStatsRowLabel:(0,k.css)({display:"flex",marginBottom:"1px"}),logsStatsRowPercent:(0,k.css)({marginLeft:e.spacing(.75),textAlign:"right",width:e.spacing(4.5)}),logsStatsRowValue:(0,k.css)({flex:1,overflow:"hidden",textOverflow:"ellipsis"})}),G=({active:e,count:t,proportion:n,value:r})=>{const i=(0,l.useStyles2)(U),s=`${Math.round(100*n)}%`,o={width:s};return a().createElement("div",{className:e?`${i.logsStatsRow} ${i.logsStatsRowActive}`:i.logsStatsRow},a().createElement("div",{className:i.logsStatsRowLabel},a().createElement("div",{className:i.logsStatsRowValue,title:r},r),a().createElement("div",{className:i.logsStatsRowCount},t),a().createElement("div",{className:i.logsStatsRowPercent},s)),a().createElement("div",{className:i.logsStatsRowBar},a().createElement("div",{className:i.logsStatsRowInnerBar,style:o})))};function Q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const J=e=>({logsStats:(0,k.css)({background:"inherit",color:e.colors.text.primary,marginTop:e.spacing(1),maxHeight:"40vh",overflowY:"auto",width:"fit-content",wordBreak:"break-all"}),logsStatsBody:(0,k.css)({padding:"5px 0px"}),logsStatsClose:(0,k.css)({cursor:"pointer"}),logsStatsHeader:(0,k.css)({borderBottom:`1px solid ${e.colors.border.medium}`,display:"flex"}),logsStatsTitle:(0,k.css)({display:"inline-block",flexGrow:1,fontWeight:e.typography.fontWeightMedium,paddingRight:e.spacing(2),textOverflow:"ellipsis",whiteSpace:"nowrap"})}),Y=e=>{const t=(0,l.useStyles2)(J),{stats:n,value:r}=e,i=n.slice(0,10);let s=i.find((e=>e.value===r)),o=n.slice(10);!s&&(s=o.find((e=>e.value===r)),o=o.filter((e=>e.value!==r)));const c=o.reduce(((e,t)=>e+t.count),0),u=i.reduce(((e,t)=>e+t.count),0)+c;let d=[...i];return c>0&&d.push({count:c,proportion:c/u,value:"Other"}),d.sort(((e,t)=>t.count-e.count)),a().createElement("div",{className:t.logsStats},a().createElement("div",{className:t.logsStatsHeader},a().createElement("div",{className:t.logsStatsTitle},"From a sample of ",u," rows found")),a().createElement("div",{className:t.logsStatsBody},d.map((e=>a().createElement(G,q(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Q(e,t,n[t])}))}return e}({key:e.value},e),{active:e.value===r}))))))};var X=n(5719);function Z(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}const ee=({exploration:e,maxLines:t,pattern:n})=>{const o=function(e){const t=[];let n=e.indexOf("<_>");for(;-1!==n;)t.push(n),n=e.indexOf("<_>",n+1);return t}(n),[c,u]=(0,r.useState)(void 0),[d,p]=(0,r.useState)(!1),g=(0,l.useStyles2)(te),h=(0,r.useRef)(null),f=(0,r.useRef)(null),m=function(){var r,a=(r=function*(){(0,P.EE)(P.NO.service_details,P.ir.service_details.pattern_field_clicked);const r=function(e,t,n){let r=1;const a=e.replace(/<_>/g,(()=>`<field_${r++}>`)),i=n.state.filterExpression,s=t.map(((e,t)=>`field_${t+1}`)).join(" ,");return`{${i}} |> \`${e}\` | pattern \`${a}\` | keep ${s} | line_format ""`}(n,o,(0,y.cR)(e)),a=yield(0,X.hJ)(e),l=s.jh.getTimeRange(e).state.value;c&&r===h.current&&l===f.current||(h.current=r,f.current=l,null==a||a.query({app:"",interval:"",intervalMs:0,range:l,requestId:"1",scopedVars:{},startTime:0,targets:[(0,j.l)(r,{maxLines:t})],timezone:""}).forEach((e=>{var n,r;e.state!==i.LoadingState.Done||(null===(n=e.errors)||void 0===n?void 0:n.length)?(e.state===i.LoadingState.Error||(null===(r=e.errors)||void 0===r?void 0:r.length))&&(u(void 0),p(!0)):(u(function(e,t,n){const r=new Map;e.data[0].fields[0].values.toArray().forEach((e=>{Object.keys(e).forEach((t=>{var n,a;r.has(t)||r.set(t,new Map),null===(a=r.get(t))||void 0===a||a.set(e[t],((null===(n=r.get(t))||void 0===n?void 0:n.get(e[t]))||0)+1)}))}));const a=[];for(let e=0;e<=t;e++){var i;const t=[];null===(i=r.get(`field_${e+1}`))||void 0===i||i.forEach(((e,r)=>{t.push({count:e,proportion:e/n,value:r})})),t.sort(((e,t)=>t.count-e.count)),a.push(t)}return a}(e,o.length,t)),p(!1))})))},function(){var e=this,t=arguments;return new Promise((function(n,a){var i=r.apply(e,t);function s(e){Z(i,n,a,s,o,"next",e)}function o(e){Z(i,n,a,s,o,"throw",e)}s(void 0)}))});return function(){return a.apply(this,arguments)}}(),v=(0,r.useMemo)((()=>n.split("<_>")),[n]);return a().createElement("div",null,v.map(((e,t)=>a().createElement("span",{key:t},e,t!==o.length&&a().createElement(l.Toggletip,{onOpen:m,content:a().createElement(a().Fragment,null,c&&c[t].length>0&&a().createElement(Y,{stats:c[t],value:""}),c&&0===c[t].length&&a().createElement("div",null,"No available stats for this field in the current timestamp."),!c&&d&&a().createElement("div",null,"Could not load stats for this pattern."),!c&&!d&&a().createElement("div",{style:{padding:"10px"}},a().createElement(l.Spinner,{size:"xl"})))},a().createElement("span",{className:g.pattern},"<_>"))))))};function te(e){return{pattern:(0,k.css)({"&:hover":{backgroundColor:e.colors.emphasize(e.colors.background.primary,.2)},backgroundColor:e.colors.emphasize(e.colors.background.primary,.1),cursor:"pointer",margin:"0 2px"})}}var ne=n(1475),re=n(2499),ae=n(20);function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class se extends s.Bs{onActivate(){if(this.state.body)return;const e=(0,j.l)(ae.SA);this.replacePatternsInQuery(e);const t=(0,ne.rS)([e]);t.getResultsStream().subscribe((e=>{this.onQueryWithFiltersResult(e)})),this.setState({body:new s.G1({children:[new s.vA({body:void 0,height:0,width:"100%"}),new s.vA({body:s.d0.logs().setHoverHeader(!0).setOption("showLogContextToggle",!0).setOption("showTime",!0).setData(t).build(),height:300,width:"100%"})],direction:"column"})})}replacePatternsInQuery(e){const t={pattern:this.state.pattern,type:"include"},n=(0,re.M)([t]);e.expr=e.expr.replace(ae.sC,n)}removePatternFromFilterExclusion(){const e=s.jh.getAncestor(this,ce);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[],r=n.findIndex((e=>e===this.state.pattern));-1!==r&&(n.splice(r,1),e.setState({patternsNotMatchingFilters:n}))}setWarningMessage(e){const t=this.getNoticeFlexItem(),n=this.getVizFlexItem();return t instanceof s.vA&&t.setState({body:new s.dM({reactNode:e}),height:"auto",isHidden:!1}),n}getNoticeFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[0]}getVizFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[1]}getFlexItemChildren(){var e;return null===(e=this.state.body)||void 0===e?void 0:e.state.children}excludeThisPatternFromFiltering(){const e=s.jh.getAncestor(this,ce);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[];e.setState({patternsNotMatchingFilters:[...n,this.state.pattern]})}static Component({model:e}){const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):null}constructor(e){super(e),ie(this,"clearFilters",(()=>{const e=(0,y.ir)(this),t=(0,y.Gk)(this),n=(0,y.iw)(this);if(e.setState({filters:[]}),n.setState({filters:[]}),t.state.filters.length){t.setState({filters:[]});const e=this.getNoticeFlexItem();null==e||e.setState({isHidden:!0}),this.removePatternFromFilterExclusion()}})),ie(this,"onQueryError",(e=>{if(e.data.state===i.LoadingState.Done&&(0===e.data.series.length||e.data.series.every((e=>0===e.length)))||e.data.state===i.LoadingState.Error){let t;try{t={msg:"onQueryError",pattern:this.state.pattern,request:JSON.stringify(e.data.request),traceIds:JSON.stringify(e.data.traceIds)}}catch(e){t={msg:"Failed to encode context",pattern:this.state.pattern}}h.v.error(new Error("Pattern sample query returns no results"),t),this.setWarningMessage(a().createElement(l.Alert,{severity:"error",title:""},"This pattern returns no logs."));const n=this.getVizFlexItem();n instanceof s.vA&&n.setState({isHidden:!0})}})),ie(this,"onQueryWithFiltersResult",(e=>{const t=(0,j.l)(ae.pT);this.replacePatternsInQuery(t);const n=(0,ne.rS)([t]);if(n.getResultsStream().subscribe(this.onQueryError),e.data.state===i.LoadingState.Done&&(0===e.data.series.length||e.data.series.every((e=>0===e.length)))){const e=this.getNoticeFlexItem(),t=this.getVizFlexItem();if(e instanceof s.vA&&e.setState({body:new s.dM({reactNode:a().createElement(l.Alert,{severity:"warning",title:""},"The logs returned by this pattern do not match the current query filters.",a().createElement(l.Button,{className:V.ZI.button,onClick:()=>this.clearFilters()},"Clear filters"))}),height:"auto",isHidden:!1}),t instanceof s.vA){const e=t.state.body;e instanceof s.Eb&&(null==e||e.setState({$data:n}))}this.excludeThisPatternFromFiltering()}e.data.state===i.LoadingState.Error&&this.onQueryError(e)})),this.addActivationHandler(this.onActivate.bind(this))}}function oe({row:e,tableViz:t}){const{expandedRows:n}=t.useState(),i=null==n?void 0:n.find((t=>t.state.key===e.pattern));return(0,r.useEffect)((()=>{if(!i){const a=(r=e.pattern,new se({key:r,pattern:r}));var n;t.setState({expandedRows:[...null!==(n=t.state.expandedRows)&&void 0!==n?n:[],a]})}var r}),[e,t,i]),i?a().createElement(i.Component,{model:i}):null}const le=[""," K"," Mil"," Bil"," Tri"," Quadr"," Quint"," Sext"," Sept"];class ce extends s.Bs{onActivate(){var e;const t=null===(e=s.jh.getAncestor(this,v.P).state.ds)||void 0===e?void 0:e.maxLines;this.setState({maxLines:t})}buildColumns(e,t,n,r,o){const c=me(n),u=s.jh.getTimeRange(this).state.value;return[{cell:e=>{const t={series:[e.cell.row.original.dataFrame],state:i.LoadingState.Done,timeRange:u},n=new s.Zv({data:t}),r=s.d0.timeseries().setData(n).setHoverHeader(!0).setOption("tooltip",{mode:l.TooltipDisplayMode.None}).setCustomFieldConfig("hideFrom",{legend:!0,tooltip:!0}).setCustomFieldConfig("axisPlacement",l.AxisPlacement.Hidden).setDisplayMode("transparent").build();return a().createElement("div",{className:c.tableTimeSeriesWrap},a().createElement("div",{className:c.tableTimeSeries},a().createElement(r.Component,{model:r})))},header:"",id:"volume-samples"},{cell:e=>{const t=(0,i.scaledUnits)(1e3,le)(e.cell.row.original.sum);var n,r;return a().createElement("div",{className:c.countTextWrap},a().createElement("div",null,null!==(n=t.prefix)&&void 0!==n?n:"",t.text,null!==(r=t.suffix)&&void 0!==r?r:""))},header:"Count",id:"count",sortType:"number"},{cell:t=>a().createElement("div",{className:c.countTextWrap},a().createElement("div",null,(100*t.cell.row.original.sum/e).toFixed(0),"%")),header:"%",id:"percent",sortType:"number"},{cell:e=>a().createElement("div",{className:(0,k.cx)(he(),c.tablePatternTextDefault)},a().createElement(ee,{exploration:(0,X.Ti)(this),pattern:e.cell.row.original.pattern,maxLines:r})),header:"Pattern",id:"pattern"},{cell:e=>{if(null==o?void 0:o.includes(e.cell.row.original.pattern))return;const n=null==t?void 0:t.find((t=>t.pattern===e.cell.row.original.pattern)),r="include"===(null==n?void 0:n.type),i="exclude"===(null==n?void 0:n.type);return a().createElement(H.F,{isExcluded:i,isIncluded:r,onInclude:()=>e.cell.row.original.includeLink(),onExclude:()=>e.cell.row.original.excludeLink(),onClear:()=>e.cell.row.original.undoLink(),buttonFill:"outline"})},disableGrow:!0,header:void 0,id:"include"}]}buildTableData(e,t){const n=s.jh.getAncestor(this,v.P);return e.filter((e=>!t.size||t.has(e.pattern))).map((e=>({dataFrame:e.dataFrame,excludeLink:()=>K({indexScene:n,pattern:e.pattern,type:"exclude"}),includeLink:()=>K({indexScene:n,pattern:e.pattern,type:"include"}),pattern:e.pattern,sum:e.sum,undoLink:()=>K({indexScene:n,pattern:e.pattern,type:"undo"})})))}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}var ue,de,pe;pe=function({model:e}){const t=s.jh.getAncestor(e,v.P),{patterns:n}=t.useState(),r=(0,l.useTheme2)(),i=fe(r),o=s.jh.getAncestor(e,Oe),{legendSyncPatterns:c}=o.useState(),{patternFrames:u,patternsNotMatchingFilters:d}=e.useState(),p=null!=u?u:[],g=s.jh.getAncestor(e,$e);var h;const f=(null!==(h=g.state.patternFrames)&&void 0!==h?h:[]).reduce(((e,t)=>e+t.sum),0),m=e.buildTableData(p,c);var b;const y=e.buildColumns(f,n,r,null!==(b=e.state.maxLines)&&void 0!==b?b:j.by,d);return a().createElement("div",{"data-testid":R.b.patterns.tableWrapper,className:i.tableWrap},a().createElement(l.InteractiveTable,{columns:y,data:m,getRowId:e=>e.pattern,renderExpandedRow:t=>a().createElement(oe,{tableViz:e,row:t})}))},(de="Component")in(ue=ce)?Object.defineProperty(ue,de,{value:pe,enumerable:!0,configurable:!0,writable:!0}):ue[de]=pe;const ge=m.config.theme2,he=()=>(0,k.css)({fontFamily:ge.typography.fontFamilyMonospace,minWidth:"200px",overflow:"hidden",overflowWrap:"break-word"}),fe=e=>({link:(0,k.css)({textDecoration:"underline"}),tableWrap:(0,k.css)({"> div":{height:"calc(100vh - 450px)",minHeight:"470px"},th:{backgroundColor:e.colors.background.canvas,position:"sticky",top:0,zIndex:e.zIndex.navbarFixed}})}),me=e=>({countTextWrap:(0,k.css)({fontSize:e.typography.bodySmall.fontSize,textAlign:"right"}),tablePatternTextDefault:(0,k.css)({fontFamily:e.typography.fontFamilyMonospace,fontSize:e.typography.bodySmall.fontSize,maxWidth:"100%",minWidth:"200px",overflow:"hidden",overflowWrap:"break-word",wordBreak:"break-word"}),tableTimeSeries:(0,k.css)({height:"30px",overflow:"hidden"}),tableTimeSeriesWrap:(0,k.css)({pointerEvents:"none",width:"230px"})});function ve(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function be(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){ve(i,r,a,s,o,"next",e)}function o(e){ve(i,r,a,s,o,"throw",e)}s(void 0)}))}}function ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Se(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const we=m.config.theme2.visualization.palette;class Oe extends s.Bs{onActivate(){this.updateBody(),this._subs.add(s.jh.getAncestor(this,rt).subscribeToState(((e,t)=>{var n,r,a,i,o,l;const u=null==e||null===(a=e.$patternsData)||void 0===a||null===(r=a.state)||void 0===r||null===(n=r.data)||void 0===n?void 0:n.series,d=null==t||null===(l=t.$patternsData)||void 0===l||null===(o=l.state)||void 0===o||null===(i=o.data)||void 0===i?void 0:i.series;if(!(0,c.B)(u,d)){const e=s.jh.getAncestor(this,$e);this.updatePatterns(e.state.patternFrames),e.setState({filteredPatterns:void 0})}}))),this._subs.add(s.jh.getAncestor(this,$e).subscribeToState(((e,t)=>{const n=s.jh.getAncestor(this,$e);e.filteredPatterns&&!(0,c.B)(e.filteredPatterns,t.filteredPatterns)?this.updatePatterns(n.state.filteredPatterns):n.state.patternFilter||this.updatePatterns(n.state.patternFrames)})))}updatePatterns(e=[]){var t=this;return be((function*(){var n;null===(n=t.state.body)||void 0===n||n.forEachChild((n=>{n instanceof s.Eb&&n.setState({$data:t.getTimeseriesDataNode(e)}),n instanceof ce&&n.setState({patternFrames:e})}))}))()}updateBody(){var e=this;return be((function*(){var t,n;const r=s.jh.getAncestor(e,$e).state.patternFrames;(null===(n=s.jh.getAncestor(e,rt).state.$patternsData)||void 0===n||null===(t=n.state.data)||void 0===t?void 0:t.series)&&r?e.setState({body:e.getSingleViewLayout(),legendSyncPatterns:new Set,loading:!1}):h.v.warn("Failed to update PatternsFrameScene body")}))()}extendTimeSeriesLegendBus(e,t){const n=t.onToggleSeriesVisibility;t.onToggleSeriesVisibility=(t,r)=>{var a;null==n||n(t,r);const i=null===(a=e.state.fieldConfig.overrides)||void 0===a?void 0:a[0],s=null==i?void 0:i.matcher.options.names,o=new Set;s&&s.forEach(o.add,o),this.setState({legendSyncPatterns:o})}}getSingleViewLayout(){const e=s.jh.getAncestor(this,$e).state.patternFrames;if(!e)return void h.v.warn("Failed to set getSingleViewLayout");const t=this.getTimeSeries(e);return new s.gF({autoRows:"200px",children:[t,new ce({patternFrames:e})],isLazy:!0,templateColumns:"100%"})}getTimeSeries(e){const t=s.jh.getAncestor(this,v.P),n=s.d0.timeseries().setData(this.getTimeseriesDataNode(e)).setOption("legend",{asTable:!0,displayMode:l.LegendDisplayMode.Table,placement:"right",showLegend:!0,width:200}).setHoverHeader(!0).setUnit("short").setLinks([{onClick:e=>{K({indexScene:t,pattern:e.origin.labels.name,type:"include"})},targetBlank:!1,title:"Include",url:"#"},{onClick:e=>{K({indexScene:t,pattern:e.origin.labels.name,type:"exclude"})},targetBlank:!1,title:"Exclude",url:"#"}]).build();return n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(e,t)}),n}getTimeseriesDataNode(e){const t=s.jh.getTimeRange(this).state.value;return new s.Zv({data:{series:e.map(((e,t)=>{const n=e.dataFrame;return n.fields[1].config.color=function(e){return{fixedColor:we[e],mode:"fixed"}}(t),n.fields[1].name="",n})),state:i.LoadingState.Done,timeRange:t}})}constructor(e){super(Se(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ye(e,t,n[t])}))}return e}({loading:!0},e),{legendSyncPatterns:new Set})),this.addActivationHandler(this.onActivate.bind(this))}}ye(Oe,"Component",(({model:e})=>{var t;const{body:n,loading:r}=e.useState(),i=s.jh.getAncestor(e,rt),{$patternsData:o}=i.useState(),l=null==o||null===(t=o.state.data)||void 0===t?void 0:t.series;return a().createElement("div",{className:xe.container},!r&&l&&l.length>0&&a().createElement(a().Fragment,null,n&&a().createElement(n.Component,{model:n})))}));const xe={container:(0,k.css)({".show-on-hover":{display:"none"},width:"100%"})};var Ee=n(7191);function Ce(){return a().createElement(Ee.R,null,a().createElement("div",null,a().createElement("p",null,a().createElement("strong",null,"Sorry, we could not detect any patterns.")),a().createElement("p",null,"Check back later or reach out to the team in the"," ",a().createElement(l.TextLink,{href:"https://slack.grafana.com/",external:!0},"Grafana Labs community Slack channel")),a().createElement("p",null,"Patterns let you detect similar log lines to include or exclude from your search.")))}function ke(){return a().createElement(Ee.R,null,a().createElement("div",null,a().createElement("p",null,a().createElement("strong",null,"Patterns are only available for the most recent ",Ne," hours of data.")),a().createElement("p",null,"See the"," ",a().createElement(l.TextLink,{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/patterns/",external:!0},"patterns docs")," ","for more info.")))}var Pe=n(9193),je=n(9284);function Fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Le extends s.Bs{onActivate(){const e=s.jh.getAncestor(this,$e);this._subs.add(e.subscribeToState(((e,t)=>{if(e.patternFilter!==t.patternFilter){const e=s.jh.getAncestor(this,$e);e.state.patternFrames&&(0,Pe.E)(e.state.patternFrames.map((e=>e.pattern)),e.state.patternFilter,this.onSearchResult)}}))),this._subs.add(e.subscribeToState(((e,t)=>{e.patternFilter&&!e.filteredPatterns&&e.patternFrames&&!(0,c.B)(e.filteredPatterns,t.filteredPatterns)&&(0,Pe.X)(e.patternFrames.map((e=>e.pattern)),e.patternFilter,this.onSearchResult)})))}setFilteredPatterns(e,t){const n=s.jh.getAncestor(this,$e),r=null!=t?t:n.state.patternFrames;if(r){const t=r.filter((t=>!(!n.state.patternFilter||!(null==r?void 0:r.length))&&e.find((e=>e===t.pattern))));n.setState({filteredPatterns:t})}}setEmptySearch(){s.jh.getAncestor(this,$e).setState({filteredPatterns:void 0})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Fe(e,t,n[t])}))}return e}({},e)),Fe(this,"clearSearch",(()=>{s.jh.getAncestor(this,$e).setState({patternFilter:""})})),Fe(this,"handleSearchChange",(e=>{s.jh.getAncestor(this,$e).setState({patternFilter:e.target.value})})),Fe(this,"onSearchResult",(e=>{const t=s.jh.getAncestor(this,$e);t.state.patternFilter?this.setFilteredPatterns(e[0]):t.state.filteredPatterns&&!t.state.patternFilter&&this.setEmptySearch()})),this.addActivationHandler(this.onActivate.bind(this))}}Fe(Le,"Component",(function({model:e}){const t=s.jh.getAncestor(e,$e),{patternFilter:n}=t.useState();return a().createElement(l.Field,{className:_e.field},a().createElement(je.D,{onChange:e.handleSearchChange,onClear:e.clearSearch,value:n,placeholder:"Search patterns"}))}));const _e={field:(0,k.css)({label:"field",marginBottom:0}),icon:(0,k.css)({cursor:"pointer"})};var Te=n(5659);function De(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Ne=3;class $e extends s.Bs{onActivate(){var e,t;const n=s.jh.getAncestor(this,rt);var r;(this.setBody(),null===(e=n.state.$patternsData)||void 0===e?void 0:e.state)&&this.onDataChange(null===(r=n.state.$patternsData)||void 0===r?void 0:r.state);this._subs.add(null===(t=n.state.$patternsData)||void 0===t?void 0:t.subscribeToState(this.onDataChange))}setBody(){this.setState({body:new s.G1({children:[new s.vA({body:new Le,ySizing:"content"}),new s.vA({body:new Oe})],direction:"column"})})}updatePatternFrames(e){if(!e)return;const t=this.dataFrameToPatternFrame(e);this.setState({patternFrames:t})}dataFrameToPatternFrame(e){const t=s.jh.getAncestor(this,rt),n=s.jh.getAncestor(t,v.P).state.patterns;return e.map((e=>{var t,r;const a=null==n?void 0:n.find((t=>t.pattern===e.name)),i=null===(r=e.meta)||void 0===r||null===(t=r.custom)||void 0===t?void 0:t.sum;var s;return{dataFrame:e,pattern:null!==(s=e.name)&&void 0!==s?s:"",status:null==a?void 0:a.type,sum:i}}))}constructor(e){var t;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){De(e,t,n[t])}))}return e}({$variables:null!==(t=e.$variables)&&void 0!==t?t:new s.Pj({variables:[new s.yP({defaultToAll:!0,includeAll:!0,name:ae.Jg})]}),loading:!0,patternFilter:""},e)),De(this,"onDataChange",((e,t)=>{var n,r,a,s,o;const l=null===(n=e.data)||void 0===n?void 0:n.series,u=null==t||null===(r=t.data)||void 0===r?void 0:r.series;(null===(a=e.data)||void 0===a?void 0:a.state)===i.LoadingState.Done?(this.setState({error:!1,loading:!1}),(0,c.B)(l,u)||this.updatePatternFrames(l)):(null===(s=e.data)||void 0===s?void 0:s.state)===i.LoadingState.Loading?this.setState({error:!1,loading:!0}):(null===(o=e.data)||void 0===o?void 0:o.state)===i.LoadingState.Error&&this.setState({error:!0,loading:!1})})),this.addActivationHandler(this.onActivate.bind(this))}}function Ie(e){return{container:(0,k.css)({display:"flex",flexDirection:"column",flexGrow:1,minHeight:"100%"}),content:(0,k.css)({display:"flex",flexGrow:1,paddingTop:e.spacing(0)}),controls:(0,k.css)({alignItems:"top",display:"flex",flexGrow:0,gap:e.spacing(2)}),controlsLeft:(0,k.css)({display:"flex",flexDirection:"column",justifyContent:"flex-left",justifyItems:"left",width:"100%"}),controlsRight:(0,k.css)({display:"flex",flexGrow:0,justifyContent:"flex-end"}),patternMissingText:(0,k.css)({padding:e.spacing(2)})}}De($e,"Component",(({model:e})=>{const{blockingMessage:t,body:n,error:r,loading:o,patternFrames:c}=e.useState(),{value:u}=s.jh.getTimeRange(e).useState(),d=(0,l.useStyles2)(Ie),p=(0,i.dateTime)().diff(u.to,"hours")>=Ne;return a().createElement("div",{className:d.container},a().createElement(Te.O,{blockingMessage:t,isLoading:o},!o&&r&&a().createElement("div",{className:d.patternMissingText},a().createElement(l.Text,{textAlignment:"center",color:"primary"},a().createElement("p",null,"There are no pattern matches."),a().createElement("p",null,"Pattern matching has not been configured."),a().createElement("p",null,"Patterns let you detect similar log lines and add or exclude them from your search."),a().createElement("p",null,"To see them in action, add the following to your Loki configuration"),a().createElement("p",null,a().createElement("code",null,"--pattern-ingester.enabled=true")))),!r&&!o&&0===(null==c?void 0:c.length)&&p&&a().createElement(ke,null),!r&&!o&&0===(null==c?void 0:c.length)&&!p&&a().createElement(Ce,null),!r&&!o&&c&&c.length>0&&a().createElement("div",{className:d.content},n&&a().createElement(n.Component,{model:n}))))}));var Be=n(7902),Ae=n(71);const Me=[{displayName:u.ob.logs,getScene:()=>new s.G1({children:[new s.vA({body:new Ae._({})}),new s.vA({body:new Be.i({}),height:"calc(100vh - 500px)",minHeight:"470px"})],direction:"column"}),testId:R.b.exploreServiceDetails.tabLogs,value:u.G3.logs},{displayName:u.ob.labels,getScene:()=>new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:M.yV.Crosshair})],children:[new s.vA({body:new W.O({})})]}),testId:R.b.exploreServiceDetails.tabLabels,value:u.G3.labels},{displayName:u.ob.fields,getScene:e=>{return t=e,new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:M.yV.Crosshair})],children:[new s.vA({body:new V.J6({changeFieldCount:t})})]});var t},testId:R.b.exploreServiceDetails.tabFields,value:u.G3.fields},{displayName:u.ob.patterns,getScene:()=>new s.G1({children:[new s.vA({body:new $e({})})]}),testId:R.b.exploreServiceDetails.tabPatterns,value:u.G3.patterns}],Re=[{displayName:"Label",getScene:e=>function(e){return new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:M.yV.Crosshair})],children:[new s.vA({body:new W.O({value:e})})]})}(e),testId:R.b.exploreServiceDetails.tabLabels,value:u._J.label},{displayName:"Field",getScene:e=>function(e){return new s.G1({$behaviors:[new s.Gg.K2({key:"sync",sync:M.yV.Crosshair})],children:[new s.vA({body:new V.J6({value:e})})]})}(e),testId:R.b.exploreServiceDetails.tabFields,value:u._J.field}];function Ve(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}class We extends s.Bs{onActivate(){const e=s.jh.getAncestor(this,v.P).state.ds;void 0!==(null==e?void 0:e.maxLines)&&this.setState({maxLines:e.maxLines}),this.state.shareButtonScene||this.setState({shareButtonScene:new D({})})}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(We,"Component",(({model:e})=>{const t=(0,l.useStyles2)(Ke);let n=(0,x.FT)(),r=!1;if(!Object.values(u.G3).includes(n)){const e=(0,x.er)();r=!0,e===u._J.field&&(n=u.G3.fields),e===u._J.label&&(n=u.G3.labels)}const o=s.jh.getAncestor(e,rt),c=o.useState(),{$data:d,loading:p,logsCount:g,totalLogsCount:h}=c,f=Ve(c,["$data","loading","logsCount","totalLogsCount"]),{maxLines:m}=e.useState(),v=f.loadingStates;return a().createElement(l.Box,{paddingY:0},a().createElement("div",{className:t.actions},a().createElement(l.Stack,{gap:1},e.state.shareButtonScene&&a().createElement(e.state.shareButtonScene.Component,{model:e.state.shareButtonScene}))),a().createElement(l.TabsBar,null,Me.map(((e,t)=>a().createElement(l.Tab,{"data-testid":e.testId,key:t,label:e.displayName,active:n===e.value,counter:v[e.displayName]?void 0:ze(e,f),suffix:e.displayName===u.ob.logs?({className:e})=>function(e,t,n,r){const s=(0,l.useStyles2)(He),o=(0,i.getValueFormat)("short");if(void 0===t&&void 0!==n&&n<r){var c;const t=o(n,0);return a().createElement("span",{className:(0,k.cx)(e,s.logsCountStyles)},t.text,null===(c=t.suffix)||void 0===c?void 0:c.trim())}if(void 0!==t){var u;const n=o(t,0);return a().createElement("span",{className:(0,k.cx)(e,s.logsCountStyles)},n.text,null===(u=n.suffix)||void 0===u?void 0:u.trim())}return a().createElement("span",{className:(0,k.cx)(e,s.emptyCountStyles)})}(e,h,g,null!=m?m:j.by):void 0,icon:v[e.displayName]?"spinner":void 0,href:(0,w.rs)(e.value,o),onChangeTab:()=>{(e.value&&e.value!==n||r)&&(0,P.EE)(P.NO.service_details,P.ir.service_details.action_view_changed,{newActionView:e.value,previousActionView:n})}})))))}));const ze=(e,t)=>{switch(e.value){case"fields":return t.fieldsCount;case"patterns":return t.patternsCount;case"labels":return t.labelsCount;default:return}};function Ke(e){return{actions:(0,k.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,zIndex:2},display:"flex",justifyContent:"flex-end"})}}function He(e){return{emptyCountStyles:(0,k.css)({display:"inline-block",fontSize:e.typography.bodySmall.fontSize,marginLeft:e.spacing(1),minWidth:"1em",padding:e.spacing(.25,1)}),logsCountStyles:(0,k.css)({backgroundColor:e.colors.action.hover,borderRadius:e.spacing(3),color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,fontWeight:e.typography.fontWeightMedium,label:"counter",marginLeft:e.spacing(1),padding:e.spacing(.25,1)})}}function Ue(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ge(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ue(e,t,n[t])}))}return e}function Qe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const qe="logsPanelQuery",Je="logsCountQuery";function Ye(e){return null==e?void 0:e.series.find((e=>e.refId===qe))}function Xe(e){var t,n,r;return null===(r=s.jh.getAncestor(e,rt).state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]}function Ze(e){var t;const n=s.jh.getAncestor(e,rt);return et(null===(t=n.state.$detectedFieldsData)||void 0===t?void 0:t.state)}const et=e=>{var t,n;return null==e||null===(n=e.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]},tt=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[0]},nt=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[2]};class rt extends s.Bs{setSubscribeToLabelsVariable(){const e=(0,y.cR)(this);0!==e.state.filters.length?this._subs.add(e.subscribeToState(((e,t)=>{0===e.filters.length&&this.redirectToStart();let{breakdownLabel:n,labelName:r,labelValue:a}=(0,x.W6)();r===ae.ky&&(r=ae.OX);const i=s.jh.getAncestor(this,v.P),o=i.state.routeMatch;if(e.filters.some((e=>e.key===r&&(0,O.BG)(e.operator)&&(0,d.uu)(e.value)===a))){if(!(0,c.B)(e.filters,t.filters)){var l,u,p,g;null===(l=this.state.$patternsData)||void 0===l||l.runQueries(),null===(u=this.state.$detectedLabelsData)||void 0===u||u.runQueries(),null===(p=this.state.$detectedFieldsData)||void 0===p||p.runQueries(),null===(g=this.state.$logsCount)||void 0===g||g.runQueries()}}else{const t=e.filters.find((e=>(0,O.BG)(e.operator)&&e.value!==ae.ZO));if(t){const e=(0,ae.zE)(t.value)?(0,d.uu)((0,ae.Dx)(t.value)):(0,d.uu)(t.value);var h,f,m;i.setState({routeMatch:Qe(Ge({},o),{isExact:null===(h=null==o?void 0:o.isExact)||void 0===h||h,params:Qe(Ge({},null==o?void 0:o.params),{labelName:t.key===ae.OX?ae.ky:t.key,labelValue:e.split("|")[0]}),path:null!==(f=null==o?void 0:o.path)&&void 0!==f?f:"",url:null!==(m=null==o?void 0:o.url)&&void 0!==m?m:""})}),this.resetTabCount(),n?(0,w.fg)((0,x.er)(),n,this):(0,w.Vt)((0,x.FT)(),this)}else this.redirectToStart()}}))):this.redirectToStart()}redirectToStart(){this.setState({$data:void 0,$detectedFieldsData:void 0,$detectedLabelsData:void 0,$logsCount:void 0,$patternsData:void 0,body:void 0,fieldsCount:void 0,labelsCount:void 0,logsCount:void 0,patternsCount:void 0,totalLogsCount:void 0}),(0,f.JO)().setServiceSceneState(this.state),this._subs.unsubscribe(),(0,w.Ns)()}showVariables(){s.jh.findByKeyAndType(this,E.kz,E.qV).setState({visible:!0}),(0,y.YS)(this).setState({hide:o.zL.dontHide})}getMetadata(){const e=(0,f.JO)().getServiceSceneState();e&&this.setState(Ge({},e))}onActivate(){s.jh.findByKeyAndType(this,v.y,C.H).setState({hidden:!0}),this.showVariables(),this.getMetadata(),this.resetBodyAndData(),this.setBreakdownView(),this.runQueries(),this._subs.add(this.subscribeToPatternsQuery()),this._subs.add(this.subscribeToDetectedLabelsQuery()),this._subs.add(this.subscribeToDetectedFieldsQuery((0,x.FT)()!==u.G3.fields)),this._subs.add(this.subscribeToLogsQuery()),this._subs.add(this.subscribeToLogsCountQuery()),this.setSubscribeToLabelsVariable(),this._subs.add(this.subscribeToFieldsVariable()),this._subs.add(this.subscribeToMetadataVariable()),this._subs.add(this.subscribeToLevelsVariableChangedEvent()),this._subs.add(this.subscribeToLevelsVariableFiltersState()),this._subs.add(this.subscribeToDataSourceVariable()),this._subs.add(this.subscribeToPatternsVariable()),this._subs.add(this.subscribeToLineFiltersVariable()),this._subs.add(this.subscribeToTimeRange()),function(e){const t=i.urlUtil.getUrlSearchParams(),n=t["var-lineFilter"];if(!Array.isArray(n)||!n.length)return;const r=n[0];if("string"!=typeof r||!r)return;const a=s.jh.getAncestor(e,v.P),o=(0,y.Gk)(e),l=null==r?void 0:r.match(/\|=.`(.+?)`/);var c,u;l&&2===l.length&&(null===(u=a.state.body)||void 0===u||null===(c=u.state.lineFilterRenderer)||void 0===c||c.addActivationHandler((()=>{o.setState({filters:[{key:b.ld.caseSensitive,keyLabel:"0",operator:b.cK.match,value:S(l[1],!0)}]})})));const d=null==r?void 0:r.match(/`\(\?i\)(.+)`/);var p,g;d&&2===d.length&&(null===(g=a.state.body)||void 0===g||null===(p=g.state.lineFilterRenderer)||void 0===p||p.addActivationHandler((()=>{o.updateFilters([{key:b.ld.caseInsensitive,keyLabel:"0",operator:b.cK.match,value:S(d[1],!1)}])})));const h=m.locationService.getLocation();delete t["var-lineFilter"],m.locationService.replace(i.urlUtil.renderUrl(h.pathname,t))}(this)}subscribeToPatternsVariable(){return(0,y.Ku)(this).subscribeToState(((e,t)=>{var n,r;e.value!==t.value&&(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())}))}subscribeToLineFiltersVariable(){return(0,y.Gk)(this).subscribeToEvent(s.oh,(()=>{var e,t;null===(e=this.state.$logsCount)||void 0===e||e.runQueries(),null===(t=this.state.$detectedFieldsData)||void 0===t||t.runQueries()}))}subscribeToDataSourceVariable(){return(0,y.S9)(this).subscribeToState((()=>{this.redirectToStart()}))}resetTabCount(){this.setState({fieldsCount:void 0,labelsCount:void 0,patternsCount:void 0}),(0,f.JO)().setServiceSceneState(this.state)}subscribeToFieldsVariable(){return(0,y.ir)(this).subscribeToState(((e,t)=>{var n,r;(0,c.B)(e.filters,t.filters)||(this.removeInactiveJsonParserProps(e,t),null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())}))}removeInactiveJsonParserProps(e,t){const n=(0,y.U2)(this);if(e.filters.length||n.state.filters.length){if(e.filters.length<t.filters.length){t.filters.filter((t=>!e.filters.find((e=>e.key===t.key)))).length&&(0,g.AY)(this)}}else(0,p.sg)(this)}subscribeToMetadataVariable(){return(0,y.oY)(this).subscribeToState(((e,t)=>{var n,r;(0,c.B)(e.filters,t.filters)||(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())}))}subscribeToLevelsVariableChangedEvent(){return(0,y.iw)(this).subscribeToEvent(s.oh,(()=>{var e;null===(e=this.state.$detectedFieldsData)||void 0===e||e.runQueries()}))}subscribeToLevelsVariableFiltersState(){return(0,y.iw)(this).subscribeToState(((e,t)=>{var n;(0,c.B)(e.filters,t.filters)||(null===(n=this.state.$logsCount)||void 0===n||n.runQueries())}))}runQueries(){const e=(0,x.FT)(),t=(0,x.er)();var n,r,a,i;e!==u.G3.patterns&&void 0!==this.state.patternsCount||(null===(n=this.state.$patternsData)||void 0===n||n.runQueries());e!==u.G3.labels&&t!==u._J.label&&void 0!==this.state.labelsCount||(null===(r=this.state.$detectedLabelsData)||void 0===r||r.runQueries());e!==u.G3.fields&&t!==u._J.field&&void 0!==this.state.fieldsCount||(null===(a=this.state.$detectedFieldsData)||void 0===a||a.runQueries());void 0===this.state.logsCount&&(null===(i=this.state.$logsCount)||void 0===i||i.runQueries())}subscribeToPatternsQuery(){var e;return null===(e=this.state.$patternsData)||void 0===e?void 0:e.subscribeToState((e=>{var t;if(this.updateLoadingState(e,u.ob.patterns),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data.series;void 0!==(null==t?void 0:t.length)&&(this.setState({patternsCount:t.length}),(0,f.JO)().setPatternsCount(t.length))}}))}subscribeToDetectedLabelsQuery(){var e;return null===(e=this.state.$detectedLabelsData)||void 0===e?void 0:e.subscribeToState((e=>{var t;if(this.updateLoadingState(e,u.ob.labels),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data,n=t.series[0].fields;if(void 0!==t.series.length&&void 0!==n.length){const e=t.series[0].fields.filter((e=>ae.e4!==e.name));this.setState({labelsCount:e.length+1}),(0,f.JO)().setLabelsCount(n.length)}}}))}updateLoadingState(e,t){var n;const r=this.state.loadingStates;r[t]=(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Loading;const a=Object.values(r).some((e=>e));this.setState({loading:a,loadingStates:r})}subscribeToLogsQuery(){var e;return null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(((e,t)=>{var n,r;if(this.updateLoadingState(e,u.ob.logs),(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Streaming){var a,s;const t=null!==(s=null===(a=e.data.series[0])||void 0===a?void 0:a.length)&&void 0!==s?s:0;t!==this.state.logsCount&&this.setState({logsCount:t})}}))}subscribeToLogsCountQuery(){var e;return null===(e=this.state.$logsCount)||void 0===e?void 0:e.subscribeToState((e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){var n,r,a,s;const t=null===(s=e.data.series[0])||void 0===s||null===(a=s.fields)||void 0===a||null===(r=a[1])||void 0===r||null===(n=r.values)||void 0===n?void 0:n[0];this.setState({totalLogsCount:t})}}))}subscribeToDetectedFieldsQuery(e){var t;return null===(t=this.state.$detectedFieldsData)||void 0===t?void 0:t.subscribeToState((t=>{var n;this.updateLoadingState(t,u.ob.fields);const r=t.data,a=null==r?void 0:r.series[0];e&&(null===(n=t.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&void 0!==a&&a.length!==this.state.fieldsCount&&(this.setState({fieldsCount:a.length}),(0,f.JO)().setFieldsCount(a.length))}))}subscribeToTimeRange(){return s.jh.getTimeRange(this).subscribeToState((()=>{var e,t,n,r;null===(e=this.state.$patternsData)||void 0===e||e.runQueries(),null===(t=this.state.$detectedLabelsData)||void 0===t||t.runQueries(),null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries()}))}resetBodyAndData(){let e={};this.state.$data||(e.$data=lt()),this.state.$patternsData||(e.$patternsData=it()),this.state.$detectedLabelsData||(e.$detectedLabelsData=st()),this.state.$detectedFieldsData||(e.$detectedFieldsData=ot()),this.state.$logsCount||(e.$logsCount=ct()),this.state.body||(e.body=at()),Object.keys(e).length&&this.setState(e)}setBreakdownView(){const{body:e}=this.state,t=(0,x.FT)(),n=Me.find((e=>e.value===t));if(!e){const e=new Error("body is not defined in setBreakdownView!");throw h.v.error(e,{msg:"ServiceScene setBreakdownView error"}),e}if(n)e.setState({children:[...e.state.children.slice(0,1),n.getScene((e=>{"fields"===n.value&&this.setState({fieldsCount:e})}))]});else{const t=(0,x.er)(),n=Re.find((e=>e.value===t));n&&this.state.drillDownLabel?e.setState({children:[...e.state.children.slice(0,1),n.getScene(this.state.drillDownLabel)]}):h.v.error(new Error("not setting breakdown view"),{msg:"setBreakdownView error"})}}constructor(e){var t;super(Ge({$data:lt(),$detectedFieldsData:ot(),$detectedLabelsData:st(),$logsCount:ct(),$patternsData:it(),body:null!==(t=e.body)&&void 0!==t?t:at(),loading:!0,loadingStates:{[u.ob.patterns]:!1,[u.ob.labels]:!1,[u.ob.fields]:!1,[u.ob.logs]:!1}},e)),Ue(this,"_variableDependency",new s.Sh(this,{variableNames:[ae.EY,ae.MB,ae.mB,ae.uw,ae._Y]})),this.addActivationHandler(this.onActivate.bind(this))}}function at(){return new s.G1({children:[new s.vA({body:new We({}),ySizing:"content"})],direction:"column"})}function it(){return(0,ne.FH)([(0,j.BM)(`{${ae.S1}}`,"patterns",{refId:"patterns"})])}function st(){return(0,ne.FH)([(0,j.BM)(`{${ae.S1}}`,"detected_labels",{refId:"detectedLabels"})])}function ot(){return(0,ne.FH)([(0,j.BM)(ae.Do,"detected_fields",{refId:"detectedFields"})])}function lt(){return(0,ne.rS)([(0,j.l)(ae.SA,{refId:qe})])}function ct(){const e=(0,ne.rS)([(0,j.l)(`sum(count_over_time(${ae.SA}[$__auto]))`,{queryType:"instant",refId:Je})],{runQueriesMode:"manual"});if(e instanceof s.dt)return e;const t=new Error("log count query provider is not query runner!");throw h.v.error(t,{msg:"getLogCountQueryRunner: invalid return type"}),t}Ue(rt,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})}))},577:(e,t,n)=>{n.d(t,{p:()=>g});var r,a,i,s=n(5959),o=n.n(s),l=n(6089),c=n(9736),u=n(2007),d=n(696),p=n(4351);class g extends c.Bs{setHover(e){this.setState({hover:e})}onClick(e){e?(0,d.wy)(this.state.labelName,this.state.labelValue,this):(0,d._J)(this.state.labelName,this.state.labelValue,this)}}i=({model:e})=>{const{ds:t,hover:n,labelName:r,labelValue:a}=e.useState(),i=(0,p.eT)(t,r).includes(a),s=(0,u.useStyles2)((e=>({wrapper:(0,l.css)({alignSelf:"center",display:"flex",flexDirection:"column",justifyContent:"center"})}))),c=i?`Remove  ${a} from favorites`:`Add ${a} to favorites`;return o().createElement("span",{className:s.wrapper},o().createElement(u.ToolbarButton,{onMouseOver:()=>{e.setHover(!0)},onMouseOut:()=>{e.setHover(!1)},icon:o().createElement(u.Icon,{name:i?"favorite":"star",size:"lg",type:i?"mono":"default"}),color:i?"rgb(235, 123, 24)":"#ccc",onClick:()=>e.onClick(i),name:"star","aria-label":c,tooltip:c}))},(a="Component")in(r=g)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},173:(e,t,n)=>{n.d(t,{X:()=>ue,y:()=>ge});var r=n(5959),a=n.n(r),i=n(6089),s=n(3241),o=n(7781),l=n(8531),c=n(9736),u=n(2245),d=n(2007),p=n(1532),g=n(4702),h=n(7478),f=n(5719),m=n(5553),v=n(2661),b=n(7796),y=n(9731),S=n(7243),w=n(4509),O=n(6854),x=n(3571),E=n(20),C=n(9405);function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){k(e,t,n[t])}))}return e}function j(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class F extends c.Bs{onActivate(){this.setState(P({},this.isSelected())),this._subs.add((0,m.cR)(this).subscribeToState((()=>{const e=this.isSelected();this.state.included!==e.included&&this.setState(P({},e))})))}getFilter(){return{name:this.state.name,value:this.state.value}}constructor(e){super(j(P({},e),{included:null})),k(this,"isSelected",(()=>{const e=(0,m.cR)(this).state.filters.find((e=>{const t=(0,m.z2)(E.MB,e);return e.key===this.state.name&&t.value===this.state.value}));return e?{included:e.operator===O.w7.Equal}:{included:!1}})),k(this,"onClick",(e=>{const t=this.getFilter();(0,C.Qt)(t.name,t.value,e,this,E.MB);const n=(0,m.cR)(this);(0,w.EE)(w.NO.service_selection,w.ir.service_selection.add_to_filters,{action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0,filterType:"index-filters",key:t.name}),this.setState(P({},this.isSelected()))})),this.addActivationHandler(this.onActivate.bind(this))}}k(F,"Component",(({model:e})=>{const{included:t,value:n}=e.useState(),r=(0,d.useStyles2)(L);return a().createElement("span",{className:r.wrapper},a().createElement(d.Button,{tooltip:!0===t?`Remove ${n} from filters`:`Add ${n} to filters`,variant:"secondary",fill:"outline",size:"sm","aria-selected":!0===t,className:r.includeButton,onClick:()=>!0===t?e.onClick("clear"):e.onClick("include"),"data-testid":x.b.exploreServiceDetails.buttonFilterInclude},t?"Remove":"Include"))}));const L=()=>({container:(0,i.css)({display:"flex",justifyContent:"center"}),includeButton:(0,i.css)({borderRadius:0}),wrapper:(0,i.css)({alignSelf:"center",display:"flex",flexDirection:"column",justifyContent:"center"})});var _=n(7191);const T=()=>a().createElement(_.R,null,a().createElement("p",null,"Log volume has not been configured."),a().createElement("p",null,a().createElement(d.TextLink,{href:"https://grafana.com/docs/loki/latest/reference/api/#query-log-volume",external:!0},"Instructions to enable volume in the Loki config:")),a().createElement(d.Text,{textAlignment:"left"},a().createElement("pre",null,a().createElement("code",null,"limits_config:",a().createElement("br",null),"  volume_enabled: true"))));var D=n(577);const N=()=>a().createElement(_.R,null,a().createElement("p",null,"No service matched your search.")),$=e=>a().createElement(_.R,null,a().createElement("p",null,"No logs found in ",a().createElement("strong",null,e.labelName),".",a().createElement("br",null),"Please adjust time range or select another label."));var I=n(696);function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class A extends c.Bs{onActivate(){const e=(0,m.cR)(this);this.setState({hidden:e.state.filters.length>0}),e.subscribeToState((e=>{this.setState({hidden:e.filters.length>0})}))}constructor(e){super(e),B(this,"getLink",(()=>{if(this.state.labelValue)return R(this.state.labelName,this.state.labelValue,this)})),B(this,"onClick",(()=>{M(this.state.labelName,this.state.labelValue,this)})),this.addActivationHandler(this.onActivate.bind(this))}}function M(e,t,n){(0,w.EE)(w.NO.service_selection,w.ir.service_selection.service_selected,{label:e,value:t}),(0,I._J)(e,t,n)}function R(e,t,n){var r;const a=(0,m.cR)(n),i=[...a.state.filters.filter((n=>!(n.key===e&&n.value===t))),{key:e,operator:O.w7.Equal,value:t}];e===E.OX&&(e=E.ky);const s=a.clone({filters:i});return(0,h.k9)(e,t,null===(r=s.urlSync)||void 0===r?void 0:r.getUrlState())}function V(e){return{button:(0,i.css)({alignSelf:"center"})}}B(A,"Component",(({model:e})=>{const t=(0,d.useStyles2)(V);(0,m.cR)(e).useState();const{hidden:n}=e.useState();if(n)return null;const r=e.getLink();return a().createElement(d.LinkButton,{"data-testid":x.b.index.selectServiceButton,tooltip:`View logs for ${e.state.labelValue}`,className:t.button,variant:"primary",fill:"outline",size:"sm",disabled:!r,href:e.getLink(),onClick:e.onClick},"Show logs")}));var W=n(4351);function z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class K extends c.Bs{}function H(e){return{icon:(0,i.css)({color:e.colors.text.disabled,marginLeft:e.spacing.x1}),searchFieldPlaceholderText:(0,i.css)({alignItems:"center",color:e.colors.text.disabled,display:"flex",flex:"1 0 auto",fontSize:e.typography.bodySmall.fontSize,textWrapMode:"nowrap"}),searchPageCountWrap:(0,i.css)({alignItems:"center",display:"flex"}),select:(0,i.css)({marginLeft:e.spacing(1),marginRight:e.spacing(1),maxWidth:"65px"})}}z(K,"PageCount",(({model:e,totalCount:t})=>{const n=(0,d.useStyles2)(H),i=c.jh.getAncestor(e,ge),{countPerPage:s}=i.useState(),o=function(e){const t=20,n=60,r=Math.ceil(e/t)*t,a=[];for(let i=t;i<=n&&i<=r;i+=t){let n=i.toString();i<t?n=i.toString():i>e&&(n=e.toString()),a.push({label:n,value:i.toString()})}return a}(t);return(0,r.useEffect)((()=>{var e,t;const n=null!==(t=null===(e=o[o.length-1])||void 0===e?void 0:e.value)&&void 0!==t?t:s.toString();s.toString()>n&&i.setState({countPerPage:parseInt(n,10)})}),[s,o,i]),a().createElement("span",{className:n.searchPageCountWrap},a().createElement("span",{className:n.searchFieldPlaceholderText},"Showing"," ",a().createElement(d.Select,{className:n.select,onChange:e=>{if(e.value){const t=parseInt(e.value,10);i.setState({countPerPage:t,currentPage:1}),i.updateBody(),(0,W.uF)(t)}},options:o,value:s.toString()})," ","of ",t," ",a().createElement(d.IconButton,{className:n.icon,"aria-label":"Count info",name:"info-circle",tooltip:`${t} labels have values for the selected time range. Total label count may differ`})))})),z(K,"Component",(({model:e,totalCount:t})=>{const n=c.jh.getAncestor(e,ge),{countPerPage:r,currentPage:s}=n.useState(),o=(0,d.useStyles2)((e=>({pagination:(0,i.css)({float:"none"}),paginationWrap:(0,i.css)({[e.breakpoints.up("lg")]:{display:"none"},[e.breakpoints.down("lg")]:{display:"flex",flex:"1 0 auto",justifyContent:"flex-end"}}),paginationWrapMd:(0,i.css)({[e.breakpoints.down("lg")]:{display:"none"},[e.breakpoints.up("lg")]:{display:"flex",flex:"1 0 auto",justifyContent:"flex-end"}})})));return t>r?a().createElement(a().Fragment,null,a().createElement("span",{className:o.paginationWrapMd},a().createElement(d.Pagination,{className:o.pagination,currentPage:s,numberOfPages:Math.ceil(t/r),onNavigate:e=>{n.setState({currentPage:e}),n.updateBody()}})),a().createElement("span",{className:o.paginationWrap},a().createElement(d.Pagination,{showSmallVersion:!0,className:o.pagination,currentPage:s,numberOfPages:Math.ceil(t/r),onNavigate:e=>{n.setState({currentPage:e}),n.updateBody()}}))):null}));var U=n(1475),G=n(7985),Q=n(5002);function q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class Y extends c.Bs{}q(Y,"Component",(({model:e})=>{const t=c.jh.getAncestor(e,ge),n=c.jh.getAncestor(e,ne),{showPopover:r,tabOptions:i}=n.useState(),s=(0,d.useStyles2)(X),o=i.map((e=>J(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){q(e,t,n[t])}))}return e}({},e),{icon:e.saved?"save":void 0,label:`${e.label}`})));return a().createElement(d.Stack,{direction:"column",gap:0,role:"tooltip"},a().createElement("div",{className:s.card.body},a().createElement(d.Select,{menuShouldPortal:!1,width:50,onBlur:()=>{n.toggleShowPopover()},autoFocus:!0,isOpen:r,placeholder:"Search labels",options:o,isSearchable:!0,openMenuOnFocus:!0,onChange:e=>{e.value&&(n.toggleShowPopover(),t.setSelectedTab(e.value))}})))}));const X=e=>({card:{body:(0,i.css)({padding:e.spacing(1)}),p:(0,i.css)({maxWidth:300})}});function Z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Z(e,t,n[t])}))}return e}function te(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class ne extends c.Bs{getLabelsFromQueryRunnerState(e=(()=>{var e;return null===(e=this.state.$labelsData)||void 0===e?void 0:e.state})()){var t;return null===(t=e.data)||void 0===t?void 0:t.series[0].fields.map((e=>({cardinality:e.values[0],label:e.name})))}populatePrimaryLabelsVariableOptions(e){const t=c.jh.getAncestor(this,ge).getSelectedTab(),n=(0,W.sj)((0,m.S9)(this).getValue().toString()),r=e.map((e=>{const r=n.indexOf(e.label);return{active:t===e.label,label:e.label===E.OX?E.ky:e.label,saved:-1!==r,savedIndex:r,value:e.label}})).sort(((e,t)=>e.value===E.OX||t.value===E.OX?e.value===E.OX?-1:1:e.label<t.label?-1:e.label>t.label?1:0));this.setState({tabOptions:r})}runDetectedLabels(){this.state.$labelsData.runQueries()}runDetectedLabelsSubs(){this._subs.add(c.jh.getTimeRange(this).subscribeToState((()=>{this.runDetectedLabels()}))),this._subs.add((0,m.S9)(this).subscribeToState((()=>{this.runDetectedLabels()})))}onActivate(){this.runDetectedLabels(),this.setState({popover:new Y({})}),this.runDetectedLabelsSubs(),this._subs.add((0,m.S9)(this).subscribeToState((()=>{this.state.$labelsData.runQueries()}))),this._subs.add((0,m.El)(this).subscribeToState((()=>{var e;const t=this.getLabelsFromQueryRunnerState(null===(e=this.state.$labelsData)||void 0===e?void 0:e.state);t&&this.populatePrimaryLabelsVariableOptions(t)}))),this._subs.add(this.state.$labelsData.subscribeToState((e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===o.LoadingState.Done){const t=this.getLabelsFromQueryRunnerState(e),n=c.jh.getAncestor(this,ge);t&&this.populatePrimaryLabelsVariableOptions(t);const r=n.getSelectedTab();(null==t?void 0:t.some((e=>e.label===r)))||n.selectDefaultLabelTab()}})))}constructor(e){super(ee({$labelsData:(0,U.HF)({queries:[(0,G.BM)("","detected_labels")],runQueriesMode:"manual"}),showPopover:!1,tabOptions:[{label:E.ky,saved:!0,value:E.OX}]},e)),Z(this,"removeSavedTab",(e=>{(0,W.Gg)((0,m.S9)(this).getValue().toString(),e);const t=this.getLabelsFromQueryRunnerState();t&&this.populatePrimaryLabelsVariableOptions(t);const n=c.jh.getAncestor(this,ge);n.getSelectedTab()===e&&n.selectDefaultLabelTab()})),Z(this,"toggleShowPopover",(()=>{this.setState({showPopover:!this.state.showPopover})})),this.addActivationHandler(this.onActivate.bind(this))}}Z(ne,"Component",(({model:e})=>{const{$labelsData:t,popover:n,showPopover:l,tabOptions:u}=e.useState(),{data:p}=t.useState(),g=c.jh.getAncestor(e,ge);(0,m.El)(e).useState();const h=(0,d.useStyles2)(re),f=(0,r.useRef)(null);return a().createElement(d.TabsBar,{className:h.tabs},u.filter((e=>e.saved||e.active||e.value===E.OX)).sort(((e,t)=>{return e.value===E.OX||t.value===E.OX?e.value===E.OX?-1:1:(null!==(n=e.savedIndex)&&void 0!==n?n:0)-(null!==(r=t.savedIndex)&&void 0!==r?r:0);var n,r})).map((t=>{const n=a().createElement(d.Tab,{key:t.value,onChangeTab:()=>{g.setSelectedTab(t.value)},label:(0,Q.EJ)(t.label,15,!0),active:t.active,suffix:t.value!==E.OX?n=>a().createElement(a().Fragment,null,a().createElement(d.Tooltip,{content:"Remove tab"},a().createElement(d.Icon,{onKeyDownCapture:n=>{"Enter"===n.key&&e.removeSavedTab(t.value)},onClick:n=>{n.stopPropagation(),e.removeSavedTab(t.value)},name:"times",className:(0,i.cx)(n.className)}))):void 0});return t.label.length>15?a().createElement(d.Tooltip,{key:t.value,content:t.label},n):n})),(null==p?void 0:p.state)===o.LoadingState.Loading&&a().createElement(d.Tab,{label:"Loading tabs",icon:"spinner"}),(null==p?void 0:p.state)===o.LoadingState.Done&&a().createElement("span",{className:h.addTab},a().createElement(d.Tab,{onChangeTab:e.toggleShowPopover,label:"Add label",ref:f,icon:"plus-circle"})),n&&a().createElement(d.PopoverController,{content:a().createElement(n.Component,{model:n})},((e,t,n)=>{const r={onBlur:t,onFocus:e};return a().createElement(a().Fragment,null,f.current&&a().createElement(a().Fragment,null,a().createElement(d.Popover,ee(te(ee({},n,s.rest),{show:l,wrapperClassName:h.popover,referenceElement:f.current,renderArrow:!0}),r))))})))}));const re=e=>({addTab:(0,i.css)({"& button":{color:e.colors.primary.text},color:e.colors.primary.text,label:"add-label-tab"}),popover:(0,i.css)({background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3}),tabs:(0,i.css)({overflowY:"hidden"})});var ae=n(5570);function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ie(e,t,n[t])}))}return e}function oe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const le=l.config.featureToggles.exploreLogsAggregatedMetrics,ce="__aggregated_metric__",ue=(0,o.dateTime)("2024-08-30","YYYY-MM-DD");const de="var-primary_label",pe="var-ds";class ge extends c.Bs{getUrlState(){const{key:e}=he(),t=(0,m.El)(this).state.filters[0];return t.key&&t.key!==e&&(0,m.El)(this).setState({filters:[oe(se({},t),{key:null!=e?e:t.key})]}),{}}updateFromUrl(e){}addDatasourceChangeToBrowserHistory(e){const t=l.locationService.getLocation(),n=new URLSearchParams(t.search),r=n.get(pe);if(r&&e!==r){const r=t.pathname+t.search;n.set(pe,e);const a=t.pathname+"?"+n.toString();r!==a&&(0,h.ad)(a)}}addLabelChangeToBrowserHistory(e,t=!1){const{key:n,location:r,search:a}=he();if(n){const i=null==n?void 0:n.split("|");if((null==i?void 0:i[0])!==e){i[0]=e,a.set(de,i.join("|"));const n=r.pathname+r.search,s=r.pathname+"?"+a.toString();n!==s&&(t?l.locationService.replace(s):(0,h.ad)(s))}}}getSelectedTab(){var e;return null===(e=(0,m.El)(this).state.filters[0])||void 0===e?void 0:e.key}selectDefaultLabelTab(){this.addLabelChangeToBrowserHistory(E.OX,!0),this.setSelectedTab(E.OX)}setSelectedTab(e){(0,W.cO)((0,m.S9)(this).getValue().toString(),e),(0,m.h)(this),(0,m.BL)(e,this)}buildServiceLayout(e,t,n,r,a,i){var s;let l;n.to.diff(n.from,"hours")>=4&&n.to.diff(n.from,"hours")<=26&&(l="2h");const u=[];var p;this.isAggregatedMetricsActive()||u.push(new F({name:e,value:t})),u.push(new A({labelName:e,labelValue:t}));const g=c.d0.timeseries().setTitle(t).setData((0,U.rS)([(0,G.l)(this.getMetricExpression(t,r,a),{legendFormat:`{{${E.e4}}}`,refId:`ts-${t}`,splitDuration:l,step:r.state.value===ce?"10s":void 0})],{runQueriesMode:"manual"})).setCustomFieldConfig("stacking",{mode:d.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",d.DrawStyle.Bars).setUnit("short").setOverrides(U.jC).setOption("legend",{calcs:["sum"],displayMode:d.LegendDisplayMode.Table,placement:"right",showLegend:!0}).setHeaderActions([new D.p({ds:null!==(p=null===(s=i.getValue())||void 0===s?void 0:s.toString())&&void 0!==p?p:"",labelName:e,labelValue:t}),...u]).build();g.setState({extendPanelContext:(n,r)=>this.extendTimeSeriesLegendBus(e,t,r,g)});const h=new c.xK({$behaviors:[new c.Gg.K2({key:"serviceCrosshairSync",sync:o.DashboardCursorSync.Crosshair})],body:g});return h.addActivationHandler((()=>{var e;(null===(e=(0,f.oh)(h)[0].state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runPanelQuery(h)})),h}isAggregatedMetricsActive(){const e=this.getQueryOptionsToolbar();return!(null==e?void 0:e.state.options.aggregatedMetrics.disabled)&&(null==e?void 0:e.state.options.aggregatedMetrics.active)}formatPrimaryLabelForUI(){const e=this.getSelectedTab();return e===E.OX?E.ky:e}setVolumeQueryRunner(){this.setState({$data:(0,U.HF)({queries:[(0,G.$k)(`{${E.kl}, ${E.ll}}`,"volume",this.getSelectedTab())],runQueriesMode:"manual"})}),this.subscribeToVolume()}doVariablesNeedSync(){const e=(0,m.cR)(this),t=(0,m.aW)(this),n=this.getSelectedTab(),r=e.state.filters.filter((e=>e.key!==n));return{filters:r,needsSync:!(0,p.B)(r,t.state.filters)}}syncVariables(){const e=(0,m.aW)(this),{filters:t,needsSync:n}=this.doVariablesNeedSync();n&&e.setState({filters:t})}onActivate(){var e;this.fixRequiredUrlParams(),this.syncVariables(),this.setVolumeQueryRunner(),this.subscribeToPrimaryLabelsVariable(),this.subscribeToLabelFilterChanges(),this.subscribeToActiveTabVariable((0,m.El)(this)),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runVolumeOnActivate(),this.subscribeToTimeRange(),this.subscribeToDatasource(),this.subscribeToAggregatedMetricToggle(),this.subscribeToAggregatedMetricVariable()}runVolumeOnActivate(){var e,t;this.isTimeRangeTooEarlyForAggMetrics()?(this.onUnsupportedAggregatedMetricTimeRange(),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runVolumeQuery()):(this.onSupportedAggregatedMetricTimeRange(),(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.state)!==o.LoadingState.Done&&this.runVolumeQuery())}subscribeToAggregatedMetricToggle(){var e;this._subs.add(null===(e=this.getQueryOptionsToolbar())||void 0===e?void 0:e.subscribeToState(((e,t)=>{e.options.aggregatedMetrics.userOverride!==t.options.aggregatedMetrics.userOverride&&this.runVolumeQuery(!0)})))}subscribeToDatasource(){this._subs.add((0,m.S9)(this).subscribeToState((e=>{this.addDatasourceChangeToBrowserHistory(e.value.toString()),this.runVolumeQuery()})))}subscribeToActiveTabVariable(e){this._subs.add(e.subscribeToState(((e,t)=>{if(e.filterExpression!==t.filterExpression){const t=e.filters[0].key;this.addLabelChangeToBrowserHistory(t);const{needsSync:n}=this.doVariablesNeedSync();n?this.syncVariables():this.runVolumeQuery(!0)}})))}subscribeToAggregatedMetricVariable(){this._subs.add((0,m.vm)(this).subscribeToState(((e,t)=>{e.value!==t.value&&(this.setState({body:new c.gF({children:[]})}),this.updateBody(!0))})))}subscribeToPrimaryLabelsVariable(){const e=(0,m.cR)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,p.B)(e.filters,t.filters)||this.syncVariables()})))}subscribeToLabelFilterChanges(){const e=(0,m.aW)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,p.B)(e.filters,t.filters)||this.runVolumeQuery(!0)})))}subscribeToVolume(){this._subs.add(this.state.$data.subscribeToState(((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)!==o.LoadingState.Done||(0,p.B)(null==t||null===(r=t.data)||void 0===r?void 0:r.series,null==e||null===(a=e.data)||void 0===a?void 0:a.series)||this.updateBody(!0)})))}subscribeToTimeRange(){this._subs.add(c.jh.getTimeRange(this).subscribeToState((()=>{this.isTimeRangeTooEarlyForAggMetrics()?this.onUnsupportedAggregatedMetricTimeRange():this.onSupportedAggregatedMetricTimeRange(),this.runVolumeQuery()})))}fixRequiredUrlParams(){const{key:e}=he();e||this.selectDefaultLabelTab()}isTimeRangeTooEarlyForAggMetrics(){return c.jh.getTimeRange(this).state.value.from.isBefore((0,o.dateTime)(ue))}onUnsupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:oe(se({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!0})}})}getQueryOptionsToolbar(){return c.jh.getAncestor(this,v.P).state.controls.find((e=>e instanceof y.s))}onSupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:oe(se({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!1})}})}runVolumeQuery(e=!1){e&&this.setVolumeQueryRunner(),this.updateAggregatedMetricVariable(),this.state.$data.runQueries()}updateAggregatedMetricVariable(){const e=(0,m.vm)(this),t=(0,m.cR)(this);if(this.isTimeRangeTooEarlyForAggMetrics()&&le||!this.isAggregatedMetricsActive()){e.changeValueTo(E.OX),t.setState({hide:u.zL.dontHide}),e.changeValueTo(E.OX);c.jh.findByKeyAndType(this,v.y,b.H).setState({hidden:!1})}else{e.changeValueTo(ce),t.setState({filters:[],hide:u.zL.hideVariable});c.jh.findByKeyAndType(this,v.y,b.H).setState({hidden:!0})}}updateTabs(){if(!this.state.tabs){const e=new ne({});this.setState({tabs:e})}}getGridItems(){return this.state.body.state.children}getVizPanel(e){return e.state.body instanceof c.Eb?e.state.body:void 0}runPanelQuery(e){if(e.isActive){const n=(0,f.oh)(e);if(1===n.length){var t;const e=n[0],r=e.state.queries[0],a=null===(t=e.state.data)||void 0===t?void 0:t.timeRange,i=c.jh.getTimeRange(this),s=a?Math.abs(i.state.value.from.diff(null==a?void 0:a.from,"s")):1/0,o=a?Math.abs(i.state.value.to.diff(null==a?void 0:a.to,"s")):1/0,l=c.jh.interpolate(this,r.expr);(e.state.key!==l||s>0||o>0)&&(e.setState({key:l}),e.runQueries())}}}updateBody(e=!1){var t;const{labelsToQuery:n}=this.getLabels(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.series),r=this.getSelectedTab();if(this.updateTabs(),this.state.paginationScene||this.setState({paginationScene:new K({})}),n&&0!==n.length){const t=[],a=this.getGridItems(),i=c.jh.getTimeRange(this).state.value,s=(0,m.vm)(this),o=(0,m.El)(this),l=(0,m.S9)(this),u=(this.state.currentPage-1)*this.state.countPerPage,d=u+this.state.countPerPage;for(const c of n.slice(u,d)){const n=a.filter((e=>{const t=this.getVizPanel(e);return(null==t?void 0:t.state.title)===c}));if(2===n.length)t.push(n[0],n[1]),n[0].isActive&&e&&this.runPanelQuery(n[0]),n[1].isActive&&e&&this.runPanelQuery(n[1]);else{const e=this.buildServiceLayout(r,c,i,s,o,l),n=this.buildServiceLogsLayout(r,c);t.push(e,n)}}this.state.body.setState({autoRows:"200px",children:t,isLazy:!0,md:{columnGap:1,rowGap:1,templateColumns:"1fr"},templateColumns:"repeat(auto-fit, minmax(350px, 1fr) minmax(300px, calc(70vw - 100px)))"})}else this.state.body.setState({children:[]})}updateServiceLogs(e,t){var n;if(!this.state.body)return void this.updateBody();const{labelsToQuery:r}=this.getLabels(null===(n=this.state.$data.state.data)||void 0===n?void 0:n.series),a=null==r?void 0:r.indexOf(t);if(void 0===a||a<0)return;let i=[...this.getGridItems()];i.splice(2*a+1,1,this.buildServiceLogsLayout(e,t)),this.state.body.setState({children:i})}getLogExpression(e,t,n){return`{${e}=\`${t}\` , ${E.ll} }${n}`}getMetricExpression(e,t,n){const r=n.state.filters[0];return t.state.value===ce?r.key===E.OX?`sum by (${E.e4}) (sum_over_time({${ce}=\`${e}\` } | logfmt | unwrap count [$__auto]))`:`sum by (${E.e4}) (sum_over_time({${ce}=~\`.+\` } | logfmt | ${r.key}=\`${e}\` | unwrap count [$__auto]))`:`sum by (${E.e4}) (count_over_time({ ${r.key}=\`${e}\`, ${E.ll} } [$__auto]))`}getLabels(e){var t,n,r;const a=null!==(r=null==e||null===(t=e[0])||void 0===t?void 0:t.fields[0].values)&&void 0!==r?r:[],i=null===(n=(0,m.S9)(this).getValue())||void 0===n?void 0:n.toString(),s=(0,m.eY)(this).getValue(),o=this.getSelectedTab(),l=function(e,t,n,r){if(!(null==e?void 0:e.length))return[];".+"===n&&(n="");const a=(0,W.eT)(t,r).filter((t=>t.toLowerCase().includes(n.toLowerCase())&&e.includes(t)));return Array.from(new Set([...a,...e]))}(a,i,String(s),o);return{labelsByVolume:a,labelsToQuery:l}}constructor(e){var t,n;super(se({$data:(0,U.HF)({queries:[],runQueriesMode:"manual"}),$variables:new c.Pj({variables:[new g.m({hide:u.zL.hideVariable,label:"Service",name:E.Du,skipUrlSync:!0,value:".+"}),new g.m({hide:u.zL.hideLabel,label:"",name:E.Wi,options:[{label:E.OX,value:E.OX},{label:ce,value:ce}],skipUrlSync:!0,value:E.OX}),new c.H9({expressionBuilder:e=>function(e){if(e.length){const t=e[0];return`${t.key}${t.operator}\`${t.value}\``}return""}(e),filters:[{key:null!==(t=he().key)&&void 0!==t?t:E.OX,operator:"=~",value:".+"}],hide:u.zL.hideLabel,name:E.Gb}),new c.H9({datasource:E.eL,expressionBuilder:G.VW,filters:[],hide:u.zL.hideVariable,key:"adhoc_service_filter_replica",layout:"vertical",name:E.fi,skipUrlSync:!0})]}),body:new c.gF({children:[]}),countPerPage:null!==(n=(0,W.KH)())&&void 0!==n?n:20,currentPage:1,serviceLevel:new Map,showPopover:!1,tabOptions:[{label:E.ky,value:E.OX}]},e)),ie(this,"_urlSync",new c.So(this,{keys:[de]})),ie(this,"onSearchServicesChange",(0,s.debounce)((e=>{const t=(0,m.eY)(this);(e?(0,G.vC)(e):".+")!==t.state.value&&t.setState({label:null!=e?e:"",value:e?(0,G.vC)(e):".+"});const n=(0,m.El)(this),r=n.state.filters[0];(0,G.vC)(t.state.value.toString())!==r.value&&n.setState({filters:[oe(se({},r),{value:(0,G.vC)(t.state.value.toString())})]}),this.setState({currentPage:1}),(0,w.EE)(w.NO.service_selection,w.ir.service_selection.search_services_changed,{searchQuery:e})}),500)),ie(this,"getLevelFilterForService",(e=>{let t=this.state.serviceLevel.get(e)||[];if(0===t.length)return"";return` | ${t.map((e=>("logs"===e&&(e=""),`${E.e4}=\`${e}\``))).join(" or ")} `})),ie(this,"buildServiceLogsLayout",((e,t)=>{const n=this.getLevelFilterForService(t),r=new c.xK({$behaviors:[new c.Gg.K2({sync:o.DashboardCursorSync.Off})],body:c.d0.logs().setHoverHeader(!0).setData((0,U.rS)([(0,G.l)(this.getLogExpression(e,t,n),{maxLines:100,refId:`logs-${t}`})],{runQueriesMode:"manual"})).setTitle(t).setOption("showTime",!0).setOption("enableLogDetails",!1).build()});return r.addActivationHandler((()=>{var e;(null===(e=(0,f.oh)(r)[0].state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runPanelQuery(r)})),r})),ie(this,"extendTimeSeriesLegendBus",((e,t,n,r)=>{const a=n.onToggleSeriesVisibility;n.onToggleSeriesVisibility=(n,i)=>{var s,o,l;null==a||a(n,i);const c=(0,ae.vX)(null!==(l=null===(o=r.state.$data)||void 0===o||null===(s=o.state.data)||void 0===s?void 0:s.series)&&void 0!==l?l:[]),u=(0,ae.pC)(n,this.state.serviceLevel.get(t),i,c);this.state.serviceLevel.set(t,u),this.updateServiceLogs(e,t)}})),this.addActivationHandler(this.onActivate.bind(this))}}function he(){const e=l.locationService.getLocation(),t=new URLSearchParams(e.search),n=t.get(de),r=null==n?void 0:n.split("|");return{key:null==r?void 0:r[0],location:e,search:t}}function fe(e){return{body:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1}),bodyWrapper:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1}),container:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1,position:"relative"}),header:(0,i.css)({position:"absolute",right:0,top:"4px",zIndex:2}),headingWrapper:(0,i.css)({marginTop:e.spacing(1)}),loadingText:(0,i.css)({margin:0}),searchField:(0,i.css)({marginTop:e.spacing(1),position:"relative"}),searchPaginationWrap:(0,i.css)({[e.breakpoints.down("md")]:{marginTop:e.spacing(1),width:"100%"},alignItems:"center",display:"flex",flex:"1 0 auto",flexWrap:"wrap",label:"search-pagination-wrap"}),searchWrapper:(0,i.css)({[e.breakpoints.down("md")]:{alignItems:"flex-start",flexDirection:"column"},alignItems:"center",display:"flex",flexWrap:"wrap",label:"search-wrapper"})}}ie(ge,"Component",(({model:e})=>{var t;const n=(0,d.useStyles2)(fe),{$data:r,body:i,paginationScene:s,tabs:l}=e.useState(),{data:c}=r.useState(),u=e.getSelectedTab(),p=(0,m.eY)(e),{label:g,value:f}=p.useState(),v=f&&".+"!==f,{labelsByVolume:b,labelsToQuery:y}=e.getLabels(null==c?void 0:c.series),w=(null==c?void 0:c.state)===o.LoadingState.Loading||(null==c?void 0:c.state)===o.LoadingState.Streaming||void 0===c,O=(null===(t=r.state.data)||void 0===t?void 0:t.state)===o.LoadingState.Error,x=e.formatPrimaryLabelForUI();let E=p.getValue().toString();".+"===E&&(E="");const C=(0,G.sT)(E);var k;return a().createElement("div",{className:n.container},a().createElement("div",{className:n.bodyWrapper},l&&a().createElement(l.Component,{model:l}),a().createElement(d.Field,{className:n.searchField},a().createElement("div",{className:n.searchWrapper},a().createElement(S.f,{initialFilter:{icon:"filter",label:C,value:E},isLoading:w,value:E||g,onChange:t=>(t=>{e.onSearchServicesChange(t)})(t),selectOption:t=>{!function(e,t,n){const r=R(e,t,n);M(e,t,n),(0,h.ad)(r)}(u,t,e)},label:x,options:null!==(k=null==y?void 0:y.map((e=>({label:e,value:e}))))&&void 0!==k?k:[]}),!w&&a().createElement("span",{className:n.searchPaginationWrap},s&&a().createElement(K.PageCount,{model:s,totalCount:y.length}),s&&a().createElement(K.Component,{model:s,totalCount:y.length})))),!w&&O&&a().createElement(T,null),!w&&!O&&v&&!(null==b?void 0:b.length)&&a().createElement(N,null),!w&&!O&&!v&&!(null==b?void 0:b.length)&&a().createElement($,{labelName:u}),!(!w&&O)&&a().createElement("div",{className:n.body},a().createElement(i.Component,{model:i}),a().createElement("div",{className:n.headingWrapper},s&&a().createElement(K.Component,{totalCount:y.length,model:s})))))}))},9641:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(1475);function l(e){return a().createElement("div",{className:o.CT?c.container:void 0},a().createElement(s.RadioButtonGroup,{options:[{description:"Show results in logs visualisation",label:"Logs",value:"logs"},{description:"Show results in table visualisation",label:"Table",value:"table"},{description:"Show results in json visualisation",label:"JSON",value:"json"}],size:"sm",value:e.vizType,onChange:e.onChange}))}const c={container:(0,i.css)({paddingRight:6})}},4702:(e,t,n)=>{n.d(t,{m:()=>s});var r=n(1269),a=n(9736);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class s extends a.n8{getValueOptions(e){return(0,r.of)(this.state.options)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}({name:"",options:[],text:"",type:"custom",value:""},e))}}i(s,"Component",(({model:e})=>(0,a.WY)({model:e})))},6464:(e,t,n)=>{n.d(t,{K:()=>g});var r=n(3241),a=n(9736),i=n(6854),s=n(8428),o=n(708),l=n(4351),c=n(5553),u=n(20);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}class g{getJoinedLabelsFilters(){let{equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r}=this.getCombinedLabelFilters();const a=[];return[e,t,n,r].filter((e=>e)).forEach((e=>{const t=this.joinCombinedFiltersValues(e,"|");for(const n in e){const r=e[n].operator;a.push({key:n,operator:r,value:t[n]})}})),a}getExpr(){let{equalsFilters:e,gteFilters:t,gtFilters:n,lteFilters:r,ltFilters:a,notEqualsFilters:i,regexEqualFilters:s,regexNotEqualFilters:o}=this.getCombinedLabelFilters();this.options.debug;const l=this.buildLabelsLogQLFromFilters({equalsFilters:e,gteFilters:t,gtFilters:n,lteFilters:r,ltFilters:a,notEqualsFilters:i,regexEqualFilters:s,regexNotEqualFilters:o});var c;return l?(null!==(c=this.options.prefix)&&void 0!==c?c:"")+l:""}getLabelsExpr(e){return this.options=p({},{decodeFilters:!1,filterType:"indexed",joinMatchFilters:!0},e),this.getExpr()}getMetadataExpr(e){return this.options=p({},{decodeFilters:!1,filterSeparator:" |",filterType:"field",joinMatchFilters:!1,prefix:"| "},e),this.getExpr()}getLevelsExpr(e){return this.options=p({},{decodeFilters:!1,filterSeparator:" |",filterType:"field",joinMatchFilters:!1,prefix:"| "},e),this.getExpr()}getFieldsExpr(e){return this.options=p({},{decodeFilters:!0,filterSeparator:" |",filterType:"field",joinMatchFilters:!1,prefix:"| "},e),this.getExpr()}buildLabelsLogQLFromFilters({equalsFilters:e,gteFilters:t,gtFilters:n,lteFilters:a,ltFilters:s,notEqualsFilters:o,regexEqualFilters:l,regexNotEqualFilters:c}){let u,d,p,g,h,f,m,v;const b=[];var y;this.options.joinMatchFilters?(u=this.joinCombinedFiltersValues(e,"|"),d=this.joinCombinedFiltersValues(o,"|"),p=this.joinCombinedFiltersValues(l,"|"),g=this.joinCombinedFiltersValues(c,"|"),b.push(...this.buildJoinedFilters(u,i.KQ.Equal)),b.push(...this.buildJoinedFilters(d,i.KQ.NotEqual)),b.push(...this.buildJoinedFilters(p,i.KQ.RegexEqual)),b.push(...this.buildJoinedFilters(g,i.KQ.RegexNotEqual))):(u=this.getFilterValues(e),d=this.getFilterValues(o),p=this.getFilterValues(l),g=this.getFilterValues(c),b.push(...this.buildFilter(u,i.KQ.Equal)),b.push(...this.buildFilter(d,i.KQ.NotEqual)),b.push(...this.buildFilter(p,i.KQ.RegexEqual)),b.push(...this.buildFilter(g,i.KQ.RegexNotEqual))),h=this.getFilterValues(s),f=this.getFilterValues(a),m=this.getFilterValues(n),v=this.getFilterValues(t),b.push(...this.buildFilter(h,i.Rk.lt)),b.push(...this.buildFilter(f,i.Rk.lte)),b.push(...this.buildFilter(m,i.Rk.gt)),b.push(...this.buildFilter(v,i.Rk.gte)),this.options.debug;const S=(0,r.trim)(this.combineValues(b,`${null!==(y=this.options.filterSeparator)&&void 0!==y?y:","} `));return this.options.debug,S}getCombinedLabelFilters(){const{[i.KQ.Equal]:e,[i.KQ.NotEqual]:t,[i.KQ.RegexEqual]:n,[i.KQ.RegexNotEqual]:r,[i.Rk.lt]:a,[i.Rk.lte]:s,[i.Rk.gt]:o,[i.Rk.gte]:l}=this.groupFiltersByKey(this.filters);let c,u,d,p,g,h,f,m;return this.options.joinMatchFilters?(c=this.combineFiltersValues(e,i.KQ.RegexEqual),u=this.combineFiltersValues(t,i.KQ.RegexNotEqual),d=this.combineFiltersValues(n),p=this.combineFiltersValues(r)):(c=this.combineFiltersValues(e),u=this.combineFiltersValues(t),d=this.combineFiltersValues(n),p=this.combineFiltersValues(r)),g=this.combineFiltersValues(a),h=this.combineFiltersValues(s),f=this.combineFiltersValues(o),m=this.combineFiltersValues(l),this.options.debug,this.options.joinMatchFilters&&(c&&(d=this.mergeFilters(i.KQ.RegexEqual,c,d),c=this.removeStaleOperators(c,i.KQ.Equal)),u&&(p=this.mergeFilters(i.KQ.RegexNotEqual,u,p),u=this.removeStaleOperators(u,i.KQ.NotEqual))),{equalsFilters:c,gteFilters:m,gtFilters:f,lteFilters:h,ltFilters:g,notEqualsFilters:u,regexEqualFilters:d,regexNotEqualFilters:p}}buildFilter(e,t){const n=[];for(const r in e){const a=[],i=e[r];(0,o.iu)(t)?i.forEach((e=>a.push(this.buildFilterString(r,t,e,"")))):i.forEach((e=>a.push(this.buildFilterString(r,t,e)))),(0,o.BG)(t)?n.push(a.join(` ${this.positiveFilterValueSeparator} `)):n.push(a.join(` ${this.negativeFilterValueSeparator} `))}return n}buildJoinedFilters(e,t){const n=[];for(const r in e)n.push(this.buildFilterString(r,t,e[r]));return n}removeStaleOperators(e,t){const n={};return Object.keys(e).forEach((r=>{e[r].operator===t&&(n[r]=e[r])})),n}mergeFilters(e,t,n){return Object.keys(t).filter((n=>t[n].operator===e)).map((e=>({key:e,values:t[e].values}))).forEach((r=>{void 0===n&&(n={[r.key]:{operator:e,values:[]}}),void 0===n[r.key]&&(n[r.key]={operator:e,values:[]}),n[r.key].values.push(...this.mergeCombinedFiltersValues(t[r.key],e))})),n}mergeCombinedFiltersValues(e,t){var n;const r=[];return e.operator===t&&(null===(n=e.values)||void 0===n?void 0:n.length)&&r.push(...e.values),r}joinCombinedFiltersValues(e,t){const n={};for(const r in e)e[r].values.length&&(n[r]=this.combineValues(e[r].values,t));return n}getFilterValues(e){const t={};for(const n in e)e[n].values.length&&(t[n]=e[n].values);return t}combineValues(e,t){return e.join(`${t}`)}combineFiltersValues(e,t){let n={};for(const i in e){if(!e[i].length)continue;const o=(0,s.kR)(e[i][0].operator),l=null!=t?t:o,c=e[i][0];if(n[i]={operator:l,values:[]},1===e[i].length){var r;const e=this.escapeFieldValue(c.operator,c.value,null!==(r=c.valueLabels)&&void 0!==r?r:[]);n[i]={operator:o,values:[e]},this.options.debug}else{const t=this.escapeFieldValues(i,e,l);var a;if(void 0===n[i].operator)n[i]={operator:l,values:t};else null===(a=n[i].values)||void 0===a||a.push(...t)}}return n}escapeFieldValues(e,t,n){return t[e].map((e=>{var t;return this.escapeFieldValue(n,e.value,null!==(t=e.valueLabels)&&void 0!==t?t:[])}))}escapeFieldValue(e,t,n){const r=(0,u.zE)(t);if(this.options.decodeFilters){t=(0,c.bu)({value:t,valueLabels:n}).value}return t===u.ZO?(this.options.debug,t):r?(this.options.debug,a.Go.escapeLabelValueInExactSelector((0,u.Dx)(t))):(0,o.SM)(e)?(this.options.debug,a.Go.escapeLabelValueInRegexSelector(t)):(this.options.debug,a.Go.escapeLabelValueInExactSelector(t))}buildFilterString(e,t,n,r='"'){if(n===u.ZO)return`${e}${t}${n}`;const a=`${e}${t}${r}${n}${r}`;return this.options.debug,a}groupFiltersByKey(e){let t=e.filter((e=>{var t;return!(null===(t=this.options.ignoreKeys)||void 0===t?void 0:t.includes(e.key))||(0,o.SM)(e.operator)}));"indexed"===this.options.filterType&&t.length<1&&(t=e);const n=t.filter((e=>(0,o.BG)(e.operator)&&!(0,o.SM)(e.operator))),a=t.filter((e=>(0,o.BG)(e.operator)&&(0,o.SM)(e.operator))),s=t.filter((e=>(0,o.Lw)(e.operator)&&!(0,o.SM)(e.operator))),l=t.filter((e=>(0,o.Lw)(e.operator)&&(0,o.SM)(e.operator))),c=t.filter((e=>e.operator===i.w7.gt)),u=t.filter((e=>e.operator===i.w7.gte)),d=t.filter((e=>e.operator===i.w7.lt)),p=t.filter((e=>e.operator===i.w7.lte)),g=(0,r.groupBy)(n,(e=>e.key)),h=(0,r.groupBy)(a,(e=>e.key)),f=(0,r.groupBy)(s,(e=>e.key)),m=(0,r.groupBy)(l,(e=>e.key)),v=(0,r.groupBy)(c,(e=>e.key)),b=(0,r.groupBy)(u,(e=>e.key)),y=(0,r.groupBy)(d,(e=>e.key)),S=(0,r.groupBy)(p,(e=>e.key));return{[i.w7.Equal]:g,[i.w7.RegexEqual]:h,[i.w7.NotEqual]:f,[i.w7.RegexNotEqual]:m,[i.w7.gt]:v,[i.w7.gte]:b,[i.w7.lt]:y,[i.w7.lte]:S}}constructor(e,t={decodeFilters:!1,filterType:"field",joinMatchFilters:!0}){d(this,"filters",void 0),d(this,"options",void 0),d(this,"positiveFilterValueSeparator","or"),d(this,"negativeFilterValueSeparator","|"),this.filters=e,this.options=t,this.options.debug||(this.options.debug=(0,l.Rb)())}}},4509:(e,t,n)=>{n.d(t,{EE:()=>i,NO:()=>s,ir:()=>o});var r=n(8531),a=n(2533);const i=(e,t,n)=>{(0,r.reportInteraction)(((e,t)=>`${a.id.replace(/-/g,"_")}_${e}_${t}`)(e,t),n)},s={all:"all",service_details:"service_details",service_selection:"service_selection"},o={[s.service_selection]:{add_to_filters:"add_to_filters",aggregated_metrics_toggled:"aggregated_metrics_toggled",search_services_changed:"search_services_changed",service_selected:"service_selected"},[s.service_details]:{action_view_changed:"action_view_changed",add_to_filters_in_breakdown_clicked:"add_to_filters_in_breakdown_clicked",add_to_filters_in_json_panel:"add_to_filters_in_json_panel",change_line_format_in_json_panel:"change_line_format_in_json_panel",change_viz_type:"change_viz_type",label_in_panel_summary_clicked:"label_in_panel_summary_clicked",layout_type_changed:"layout_type_changed",level_in_logs_volume_clicked:"level_in_logs_volume_clicked",logs_clear_displayed_fields:"logs_clear_displayed_fields",logs_detail_filter_applied:"logs_detail_filter_applied",logs_popover_line_filter:"logs_popover_line_filter",logs_toggle_displayed_field:"logs_toggle_displayed_field",logs_visualization_toggle:"logs_visualization_toggle",open_in_explore_clicked:"open_in_explore_clicked",pattern_field_clicked:"pattern_field_clicked",pattern_removed:"pattern_removed",pattern_selected:"pattern_selected",search_string_in_logs_changed:"search_string_in_logs_changed",search_string_in_variables_changed:"search_string_in_variables_changed",select_field_in_breakdown_clicked:"select_field_in_breakdown_clicked",value_breakdown_sort_change:"value_breakdown_sort_change",wasm_not_supported:"wasm_not_supported"},[s.all]:{interval_too_long:"interval_too_long",open_in_explore_menu_clicked:"open_in_explore_menu_clicked"}}},1532:(e,t,n)=>{n.d(t,{B:()=>i,n:()=>s});var r=n(3241),a=n.n(r);const i=(e,t)=>{if(typeof e!=typeof t)return!1;const n=new Set(e),r=new Set(t);return n.size===r.size&&a().isEqual(n,r)},s=(e,t)=>typeof e==typeof t&&a().isEqual(e,t)},8955:(e,t,n)=>{n.r(t),n.d(t,{DETECTED_FIELDS_CARDINALITY_NAME:()=>G,DETECTED_FIELDS_NAME_FIELD:()=>U,DETECTED_FIELDS_PARSER_NAME:()=>Q,DETECTED_FIELDS_PATH_NAME:()=>J,DETECTED_FIELDS_TYPE_NAME:()=>q,WRAPPED_LOKI_DS_UID:()=>H,WrappedLokiDatasource:()=>Y,default:()=>X});var r=n(1269),a=n(7781),i=n(8531),s=n(9736),o=n(8502),l=n(5953),c=n(2344),u=n(3257);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}function g(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function h(e){return e.trim().length>2&&!function(e,t){let n=!1;return c.K3.parse(e).iterate({enter:({type:e})=>{if(e.id===t)return n=!0,!1}}),n}(e,c.Yw)}function f(e){if(function(e){return void 0!==e.targets.find((e=>h(e.expr)))}(e))return!1;if(function(e){return e.targets.find((e=>"instant"===e.queryType))}(e))return!1;for(let n=0;n<e.targets.length;n++){var t;if(null===(t=e.targets[n].expr)||void 0===t?void 0:t.includes("avg_over_time"))return!1}return!0}const m="__stream_shard_number__",v=e=>e.replace("}",`, __stream_shard__=~"${m}"}`),b=(e,t)=>{if(void 0===t||0===t.length)return e.map((e=>g(p({},e),{expr:e.expr.replace(`, __stream_shard__=~"${m}"}`,"}")})));let n=t.join("|");return"-1"===n||1===t.length?(n="-1"===n?"":n,e.map((e=>g(p({},e),{expr:e.expr.replace(`, __stream_shard__=~"${m}"}`,`, __stream_shard__="${n}"}`)})))):e.map((e=>g(p({},e),{expr:e.expr.replace(new RegExp(`${m}`,"g"),n)})))};var y=n(9598),S=n(7985),w=n(5719),O=n(5745),x=n(2533);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){E(e,t,n[t])}))}return e}function k(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function P(e,t){if(!e)return k(C({},n=t),{data:n.data.map(D)});var n,r,i;t.data.forEach((t=>{const n=e.data.find((e=>function(e,t){var n,r,i,s,o,l;if(e.refId!==t.refId)return!1;if(null!=e.name&&null!=t.name&&e.name!==t.name)return!1;const c=null===(n=e.meta)||void 0===n?void 0:n.type,u=null===(r=t.meta)||void 0===r?void 0:r.type;if(c!==u)return!1;if(c===a.DataFrameType.TimeSeriesMulti)return function(e,t){const n=e.fields.find((e=>e.type===a.FieldType.number)),r=t.fields.find((e=>e.type===a.FieldType.number));if(void 0===n||void 0===r)return!1;null==e.name&&(e.name=JSON.stringify(n.labels));null==t.name&&(t.name=JSON.stringify(r.labels));return e.name===t.name}(e,t);const d=null===(s=e.meta)||void 0===s||null===(i=s.custom)||void 0===i?void 0:i.frameType,p=null===(l=t.meta)||void 0===l||null===(o=l.custom)||void 0===o?void 0:o.frameType;if("LabeledTimeValues"===d&&"LabeledTimeValues"===p)return!0;if(d===p)return!0;return!1}(e,t)));n?function(e,t){var n,r;const i=e.fields.find((e=>e.type===a.FieldType.time)),s=e.fields.find((e=>e.type===a.FieldType.string&&"id"===e.name)),o=t.fields.find((e=>e.type===a.FieldType.time)),c=t.fields.find((e=>e.type===a.FieldType.string&&"id"===e.name));if(!i||!o)return void l.v.error(new Error("Time fields not found in the data frames"));var u;const d=null!==(u=null==o?void 0:o.values.slice(0))&&void 0!==u?u:[],p=Math.max(e.fields.length,t.fields.length);for(let n=0;n<d.length;n++){const r=j(i,o,n),l=F(i,s,r,o,c,n);for(let i=0;i<p;i++){if(!e.fields[i])continue;const s=L(e.fields[i],t.fields,i);if(s)if(l){if(e.fields[i].type===a.FieldType.time)continue;var g;e.fields[i].type===a.FieldType.number?e.fields[i].values[r]=(null!==(g=e.fields[i].values[r])&&void 0!==g?g:0)+s.values[n]:e.fields[i].type===a.FieldType.other?"object"==typeof s.values[n]?e.fields[i].values[r]=C({},e.fields[i].values[r],s.values[n]):null!=s.values[n]&&(e.fields[i].values[r]=s.values[n]):e.fields[i].values[r]=s.values[n]}else if(void 0!==s.values[n]){var h,f;if(e.fields[i].values.splice(r,0,s.values[n]),s.nanos)e.fields[i].nanos=null!==(f=e.fields[i].nanos)&&void 0!==f?f:new Array(e.fields[i].values.length-1).fill(0),null===(h=e.fields[i].nanos)||void 0===h||h.splice(r,0,s.nanos[n])}}}var m,v;e.length=e.fields[0].values.length,e.meta=k(C({},e.meta),{stats:T(null!==(m=null===(n=e.meta)||void 0===n?void 0:n.stats)&&void 0!==m?m:[],null!==(v=null===(r=t.meta)||void 0===r?void 0:r.stats)&&void 0!==v?v:[])})}(n,t):e.data.push(D(t))}));const s=[...null!==(r=e.errors)&&void 0!==r?r:[],...null!==(i=t.errors)&&void 0!==i?i:[]];var o;s.length>0&&(e.errors=s);const c=null!==(o=e.error)&&void 0!==o?o:t.error;var u,d;null!=c&&(e.error=c);const p=[...null!==(u=e.traceIds)&&void 0!==u?u:[],...null!==(d=t.traceIds)&&void 0!==d?d:[]];return p.length>0&&(e.traceIds=p),e}function j(e,t,n){const r=(0,a.closestIdx)(t.values[n],e.values);return r<0?0:t.values[n]===e.values[r]&&null!=t.nanos&&null!=e.nanos?t.nanos[n]>e.nanos[r]?r+1:r:t.values[n]>e.values[r]?r+1:r}function F(e,t,n,r,a,i){const s=function(e,t,n,r){if(e.nanos&&n.nanos)return void 0!==e.values[t]&&e.values[t]===n.values[r]&&void 0!==e.nanos[t]&&e.nanos[t]===n.nanos[r];return void 0!==e.values[t]&&e.values[t]===n.values[r]}(e,n,r,i);return!!s&&(null==t||null==a||void 0!==t.values[n]&&t.values[n]===a.values[i])}function L(e,t,n){const r=t.filter((t=>t.name===e.name));return 1===r.length?r[0]:t[n]}const _="Summary: total bytes processed";function T(e,t){const n=e.find((e=>e.displayName===_)),r=t.find((e=>e.displayName===_));if(null!=r&&null!=n)return[{displayName:_,unit:n.unit,value:r.value+n.value}];const a=null!=r?r:n;return null!=a?[a]:[]}function D(e){return k(C({},e),{fields:e.fields.map((e=>k(C({},e),{values:e.values})))})}function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){N(e,t,n[t])}))}return e}function I(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function B(e,t){const n=e.interpolateVariablesInQueries(t.targets,t.scopedVars).filter((e=>e.expr)).map((e=>I($({},e),{expr:v(e.expr)})));return function(e,t,n){let i=!1,s={data:[],key:(0,O.A)(),state:a.LoadingState.Streaming},o=null,d=new Map,p=null;const g=(r,c,u,h)=>{let f=h,m=!1;null!=o&&(o.unsubscribe(),o=null);const v=()=>{s.state=i?a.LoadingState.Error:a.LoadingState.Done,r.next(s),r.complete()};if(i)return void v();const y=()=>{const e=Math.min(c+h,u.length);c<u.length&&e<=u.length?g(r,e,u,f):v()},S=e=>{try{if(e&&!function(e){var t,n,r;const a=e.errors?(null!==(n=e.errors[0].message)&&void 0!==n?n:"").toLowerCase():null!==(r=null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==r?r:"";if(a.includes("timeout"))return!0;if(a.includes("parse error"))throw new Error(a);return!1}(e))return!1}catch(r){var t,n,a,s,o,f;return l.v.error(r,{error:null!==(s=null==e||null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==s?s:"",errors:null!==(o=null==e||null===(n=e.errors)||void 0===n?void 0:n.map((e=>e.message)).join(" | "))&&void 0!==o?o:"",msg:"sharding retry error",traces:null!==(f=null==e||null===(a=e.traceIds)||void 0===a?void 0:a.join("|"))&&void 0!==f?f:""}),i=!0,!1}if(h>1)return A(`Possible time out, new group size ${h=Math.floor(Math.sqrt(h))}`),m=!0,g(r,c,u,h),!0;var v;const b=null!==(v=d.get(c))&&void 0!==v?v:0;return b>3?(i=!0,!1):(d.set(c,b+1),p=setTimeout((()=>{l.v.info(`Retrying ${c} (${b+1})`),g(r,c,u,h),p=null}),1500*Math.pow(2,b)),m=!0,!0)},w=function(e,t,n){if(t===e.length)return[-1];return e.slice(t,t+n)}(u,c,h);A(`Querying ${w.join(", ")}`);const O=I($({},t),{targets:b(n,w)});t.requestId&&(O.requestId=`${t.requestId}_shard_${c}_${h}`),o=e.runQuery(O).subscribe({complete:()=>{m||(s.data.length&&r.next(s),y())},error:e=>{l.v.error(e,{msg:"failed to shard"}),r.next(s),S()||y()},next:e=>{var t;((null!==(t=e.errors)&&void 0!==t?t:[]).length>0||null!=e.error)&&S(e)||(f=function(e,t,n){const r=.7;return Math.min(t,Math.max(Math.floor((n-e)*r),1))}(c+h,function(e,t){var n,r;if(!e.data.length)return t+1;const a=null===(r=e.data[0].meta)||void 0===r||null===(n=r.stats)||void 0===n?void 0:n.find((e=>"Summary: exec time"===e.displayName));if(a){const e=Math.round(a.value);return A(`${a.value}`),e<=1?Math.floor(1.5*t):e<6?Math.ceil(1.1*t):1===t?t:e<20?Math.ceil(.9*t):Math.floor(t/2)}return t}(e,h),u.length),f!==h&&A(`New group size ${f}`),s=P(s,e))}})},h=n=>{o=e.query(t).subscribe({complete:()=>{n.next(s)},error:e=>{l.v.error(e,{msg:"runNonSplitRequest subscription error"}),n.error(s)},next:e=>{s=e}})},f=new r.Observable((r=>{const a=(e=>{const t=(0,u.QH)(e,[c.MD]);return t.length>0?e.substring(t[0].from,t[0].to).replace(`, __stream_shard__=~"${m}"}`,"}"):""})(n[0].expr);return(0,u.T0)(a)?(e.languageProvider.fetchLabelValues("__stream_shard__",{streamSelector:a||void 0,timeRange:t.range}).then((e=>{const t=e.map((e=>parseInt(e,10)));t&&t.length?(t.sort(((e,t)=>t-e)),A(`Querying ${t.join(", ")} shards`),g(r,0,t,function(e){return Math.floor(Math.sqrt(e.length))}(t))):(l.v.warn("Shard splitting not supported. Issuing a regular query."),h(r))})).catch((e=>{l.v.error(e,{msg:"failed to fetch label values for __stream_shard__"}),h(r)})),()=>{i=!0,p&&clearTimeout(p),null!=o&&(o.unsubscribe(),o=null)}):(A(`Skipping invalid selector: ${a}`),void r.complete())}));return f}(e,t,n)}Boolean(localStorage.getItem(`${x.id}.sharding_debug_enabled`));function A(e){}var M=n(20);function R(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function V(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){R(i,r,a,s,o,"next",e)}function o(e){R(i,r,a,s,o,"throw",e)}s(void 0)}))}}function W(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){W(e,t,n[t])}))}return e}function K(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const H="wrapped-loki-ds-uid",U="name",G="cardinality",Q="parser",q="type",J="jsonPath";class Y extends s.UU{query(e){return new r.Observable((t=>{var n;if(!(null===(n=e.scopedVars)||void 0===n?void 0:n.__sceneObject))throw new Error("Scene object not found in request");var r=this;(0,i.getDataSourceSrv)().get((0,w.U4)(e.scopedVars.__sceneObject.valueOf())).then(function(){var n=V((function*(n){var a;if(!(n instanceof i.DataSourceWithBackend&&"interpolateString"in n&&"getTimeRangeParams"in n))throw new Error("Invalid datasource!");const s=n;e.targets=null===(a=e.targets)||void 0===a?void 0:a.map((e=>(e.datasource=s,e)));const o=new Set;if(e.targets.forEach((e=>{var t;o.add(null!==(t=e.resource)&&void 0!==t?t:"")})),1!==o.size)throw new Error("A request cannot contain queries to multiple endpoints");switch(e.targets[0].resource){case"volume":yield r.getVolume(e,s,t);break;case"patterns":yield r.getPatterns(e,s,t);break;case"detected_labels":yield r.getDetectedLabels(e,s,t);break;case"detected_fields":yield r.getDetectedFields(e,s,t);break;case"labels":yield r.getLabels(e,s,t);break;default:r.getData(e,s,t)}}));return function(e){return n.apply(this,arguments)}}())}))}getData(e,t,n){const r=i.config.featureToggles.exploreLogsShardSplitting,a=K(z({},e),{targets:t.interpolateVariablesInQueries(e.targets,e.scopedVars).map((e=>K(z({},e),{expr:(0,S.VT)(e.expr),resource:void 0})))});return(!1!==f(a)&&r?B(t,a):t.query(a)).subscribe(n),n}getPatterns(e,t,n){var r=this;return V((function*(){const i=e.targets.filter((e=>"patterns"===e.resource));if(1!==i.length)throw new Error("Patterns query can only have a single target!");const{expression:s,interpolatedTarget:o}=r.interpolate(t,i,e);n.next({data:[],state:a.LoadingState.Loading});try{var l;const r=t.getResource("patterns",{end:e.range.to.utc().toISOString(),query:s,start:e.range.from.utc().toISOString(),step:e.interval},{headers:{"X-Query-Tags":`Source=${y.s_}`},requestId:null!==(l=e.requestId)&&void 0!==l?l:"patterns"}),i=yield r,u=null==i?void 0:i.data;let d=-1/0,p=0;var c;const g=null!==(c=null==u?void 0:u.map((e=>{const t=[],n=[];let r=0;return e.samples.forEach((([e,a])=>{t.push(1e3*e),n.push(a),a>d&&(d=a),a<p&&(p=a),a>d&&(d=a),a<p&&(p=a),r+=a})),(0,a.createDataFrame)({fields:[{config:{},name:"time",type:a.FieldType.time,values:t},{config:{},name:e.pattern,type:a.FieldType.number,values:n}],meta:{custom:{sum:r},preferredVisualisationType:"graph"},name:e.pattern,refId:o.refId})})))&&void 0!==c?c:[];g.sort(((e,t)=>{var n,r,a,i;return(null===(r=t.meta)||void 0===r||null===(n=r.custom)||void 0===n?void 0:n.sum)-(null===(i=e.meta)||void 0===i||null===(a=i.custom)||void 0===a?void 0:a.sum)})),n.next({data:g,state:a.LoadingState.Done})}catch(e){n.next({data:[],state:a.LoadingState.Error})}return n}))()}interpolate(e,t,n){const r=e.interpolateVariablesInQueries(t,n.scopedVars);if(!r.length)throw new Error("Datasource failed to interpolate query!");const a=r[0];return{expression:(0,S.VT)(a.expr),interpolatedTarget:a}}getDetectedLabels(e,t,n){var r=this;return V((function*(){const i=e.targets.filter((e=>"detected_labels"===e.resource));if(1!==i.length)throw new Error("Detected labels query can only have a single target!");let{expression:s,interpolatedTarget:l}=r.interpolate(t,i,e);"{}"===s&&(s=""),n.next({data:[],state:a.LoadingState.Loading});try{var c,u,d;const r=null===(u=(yield t.getResource("detected_labels",{end:e.range.to.utc().toISOString(),query:s,start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${y.s_}`},requestId:null!==(d=e.requestId)&&void 0!==d?d:"detected_labels"})).detectedLabels)||void 0===u||null===(c=u.filter((e=>!o.rm.includes(e.label))))||void 0===c?void 0:c.sort(((e,t)=>(0,o.p_)(e,t))),i=null==r?void 0:r.map((e=>({name:e.label,values:[e.cardinality]}))),p=(0,a.createDataFrame)({fields:null!=i?i:[],refId:l.refId});n.next({data:[p],state:a.LoadingState.Done})}catch(e){n.next({data:[],state:a.LoadingState.Error})}return n}))()}getDetectedFields(e,t,n){var r=this;return V((function*(){const i=e.targets.filter((e=>"detected_fields"===e.resource));if(1!==i.length)throw new Error("Detected fields query can only have a single target!");n.next({data:[],state:a.LoadingState.Loading});const{expression:s,interpolatedTarget:c}=r.interpolate(t,i,e);try{var u,d;const r=yield t.getResource("detected_fields",{end:e.range.to.utc().toISOString(),query:s,start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${y.s_}`},requestId:null!==(d=e.requestId)&&void 0!==d?d:"detected_fields"}),i={config:{},name:U,type:a.FieldType.string,values:[]},l={config:{},name:G,type:a.FieldType.number,values:[]},p={config:{},name:Q,type:a.FieldType.string,values:[]},g={config:{},name:q,type:a.FieldType.string,values:[]},h={config:{},name:J,type:a.FieldType.string,values:[]};null===(u=r.fields)||void 0===u||u.forEach((e=>{var t;o.$R.includes(e.label)||(i.values.push(e.label),l.values.push(e.cardinality),p.values.push((null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers.join(", "):"structuredMetadata"),g.values.push(e.type),h.values.push(e.jsonPath))}));const f=(0,a.createDataFrame)({fields:[i,l,p,g,h],refId:c.refId});n.next({data:[f],state:a.LoadingState.Done})}catch(e){l.v.error(e,{msg:"Detected fields error"}),n.next({data:[],state:a.LoadingState.Error})}return n}))()}getVolume(e,t,n){return V((function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");const r=e.targets[0],i=r.primaryLabel;if(!i)throw new Error("Primary label is required for volume queries!");const s=t.interpolateVariablesInQueries([r],e.scopedVars),o=(0,S.VT)(s[0].expr.replace(".*.*",".+"));n.next({data:[],state:a.LoadingState.Loading});try{var c,u,d;const r=yield t.getResource("index/volume",{end:e.range.to.utc().toISOString(),limit:5e3,query:o,start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${y.s_}`},requestId:null!==(d=e.requestId)&&void 0!==d?d:"volume"});null==r||r.data.result.sort(((e,t)=>{const n=e.value[1],r=t.value[1];return Number(r)-Number(n)}));const s=(0,a.createDataFrame)({fields:[{name:M.OX,values:null==r||null===(c=r.data.result)||void 0===c?void 0:c.map((e=>e.metric[i]))},{name:"volume",values:null==r||null===(u=r.data.result)||void 0===u?void 0:u.map((e=>Number(e.value[1])))}]});n.next({data:[s]})}catch(e){l.v.error(e),n.next({data:[],state:a.LoadingState.Error})}return n.complete(),n}))()}getLabels(e,t,n){return V((function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");try{var r;const i=yield t.getResource("labels",{end:e.range.to.utc().toISOString(),start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${y.s_}`},requestId:null!==(r=e.requestId)&&void 0!==r?r:"labels"}),s=(0,a.createDataFrame)({fields:[{name:"labels",values:null==i?void 0:i.data}]});n.next({data:[s],state:a.LoadingState.Done})}catch(e){n.next({data:[],state:a.LoadingState.Error})}return n.complete(),n}))()}testDatasource(){return Promise.resolve({message:"Data source is working",status:"success",title:"Success"})}constructor(e,t){super(e,t)}}const X=function(){s.Go.registerRuntimeDataSource({dataSource:new Y("wrapped-loki-ds",H)})}},7839:(e,t,n)=>{n.d(t,{G3:()=>a,_J:()=>i,ob:()=>r});var r=function(e){return e.logs="Logs",e.labels="Labels",e.fields="Fields",e.patterns="Patterns",e}({}),a=function(e){return e.explore="explore",e.logs="logs",e.labels="labels",e.patterns="patterns",e.fields="fields",e}({}),i=function(e){return e.field="field",e.label="label",e}({})},6838:(e,t,n)=>{n.d(t,{O:()=>l,m:()=>o});var r=n(376),a=n(5953),i=n(5553),s=n(20);function o(e,t,n=!0){const a=(0,i.ir)(e);let o="";n&&t===s.e4&&(o=`| ${s.e4} != ""`);const l=a.state.filters,c=(0,r.k$)(a);if(l.length){if("mixed"===c)return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.YN} ${s.Oc} ${s.jf} [$__auto])) by (${t})`;if("json"===c)return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.VL} ${s.Oc} ${s.jf} [$__auto])) by (${t})`;if("logfmt"===c)return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.mF} ${s.Oc} ${s.jf} [$__auto])) by (${t})`}return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.Oc} ${s.jf} [$__auto])) by (${t})`}function l(e){switch(e){case s._Y:return s.Sy;case s.sL:return s.fJ;default:const t=new Error(`Unknown variable type: ${e}`);throw a.v.error(t,{msg:`getFieldsTagValuesExpression: Unknown variable type: ${e}`,variableType:e}),t}}},696:(e,t,n)=>{n.d(t,{_J:()=>u,wy:()=>d});var r=n(9736),a=n(2661),i=n(577),s=n(173),o=n(4351),l=n(5553);function c(e){const t=r.jh.getAncestor(e,a.P);r.jh.findAllObjects(t,(e=>e instanceof i.p)).forEach((e=>e.forceRender()));r.jh.findDescendents(t,s.y).forEach((e=>e.forceRender()))}function u(e,t,n){const r=(0,l.S9)(n).getValue();(0,o.OB)(r,e,t),c(n)}function d(e,t,n){const r=(0,l.S9)(n).getValue();(0,o.cC)(r,e,t),c(n)}},376:(e,t,n)=>{n.d(t,{$1:()=>O,$E:()=>k,JI:()=>L,Jl:()=>T,Mz:()=>$,OE:()=>j,Qg:()=>S,Ri:()=>C,XK:()=>E,Z6:()=>D,Zp:()=>P,k$:()=>F,ph:()=>_,sB:()=>b,sg:()=>N});var r=n(1269),a=n(7781),i=n(9736),s=n(2007),o=n(5700),l=n(7352),c=n(4247),u=n(5953),d=n(2165),p=n(3142),g=n(1475),h=n(5553),f=n(20),m=n(9405);const v=e=>{if(e){if(Object.values(a.ReducerID).includes(e))return e}};function b(e){switch(e){case"json":return"json";case"logfmt":return"logfmt";case"":case"structuredMetadata":return"structuredMetadata";default:return"mixed"}}function y(e){switch(e){case"int":case"float":case"duration":case"boolean":case"bytes":return e;default:return"string"}}function S(e){var t;const n=new Set(null!==(t=null==e?void 0:e.map((e=>e.toString())))&&void 0!==t?t:[]);n.delete("structuredMetadata");const r=Array.from(n);return 1===r.length?b(r[0]):0===n.size?"structuredMetadata":"mixed"}function w(e){return null==e?void 0:e.fields[0]}function O(e){return null==e?void 0:e.fields[2]}function x(e){return null==e?void 0:e.fields[3]}function E(e){return null==e?void 0:e.fields[4]}function C(e,t){var n;const r=(0,l.rD)(t),a=O(r),i=w(r),s=null==i?void 0:i.values.indexOf(e);var o;const c=void 0!==s&&-1!==s?b(null!==(o=null==a||null===(n=a.values)||void 0===n?void 0:n[s])&&void 0!==o?o:""):void 0;return void 0===c?(u.v.warn("missing parser, using mixed format for",{fieldName:e}),"mixed"):c}function k(e,t){var n,r;const a=(0,l.rD)(t),i=O(a),s=w(a),o=E(a),c=null==s?void 0:s.values.indexOf(e);var d;const p=void 0!==c&&-1!==c?b(null!==(d=null==i||null===(n=i.values)||void 0===n?void 0:n[c])&&void 0!==d?d:""):void 0,g=void 0!==c?null==o||null===(r=o.values)||void 0===r?void 0:r[c]:void 0,h=g?$(g):void 0;return void 0===p?(u.v.warn("missing parser, using mixed format for",{fieldName:e}),{parser:"mixed",path:h}):{parser:p,path:h}}function P(e,t,n,a,l){return(c,u)=>{const d=v(a.state.sortBy),p=i.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("fillOpacity",9).setTitle(e(c)).setShowMenuAlways(!0).setData(new i.Es({transformations:[()=>function(e){return t=>t.pipe((0,r.map)((()=>[e])))}(c)]})).setOverrides(g.jC).setMenu(new o.GD({investigationOptions:{fieldName:e(c),frame:c,labelName:l}})).setHeaderActions([new m.oR({frame:c,hideExclude:l===f.e4,variableName:n})]);return t===s.DrawStyle.Bars&&p.setCustomFieldConfig("stacking",{mode:s.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setOverrides(g.jC).setCustomFieldConfig("drawStyle",s.DrawStyle.Bars),d&&(p.setOption("legend",{calcs:[d],showLegend:!0}),p.setDisplayName(" ")),new i.xK({body:p.build()})}}function j(e,t,n){const r=e?(0,p.E)(t,e):c.H.Parsed;if(r)return function(e,t){switch(e){case c.H.Indexed:return f.MB;case c.H.Parsed:return f.mB;case c.H.StructuredMetadata:return t===f.e4?f._Y:f._P;default:{const n=new Error(`Invalid label type for ${t}`);throw u.v.error(n,{msg:`Invalid label type for ${t}`,type:e}),n}}}(r,t);const a=C(t,n);return"structuredMetadata"===a?f._P:(u.v.warn("unable to determine label variable, falling back to parsed field",{key:t,parserForThisField:null!=a?a:""}),f.mB)}function F(e){return S(e.state.filters.map((e=>(0,h.bu)(e).parser)))}function L(e){return"duration"===e||"bytes"===e||"float"===e}function _(e,t){var n;const r=w(t),a=x(t),i=null==r?void 0:r.values.indexOf(e);return void 0!==i&&-1!==i?y(null==a||null===(n=a.values)||void 0===n?void 0:n[i]):void 0}function T(e,t,n,r){var a,i,s;const o=w(n),l=x(n),c=O(n),d=E(n),p=null==o?void 0:o.values.indexOf(e),g=void 0!==p&&-1!==p?b(null==c||null===(a=c.values)||void 0===a?void 0:a[p]):"mixed",f=void 0!==p&&-1!==p?y(null==l||null===(i=l.values)||void 0===i?void 0:i[p]):void 0,m=void 0!==p&&-1!==p?null==d||null===(s=d.values)||void 0===s?void 0:s[p]:void 0,v=t.state.filters.map((e=>{var t;const n=null==o?void 0:o.values.indexOf(e.key),r=(0,h.bu)(e);if(r.parser)return r.parser;var a;const i=void 0!==n&&-1!==n?b(null!==(a=null==c||null===(t=c.values)||void 0===t?void 0:t[n])&&void 0!==a?a:"mixed"):void 0;return null!=i?i:"mixed"})),C=S([...v,g]);let k="",P="";"structuredMetadata"===g?P=`| ${e}!=""`:k=`| ${e}!=""`;const j={fieldExpressionToAdd:k,fieldType:f,parser:C,structuredMetadataToAdd:P};if(("json"===C||"mixed"===C)&&m){const n=$(m),a=t.state.filters,i=null==r?void 0:r.state.filters;a.every((e=>null==i?void 0:i.some((t=>e.key===t.key))))?j.jsonParserPropToAdd=(null==r?void 0:r.state.filters.length)?`${e}="${n}",`:`${e}="${n}"`:u.v.warn("missing json path for field filters",{fieldFilters:JSON.stringify(a),jsonFilters:JSON.stringify(i)})}return function(e,t){return t.fieldType&&["bytes","duration"].includes(t.fieldType)?`avg_over_time(${(0,h.DX)(t)} | unwrap `+t.fieldType+`(${e}) | __error__="" [$__auto]) by ()`:t.fieldType&&"float"===t.fieldType?`avg_over_time(${(0,h.DX)(t)} | unwrap `+e+' | __error__="" [$__auto]) by ()':`sum by (${e}) (count_over_time(${(0,h.DX)(t)} [$__auto]))`}(e,j)}function D(e){return e===d.wu||e===d.eB}function N(e){const t=(0,h.ir)(e),n=(0,h.WA)(e),r=(0,h.U2)(e);t.state.filters.length||r.state.filters.length||n.setState({filters:[]})}function $(e){var t;return null===(t=e.map((e=>`[\\"${e}\\"]`)))||void 0===t?void 0:t.join("")}},8502:(e,t,n)=>{n.d(t,{$R:()=>u,AY:()=>m,OH:()=>f,X:()=>v,dD:()=>p,eO:()=>y,gR:()=>b,p_:()=>d,rd:()=>h,rm:()=>g,uE:()=>c});var r=n(3241),a=n(376),i=n(6854),s=n(42),o=n(5553),l=n(20);const c=" ",u=["level_extracted",l.e4,"level"];function d(e,t){return 1===e.cardinality?1:1===t.cardinality?-1:e.cardinality-t.cardinality}function p(e){const t=[...e];e.includes(l.e4)||t.unshift(l.e4);const n=t.map((e=>({label:e,value:String(e)})));return[{label:"All",value:l.To},...n]}const g=["__aggregated_metric__","__stream_shard__"];function h(e){const t=[...e].map((e=>({label:e,value:String(e)})));return[{label:"All",value:l.To},...t]}function f(e){var t;return((0,l.zE)(e.value)?(0,l.Dx)(e.value):e.value)===(null===(t=e.valueLabels)||void 0===t?void 0:t[0])}function m(e){const t=(0,o.U2)(e).state.filters,n=(0,o.ir)(e),r=(0,o.WA)(e),a=new Set;t.forEach((e=>{a.add(e.key)}));const i=new Set;n.state.filters.forEach((e=>i.add(e.key)));const s=r.state.filters.filter((e=>{let n=!1,r=[];n=t.some((t=>{r.push(t.key);return r.join("_")===e.key}));return i.has(e.key)||n}));r.setState({filters:s})}function v(e){const t=(0,o.U2)(e);m(e),t.setState({filters:[]})}function b(e,t){const n=(0,o.WA)(e),l=S(t),c=function(e){return e.match(s.HO)&&(e=e.replace(s.HO,"_")),e}(y(t)),u=[...t];let d=u.shift(),p=[...n.state.filters.filter((e=>e.key!==c)),{key:c,operator:i.w7.Equal,value:l}];for(;d&&!(0,a.Z6)(d.toString())&&!(0,r.isNumber)(d)&&"root"!==d;){const e=y(u),t=S(u);e&&!p.find((n=>n.key===e&&n.value===t&&n.operator===i.w7.Equal))&&(p=[...p.filter((t=>t.key!==e)),{key:e,operator:i.w7.Equal,value:t}]),d=u.shift()}n.setState({filters:p})}function y(e){return w(e).join("_")}function S(e){const t=w(e);return(0,a.Mz)(t)}function w(e){let t;const n=[...e],i=[];for(;(t=n.shift())&&!(0,a.Z6)(t.toString())&&!(0,r.isNumber)(t)&&"root"!==t;)i.unshift(t);return i}},8839:(e,t,n)=>{n.d(t,{K:()=>i,Q:()=>a});var r=n(7781);function a(e){return e===r.LogsSortOrder.Ascending||e===r.LogsSortOrder.Descending}function i(e){const t=Object.values(r.LogsDedupStrategy).map((e=>e.toString()));return"string"==typeof e&&t.includes(e)}},42:(e,t,n)=>{n.d(t,{CP:()=>v,HO:()=>y,R7:()=>h,_t:()=>m,de:()=>p,di:()=>u,ec:()=>f,oj:()=>d,zr:()=>g});var r=n(9405),a=n(376),i=n(5570),s=n(708),o=n(7985),l=n(5553),c=n(20);const u="repeat(auto-fit, minmax(400px, 1fr))";function d(e,t,n){let r="",i="";const s=(0,l.ir)(e),u=(0,a.k$)(s);return n&&n!==c.e4?r=` ,${n} != ""`:n&&n===c.e4&&(i=` | ${n} != ""`),(0,o.l)(`sum(count_over_time(${(0,l.DX)({labelExpressionToAdd:r,parser:u,structuredMetadataToAdd:i})} [$__auto])) by (${t})`,{legendFormat:`{{${t}}}`,refId:"LABEL_BREAKDOWN_VALUES"})}function p(e){return e.map((e=>(0,i.H7)(e))).flatMap((e=>e?[e]:[]))}function g(e,t,n){const i=(0,l.YS)(n),o=0===i.state.filters.length,u="structuredMetadata"===(0,a.Ri)(e,n),d=i.state.filters.find((e=>u?(0,s.BG)(e.operator)&&e.value===t:(0,s.BG)(e.operator)&&(0,l.bu)(e).value===t));return o||!d?((0,r.Qt)(e,t,"include",n,u?c._P:c.mB),"include"):((0,r.Qt)(e,t,"toggle",n,u?c._P:c.mB),"toggle")}function h(e,t,n){const a=(0,l.cR)(n),i=0===a.state.filters.length,o=a.state.filters.find((e=>e.value===t&&(0,s.BG)(e.operator)));return i||!o?((0,r.Qt)(e,t,"include",n,c.MB),"include"):((0,r.Qt)(e,t,"toggle",n,c.MB),"toggle")}function f(e,t,n){return b(e,t,(0,l.cR)(n))}function m(e,t,n){return b(e,t,(0,l.ir)(n))}function v(e,t,n){return b(e,t,(0,l.oY)(n))}function b(e,t,n){const r=n.state.filters.filter((t=>t.key===e&&(0,s.BG)(t.operator))).map((e=>n.state.name===c.mB?(0,l.bu)(e).value:e.value)),a=n.state.filters.filter((t=>t.key===e&&(0,s.Lw)(t.operator))).map((e=>n.state.name===c.mB?(0,l.bu)(e).value:e.value));return t.filter((e=>!a.includes(e)&&(0===r.length||r.includes(e))))}const y=/[^a-zA-Z0-9_:]/g},5570:(e,t,n)=>{n.d(t,{Ex:()=>d,H7:()=>u,PE:()=>g,pC:()=>l,vX:()=>c});var r=n(2007),a=n(708),i=n(5553),s=n(20),o=n(9405);function l(e,t,n,a){if(n===r.SeriesVisibilityChangeMode.ToggleSelection){const n=null!=t?t:[];return 1===n.length&&n.includes(e)?[]:[e]}let i=(null==t?void 0:t.length)?t:a;return i.includes(e)?i.filter((t=>t!==e)):[...i,e]}function c(e){return e.map((e=>{var t;return null!==(t=u(e))&&void 0!==t?t:"logs"}))}function u(e){var t;const n=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!n)return null;const r=Object.keys(n);return 0===r.length?null:n[r[0]]}function d(e,t){const n=(0,i.iw)(t),r=n.state.filters.filter((e=>(0,a.BG)(e.operator))).map((e=>e.value.split("|").map(p))).join("|"),s=n.state.filters.filter((e=>(0,a.Lw)(e.operator))).map((e=>e.value.split("|").map(p))).join("|");return e.filter((e=>!s.includes(e)&&(0===r.length||r.includes(e))))}function p(e){return'""'===e?"logs":e}function g(e,t){const n=(0,i.iw)(t),r=0===n.state.filters.length,l=n.state.filters.find((t=>t.value===e&&(0,a.BG)(t.operator)));return"logs"===e&&(e='""'),r||!l?((0,o.Qt)(s.e4,e,"include",t,s._Y),"include"):((0,o.Qt)(s.e4,e,"toggle",t,s._Y),"toggle")}},2165:(e,t,n)=>{n.d(t,{Il:()=>h,Os:()=>p,bz:()=>u,eB:()=>s,fF:()=>g,hy:()=>b,po:()=>f,wu:()=>o,y2:()=>y,z5:()=>m});var r=n(7781);function a(e,t,n){const r=e.getFieldByName(t);if(void 0!==r)return r.type===n?r:void 0}const i="timestamp",s="body",o="Line",l="severity",c="id",u="labels";function d(e){const t={};return Object.entries(e).forEach((([e,n])=>{t[e]="string"==typeof n?n:JSON.stringify(n)})),t}function p(e){var t;return(null===(t=e.meta)||void 0===t?void 0:t.type)===r.DataFrameType.LogLines?function(e){const t=new r.FieldCache(e),n=a(t,i,r.FieldType.time),o=a(t,s,r.FieldType.string);if(void 0===n||void 0===o)return null;var p;const g=null!==(p=a(t,l,r.FieldType.string))&&void 0!==p?p:null;var h;const f=null!==(h=a(t,c,r.FieldType.string))&&void 0!==h?h:null;var m;const v=null!==(m=a(t,u,r.FieldType.other))&&void 0!==m?m:null,b=null===v?null:v.values,y=t.fields.filter(((e,t)=>t!==n.index&&t!==o.index&&t!==(null==g?void 0:g.index)&&t!==(null==f?void 0:f.index)&&t!==(null==v?void 0:v.index)));return{bodyField:o,extraFields:y,getLabelFieldName:()=>null!==v?v.name:null,getLogFrameLabels:()=>b,getLogFrameLabelsAsLabels:()=>null!==b?b.map(d):null,idField:f,raw:e,severityField:g,timeField:n,timeNanosecondField:null}}(e):function(e){const t=new r.FieldCache(e),n=t.getFirstFieldOfType(r.FieldType.time),a=t.getFirstFieldOfType(r.FieldType.string);if(void 0===n||void 0===a)return null;var i;const s=null!==(i=t.getFieldByName("tsNs"))&&void 0!==i?i:null;var o;const l=null!==(o=t.getFieldByName("level"))&&void 0!==o?o:null;var c;const u=null!==(c=t.getFieldByName("id"))&&void 0!==c?c:null,[p,g]=function(e,t,n){const a=e.getFieldByName("labels");if(void 0!==a&&a.type===r.FieldType.other){const e=a.values.map(d);return[a,()=>e]}return[null,()=>function(e,t){const n=e.labels;if(void 0!==n){const e=new Array(t);return e.fill(n),e}return null}(t,n.length)]}(t,a,e),h=t.fields.filter(((e,t)=>t!==n.index&&t!==a.index&&t!==(null==s?void 0:s.index)&&t!==(null==l?void 0:l.index)&&t!==(null==u?void 0:u.index)&&t!==(null==p?void 0:p.index)));return{bodyField:a,extraFields:h,getLabelFieldName:()=>{var e;return null!==(e=null==p?void 0:p.name)&&void 0!==e?e:null},getLogFrameLabels:g,getLogFrameLabelsAsLabels:g,idField:u,raw:e,severityField:l,timeField:n,timeNanosecondField:s}}(e)}function g(e){var t;return null!==(t=null==e?void 0:e.timeField.name)&&void 0!==t?t:i}function h(e){var t;return null!==(t=null==e?void 0:e.bodyField.name)&&void 0!==t?t:s}function f(e){var t,n;return null!==(n=null==e||null===(t=e.idField)||void 0===t?void 0:t.name)&&void 0!==n?n:c}function m(e){var t;let n=0,a=0;const i=null===(t=e[0])||void 0===t?void 0:t.fields.find((e=>e.type===r.FieldType.time));if(i){const e=[...i.values].sort(),t=e[0]<e[e.length-1];n=t?e[0]:e[e.length-1],a=t?e[e.length-1]:e[0]}return{end:a,start:n}}const v="Visible range";function b(e,t){const n=(0,r.arrayToDataFrame)([{color:"rgba(58, 113, 255, 0.3)",isRegion:!0,text:"Range from oldest to newest logs in display",time:e,timeEnd:t}]);return n.name=v,n.meta={dataTopic:r.DataTopic.Annotations},n}function y(e){return 0===e.length||0===e[0].fields[0].values.length}},2152:(e,t,n)=>{let r;function a(){r||(r=new i)}n.d(t,{JO:()=>s,rX:()=>a});class i{getServiceSceneState(){return this.serviceSceneState}setPatternsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.patternsCount=e}setLabelsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.labelsCount=e}setFieldsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.fieldsCount=e}setServiceSceneState(e){this.serviceSceneState={fieldsCount:e.fieldsCount,labelsCount:e.labelsCount,loading:e.loading,logsCount:e.logsCount,patternsCount:e.patternsCount,totalLogsCount:e.totalLogsCount}}constructor(){var e,t,n;n=void 0,(t="serviceSceneState")in(e=this)?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}}function s(){return r}},7478:(e,t,n)=>{n.d(t,{FB:()=>m,Ns:()=>x,Vt:()=>S,ad:()=>w,bN:()=>O,fg:()=>v,k9:()=>b,rs:()=>y});var r=n(7781),a=n(8531),i=n(9736),s=n(2661),o=n(7839),l=n(7389),c=n(2152),u=n(9598),d=n(9683),p=n(20);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let h;function f(e,t){return r.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({},Object.entries(r.urlUtil.getUrlSearchParams()).reduce(((e,[t,n])=>(d.tm.includes(t)&&(e[t]=n),e)),{}),e)}(t))}function m(e,t,n){var r,a;const d=i.jh.getAncestor(n,s.P),g=null===(r=d.state.routeMatch)||void 0===r?void 0:r.params.labelName,h=null===(a=d.state.routeMatch)||void 0===a?void 0:a.params.labelValue;if(g&&h){let r=function(e,t,n,r="service"){return e===p.To&&t===o._J.label?(0,u._F)(`${o.G3.explore}/${r}/${(0,l.uu)(n)}/${o.G3.labels}`):e===p.To&&t===o._J.field?(0,u._F)(`${o.G3.explore}/${r}/${(0,l.uu)(n)}/${o.G3.fields}`):(0,u._F)(`${o.G3.explore}/${r}/${(0,l.uu)(n)}/${t}/${(0,l.uu)(e)}`)}(t,e,h,g);const a=f(r);if(n){(0,c.JO)().setServiceSceneState(n.state)}return a}return""}function v(e,t,n){const r=m(e,t,n);r&&w(r)}function b(e,t,n){return f(d.bw.logs(t,e),n)}function y(e,t,n){var r,a;const c=i.jh.getAncestor(t,s.P),d=null===(r=c.state.routeMatch)||void 0===r?void 0:r.params.labelValue,p=null===(a=c.state.routeMatch)||void 0===a?void 0:a.params.labelName;if(d){return f((0,u._F)(`${o.G3.explore}/${p}/${(0,l.uu)(d)}/${e}`),n)}return""}function S(e,t,n){const r=y(e,t,n);if(r){if(t){(0,c.JO)().setServiceSceneState(t.state)}w(r)}else;}function w(e){h=e,a.locationService.push(e)}function O(){const e=a.locationService.getLocation();a.locationService.push(e.pathname+e.search)}function x(){const e=a.locationService.getLocation(),t=(0,d.qe)(d.bw.explore()),n=e.pathname+e.search,r=a.locationService.getSearch();t===n||n.includes(t)||(r.get("var-filters")?w(t):(h&&a.locationService.replace(h),a.locationService.push(t)))}},1475:(e,t,n)=>{n.d(t,{rS:()=>A,FH:()=>B,HF:()=>M,CT:()=>R,jC:()=>j,ZC:()=>F,Nr:()=>N,dO:()=>D,C6:()=>T});var r=n(1269),a=n(7781),i=n(8531),s=n(9736),o=n(1625),l=n(2007),c=n(7352),u=n(8955),d=n(376),p=n(42),g=n(5570);class h extends s.dt{runQueries(){const e=s.jh.getTimeRange(this);this.runWithTimeRangeAndScopes(e)}constructor(e){super(e)}}var f=n(3142),m=n(4351),v=n(2649);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){b(e,t,n[t])}))}return e}function S(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const w="logs",O=/^info$/i,x=/^debug$/i,E=/^(warn|warning)$/i,C=/^error$/i,k=/^(crit|critical|fatal)$/i,P=/^(logs|unknown)$/i;function j(e){e.matchFieldsWithNameByRegex(O.source).overrideColor({fixedColor:"semi-dark-green",mode:"fixed"}),e.matchFieldsWithNameByRegex(x.source).overrideColor({fixedColor:"semi-dark-blue",mode:"fixed"}),e.matchFieldsWithNameByRegex(E.source).overrideColor({fixedColor:"semi-dark-orange",mode:"fixed"}),e.matchFieldsWithNameByRegex(C.source).overrideColor({fixedColor:"semi-dark-red",mode:"fixed"}),e.matchFieldsWithNameByRegex(k.source).overrideColor({fixedColor:"#705da0",mode:"fixed"}),e.matchFieldsWithNameByRegex(P.source).overrideColor({fixedColor:"darkgray",mode:"fixed"})}function F(e){return e.setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("axisSoftMin",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars).setOverrides(j)}function L(e){return e.setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars)}function _(e,t){t.match({id:a.FieldMatcherID.byNames,options:{mode:"exclude",names:e,prefix:"All except:",readOnly:!0}}).overrideCustomFieldConfig("hideFrom",{legend:!1,tooltip:!1,viz:!0});const n=t.build();n[n.length-1].__systemRef="hideSeriesFrom"}function T(e,t,n){const r=(0,g.Ex)((0,g.vX)(t),n),a=F(s.No.timeseries()).setOverrides(_.bind(null,r));a instanceof s.OS&&e.getPlugin()&&e.onFieldConfigChange(a.build(),!0)}function D(e,t,n,r){const a=(0,p.de)(n),i=(0,p.ec)(e,a,r),o=L(s.No.timeseries());i.length&&o.setOverrides(_.bind(null,i)),o instanceof s.OS&&t.onFieldConfigChange(o.build(),!0)}function N(e,t,n,r){const a=(0,p.de)(n),i="structuredMetadata"===(0,d.Ri)(e,r)?(0,p.CP)(e,a,r):(0,p._t)(e,a,r),o=L(s.No.timeseries());i.length&&o.setOverrides(_.bind(null,i)),o instanceof s.OS&&t.onFieldConfigChange(o.build(),!0)}function $(){return e=>e.pipe((0,r.map)((e=>e.map(((t,n)=>S(y({},t),{fields:t.fields.map(((n,r)=>{if(n.type===a.FieldType.time)return n;const i=(0,a.getFieldDisplayName)(n,t,e);return S(y({},n),{config:S(y({},n.config),{color:{mode:a.FieldColorModeId.PaletteClassicByName},displayName:i})})}))}))))))}function I(){return e=>e.pipe((0,r.map)((e=>e.map((e=>(e.fields.length<2||e.fields[1].config.displayNameFromDS||(e.fields[1].config.displayNameFromDS=w),e))).sort(((e,t)=>{if(e.fields.length<2||t.fields.length<2)return 0;const n=e.fields[1].config.displayNameFromDS,r=(null==n?void 0:n.match(k))?5:(null==n?void 0:n.match(C))?4:(null==n?void 0:n.match(E))?3:(null==n?void 0:n.match(x))||(null==n?void 0:n.match(O))?2:1,a=t.fields[1].config.displayNameFromDS;return r-((null==a?void 0:a.match(k))?5:(null==a?void 0:a.match(C))?4:(null==a?void 0:a.match(E))?3:(null==a?void 0:a.match(x))||(null==a?void 0:a.match(O))?2:1)})))))}function B(e){return new h({datasource:{uid:u.WRAPPED_LOKI_DS_UID},queries:e})}function A(e,t){const n=e.find((e=>{var t;return null===(t=e.legendFormat)||void 0===t?void 0:t.toLowerCase().includes("level")})),r=e.find((e=>e.refId===c.DS||e.refId===c.AA));return n?new s.Es({$data:M(y({datasource:{uid:u.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[I]}):r?(e=e.map((e=>S(y({},e),{get direction(){return((0,v.P)()||(0,m.YM)("sortOrder",o.uH.Descending))===o.uH.Descending?f.t.Backward:f.t.Forward}}))),M(y({datasource:{uid:u.WRAPPED_LOKI_DS_UID},queries:e},t))):new s.Es({$data:M(y({datasource:{uid:u.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[$]})}function M(e){return new s.dt(y({datasource:{uid:u.WRAPPED_LOKI_DS_UID},queries:[]},e))}const R=i.config.featureToggles.logsPanelControls&&(i.config.buildInfo.version>"12.1"||i.config.buildInfo.version.includes("12.1"))},9598:(e,t,n)=>{n.d(t,{Gy:()=>a,_F:()=>i,s_:()=>r});const r=n(2533).id,a=`/a/${r}`;function i(e){return`${a}/${e}`}},7985:(e,t,n)=>{n.d(t,{$k:()=>f,BM:()=>p,CY:()=>O,E3:()=>S,Hs:()=>P,PP:()=>b,Sh:()=>k,VT:()=>C,VW:()=>m,ZX:()=>w,_q:()=>y,by:()=>F,c0:()=>v,l:()=>g,sT:()=>E,tR:()=>j,vC:()=>x});var r=n(9736),a=n(1459),i=n(6464),s=n(6854),o=n(9598),l=n(20);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}function d(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const p=(e,t,n,r)=>d(u(d(u({},h),{refId:t,resource:t}),n),{datasource:{uid:l.gR},expr:e,primaryLabel:r}),g=(e,t)=>d(u({},h,t),{expr:e}),h={editorMode:"code",queryType:"range",refId:"A",supportingQueryType:o.s_},f=(e,t,n,r)=>p(e,t,u({},r),n);function m(e,t){return new i.K(e).getLabelsExpr({ignoreKeys:t})}function v(e){var t,n,r;return e.value?{value:(0,l.OQ)(e.value),valueLabels:[null!==(t=e.label)&&void 0!==t?t:e.value]}:{value:e.value,valueLabels:[null!==(r=null!==(n=e.label)&&void 0!==n?n:e.value)&&void 0!==r?r:""]}}function b(e,t){var n,r,a;const i={parser:null!==(r=null==t||null===(n=t.meta)||void 0===n?void 0:n.parser)&&void 0!==r?r:"mixed",value:null!==(a=e.value)&&void 0!==a?a:""};var s,o;return"structuredMetadata"===i.parser?{value:(0,l.OQ)(i.value),valueLabels:[null!==(s=e.label)&&void 0!==s?s:i.value]}:{value:(0,l.OQ)(JSON.stringify(i)),valueLabels:[null!==(o=e.label)&&void 0!==o?o:i.value]}}function y(e,t){return new i.K(e).getLevelsExpr({ignoreKeys:t})}function S(e,t){return new i.K(e).getMetadataExpr({ignoreKeys:t})}function w(e,t){return new i.K(e).getFieldsExpr({ignoreKeys:t})}function O(e){return(0,a.F)(e),e.map((e=>{if(!e.value)return"";const t=function(e){var t,n,a;return e.operator===s.cK.match||e.operator===s.cK.negativeMatch?e.key===s.ld.caseInsensitive?r.Go.escapeLabelValueInRegexSelector(null!==(t=e.value)&&void 0!==t?t:""):r.Go.escapeLabelValueInExactSelector(null!==(n=e.value)&&void 0!==n?n:""):r.Go.escapeLabelValueInExactSelector(null!==(a=e.value)&&void 0!==a?a:"")}(e);return function(e,t){return e.key===s.ld.caseInsensitive?e.operator===s.cK.negativeRegex||e.operator===s.cK.negativeMatch?`${s.cK.negativeRegex} "(?i)${t}"`:`${s.cK.regex} "(?i)${t}"`:`${e.operator} "${t}"`}(e,t)})).join(" ")}function x(e){return".+"===e?e:"(?i).*"!==e.substring(0,6)?`(?i).*${e}.*`:e}function E(e){return"(?i).*"===e.substring(0,6)&&".*"===e.slice(-2)?e.slice(6).slice(0,-2):e}function C(e){return e.replace(/\s*,\s*}/,"}")}function k(e,t){let n=r.jh.interpolate(e,t);return n.includes(l.fK)&&(n=r.jh.interpolate(e,n)),n}function P(){return e=>e.map((e=>`${e.key}${e.operator}"${e.value}"`)).join(",")}function j(){return e=>{if(e.length){return`| line_format "{{.${e.map((e=>e.key)).join("_")}}}"`}return""}}const F=1e3},9683:(e,t,n)=>{n.d(t,{FT:()=>b,HU:()=>h,KL:()=>f,NX:()=>x,W6:()=>y,XJ:()=>O,Zt:()=>m,bw:()=>p,er:()=>S,mC:()=>g,qe:()=>w,tm:()=>v});var r=n(7781),a=n(8531),i=n(7839),s=n(7389),o=n(5953),l=n(9598),c=n(5553),u=n(20);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const p={explore:()=>(0,l._F)(i.G3.explore),fields:(e,t="service")=>(0,l._F)(`${i.G3.explore}/${t}/${(0,s.uu)(e)}/${i.G3.fields}`),labels:(e,t="service")=>(0,l._F)(`${i.G3.explore}/${t}/${(0,s.uu)(e)}/${i.G3.labels}`),logs:(e,t="service")=>(0,l._F)(`${i.G3.explore}/${t}/${(0,s.uu)(e)}/${i.G3.logs}`),patterns:(e,t="service")=>(0,l._F)(`${i.G3.explore}/${t}/${(0,s.uu)(e)}/${i.G3.patterns}`)},g={field:(e,t="service",n)=>(0,l._F)(`${i.G3.explore}/${t}/${(0,s.uu)(e)}/${i._J.field}/${n}`),label:(e,t="service",n)=>(0,l._F)(`${i.G3.explore}/${t}/${(0,s.uu)(e)}/${i._J.label}/${n}`)},h={explore:`${i.G3.explore}/*`,fields:`:labelName/:labelValue/${i.G3.fields}`,labels:`:labelName/:labelValue/${i.G3.labels}`,logs:`:labelName/:labelValue/${i.G3.logs}`,patterns:`:labelName/:labelValue/${i.G3.patterns}`},f={field:`:labelName/:labelValue/${i._J.field}/:breakdownLabel`,label:`:labelName/:labelValue/${i._J.label}/:breakdownLabel`},m=["from","to",`var-${u.EY}`,`var-${u.MB}`],v=["from","to","mode","urlColumns","visualizationType","selectedLine","displayedFields","panelState",u.uw,`var-${u.uw}`,`var-${u.EY}`,`var-${u.MB}`,`var-${u.mB}`,`var-${u._Y}`,`var-${u.LI}`,`var-${u.Jg}`,`var-${u.EY}`,`var-${u.WM}`,`var-${u._P}`,`var-${u.NW}`,`var-${u.lV}`,`var-${u.pw}`];function b(){const e=a.locationService.getLocation();return e.pathname.slice(e.pathname.lastIndexOf("/")+1,e.pathname.length)}function y(){const e=a.locationService.getLocation(),t="/a/grafana-lokiexplore-app/explore",n=e.pathname.slice(e.pathname.indexOf(t)+34+1).split("/");let r=n[0];const i=n[1],s=n[3];return r===u.OX&&(r=u.ky),{breakdownLabel:s,labelName:r,labelValue:i}}function S(){const e=a.locationService.getLocation().pathname.split("/");return e[e.length-2]}function w(e,t){return r.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}({},Object.entries(r.urlUtil.getUrlSearchParams()).reduce(((e,[t,n])=>(m.includes(t)&&(e[t]=n),e)),{}),e)}(t))}function O(e){return{breakdownLabel:e.params.breakdownLabel,labelName:e.params.labelName,labelValue:e.params.labelValue}}function x(e){const t=(0,c.cR)(e);let{labelName:n,labelValue:r}=y();n===u.ky&&(n=u.OX);if(!t.state.filters.find((e=>e.key===n))){const e=a.locationService.getLocation();o.v.info("invalid primary label name in url",{labelName:n,url:`${e.pathname}${e.search}`})}if(!t.state.filters.find((e=>(0,s.uu)(e.value)===r))){const e=a.locationService.getLocation();o.v.info("invalid primary label value in url",{labelValue:r,url:`${e.pathname}${e.search}`})}}},5719:(e,t,n)=>{n.d(t,{Mq:()=>p,Ti:()=>c,U4:()=>u,UX:()=>m,hJ:()=>g,m0:()=>v,oh:()=>f,u9:()=>d});n(7781);var r=n(8531),a=n(9736),i=n(5953),s=(n(9683),n(20)),o=n(2661);function l(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function c(e){return a.jh.getAncestor(e,o.P)}function u(e){return a.jh.interpolate(e,s.gR)}function d(e){return a.jh.interpolate(e,s.SA).replace(/\s+/g," ")}function p(e){return a.jh.interpolate(e,s.FX).replace(/\s+/g," ")}function g(e){return h.apply(this,arguments)}function h(){var e;return e=function*(e){return yield(0,r.getDataSourceSrv)().get(s.gR,{__sceneObject:{value:e}})},h=function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function s(e){l(i,r,a,s,o,"next",e)}function o(e){l(i,r,a,s,o,"throw",e)}s(void 0)}))},h.apply(this,arguments)}function f(e){return a.jh.findDescendents(e,a.dt)}function m(e,t,n){const r=a.jh.findObject(e,t);return r instanceof n?r:(null!==r&&i.v.warn(`invalid return type: ${n.toString()}`),null)}function v(e){var t;return null===(t=e.state.controls)||void 0===t?void 0:t.find((e=>e instanceof a.KE))}},9193:(e,t,n)=>{n.d(t,{E:()=>o,X:()=>s});var r=n(7993),a=n(3241);const i=new r.A({intraDel:1,intraIns:1,intraMode:1,intraSub:1,intraTrn:1});function s(e,t,n){const[a,s,o]=i.search(e,t,0,1e5);let l=[],c=new Set;if(a&&o){const t=(e,t)=>{t&&c.add(e)};for(let n=0;n<o.length;n++){let a=o[n];r.A.highlight(e[s.idx[a]],s.ranges[a],t),l.push(e[s.idx[a]])}n([l,[...c]])}else t||n([])}const o=(0,a.debounce)(s,300)},2601:(e,t,n)=>{n.r(t),n.d(t,{DEFAULT_SORT_BY:()=>u,calculateDataFrameChangepoints:()=>p,calculateOutlierValue:()=>m,sortSeries:()=>d,sortSeriesByName:()=>g,wasmSupported:()=>v});var r=n(1854),a=n(6944),i=n(3241),s=n(7781),o=n(4509),l=n(5570),c=n(5953);const u="changepoint",d=(0,i.memoize)(((e,t,n)=>{if("alphabetical"===t)return g(e,n);"outliers"===t&&h(e);const r=n=>{var r;try{if("changepoint"===t)return p(n);if("outliers"===t)return m(e,n)}catch(e){c.v.error(e,{msg:"failed to sort"}),t=s.ReducerID.stdDev}const a=s.fieldReducers.get(t);var i;var o;return null!==(o=(null!==(i=null===(r=a.reduce)||void 0===r?void 0:r.call(a,n.fields[1],!0,!0))&&void 0!==i?i:(0,s.doStandardCalcs)(n.fields[1],!0,!0))[t])&&void 0!==o?o:0},a=e.map((e=>({dataFrame:e,value:r(e)})));return a.sort(((e,t)=>void 0!==e.value&&void 0!==t.value?t.value-e.value:0)),"asc"===n&&a.reverse(),a.map((({dataFrame:e})=>e))}),((e,t,n)=>{const r=e.length>0?e[0].fields[0].values[0]:0,a=e.length>0?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0,i=e.length>0?(0,l.H7)(e[0]):"",s=e.length>0?(0,l.H7)(e[e.length-1]):"",o=e.map((e=>e.length+"_"+e.fields.map((e=>e.name+"_"+e.values[0]+"_"+e.values[e.values.length-1]))));return`${i}_${s}_${r}_${a}_${e.length}_${o}_${t}_${n}`})),p=e=>{if(!v())throw new Error("WASM not supported, fall back to stdDev");const t=e.fields.filter((e=>e.type===s.FieldType.number)),n=t[0].values.length;let a=Math.floor(n/100)||1;a>1&&(a=Math.ceil(a/2));const i=t[0].values.filter(((e,t)=>t%a==0)),o=new Float64Array(i);return r.ChangepointDetector.defaultArgpcp().detectChangepoints(o).indices.length},g=(e,t)=>{const n=[...e];return n.sort(((e,t)=>{const n=(0,l.H7)(e),r=(0,l.H7)(t);return n&&r&&null!==(a=null==n?void 0:n.localeCompare(r))&&void 0!==a?a:0;var a})),"desc"===t&&n.reverse(),n},h=e=>{if(!v())return;const t=(0,s.outerJoinDataFrames)({frames:e});if(!t)return;const n=t.fields.filter((e=>e.type===s.FieldType.number)).flatMap((e=>new Float64Array(e.values)));try{const e=a.OutlierDetector.dbscan({sensitivity:.4}).preprocess(n);f=e.detect()}catch(e){c.v.error(e,{msg:"initOutlierDetector: OutlierDetector error"})}};let f;const m=(e,t)=>{if(!v())throw new Error("WASM not supported, fall back to stdDev");if(!f)throw new Error("Initialize outlier detector first");const n=e.indexOf(t);return f.seriesResults[n].isOutlier?f.seriesResults[n].outlierIntervals.length:0},v=()=>{const e="object"==typeof WebAssembly;return e||(0,o.EE)(o.NO.service_details,o.ir.service_details.wasm_not_supported),e}},4351:(e,t,n)=>{n.d(t,{Bq:()=>G,Dy:()=>ie,GL:()=>U,Gg:()=>b,IL:()=>N,IW:()=>Z,KH:()=>re,N$:()=>j,OB:()=>f,QB:()=>O,Qi:()=>z,RN:()=>B,Rb:()=>te,Rf:()=>A,WO:()=>_,Xo:()=>H,YK:()=>$,YM:()=>D,ZF:()=>F,Zs:()=>J,cC:()=>m,cO:()=>v,eT:()=>h,ex:()=>X,fq:()=>k,hp:()=>Q,k5:()=>R,ke:()=>x,o5:()=>V,og:()=>q,sB:()=>L,sj:()=>y,uF:()=>ae,vR:()=>T,vs:()=>C,zu:()=>se});var r=n(7781),a=n(2533),i=n(8839),s=n(5953),o=n(8428),l=n(5553),c=n(20);const u=`${a.id}.services.favorite`,d=`${a.id}.primarylabels.tabs.favorite`,p=`${a.id}.datasource`,g=`${a.id}.scene.layout`;function h(e,t){if(!e||"string"!=typeof e)return[];const n=S(e,t);let r=[];try{r=(0,o.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){s.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(r)||(r=[]),r}function f(e,t,n){if(!e||"string"!=typeof e)return;const r=S(e,t);let a=[];try{a=(0,o.aJ)(JSON.parse(localStorage.getItem(r)||"[]"))}catch(e){s.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const i=a.filter((e=>e!==n));i.unshift(n),localStorage.setItem(r,JSON.stringify(i))}function m(e,t,n){if(!e||!t||!n||"string"!=typeof e)return;const r=S(e,t);let a=[];try{a=(0,o.aJ)(JSON.parse(localStorage.getItem(r)||"[]"))}catch(e){s.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const i=a.filter((e=>e!==n));localStorage.setItem(r,JSON.stringify(i))}function v(e,t){if(!e||!t)return;const n=w(e);let r=[];try{r=(0,o.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){s.v.error(e,{msg:"Error parsing saved tabs from local storage"})}if(Array.isArray(r)||(r=[]),-1===r.indexOf(t)){const e=r.filter((e=>e!==t));e.unshift(t),localStorage.setItem(n,JSON.stringify(e))}}function b(e,t){if(!e||!t)return;const n=w(e);let r=[];try{r=(0,o.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){s.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(r)||(r=[]);const a=r.filter((e=>e!==t));localStorage.setItem(n,JSON.stringify(a))}function y(e){if(!e||"string"!=typeof e)return[];const t=w(e);let n=[];try{n=(0,o.aJ)(JSON.parse(localStorage.getItem(t)||"[]"))}catch(e){s.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(n)||(n=[]),n}function S(e,t){return t=t===c.OX?"":`_${t}`,`${u}_${e}${t}`}function w(e){return`${d}_${e}`}function O(){var e;return null!==(e=localStorage.getItem(p))&&void 0!==e?e:void 0}function x(e){localStorage.setItem(p,e)}const E=`${a.id}.values.sort`;function C(e,t,n){var r;const a=(null!==(r=localStorage.getItem(`${E}.${e}.by`))&&void 0!==r?r:"").split(".");if(!a[0]||!a[1])return{direction:n,sortBy:t};const i=a[0];return{direction:a[1],sortBy:i}}function k(e,t,n){t&&n&&localStorage.setItem(`${E}.${e}.by`,`${t}.${n}`)}function P(e){return`${(0,l.nH)(e)}.${(0,l.p_)(e)}`}function j(e){const t=P(e),n=localStorage.getItem(`${a.id}.${t}.logs.fields`);var r;return n&&null!==(r=(0,o.aJ)(JSON.parse(n)))&&void 0!==r?r:[]}function F(e,t){const n=P(e);localStorage.setItem(`${a.id}.${n}.logs.fields`,JSON.stringify(t))}function L(e){const t=P(e),n=localStorage.getItem(`${a.id}.${t}.logs.dedupStrategy`);return n&&(0,i.K)(n)?n:r.LogsDedupStrategy.none}function _(e,t){const n=P(e);localStorage.setItem(`${a.id}.${n}.logs.dedupStrategy`,t)}const T="grafana.explore.logs";function D(e,t){const n=localStorage.getItem(`${T}.${e}`);return n||t}function N(e,t){const n=localStorage.getItem(`${T}.${e}`);return null===n?t:""!==n&&"false"!==n}function $(e,t){let n=t.toString();localStorage.setItem(`${T}.${e}`,n)}const I="grafana.explore.logs.logsVolume";function B(e,t){const n=`${I}.${e}`;void 0!==t?localStorage.setItem(n,t):localStorage.removeItem(n)}function A(e){return Boolean(localStorage.getItem(`${I}.${e}`))}const M="grafana.explore.logs.visualisationType";function R(){var e;const t=null!==(e=localStorage.getItem(M))&&void 0!==e?e:"";switch(t){case"table":case"logs":return t;case"json":return"json";default:return"logs"}}function V(e){localStorage.setItem(M,e)}const W=`${a.id}.jsonParser.visible`;function z(){return!!localStorage.getItem(W)}const K=`${a.id}.linefilter.option`;function H(e){let t=e.toString();e||(t=""),localStorage.setItem(`${K}.caseSensitive`,t)}function U(e){let t=e.toString();e||(t=""),localStorage.setItem(`${K}.regex`,t)}function G(e){let t=e.toString();e||(t=""),localStorage.setItem(`${K}.exclusive`,t)}function Q(e){return"true"===localStorage.getItem(`${K}.caseSensitive`)||e}function q(e){return"true"===localStorage.getItem(`${K}.regex`)||e}function J(e){return"true"===localStorage.getItem(`${K}.exclusive`)||e}const Y=`${a.id}.panel.option`;function X(e,t){const n=localStorage.getItem(`${Y}.${e}`);var r;return null!==n&&null!==(r=t.find((e=>n===e)))&&void 0!==r?r:null}function Z(e,t){localStorage.setItem(`${Y}.${e}`,t)}const ee=`${a.id}.expressionBuilder.debug`;function te(){return!!localStorage.getItem(ee)}const ne=`${a.id}.serviceSelection.pageCount`;function re(){const e=localStorage.getItem(ne);return e?parseInt(e,10):void 0}function ae(e){localStorage.setItem(ne,e.toString(10))}function ie(){return localStorage.getItem(g)}function se(e){localStorage.setItem(g,e)}},3571:(e,t,n)=>{n.d(t,{b:()=>r});const r={appConfig:{apiKey:"data-testid ac-api-key",apiUrl:"data-testid ac-api-url",container:"data-testid ac-container",submit:"data-testid ac-submit-form"},breakdowns:{common:{filterButton:"data-testid filter-button",filterButtonGroup:"data-testid filter-button-group",filterNumericPopover:{cancelButton:"data-testid filter-numeric-cancel",inputGreaterThan:"data-testid filter-numeric-gt",inputGreaterThanInclusive:"data-testid filter-numeric-gte",inputGreaterThanUnit:"data-testid filter-numeric-gtu",inputLessThan:"data-testid filter-numeric-lt",inputLessThanInclusive:"data-testid filter-numeric-lte",inputLessThanUnit:"data-testid filter-numeric-ltu",removeButton:"data-testid filter-numeric-remove",submitButton:"data-testid filter-numeric-submit"},filterSelect:"data-testid filter-select",sortByDirection:"data-testid SortBy direction",sortByFunction:"data-testid SortBy function"},fields:{},labels:{}},exploreServiceDetails:{buttonFilterExclude:"data-testid button-filter-exclude",buttonFilterInclude:"data-testid button-filter-include",buttonRemovePattern:"data-testid button-remove-pattern",openExplore:"data-testid open-explore",searchLogs:"data-testid search-logs",tabFields:"data-testid tab-fields",tabLabels:"data-testid tab-labels",tabLogs:"data-testid tab-logs",tabPatterns:"data-testid tab-patterns"},exploreServiceSearch:{search:"data-testid search-services"},header:{refreshPicker:"data-testid RefreshPicker run button"},index:{addNewLabelTab:"data-testid Tab Add label",aggregatedMetricsMenu:"data-testid aggregated-metrics-menu",aggregatedMetricsToggle:"data-testid aggregated-metrics-toggle",header:{showLogsButton:"data-testid Show logs header"},searchLabelValueInput:"data-testid search-services-input",selectServiceButton:"data-testid button-select-service",showLogsButton:"data-testid button-filter-include"},logsPanelHeader:{header:"data-testid Panel header Logs",radio:"data-testid radio-button"},patterns:{buttonExcludedPattern:"data-testid button-excluded-pattern",buttonIncludedPattern:"data-testid button-included-pattern",tableWrapper:"data-testid table-wrapper"},table:{inspectLine:"data-testid inspect",rawLogLine:"data-testid raw-log-line",wrapper:"data-testid table-wrapper"},variables:{combobox:{},datasource:{label:"data-testid Dashboard template variables submenu Label Data source"},levels:{inputWrap:"data-testid detected_level filter variable"},serviceName:{label:"data-testid Dashboard template variables submenu Label Labels"}}}},5002:(e,t,n)=>{n.d(t,{Dk:()=>s,EJ:()=>c,Ki:()=>u,Zr:()=>l,gW:()=>o});var r=n(7781),a=n(8531),i=n(5953);const s=e=>{if(navigator.clipboard&&window.isSecureContext)navigator.clipboard.writeText(e);else{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t)}};const o=(e,t,n)=>{const i=a.locationService.getLocation(),s=r.urlUtil.getUrlSearchParams();return s.from=n.from.toISOString(),s.to=n.to.toISOString(),s[e]=JSON.stringify(t),o=r.urlUtil.renderUrl(i.pathname,s),`${window.location.protocol}//${window.location.host}${a.config.appSubUrl}${o}`;var o};function l(e){return e.length?(null==e?void 0:e.charAt(0).toUpperCase())+e.slice(1):(i.v.warn("invalid string argument"),e)}function c(e,t,n){return e.substring(0,t)+(n&&e.length>t?"…":"")}function u(e){const t=(0,r.dateTime)(e.timeEpochMs-1),n=(0,r.dateTime)(e.timeEpochMs+1);return{from:t,raw:{from:t,to:n},to:n}}},5553:(e,t,n)=>{n.d(t,{BL:()=>P,DX:()=>c,El:()=>k,Gk:()=>O,Hj:()=>S,Ku:()=>u,P4:()=>b,Rr:()=>v,S9:()=>w,U2:()=>j,WA:()=>F,YS:()=>h,aW:()=>p,bY:()=>x,bu:()=>_,cR:()=>d,eY:()=>E,h:()=>C,ir:()=>f,iw:()=>m,n5:()=>L,nH:()=>N,oY:()=>g,p_:()=>D,vm:()=>y,z2:()=>T});var r=n(9736),a=n(4702),i=n(8502),s=n(5953),o=n(8428),l=n(20);function c(e){const{fieldExpressionToAdd:t="",jsonParserPropToAdd:n="",labelExpressionToAdd:r="",parser:a,structuredMetadataToAdd:i=""}=e;switch(a){case"structuredMetadata":return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} ${t} ${l.Oc}`;case"json":return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} | json ${n} ${l.fK} | drop __error__, __error_details__ ${t} ${l.Oc}`;case"logfmt":return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} ${l.mF} ${t} ${l.Oc}`;default:return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} | json ${n} ${l.fK} | logfmt | drop __error__, __error_details__  ${t} ${l.Oc}`}}function u(e){const t=r.jh.lookupVariable(l.uw,e);if(!(t instanceof r.yP))throw new Error("VAR_PATTERNS not found");return t}function d(e){return x(l.MB,e)}function p(e){return x(l.fi,e)}function g(e){return x(l._P,e)}function h(e){return x(l.sL,e)}function f(e){return x(l.mB,e)}function m(e){return x(l._Y,e)}function v(e){const t=r.jh.lookupVariable(l.WM,e);if(!(t instanceof r.H9))throw new Error("VAR_LINE_FILTER not found");return t}function b(e){const t=r.jh.lookupVariable(l.Jg,e);if(!(t instanceof a.m))throw new Error("VAR_LABEL_GROUP_BY not found");return t}function y(e){const t=r.jh.lookupVariable(l.Wi,e);if(!(t instanceof a.m))throw new Error("SERVICE_LABEL_VAR not found");return t}function S(e){const t=r.jh.lookupVariable(l.LI,e);if(!(t instanceof a.m))throw new Error("VAR_FIELD_GROUP_BY not found");return t}function w(e){const t=r.jh.lookupVariable(l.EY,e);if(!(t instanceof r.mI))throw new Error("VAR_DATASOURCE not found");return t}function O(e){const t=r.jh.lookupVariable(l.NW,e);if(!(t instanceof r.H9))throw new Error("VAR_LINE_FILTERS not found");return t}function x(e,t){const n=r.jh.lookupVariable(e,t);if(!(n instanceof r.H9))throw new Error(`Could not get AdHocFiltersVariable ${e}. Variable not found.`);return n}function E(e){const t=r.jh.lookupVariable(l.Du,e);if(!(t instanceof a.m))throw new Error("VAR_PRIMARY_LABEL_SEARCH not found");return t}function C(e){E(e).setState({label:"",value:".+"})}function k(e){const t=r.jh.lookupVariable(l.Gb,e);if(!(t instanceof r.H9))throw new Error("VAR_PRIMARY_LABEL not found");return t}function P(e,t){k(t).setState({filters:[{key:e,operator:"=~",value:".+"}]})}function j(e){const t=r.jh.lookupVariable(l.pw,e);if(!(t instanceof r.H9))throw new Error("VAR_JSON_PARSER not found!");return t}function F(e){const t=r.jh.lookupVariable(l.lV,e);if(!(t instanceof r.H9))throw new Error("VAR_JSON_FIELDS not found!");return t}function L(e){return`var-${e}`}function _(e,t=l.mB){if((0,i.OH)(e))return{parser:"structuredMetadata",value:e.value};try{const t=(0,l.zE)(e.value)?(0,l.Dx)(e.value):e.value,n=(0,o.fS)(JSON.parse(t));if(!1!==n)return n;throw new o.QX("getValueFromFieldsFilter: invalid filter value!")}catch(n){if(n instanceof o.QX?s.v.error(n,{msg:`getValueFromFieldsFilter: Failed to validate ${t}`,value:e.value}):s.v.error(n,{msg:`getValueFromFieldsFilter: Failed to parse ${t}`,value:e.value}),e.value)return{parser:"mixed",value:e.value};throw n}}function T(e,t){return e===l.mB&&t?_(t):{value:null==t?void 0:t.value}}function D(e){return function(e){const t=e.filters.filter((e=>e.key===l.OX)).map((e=>e.value));if(!t)throw new Error("Service present in filters selected");return t[0]}(d(e).state)}function N(e){return w(e).getValue()}},5548:(e,t,n)=>{n.d(t,{Ht:()=>g,mE:()=>d,rA:()=>p});var r=n(9736),a=n(2661),i=n(4702),s=n(6854),o=n(708),l=n(4532),c=n(9683),u=n(20);function d(e){const t=r.jh.getVariables(e);let n=[];for(const e of t.state.variables)e instanceof r.H9&&e.state.filters.length&&n.push(e),e instanceof i.m&&e.state.value&&"logsFormat"!==e.state.name&&n.push(e);return n}function p(e){const t=r.jh.getAncestor(e,a.P);t.setState({patterns:[]});d(t).forEach((e=>{if(e instanceof r.H9&&"adhoc_service_filter"===e.state.key){let{labelName:t}=(0,c.W6)();t===u.ky&&(t=u.OX),e.setState({filters:e.state.filters.filter((e=>e.key===t))})}else e instanceof r.H9?e.setState({filters:[]}):e instanceof i.m&&e.setState({text:"",value:""})}))}const g=function(e){const t=e.state._wip;if(t&&e.state.filters.some((e=>e.key===t.key&&e.operator===s.w7.Equal)))return l._i;const n=e.state.name===u.MB,r=e.state.filters.filter((e=>(0,o.BG)(e.operator))).length,a=!(null==t?void 0:t.key)&&1===r,i=(null==t?void 0:t.key)&&r<1;if(n&&(a||i))return l._i;if(null==t?void 0:t.meta){const e=t.meta.type;if("float"===e||"bytes"===e||"duration"===e)return l.hI}return l.II}}}]);
//# sourceMappingURL=328.js.map?_cache=1bae26718376f8fa2e8a