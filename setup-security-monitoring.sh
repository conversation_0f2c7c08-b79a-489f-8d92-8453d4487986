#!/bin/bash
# CTFd Security Monitoring - Complete Setup Script
# This script sets up everything needed for the security monitoring system

set -e  # Exit on error

echo "================================================"
echo "CTFd Security Monitoring - Complete Setup"
echo "================================================"
echo

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "Checking prerequisites..."
if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Prerequisites met"
echo

# Detect docker-compose command
if docker compose version >/dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

# Create all necessary directories
echo "Creating directory structure..."
directories=(
    ".data/CTFd/logs"
    ".data/CTFd/uploads"
    ".data/nginx/logs"
    ".data/mysql"
    ".data/redis"
    ".data/prometheus"
    ".data/grafana"
    ".data/loki"
    "logs"
    "monitoring/grafana/provisioning/dashboards"
    "monitoring/grafana/provisioning/datasources"
    "conf/nginx"
    "CTFd/plugins/security_monitor/templates/security_monitor/admin"
    "CTFd/plugins/security_monitor/assets"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
    echo "  ✅ Created $dir"
done

# Set permissions
chmod -R 755 .data/
chmod -R 755 logs/
echo "✅ Directory structure created"
echo

# Update docker-compose.yml to remove version warning
echo "Updating docker-compose.yml..."
if grep -q "^version:" docker-compose.yml 2>/dev/null; then
    # Remove version line
    sed -i '/^version:/d' docker-compose.yml
    # Remove empty line after version if exists
    sed -i '/^$/N;/^\n$/d' docker-compose.yml
    echo "  ✅ Removed obsolete version attribute"
fi

# Ensure security_monitor is in PLUGIN_WHITELIST
if ! grep -q "security_monitor" docker-compose.yml; then
    sed -i 's/PLUGIN_WHITELIST=\(.*\)/PLUGIN_WHITELIST=\1,security_monitor/' docker-compose.yml
    echo "  ✅ Added security_monitor to PLUGIN_WHITELIST"
else
    echo "  ✅ security_monitor already in PLUGIN_WHITELIST"
fi

# Fix promtail volume paths
if grep -q "/var/lib/docker/containers" docker-compose.yml; then
    # Create a temporary file with the fixed content
    awk '
    /promtail:/ { in_promtail = 1 }
    in_promtail && /volumes:/ { in_volumes = 1 }
    in_promtail && in_volumes && /command:/ { in_volumes = 0 }
    {
        if (in_promtail && in_volumes) {
            if ($0 ~ /\/var\/lib\/docker\/containers/ || $0 ~ /- \/var\/log:\/var\/log:ro/) {
                next  # Skip these lines
            }
            # Fix paths to use ./
            gsub(/- \.data/, "- ./.data", $0)
        }
        print
    }
    ' docker-compose.yml > docker-compose.yml.tmp
    mv docker-compose.yml.tmp docker-compose.yml
    echo "  ✅ Fixed promtail volume paths"
fi

echo "✅ docker-compose.yml updated"
echo

# Check if nginx security.conf exists
if [ ! -f "conf/nginx/security.conf" ]; then
    echo "Creating nginx security configuration..."
    cat > conf/nginx/security.conf << 'EOF'
# Rate limiting zones
limit_req_zone $binary_remote_addr zone=strict:10m rate=2r/s;
limit_req_zone $binary_remote_addr zone=normal:10m rate=20r/s;
limit_conn_zone $binary_remote_addr zone=addr:10m;

server {
    listen 80;
    server_name localhost;

    # Strict limits for sensitive endpoints
    location ~ ^/(login|register|api/v1/challenges/.*/attempt) {
        limit_req zone=strict burst=5 nodelay;
        limit_req_status 429;
        limit_conn addr 10;
        proxy_pass http://ctfd:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Normal limits for everything else
    location / {
        limit_req zone=normal burst=50 nodelay;
        limit_conn addr 100;
        proxy_pass http://ctfd:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
    echo "  ✅ Created nginx security.conf"
fi

# Verify Security Monitor plugin files exist
echo "Checking Security Monitor plugin..."
plugin_files=(
    "CTFd/plugins/security_monitor/__init__.py"
    "CTFd/plugins/security_monitor/models.py"
    "CTFd/plugins/security_monitor/monitor.py"
    "CTFd/plugins/security_monitor/routes.py"
    "CTFd/plugins/security_monitor/alerts.py"
    "CTFd/plugins/security_monitor/prometheus.py"
)

all_files_exist=true
for file in "${plugin_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "  ❌ Missing: $file"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = true ]; then
    echo "✅ All Security Monitor plugin files present"
else
    echo "❌ Some plugin files are missing. Please ensure the security_monitor plugin is properly installed."
    exit 1
fi

# Stop existing containers
echo
echo "Stopping existing containers..."
$DOCKER_COMPOSE down

# Clean up any stuck containers
echo "Cleaning up..."
docker system prune -f >/dev/null 2>&1 || true

# Start containers
echo
echo "Starting containers..."
$DOCKER_COMPOSE up -d

# Wait for services to start
echo
echo "Waiting for services to start..."
sleep 10

# Check container status
echo
echo "Container Status:"
$DOCKER_COMPOSE ps

# Function to wait for service
wait_for_service() {
    local service=$1
    local url=$2
    local max_attempts=30
    local attempt=0
    
    echo -n "Waiting for $service to be ready..."
    while [ $attempt -lt $max_attempts ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            echo " ✅ Ready!"
            return 0
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    echo " ⚠️  Timeout (service may still be starting)"
    return 1
}

# Wait for services
echo
wait_for_service "CTFd" "http://localhost:8000"
wait_for_service "Grafana" "http://localhost:3000"
wait_for_service "Prometheus" "http://localhost:9090"

# Check if Security Monitor plugin loaded
echo
echo "Checking Security Monitor plugin status..."
if $DOCKER_COMPOSE logs ctfd 2>&1 | grep -q "Loaded module.*security_monitor"; then
    echo "✅ Security Monitor plugin loaded successfully!"
else
    echo "⚠️  Security Monitor plugin may not have loaded yet. Checking detailed logs..."
    $DOCKER_COMPOSE logs ctfd 2>&1 | grep -A5 -B5 "plugins" | tail -20
fi

# Create a convenience script for checking status
cat > check-security-status.sh << 'EOF'
#!/bin/bash
echo "=== CTFd Security Monitor Status ==="
echo
echo "Container Status:"
docker compose ps | grep -E "(ctfd|grafana|prometheus|loki)"
echo
echo "Plugin Loading Status:"
docker compose logs ctfd 2>&1 | grep -i "security_monitor" || echo "No security_monitor entries found in logs"
echo
echo "Access URLs:"
echo "- CTFd Admin: http://localhost:8000/admin"
echo "- Security Monitor: http://localhost:8000/admin/plugins/security_monitor/dashboard"
echo "- Grafana: http://localhost:3000 (admin/admin)"
echo "- Prometheus: http://localhost:9090"
echo
echo "Recent Errors (if any):"
docker compose logs ctfd 2>&1 | grep -i error | tail -5
EOF
chmod +x check-security-status.sh

# Final summary
echo
echo "================================================"
echo "Setup Complete!"
echo "================================================"
echo
echo "Access Points:"
echo "  📊 CTFd Admin Panel: http://localhost:8000/admin"
echo "  🔒 Security Monitor: http://localhost:8000/admin/plugins/security_monitor/dashboard"
echo "  📈 Grafana: http://localhost:3000 (login: admin/admin)"
echo "  📉 Prometheus: http://localhost:9090"
echo
echo "Useful Commands:"
echo "  ./check-security-status.sh  - Check security monitoring status"
echo "  $DOCKER_COMPOSE logs ctfd   - View CTFd logs"
echo "  $DOCKER_COMPOSE restart ctfd - Restart CTFd if needed"
echo
echo "Next Steps:"
echo "  1. Login to CTFd admin panel"
echo "  2. Look for 'Security Monitor' in the admin menu"
echo "  3. If not visible, run: $DOCKER_COMPOSE restart ctfd"
echo
echo "================================================"
