"""
Prometheus metrics for Security Monitor plugin
"""

from flask import Response
from datetime import datetime, timedelta
from CTFd.models import db
from .models import SecurityEvent, SecurityAlert


def register_prometheus_metrics(app, blueprint):
    """Register Prometheus metrics endpoint"""
    
    @blueprint.route('/api/prometheus')
    def prometheus_metrics():
        """Export metrics in Prometheus format"""
        from CTFd.utils.user import is_admin
        
        # Check if user is admin
        if not is_admin():
            return Response('Unauthorized', status=403)
        
        output = []
        
        try:
            # Security events metrics
            now = datetime.utcnow()
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)
            
            # Total events
            total_events = SecurityEvent.query.count()
            output.append('# HELP ctfd_security_events_total Total number of security events')
            output.append('# TYPE ctfd_security_events_total counter')
            output.append(f'ctfd_security_events_total {total_events}')
            
            # Events by type
            event_counts = db.session.query(
                SecurityEvent.event_type,
                db.func.count(SecurityEvent.id).label('count')
            ).group_by(SecurityEvent.event_type).all()
            
            output.append('# HELP ctfd_security_events_by_type Security events by type')
            output.append('# TYPE ctfd_security_events_by_type counter')
            for event_type, count in event_counts:
                if event_type:
                    output.append(f'ctfd_security_events_by_type{{type="{event_type}"}} {count}')
            
            # Recent events (last hour)
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.timestamp > hour_ago
            ).count()
            output.append('# HELP ctfd_security_events_recent Events in the last hour')
            output.append('# TYPE ctfd_security_events_recent gauge')
            output.append(f'ctfd_security_events_recent {recent_events}')
            
            # Failed logins
            failed_logins = SecurityEvent.query.filter_by(
                event_type='failed_login'
            ).filter(
                SecurityEvent.timestamp > day_ago
            ).count()
            output.append('# HELP ctfd_failed_logins_24h Failed logins in last 24 hours')
            output.append('# TYPE ctfd_failed_logins_24h gauge')
            output.append(f'ctfd_failed_logins_24h {failed_logins}')
            
            # Rate limit violations
            rate_violations = SecurityEvent.query.filter_by(
                event_type='rate_limit_exceeded'
            ).filter(
                SecurityEvent.timestamp > hour_ago
            ).count()
            output.append('# HELP ctfd_rate_limit_violations_1h Rate limit violations in last hour')
            output.append('# TYPE ctfd_rate_limit_violations_1h gauge')
            output.append(f'ctfd_rate_limit_violations_1h {rate_violations}')
            
            # Active alerts
            active_alerts = SecurityAlert.query.filter_by(resolved=False).count()
            output.append('# HELP ctfd_security_alerts_active Number of active security alerts')
            output.append('# TYPE ctfd_security_alerts_active gauge')
            output.append(f'ctfd_security_alerts_active {active_alerts}')
            
            # Alerts by severity
            alert_severities = db.session.query(
                SecurityAlert.severity,
                db.func.count(SecurityAlert.id).label('count')
            ).filter_by(
                resolved=False
            ).group_by(SecurityAlert.severity).all()
            
            output.append('# HELP ctfd_security_alerts_by_severity Active alerts by severity')
            output.append('# TYPE ctfd_security_alerts_by_severity gauge')
            for severity, count in alert_severities:
                if severity:
                    output.append(f'ctfd_security_alerts_by_severity{{severity="{severity}"}} {count}')
            
            # Top IPs by events
            top_ips = db.session.query(
                SecurityEvent.ip_address,
                db.func.count(SecurityEvent.id).label('count')
            ).filter(
                SecurityEvent.timestamp > hour_ago
            ).group_by(
                SecurityEvent.ip_address
            ).order_by(
                db.func.count(SecurityEvent.id).desc()
            ).limit(10).all()
            
            output.append('# HELP ctfd_security_events_by_ip Top IPs by security events')
            output.append('# TYPE ctfd_security_events_by_ip gauge')
            for ip, count in top_ips:
                if ip:
                    # Sanitize IP for Prometheus label
                    safe_ip = ip.replace('.', '_').replace(':', '_')
                    output.append(f'ctfd_security_events_by_ip{{ip="{safe_ip}"}} {count}')
            
        except Exception as e:
            # Log error but continue
            output.append(f'# Error generating metrics: {str(e)}')
        
        return Response('\n'.join(output), mimetype='text/plain')
