/* [create-plugin] version: 5.19.8 */
define(["@emotion/css","@grafana/data","@grafana/runtime","@grafana/ui","lodash","module","prismjs","react","react-dom","react-router","redux","rxjs"],((e,t,n,i,r,o,s,a,u,l,c,d)=>(()=>{var f,p,v,m,g={5438:(e,t,n)=>{"use strict";var i;n.d(t,{$b:()=>i,HT:()=>o,Ic:()=>r}),function(e){e.TRACE="trace",e.DEBUG="debug",e.INFO="info",e.LOG="log",e.WARN="warn",e.ERROR="error"}(i||(i={}));const r=i.LOG,o=[i.TRACE,i.DEBUG,i.INFO,i.L<PERSON>,i.<PERSON>,i.ERROR]},4137:(e,t,n)=>{"use strict";n.d(t,{Gy:()=>o,bw:()=>s,s_:()=>r});var i=n(2533);const r=i.id,o=`/a/${i.id}`,s={Home:"",Trail:"trail",Drilldown:"drilldown"}},6530:(e,t,n)=>{"use strict";var i;n.d(t,{n1:()=>Ki,Js:()=>Xi}),function(e){e.EXCEPTION="exception",e.LOG="log",e.MEASUREMENT="measurement",e.TRACE="trace",e.EVENT="event"}(i||(i={}));const r={[i.EXCEPTION]:"exceptions",[i.LOG]:"logs",[i.MEASUREMENT]:"measurements",[i.TRACE]:"traces",[i.EVENT]:"events"};function o(e,t){return typeof e===t}function s(e,t){return Object.prototype.toString.call(e)===`[object ${t}]`}function a(e,t){try{return e instanceof t}catch(e){return!1}}const u=e=>o(e,"null"),l=e=>o(e,"string"),c=e=>o(e,"number")&&!isNaN(e)||o(e,"bigint"),d=e=>!u(e)&&o(e,"object"),f=e=>o(e,"function"),p=e=>s(e,"Array"),v="undefined"!=typeof Event,m="undefined"!=typeof Error,g=e=>m&&a(e,Error);function h(e){return null==e||(p(e)||l(e)?0===e.length:!!d(e)&&0===Object.keys(e).length)}function b(e={}){return JSON.stringify(null!=e?e:{},function(){const e=new WeakSet;return function(t,n){if(d(n)&&null!==n){if(e.has(n))return null;e.add(n)}return n}}())}function w(e={}){const t={};for(const[n,i]of Object.entries(e))t[n]=d(i)&&null!==i?b(i):String(i);return t}function y(){return Date.now()}function S(){return(new Date).toISOString()}function T(e){return new Date(e).toISOString()}function E(e,t){if(e===t)return!0;if(o(e,"number")&&isNaN(e))return o(t,"number")&&isNaN(t);const n=p(e),i=p(t);if(n!==i)return!1;if(n&&i){const n=e.length;if(n!==t.length)return!1;for(let i=n;0!=i--;)if(!E(e[i],t[i]))return!1;return!0}const r=d(e),s=d(t);if(r!==s)return!1;if(e&&t&&r&&s){const n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(let e of n)if(!i.includes(e))return!1;for(let i of n)if(!E(e[i],t[i]))return!1;return!0}return!1}const x="Error",k=e=>e.map((e=>d(e)?b(e):String(e))).join(" ");let O;function I(e,t,n,r,o,s){var a;t.debug("Initializing exceptions API");let c=null;O=null!==(a=n.parseStacktrace)&&void 0!==a?a:O;const d=e=>{t.debug("Changing stacktrace parser"),O=null!=e?e:O},{ignoreErrors:f=[]}=n;return d(n.parseStacktrace),{changeStacktraceParser:d,getStacktraceParser:()=>O,pushError:(e,{skipDedupe:a,stackFrames:d,type:p,context:v,spanContext:m,timestampOverwriteMs:g}={})=>{if(function(e,t){const{message:n,name:i,stack:r}=t;return o=e,s=n+" "+i+" "+r,o.some((e=>l(e)?s.includes(e):!!s.match(e)));var o,s}(f,e))return;const h={meta:r.value,payload:{type:p||e.name||x,value:e.message,timestamp:g?T(g):S(),trace:m?{trace_id:m.traceId,span_id:m.spanId}:s.getTraceContext(),context:w(Object.assign(Object.assign({},C(e)),null!=v?v:{}))},type:i.EXCEPTION};(null==(d=null!=d?d:e.stack?null==O?void 0:O(e).frames:void 0)?void 0:d.length)&&(h.payload.stacktrace={frames:d});const b={type:h.payload.type,value:h.payload.value,stackTrace:h.payload.stacktrace,context:h.payload.context};a||!n.dedupe||u(c)||!E(b,c)?(c=b,t.debug("Pushing exception\n",h),o.execute(h)):t.debug("Skipping error push because it is the same as the last one\n",h.payload)}}}function C(e){let t=e.cause;return g(t)?t=e.cause.toString():null!==t&&(d(e.cause)||p(e.cause))?t=b(e.cause):null!=t&&(t=e.cause.toString()),null==t?{}:{cause:t}}var P=n(5438);const L=e=>e.map((e=>{try{return String(e)}catch(e){return""}})).join(" ");function j(e,t,n,r,o){t.debug("Initializing API");const s=function(e,t,n,r,o){let s;return t.debug("Initializing traces API"),{getOTEL:()=>s,getTraceContext:()=>{const e=null==s?void 0:s.trace.getSpanContext(s.context.active());return e?{trace_id:e.traceId,span_id:e.spanId}:void 0},initOTEL:(e,n)=>{t.debug("Initializing OpenTelemetry"),s={trace:e,context:n}},isOTELInitialized:()=>!!s,pushTraces:e=>{try{const n={type:i.TRACE,payload:e,meta:r.value};t.debug("Pushing trace\n",n),o.execute(n)}catch(e){t.error("Error pushing trace\n",e)}}}}(0,t,0,r,o);return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},s),I(0,t,n,r,o,s)),function(e,t,n,i){let r,o,s,a;t.debug("Initializing meta API");const u=e=>{o&&i.remove(o),o={user:e},i.add(o)},c=(e,t)=>{var n;const o=null==t?void 0:t.overrides,s=o?{overrides:Object.assign(Object.assign({},null===(n=null==r?void 0:r.session)||void 0===n?void 0:n.overrides),o)}:{};r&&i.remove(r),r={session:Object.assign(Object.assign({},h(e)?void 0:e),s)},i.add(r)},d=()=>i.value.session,f=()=>i.value.page;return{setUser:u,resetUser:u,setSession:c,resetSession:c,getSession:d,setView:(e,t)=>{var n;if((null==t?void 0:t.overrides)&&c(d(),{overrides:t.overrides}),(null===(n=null==s?void 0:s.view)||void 0===n?void 0:n.name)===(null==e?void 0:e.name))return;const r=s;s={view:e},i.add(s),r&&i.remove(r)},getView:()=>i.value.view,setPage:e=>{var t;const n=l(e)?Object.assign(Object.assign({},null!==(t=null==a?void 0:a.page)&&void 0!==t?t:f()),{id:e}):e;a&&i.remove(a),a={page:n},i.add(a)},getPage:f}}(0,t,0,r)),function(e,t,n,r,o,s){var a;t.debug("Initializing logs API");let l=null;const c=null!==(a=n.logArgsSerializer)&&void 0!==a?a:L;return{pushLog:(e,{context:a,level:d,skipDedupe:f,spanContext:p,timestampOverwriteMs:v}={})=>{try{const m={type:i.LOG,payload:{message:c(e),level:null!=d?d:P.Ic,context:w(a),timestamp:v?T(v):S(),trace:p?{trace_id:p.traceId,span_id:p.spanId}:s.getTraceContext()},meta:r.value},g={message:m.payload.message,level:m.payload.level,context:m.payload.context};if(!f&&n.dedupe&&!u(l)&&E(g,l))return void t.debug("Skipping log push because it is the same as the last one\n",m.payload);l=g,t.debug("Pushing log\n",m),o.execute(m)}catch(e){t.error("Error pushing log\n",e)}}}}(0,t,n,r,o,s)),function(e,t,n,r,o,s){t.debug("Initializing measurements API");let a=null;return{pushMeasurement:(e,{skipDedupe:l,context:c,spanContext:d,timestampOverwriteMs:f}={})=>{try{const p={type:i.MEASUREMENT,payload:Object.assign(Object.assign({},e),{trace:d?{trace_id:d.traceId,span_id:d.spanId}:s.getTraceContext(),timestamp:f?T(f):S(),context:w(c)}),meta:r.value},v={type:p.payload.type,values:p.payload.values,context:p.payload.context};if(!l&&n.dedupe&&!u(a)&&E(v,a))return void t.debug("Skipping measurement push because it is the same as the last one\n",p.payload);a=v,t.debug("Pushing measurement\n",p),o.execute(p)}catch(e){t.error("Error pushing measurement\n",e)}}}}(0,t,n,r,o,s)),function(e,t,n,r,o,s){let a=null;return{pushEvent:(e,l,c,{skipDedupe:d,spanContext:f,timestampOverwriteMs:p}={})=>{try{const v={meta:r.value,payload:{name:e,domain:null!=c?c:n.eventDomain,attributes:w(l),timestamp:p?T(p):S(),trace:f?{trace_id:f.traceId,span_id:f.spanId}:s.getTraceContext()},type:i.EVENT},m={name:v.payload.name,attributes:v.payload.attributes,domain:v.payload.domain};if(!d&&n.dedupe&&!u(a)&&E(m,a))return void t.debug("Skipping event push because it is the same as the last one\n",v.payload);a=m,t.debug("Pushing event\n",v),o.execute(v)}catch(e){t.error("Error pushing event",e)}}}}(0,t,n,r,o,s))}function A(){}var _;!function(e){e[e.OFF=0]="OFF",e[e.ERROR=1]="ERROR",e[e.WARN=2]="WARN",e[e.INFO=3]="INFO",e[e.VERBOSE=4]="VERBOSE"}(_||(_={}));const M={debug:A,error:A,info:A,prefix:"Faro",warn:A},R=_.ERROR,D=Object.assign({},console);function N(e=D,t=R){const n=M;return t>_.OFF&&(n.error=t>=_.ERROR?function(...t){e.error(`${n.prefix}\n`,...t)}:A,n.warn=t>=_.WARN?function(...t){e.warn(`${n.prefix}\n`,...t)}:A,n.info=t>=_.INFO?function(...t){e.info(`${n.prefix}\n`,...t)}:A,n.debug=t>=_.VERBOSE?function(...t){e.debug(`${n.prefix}\n`,...t)}:A),n}let U=M;function z(e,t){return U=N(e,t.internalLoggerLevel),U}const F="undefined"!=typeof globalThis?globalThis:void 0!==n.g?n.g:"undefined"!=typeof self?self:void 0;const B="1.14.1";const $="_faroInternal";let V={};function q(e,t,n,i,r,o,s){return t.debug("Initializing Faro"),V={api:o,config:n,instrumentations:s,internalLogger:t,metas:i,pause:r.pause,transports:r,unpatchedConsole:e,unpause:r.unpause},function(e){e.config.isolate?e.internalLogger.debug("Skipping registering internal Faro instance on global object"):(e.internalLogger.debug("Registering internal Faro instance on global object"),Object.defineProperty(F,$,{configurable:!1,enumerable:!1,writable:!1,value:e}))}(V),function(e){if(e.config.preventGlobalExposure)e.internalLogger.debug("Skipping registering public Faro instance in the global scope");else{if(e.internalLogger.debug(`Registering public faro reference in the global scope using "${e.config.globalObjectKey}" key`),e.config.globalObjectKey in F)return void e.internalLogger.warn(`Skipping global registration due to key "${e.config.globalObjectKey}" being used already. Please set "globalObjectKey" to something else or set "preventGlobalExposure" to "true"`);Object.defineProperty(F,e.config.globalObjectKey,{configurable:!1,writable:!1,value:e})}}(V),V}class H{constructor(e,t){var n,i;this.signalBuffer=[],this.itemLimit=null!==(n=null==t?void 0:t.itemLimit)&&void 0!==n?n:50,this.sendTimeout=null!==(i=null==t?void 0:t.sendTimeout)&&void 0!==i?i:250,this.paused=(null==t?void 0:t.paused)||!1,this.sendFn=e,this.flushInterval=-1,this.paused||this.start(),document.addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&this.flush()}))}addItem(e){this.paused||(this.signalBuffer.push(e),this.signalBuffer.length>=this.itemLimit&&this.flush())}start(){this.paused=!1,this.sendTimeout>0&&(this.flushInterval=window.setInterval((()=>this.flush()),this.sendTimeout))}pause(){this.paused=!0,clearInterval(this.flushInterval)}groupItems(e){const t=new Map;return e.forEach((e=>{const n=JSON.stringify(e.meta);let i=t.get(n);i=void 0===i?[e]:[...i,e],t.set(n,i)})),Array.from(t.values())}flush(){if(this.paused||0===this.signalBuffer.length)return;this.groupItems(this.signalBuffer).forEach(this.sendFn),this.signalBuffer=[]}}function W(e,t,n,i){var r;t.debug("Initializing transports");const o=[];let s=n.paused,a=[];const u=e=>{let t=e;for(const e of a){const n=t.map(e).filter(Boolean);if(0===n.length)return[];t=n}return t},l=e=>{const n=u(e);if(0!==n.length)for(const e of o)t.debug(`Transporting item using ${e.name}\n`,n),e.isBatched()&&e.send(n)};let c;(null===(r=n.batching)||void 0===r?void 0:r.enabled)&&(c=new H(l,{sendTimeout:n.batching.sendTimeout,itemLimit:n.batching.itemLimit,paused:s}));return{add:(...r)=>{t.debug("Adding transports"),r.forEach((r=>{t.debug(`Adding "${r.name}" transport`);o.some((e=>e===r))?t.warn(`Transport ${r.name} is already added`):(r.unpatchedConsole=e,r.internalLogger=t,r.config=n,r.metas=i,o.push(r))}))},addBeforeSendHooks:(...e)=>{t.debug("Adding beforeSendHooks\n",a),e.forEach((e=>{e&&a.push(e)}))},getBeforeSendHooks:()=>[...a],execute:e=>{var i;s||((null===(i=n.batching)||void 0===i?void 0:i.enabled)&&(null==c||c.addItem(e)),(e=>{var i,r;if((null===(i=n.batching)||void 0===i?void 0:i.enabled)&&o.every((e=>e.isBatched())))return;const[s]=u([e]);if(void 0!==s)for(const e of o)t.debug(`Transporting item using ${e.name}\n`,s),e.isBatched()?(null===(r=n.batching)||void 0===r?void 0:r.enabled)||e.send([s]):e.send(s)})(e))},isPaused:()=>s,pause:()=>{t.debug("Pausing transports"),null==c||c.pause(),s=!0},remove:(...e)=>{t.debug("Removing transports"),e.forEach((e=>{t.debug(`Removing "${e.name}" transport`);const n=o.indexOf(e);-1!==n?o.splice(n,1):t.warn(`Transport "${e.name}" is not added`)}))},removeBeforeSendHooks:(...e)=>{a.filter((t=>!e.includes(t)))},get transports(){return[...o]},unpause:()=>{t.debug("Unpausing transports"),null==c||c.start(),s=!1}}}let G=D;function K(e){var t;return G=null!==(t=e.unpatchedConsole)&&void 0!==t?t:G,G}function J(e){const t=K(e),n=z(t,e);if($ in F&&!e.isolate)return void n.error('Faro is already registered. Either add instrumentations, transports etc. to the global faro instance or use the "isolate" property');n.debug("Initializing");const i=function(e,t){let n=[],i=[];const r=()=>n.reduce(((e,t)=>Object.assign(e,f(t)?t():t)),{}),o=()=>{if(i.length){const e=r();i.forEach((t=>t(e)))}};return{add:(...e)=>{t.debug("Adding metas\n",e),n.push(...e),o()},remove:(...e)=>{t.debug("Removing metas\n",e),n=n.filter((t=>!e.includes(t))),o()},addListener:e=>{t.debug("Adding metas listener\n",e),i.push(e)},removeListener:e=>{t.debug("Removing metas listener\n",e),i=i.filter((t=>t!==e))},get value(){return r()}}}(0,n),r=W(t,n,e,i),o=j(0,n,e,i,r),s=function(e,t,n,i,r,o){t.debug("Initializing instrumentations");const s=[];return{add:(...a)=>{t.debug("Adding instrumentations"),a.forEach((a=>{t.debug(`Adding "${a.name}" instrumentation`),s.some((e=>e.name===a.name))?t.warn(`Instrumentation ${a.name} is already added`):(a.unpatchedConsole=e,a.internalLogger=t,a.config=n,a.metas=i,a.transports=r,a.api=o,s.push(a),a.initialize())}))},get instrumentations(){return[...s]},remove:(...e)=>{t.debug("Removing instrumentations"),e.forEach((e=>{var n,i;t.debug(`Removing "${e.name}" instrumentation`);const r=s.reduce(((t,n,i)=>null===t&&n.name===e.name?i:null),null);r?(null===(i=(n=s[r]).destroy)||void 0===i||i.call(n),s.splice(r,1)):t.warn(`Instrumentation "${e.name}" is not added`)}))}}}(t,n,e,i,r,o),a=q(t,n,e,i,r,o,s);return function(e){var t,n;const i={sdk:{version:B},app:{bundleId:e.config.app.name&&(r=e.config.app.name,null==F?void 0:F[`__faroBundleId_${r}`])}};var r;const o=null===(t=e.config.sessionTracking)||void 0===t?void 0:t.session;o&&e.api.setSession(o),e.config.app&&(i.app=Object.assign(Object.assign({},e.config.app),i.app)),e.config.user&&(i.user=e.config.user),e.config.view&&(i.view=e.config.view),e.metas.add(i,...null!==(n=e.config.metas)&&void 0!==n?n:[])}(a),function(e){e.transports.add(...e.config.transports),e.transports.addBeforeSendHooks(e.config.beforeSend)}(a),function(e){e.instrumentations.add(...e.config.instrumentations)}(a),a}const X="faro",Z={enabled:!0,sendTimeout:250,itemLimit:50},Q="browser",Y="\n",ee="eval",te="?",ne="@",ie=/^\s*at (?:(.*\).*?|.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,re=/\((\S*)(?::(\d+))(?::(\d+))\)/,oe="eval",se="address at ",ae=se.length,ue=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\/.*?|\[native code]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,le=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,ce=" > eval",de="safari-extension",fe="safari-web-extension",pe=/Minified React error #\d+;/i;function ve(e,t,n,i){const r={filename:e||document.location.href,function:t||te};return void 0!==n&&(r.lineno=n),void 0!==i&&(r.colno=i),r}function me(e,t){const n=null==e?void 0:e.includes(de),i=!n&&(null==e?void 0:e.includes(fe));return n||i?[(null==e?void 0:e.includes(ne))?e.split(ne)[0]:e,n?`${de}:${t}`:`${fe}:${t}`]:[e,t]}function ge(e){let t=[];e.stacktrace?t=e.stacktrace.split(Y).filter(((e,t)=>t%2==0)):e.stack&&(t=e.stack.split(Y));const n=t.reduce(((t,n,i)=>{let r,o,s,a,u;if(r=ie.exec(n)){if(o=r[1],s=r[2],a=r[3],u=r[4],null==s?void 0:s.startsWith(oe)){const e=re.exec(s);e&&(s=e[1],a=e[2],u=e[3])}s=(null==s?void 0:s.startsWith(se))?s.substring(ae):s,[o,s]=me(o,s)}else if(r=ue.exec(n)){if(o=r[1],s=r[3],a=r[4],u=r[5],s&&s.includes(ce)){const e=le.exec(s);e&&(o=o||ee,s=e[1],a=e[2])}else 0===i&&!u&&c(e.columnNumber)&&(u=String(e.columnNumber+1));[o,s]=me(o,s)}return(s||o)&&t.push(ve(s,o,a?Number(a):void 0,u?Number(u):void 0)),t}),[]);return pe.test(e.message)?n.slice(1):n}function he(e){return{frames:ge(e)}}const be="com.grafana.faro.session",we=9e5,ye={enabled:!0,persistent:!1,maxSessionPersistenceTime:we};var Se=n(6660);const Te="unknown",Ee=()=>{const e=new Se.UAParser,{name:t,version:n}=e.getBrowser(),{name:i,version:r}=e.getOS(),o=e.getUA(),s=navigator.language,a=navigator.userAgent.includes("Mobi"),u=function(){if(!t||!n)return;if("userAgentData"in navigator&&navigator.userAgentData)return navigator.userAgentData.brands;return}();return{browser:{name:null!=t?t:Te,version:null!=n?n:Te,os:`${null!=i?i:Te} ${null!=r?r:Te}`,userAgent:null!=o?o:Te,language:null!=s?s:Te,mobile:a,brands:null!=u?u:Te,viewportWidth:`${window.innerWidth}`,viewportHeight:`${window.innerHeight}`}}},xe=()=>{const e=window.k6;return{k6:Object.assign({isK6Browser:!0},(null==e?void 0:e.testRunId)&&{testRunId:null==e?void 0:e.testRunId})}};let ke,Oe;function Ie({generatePageId:e,initialPageMeta:t}={}){return()=>{const n=location.href;return f(e)&&ke!==n&&(ke=n,Oe=e(location)),{page:Object.assign(Object.assign({url:n},Oe?{id:Oe}:{}),t)}}}class Ce{constructor(){this.unpatchedConsole=D,this.internalLogger=M,this.config={},this.metas={}}logDebug(...e){this.internalLogger.debug(`${this.name}\n`,...e)}logInfo(...e){this.internalLogger.info(`${this.name}\n`,...e)}logWarn(...e){this.internalLogger.warn(`${this.name}\n`,...e)}logError(...e){this.internalLogger.error(`${this.name}\n`,...e)}}class Pe extends Ce{isBatched(){return!1}getIgnoreUrls(){return[]}}function Le(e,t){var n,i;if(void 0===t)return e;if(void 0===e)return{resourceSpans:t};const r=null===(n=e.resourceSpans)||void 0===n?void 0:n[0];if(void 0===r)return e;const o=(null==r?void 0:r.scopeSpans)||[],s=(null===(i=null==t?void 0:t[0])||void 0===i?void 0:i.scopeSpans)||[];return Object.assign(Object.assign({},e),{resourceSpans:[Object.assign(Object.assign({},r),{scopeSpans:[...o,...s]})]})}function je(e,t){let n,i=!1;const r=()=>{null!=n?(e(...n),n=null,setTimeout(r,t)):i=!1};return(...o)=>{i?n=o:(e(...o),i=!0,setTimeout(r,t))}}const Ae="sessionStorage",_e="localStorage";function Me(e){var t;try{let t;t=window[e];const n="__faro_storage_test__";return t.setItem(n,n),t.removeItem(n),!0}catch(n){return null===(t=V.internalLogger)||void 0===t||t.info(`Web storage of type ${e} is not available. Reason: ${n}`),!1}}function Re(e,t){return Fe(t)?window[t].getItem(e):null}function De(e,t,n){if(Fe(n))try{window[n].setItem(e,t)}catch(e){}}function Ne(e,t){Fe(t)&&window[t].removeItem(e)}const Ue=Me(_e),ze=Me(Ae);function Fe(e){return e===_e?Ue:e===Ae&&ze}const Be="abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ0123456789";function $e(e=10){return Array.from(Array(e)).map((()=>Be[Math.floor(59*Math.random())])).join("")}const Ve="session_start",qe="session_resume",He="service_name_override";function We(){var e,t,n;const i=V.config.sessionTracking;let r=null!==(n=null!==(t=null===(e=null==i?void 0:i.sampler)||void 0===e?void 0:e.call(i,{metas:V.metas.value}))&&void 0!==t?t:null==i?void 0:i.samplingRate)&&void 0!==n?n:1;if("number"!=typeof r){r=0}return Math.random()<r}function Ge({sessionId:e,started:t,lastActivity:n,isSampled:i=!0}={}){var r,o;const s=y(),a=null===(o=null===(r=V.config)||void 0===r?void 0:r.sessionTracking)||void 0===o?void 0:o.generateSessionId;return null==e&&(e="function"==typeof a?a():$e()),{sessionId:e,lastActivity:null!=n?n:s,started:null!=t?t:s,isSampled:i}}function Ke(e){if(null==e)return!1;const t=y();if(!(t-e.started<144e5))return!1;return t-e.lastActivity<we}function Je({fetchUserSession:e,storeUserSession:t}){return function({forceSessionExtend:n}={forceSessionExtend:!1}){var i,r,o;if(!e||!t)return;const s=V.config.sessionTracking,a=null==s?void 0:s.persistent;if(a&&!Ue||!a&&!ze)return;const u=e();if(!1===n&&Ke(u))t(Object.assign(Object.assign({},u),{lastActivity:y()}));else{let e=Xe(Ge({isSampled:We()}),u);t(e),null===(i=V.api)||void 0===i||i.setSession(e.sessionMeta),null===(r=null==s?void 0:s.onSessionChange)||void 0===r||r.call(s,null!==(o=null==u?void 0:u.sessionMeta)&&void 0!==o?o:null,e.sessionMeta)}}}function Xe(e,t){var n,i,r,o,s,a,u;const l=Object.assign(Object.assign({},e),{sessionMeta:{id:e.sessionId,attributes:Object.assign(Object.assign(Object.assign({},null===(i=null===(n=V.config.sessionTracking)||void 0===n?void 0:n.session)||void 0===i?void 0:i.attributes),null!==(o=null===(r=V.metas.value.session)||void 0===r?void 0:r.attributes)&&void 0!==o?o:{}),{isSampled:e.isSampled.toString()})}}),c=null!==(a=null===(s=V.metas.value.session)||void 0===s?void 0:s.overrides)&&void 0!==a?a:null===(u=null==t?void 0:t.sessionMeta)||void 0===u?void 0:u.overrides;h(c)||(l.sessionMeta.overrides=c);const d=null==t?void 0:t.sessionId;return null!=d&&(l.sessionMeta.attributes.previousSession=d),l}function Ze({fetchUserSession:e,storeUserSession:t}){return function(n){const i=n.session,r=e();let o=null==i?void 0:i.id;const s=null==i?void 0:i.attributes,a=null==i?void 0:i.overrides,u=null==r?void 0:r.sessionMeta,l=null==u?void 0:u.overrides,c=!!a&&!E(a,l),d=!!s&&!E(s,null==u?void 0:u.attributes);if(!!i&&o!==(null==r?void 0:r.sessionId)||d||c){const e=Xe(Ge({sessionId:o,isSampled:We()}),r);t(e),function(e,t={},n={}){var i,r,o;if(!e)return;const s=t.serviceName,a=null!==(o=null!==(i=n.serviceName)&&void 0!==i?i:null===(r=V.metas.value.app)||void 0===r?void 0:r.name)&&void 0!==o?o:"";s&&s!==a&&V.api.pushEvent(He,{serviceName:s,previousServiceName:a})}(c,a,l),V.api.setSession(e.sessionMeta)}}}class Qe{constructor(){this.updateSession=je((()=>this.updateUserSession()),1e3),this.updateUserSession=Je({fetchUserSession:Qe.fetchUserSession,storeUserSession:Qe.storeUserSession}),this.init()}static removeUserSession(){Ne(be,Qe.storageTypeLocal)}static storeUserSession(e){De(be,b(e),Qe.storageTypeLocal)}static fetchUserSession(){const e=Re(be,Qe.storageTypeLocal);return e?JSON.parse(e):null}init(){document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&this.updateSession()})),V.metas.addListener(Ze({fetchUserSession:Qe.fetchUserSession,storeUserSession:Qe.storeUserSession}))}}Qe.storageTypeLocal=_e;class Ye{constructor(){this.updateSession=je((()=>this.updateUserSession()),1e3),this.updateUserSession=Je({fetchUserSession:Ye.fetchUserSession,storeUserSession:Ye.storeUserSession}),this.init()}static removeUserSession(){Ne(be,Ye.storageTypeSession)}static storeUserSession(e){De(be,b(e),Ye.storageTypeSession)}static fetchUserSession(){const e=Re(be,Ye.storageTypeSession);return e?JSON.parse(e):null}init(){document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&this.updateSession()})),V.metas.addListener(Ze({fetchUserSession:Ye.fetchUserSession,storeUserSession:Ye.storeUserSession}))}}function et(e){return(null==e?void 0:e.persistent)?Qe:Ye}Ye.storageTypeSession=Ae;var tt=function(e,t,n,i){return new(n||(n=Promise))((function(r,o){function s(e){try{u(i.next(e))}catch(e){o(e)}}function a(e){try{u(i.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((i=i.apply(e,t||[])).next())}))},nt=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]])}return n};class it extends Pe{constructor(e){var t,n,i,r;super(),this.options=e,this.name="@grafana/faro-web-sdk:transport-fetch",this.version=B,this.disabledUntil=new Date,this.rateLimitBackoffMs=null!==(t=e.defaultRateLimitBackoffMs)&&void 0!==t?t:5e3,this.getNow=null!==(n=e.getNow)&&void 0!==n?n:()=>Date.now(),this.promiseBuffer=function(e){const{size:t,concurrency:n}=e,i=[];let r=0;const o=()=>{if(r<n&&i.length){const{producer:e,resolve:t,reject:n}=i.shift();r++,e().then((e=>{r--,o(),t(e)}),(e=>{r--,o(),n(e)}))}};return{add:e=>{if(i.length+r>=t)throw new Error("Task buffer full");return new Promise(((t,n)=>{i.push({producer:e,resolve:t,reject:n}),o()}))}}}({size:null!==(i=e.bufferSize)&&void 0!==i?i:30,concurrency:null!==(r=e.concurrency)&&void 0!==r?r:5})}send(e){return tt(this,void 0,void 0,(function*(){try{if(this.disabledUntil>new Date(this.getNow()))return this.logWarn(`Dropping transport item due to too many requests. Backoff until ${this.disabledUntil}`),Promise.resolve();yield this.promiseBuffer.add((()=>{const t=JSON.stringify(function(e){let t={meta:{}};return void 0!==e[0]&&(t.meta=e[0].meta),e.forEach((e=>{switch(e.type){case i.LOG:case i.EVENT:case i.EXCEPTION:case i.MEASUREMENT:const n=r[e.type],o=t[n];t=Object.assign(Object.assign({},t),{[n]:void 0===o?[e.payload]:[...o,e.payload]});break;case i.TRACE:t=Object.assign(Object.assign({},t),{traces:Le(t.traces,e.payload.resourceSpans)})}})),t}(e)),{url:n,requestOptions:o,apiKey:s}=this.options,a=null!=o?o:{},{headers:u}=a,l=nt(a,["headers"]);let c;const d=this.metas.value.session;return null!=d&&(c=d.id),fetch(n,Object.assign({method:"POST",headers:Object.assign(Object.assign(Object.assign({"Content-Type":"application/json"},null!=u?u:{}),s?{"x-api-key":s}:{}),c?{"x-faro-session-id":c}:{}),body:t,keepalive:t.length<=6e4},null!=l?l:{})).then((e=>tt(this,void 0,void 0,(function*(){if(202===e.status){"invalid"===e.headers.get("X-Faro-Session-Status")&&this.extendFaroSession(this.config,this.logDebug)}return 429===e.status&&(this.disabledUntil=this.getRetryAfterDate(e),this.logWarn(`Too many requests, backing off until ${this.disabledUntil}`)),e.text().catch(A),e})))).catch((e=>{this.logError("Failed sending payload to the receiver\n",JSON.parse(t),e)}))}))}catch(e){this.logError(e)}}))}getIgnoreUrls(){var e;return[this.options.url].concat(null!==(e=this.config.ignoreUrls)&&void 0!==e?e:[])}isBatched(){return!0}getRetryAfterDate(e){const t=this.getNow(),n=e.headers.get("Retry-After");if(n){const e=Number(n);if(!isNaN(e))return new Date(1e3*e+t);const i=Date.parse(n);if(!isNaN(i))return new Date(i)}return new Date(t+this.rateLimitBackoffMs)}extendFaroSession(e,t){const n="Session expired",i=e.sessionTracking;if(null==i?void 0:i.enabled){const{fetchUserSession:e,storeUserSession:r}=et(i);Je({fetchUserSession:e,storeUserSession:r})({forceSessionExtend:!0}),t(`${n} created new session.`)}else t(`${n}.`)}}class rt extends Ce{constructor(){super(...arguments),this.api={},this.transports={}}}const ot=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function st(e){let t,n,i,r,o=[];if((e=>s(e,"ErrorEvent"))(e)&&e.error)t=e.error.message,n=e.error.name,o=ge(e.error);else if((i=(e=>s(e,"DOMError"))(e))||(e=>s(e,"DOMException"))(e)){const{name:r,message:o}=e;n=null!=r?r:i?"DOMError":"DOMException",t=o?`${n}: ${o}`:n}else g(e)?(t=e.message,o=ge(e)):(d(e)||(r=(e=>v&&a(e,Event))(e)))&&(n=r?e.constructor.name:void 0,t=`Non-Error exception captured with keys: ${Object.keys(e)}`);return[t,n,o]}function at(e){const[t,n,i,r,o]=e;let s,a,u=[];const c=l(t),d=ve(n,"?",i,r);return o||!c?([s,a,u]=st(null!=o?o:t),0===u.length&&(u=[d])):c&&([s,a]=function(e){var t,n;const i=e.match(ot),r=null!==(t=null==i?void 0:i[1])&&void 0!==t?t:x;return[null!==(n=null==i?void 0:i[2])&&void 0!==n?n:e,r]}(t),u=[d]),{value:s,type:a,stackFrames:u}}function ut(e,t){return g(e[0])?at(e):{value:t(e)}}function lt(e){window.addEventListener("unhandledrejection",(t=>{var n,i;let r,o,s=t;s.reason?s=t.reason:(null===(n=t.detail)||void 0===n?void 0:n.reason)&&(s=null===(i=t.detail)||void 0===i?void 0:i.reason);let a=[];(e=>!d(e)&&!f(e))(s)?(r=`Non-Error promise rejection captured with value: ${String(s)}`,o="UnhandledRejection"):[r,o,a]=st(s),r&&e.pushError(new Error(r),{type:o,stackFrames:a})}))}class ct extends rt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-errors",this.version=B}initialize(){this.logDebug("Initializing"),function(e){const t=window.onerror;window.onerror=(...n)=>{try{const{value:t,type:i,stackFrames:r}=at(n);t&&e.pushError(new Error(t),{type:i,stackFrames:r})}finally{null==t||t.apply(window,n)}}}(this.api),lt(this.api)}}var dt,ft,pt,vt,mt,gt=-1,ht=function(e){addEventListener("pageshow",(function(t){t.persisted&&(gt=t.timeStamp,e(t))}),!0)},bt=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},wt=function(){var e=bt();return e&&e.activationStart||0},yt=function(e,t){var n=bt(),i="navigate";return gt>=0?i="back-forward-cache":n&&(document.prerendering||wt()>0?i="prerender":document.wasDiscarded?i="restore":n.type&&(i=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},St=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},n||{})),i}}catch(e){}},Tt=function(e,t,n,i){var r,o;return function(s){t.value>=0&&(s||i)&&((o=t.value-(r||0))||void 0===r)&&(r=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},Et=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},xt=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},kt=function(e){var t=!1;return function(){t||(e(),t=!0)}},Ot=-1,It=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},Ct=function(e){"hidden"===document.visibilityState&&Ot>-1&&(Ot="visibilitychange"===e.type?e.timeStamp:0,Lt())},Pt=function(){addEventListener("visibilitychange",Ct,!0),addEventListener("prerenderingchange",Ct,!0)},Lt=function(){removeEventListener("visibilitychange",Ct,!0),removeEventListener("prerenderingchange",Ct,!0)},jt=function(){return Ot<0&&(Ot=It(),Pt(),ht((function(){setTimeout((function(){Ot=It(),Pt()}),0)}))),{get firstHiddenTime(){return Ot}}},At=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},_t=[1800,3e3],Mt=function(e,t){t=t||{},At((function(){var n,i=jt(),r=yt("FCP"),o=St("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-wt(),0),r.entries.push(e),n(!0)))}))}));o&&(n=Tt(e,r,_t,t.reportAllChanges),ht((function(i){r=yt("FCP"),n=Tt(e,r,_t,t.reportAllChanges),Et((function(){r.value=performance.now()-i.timeStamp,n(!0)}))})))}))},Rt=[.1,.25],Dt=0,Nt=1/0,Ut=0,zt=function(e){e.forEach((function(e){e.interactionId&&(Nt=Math.min(Nt,e.interactionId),Ut=Math.max(Ut,e.interactionId),Dt=Ut?(Ut-Nt)/7+1:0)}))},Ft=function(){return dt?Dt:performance.interactionCount||0},Bt=function(){"interactionCount"in performance||dt||(dt=St("event",zt,{type:"event",buffered:!0,durationThreshold:0}))},$t=[],Vt=new Map,qt=0,Ht=[],Wt=function(e){if(Ht.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=$t[$t.length-1],n=Vt.get(e.interactionId);if(n||$t.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};Vt.set(i.id,i),$t.push(i)}$t.sort((function(e,t){return t.latency-e.latency})),$t.length>10&&$t.splice(10).forEach((function(e){return Vt.delete(e.id)}))}}},Gt=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=kt(e),"hidden"===document.visibilityState?e():(n=t(e),xt(e)),n},Kt=[200,500],Jt=[2500,4e3],Xt={},Zt=[800,1800],Qt=function e(t){document.prerendering?At((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},Yt={passive:!0,capture:!0},en=new Date,tn=function(e,t){ft||(ft=t,pt=e,vt=new Date,on(removeEventListener),nn())},nn=function(){if(pt>=0&&pt<vt-en){var e={entryType:"first-input",name:ft.type,target:ft.target,cancelable:ft.cancelable,startTime:ft.timeStamp,processingStart:ft.timeStamp+pt};mt.forEach((function(t){t(e)})),mt=[]}},rn=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){tn(e,t),r()},i=function(){r()},r=function(){removeEventListener("pointerup",n,Yt),removeEventListener("pointercancel",i,Yt)};addEventListener("pointerup",n,Yt),addEventListener("pointercancel",i,Yt)}(t,e):tn(t,e)}},on=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,rn,Yt)}))},sn=[100,300];class an{constructor(e,t){this.pushMeasurement=e,this.webVitalConfig=t}initialize(){Object.entries(an.mapping).forEach((([e,t])=>{var n;t((t=>{this.pushMeasurement({type:"web-vitals",values:{[e]:t.value}})}),{reportAllChanges:null===(n=this.webVitalConfig)||void 0===n?void 0:n.reportAllChanges})}))}}an.mapping={cls:function(e,t){t=t||{},Mt(kt((function(){var n,i=yt("CLS",0),r=0,o=[],s=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];r&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,o.push(e)):(r=e.value,o=[e])}})),r>i.value&&(i.value=r,i.entries=o,n())},a=St("layout-shift",s);a&&(n=Tt(e,i,Rt,t.reportAllChanges),xt((function(){s(a.takeRecords()),n(!0)})),ht((function(){r=0,i=yt("CLS",0),n=Tt(e,i,Rt,t.reportAllChanges),Et((function(){return n()}))})),setTimeout(n,0))})))},fcp:Mt,fid:function(e,t){t=t||{},At((function(){var n,i=jt(),r=yt("FID"),o=function(e){e.startTime<i.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),n(!0))},s=function(e){e.forEach(o)},a=St("first-input",s);n=Tt(e,r,sn,t.reportAllChanges),a&&(xt(kt((function(){s(a.takeRecords()),a.disconnect()}))),ht((function(){var i;r=yt("FID"),n=Tt(e,r,sn,t.reportAllChanges),mt=[],pt=-1,ft=null,on(addEventListener),i=o,mt.push(i),nn()})))}))},inp:function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},At((function(){var n;Bt();var i,r=yt("INP"),o=function(e){Gt((function(){e.forEach(Wt);var t=function(){var e=Math.min($t.length-1,Math.floor((Ft()-qt)/50));return $t[e]}();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())}))},s=St("event",o,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});i=Tt(e,r,Kt,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),xt((function(){o(s.takeRecords()),i(!0)})),ht((function(){qt=Ft(),$t.length=0,Vt.clear(),r=yt("INP"),i=Tt(e,r,Kt,t.reportAllChanges)})))})))},lcp:function(e,t){t=t||{},At((function(){var n,i=jt(),r=yt("LCP"),o=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-wt(),0),r.entries=[e],n())}))},s=St("largest-contentful-paint",o);if(s){n=Tt(e,r,Jt,t.reportAllChanges);var a=kt((function(){Xt[r.id]||(o(s.takeRecords()),s.disconnect(),Xt[r.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return Gt(a)}),{once:!0,capture:!0})})),xt(a),ht((function(i){r=yt("LCP"),n=Tt(e,r,Jt,t.reportAllChanges),Et((function(){r.value=performance.now()-i.timeStamp,Xt[r.id]=!0,n(!0)}))}))}}))},ttfb:function(e,t){t=t||{};var n=yt("TTFB"),i=Tt(e,n,Zt,t.reportAllChanges);Qt((function(){var r=bt();r&&(n.value=Math.max(r.responseStart-wt(),0),n.entries=[r],i(!0),ht((function(){n=yt("TTFB",0),(i=Tt(e,n,Zt,t.reportAllChanges))(!0)})))}))}};var un,ln,cn=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},dn=function(e){if("loading"===document.readyState)return"loading";var t=cn();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},fn=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},pn=function(e,t){var n="";try{for(;e&&9!==e.nodeType;){var i=e,r=i.id?"#"+i.id:fn(i)+(i.classList&&i.classList.value&&i.classList.value.trim()&&i.classList.value.trim().length?"."+i.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+r.length>(t||100)-1)return n||r;if(n=n?r+">"+n:r,i.id)break;e=i.parentNode}}catch(e){}return n},vn=-1,mn=function(){return vn},gn=function(e){addEventListener("pageshow",(function(t){t.persisted&&(vn=t.timeStamp,e(t))}),!0)},hn=function(){var e=cn();return e&&e.activationStart||0},bn=function(e,t){var n=cn(),i="navigate";return mn()>=0?i="back-forward-cache":n&&(document.prerendering||hn()>0?i="prerender":document.wasDiscarded?i="restore":n.type&&(i=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},wn=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},n||{})),i}}catch(e){}},yn=function(e,t,n,i){var r,o;return function(s){t.value>=0&&(s||i)&&((o=t.value-(r||0))||void 0===r)&&(r=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},Sn=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},Tn=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},En=function(e){var t=!1;return function(){t||(e(),t=!0)}},xn=-1,kn=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},On=function(e){"hidden"===document.visibilityState&&xn>-1&&(xn="visibilitychange"===e.type?e.timeStamp:0,Cn())},In=function(){addEventListener("visibilitychange",On,!0),addEventListener("prerenderingchange",On,!0)},Cn=function(){removeEventListener("visibilitychange",On,!0),removeEventListener("prerenderingchange",On,!0)},Pn=function(){return xn<0&&(xn=kn(),In(),gn((function(){setTimeout((function(){xn=kn(),In()}),0)}))),{get firstHiddenTime(){return xn}}},Ln=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},jn=[1800,3e3],An=function(e,t){t=t||{},Ln((function(){var n,i=Pn(),r=bn("FCP"),o=wn("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-hn(),0),r.entries.push(e),n(!0)))}))}));o&&(n=yn(e,r,jn,t.reportAllChanges),gn((function(i){r=bn("FCP"),n=yn(e,r,jn,t.reportAllChanges),Sn((function(){r.value=performance.now()-i.timeStamp,n(!0)}))})))}))},_n=[.1,.25],Mn=0,Rn=1/0,Dn=0,Nn=function(e){e.forEach((function(e){e.interactionId&&(Rn=Math.min(Rn,e.interactionId),Dn=Math.max(Dn,e.interactionId),Mn=Dn?(Dn-Rn)/7+1:0)}))},Un=function(){return un?Mn:performance.interactionCount||0},zn=[],Fn=new Map,Bn=0,$n=[],Vn=function(e){if($n.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=zn[zn.length-1],n=Fn.get(e.interactionId);if(n||zn.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};Fn.set(i.id,i),zn.push(i)}zn.sort((function(e,t){return t.latency-e.latency})),zn.length>10&&zn.splice(10).forEach((function(e){return Fn.delete(e.id)}))}}},qn=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=En(e),"hidden"===document.visibilityState?e():(n=t(e),Tn(e)),n},Hn=[200,500],Wn=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},Ln((function(){var n;"interactionCount"in performance||un||(un=wn("event",Nn,{type:"event",buffered:!0,durationThreshold:0}));var i,r=bn("INP"),o=function(e){qn((function(){e.forEach(Vn);var t=function(){var e=Math.min(zn.length-1,Math.floor((Un()-Bn)/50));return zn[e]}();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())}))},s=wn("event",o,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});i=yn(e,r,Hn,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),Tn((function(){o(s.takeRecords()),i(!0)})),gn((function(){Bn=Un(),zn.length=0,Fn.clear(),r=bn("INP"),i=yn(e,r,Hn,t.reportAllChanges)})))})))},Gn=[],Kn=[],Jn=0,Xn=new WeakMap,Zn=new Map,Qn=-1,Yn=function(e){Gn=Gn.concat(e),ei()},ei=function(){Qn<0&&(Qn=qn(ti))},ti=function(){Zn.size>10&&Zn.forEach((function(e,t){Fn.has(t)||Zn.delete(t)}));var e=zn.map((function(e){return Xn.get(e.entries[0])})),t=Kn.length-50;Kn=Kn.filter((function(n,i){return i>=t||e.includes(n)}));for(var n=new Set,i=0;i<Kn.length;i++){var r=Kn[i];si(r.startTime,r.processingEnd).forEach((function(e){n.add(e)}))}var o=Gn.length-1-50;Gn=Gn.filter((function(e,t){return e.startTime>Jn&&t>o||n.has(e)})),Qn=-1};$n.push((function(e){e.interactionId&&e.target&&!Zn.has(e.interactionId)&&Zn.set(e.interactionId,e.target)}),(function(e){var t,n=e.startTime+e.duration;Jn=Math.max(Jn,e.processingEnd);for(var i=Kn.length-1;i>=0;i--){var r=Kn[i];if(Math.abs(n-r.renderTime)<=8){(t=r).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:n,entries:[e]},Kn.push(t)),(e.interactionId||"first-input"===e.entryType)&&Xn.set(e,t),ei()}));var ni,ii,ri,oi,si=function(e,t){for(var n,i=[],r=0;n=Gn[r];r++)if(!(n.startTime+n.duration<e)){if(n.startTime>t)break;i.push(n)}return i},ai=[2500,4e3],ui={},li=[800,1800],ci=function e(t){document.prerendering?Ln((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},di=function(e,t){t=t||{};var n=bn("TTFB"),i=yn(e,n,li,t.reportAllChanges);ci((function(){var r=cn();r&&(n.value=Math.max(r.responseStart-hn(),0),n.entries=[r],i(!0),gn((function(){n=bn("TTFB",0),(i=yn(e,n,li,t.reportAllChanges))(!0)})))}))},fi={passive:!0,capture:!0},pi=new Date,vi=function(e,t){ni||(ni=t,ii=e,ri=new Date,hi(removeEventListener),mi())},mi=function(){if(ii>=0&&ii<ri-pi){var e={entryType:"first-input",name:ni.type,target:ni.target,cancelable:ni.cancelable,startTime:ni.timeStamp,processingStart:ni.timeStamp+ii};oi.forEach((function(t){t(e)})),oi=[]}},gi=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){vi(e,t),r()},i=function(){r()},r=function(){removeEventListener("pointerup",n,fi),removeEventListener("pointercancel",i,fi)};addEventListener("pointerup",n,fi),addEventListener("pointercancel",i,fi)}(t,e):vi(t,e)}},hi=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,gi,fi)}))},bi=[100,300],wi=function(e,t){!function(e,t){t=t||{},Ln((function(){var n,i=Pn(),r=bn("FID"),o=function(e){e.startTime<i.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),n(!0))},s=function(e){e.forEach(o)},a=wn("first-input",s);n=yn(e,r,bi,t.reportAllChanges),a&&(Tn(En((function(){s(a.takeRecords()),a.disconnect()}))),gn((function(){var i;r=bn("FID"),n=yn(e,r,bi,t.reportAllChanges),oi=[],ii=-1,ni=null,hi(addEventListener),i=o,oi.push(i),mi()})))}))}((function(t){var n=function(e){var t=e.entries[0],n={eventTarget:pn(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:dn(t.startTime)};return Object.assign(e,{attribution:n})}(t);e(n)}),t)};const yi="com.grafana.faro.lastNavigationId",Si="load_state",Ti="time_to_first_byte";class Ei{constructor(e,t){this.corePushMeasurement=e,this.webVitalConfig=t}initialize(){this.measureCLS(),this.measureFCP(),this.measureFID(),this.measureINP(),this.measureLCP(),this.measureTTFB()}measureCLS(){var e;!function(e,t){!function(e,t){t=t||{},An(En((function(){var n,i=bn("CLS",0),r=0,o=[],s=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];r&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,o.push(e)):(r=e.value,o=[e])}})),r>i.value&&(i.value=r,i.entries=o,n())},a=wn("layout-shift",s);a&&(n=yn(e,i,_n,t.reportAllChanges),Tn((function(){s(a.takeRecords()),n(!0)})),gn((function(){r=0,i=bn("CLS",0),n=yn(e,i,_n,t.reportAllChanges),Sn((function(){return n()}))})),setTimeout(n,0))})))}((function(t){var n=function(e){var t,n={};if(e.entries.length){var i=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(i&&i.sources&&i.sources.length){var r=(t=i.sources).find((function(e){return e.node&&1===e.node.nodeType}))||t[0];r&&(n={largestShiftTarget:pn(r.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:r,largestShiftEntry:i,loadState:dn(i.startTime)})}}return Object.assign(e,{attribution:n})}(t);e(n)}),t)}((e=>{const{loadState:t,largestShiftValue:n,largestShiftTime:i,largestShiftTarget:r}=e.attribution,o=this.buildInitialValues(e);this.addIfPresent(o,"largest_shift_value",n),this.addIfPresent(o,"largest_shift_time",i);const s=this.buildInitialContext(e);this.addIfPresent(s,Si,t),this.addIfPresent(s,"largest_shift_target",r),this.pushMeasurement(o,s)}),{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureFCP(){var e;!function(e,t){An((function(t){var n=function(e){var t={timeToFirstByte:0,firstByteToFCP:e.value,loadState:dn(mn())};if(e.entries.length){var n=cn(),i=e.entries[e.entries.length-1];if(n){var r=n.activationStart||0,o=Math.max(0,n.responseStart-r);t={timeToFirstByte:o,firstByteToFCP:e.value-o,loadState:dn(e.entries[0].startTime),navigationEntry:n,fcpEntry:i}}}return Object.assign(e,{attribution:t})}(t);e(n)}),t)}((e=>{const{firstByteToFCP:t,timeToFirstByte:n,loadState:i}=e.attribution,r=this.buildInitialValues(e);this.addIfPresent(r,"first_byte_to_fcp",t),this.addIfPresent(r,Ti,n);const o=this.buildInitialContext(e);this.addIfPresent(o,Si,i),this.pushMeasurement(r,o)}),{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureFID(){var e;wi((e=>{const{eventTime:t,eventTarget:n,eventType:i,loadState:r}=e.attribution,o=this.buildInitialValues(e);this.addIfPresent(o,"event_time",t);const s=this.buildInitialContext(e);this.addIfPresent(s,"event_target",n),this.addIfPresent(s,"event_type",i),this.addIfPresent(s,Si,r),this.pushMeasurement(o,s)}),{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureINP(){var e;!function(e,t){ln||(ln=wn("long-animation-frame",Yn)),Wn((function(t){var n=function(e){var t=e.entries[0],n=Xn.get(t),i=t.processingStart,r=n.processingEnd,o=n.entries.sort((function(e,t){return e.processingStart-t.processingStart})),s=si(t.startTime,r),a=e.entries.find((function(e){return e.target})),u=a&&a.target||Zn.get(t.interactionId),l=[t.startTime+t.duration,r].concat(s.map((function(e){return e.startTime+e.duration}))),c=Math.max.apply(Math,l),d={interactionTarget:pn(u),interactionTargetElement:u,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:c,processedEventEntries:o,longAnimationFrameEntries:s,inputDelay:i-t.startTime,processingDuration:r-i,presentationDelay:Math.max(c-r,0),loadState:dn(t.startTime)};return Object.assign(e,{attribution:d})}(t);e(n)}),t)}((e=>{const{interactionTime:t,presentationDelay:n,inputDelay:i,processingDuration:r,nextPaintTime:o,loadState:s,interactionTarget:a,interactionType:u}=e.attribution,l=this.buildInitialValues(e);this.addIfPresent(l,"interaction_time",t),this.addIfPresent(l,"presentation_delay",n),this.addIfPresent(l,"input_delay",i),this.addIfPresent(l,"processing_duration",r),this.addIfPresent(l,"next_paint_time",o);const c=this.buildInitialContext(e);this.addIfPresent(c,Si,s),this.addIfPresent(c,"interaction_target",a),this.addIfPresent(c,"interaction_type",u),this.pushMeasurement(l,c)}),{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureLCP(){var e;!function(e,t){!function(e,t){t=t||{},Ln((function(){var n,i=Pn(),r=bn("LCP"),o=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-hn(),0),r.entries=[e],n())}))},s=wn("largest-contentful-paint",o);if(s){n=yn(e,r,ai,t.reportAllChanges);var a=En((function(){ui[r.id]||(o(s.takeRecords()),s.disconnect(),ui[r.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return qn(a)}),{once:!0,capture:!0})})),Tn(a),gn((function(i){r=bn("LCP"),n=yn(e,r,ai,t.reportAllChanges),Sn((function(){r.value=performance.now()-i.timeStamp,ui[r.id]=!0,n(!0)}))}))}}))}((function(t){var n=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var n=cn();if(n){var i=n.activationStart||0,r=e.entries[e.entries.length-1],o=r.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===r.url}))[0],s=Math.max(0,n.responseStart-i),a=Math.max(s,o?(o.requestStart||o.startTime)-i:0),u=Math.max(a,o?o.responseEnd-i:0),l=Math.max(u,r.startTime-i);t={element:pn(r.element),timeToFirstByte:s,resourceLoadDelay:a-s,resourceLoadDuration:u-a,elementRenderDelay:l-u,navigationEntry:n,lcpEntry:r},r.url&&(t.url=r.url),o&&(t.lcpResourceEntry=o)}}return Object.assign(e,{attribution:t})}(t);e(n)}),t)}((e=>{const{elementRenderDelay:t,resourceLoadDelay:n,resourceLoadDuration:i,timeToFirstByte:r,element:o}=e.attribution,s=this.buildInitialValues(e);this.addIfPresent(s,"element_render_delay",t),this.addIfPresent(s,"resource_load_delay",n),this.addIfPresent(s,"resource_load_duration",i),this.addIfPresent(s,Ti,r);const a=this.buildInitialContext(e);this.addIfPresent(a,"element",o),this.pushMeasurement(s,a)}),{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureTTFB(){var e;!function(e,t){di((function(t){var n=function(e){var t={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){var n=e.entries[0],i=n.activationStart||0,r=Math.max((n.workerStart||n.fetchStart)-i,0),o=Math.max(n.domainLookupStart-i,0),s=Math.max(n.connectStart-i,0),a=Math.max(n.connectEnd-i,0);t={waitingDuration:r,cacheDuration:o-r,dnsDuration:s-o,connectionDuration:a-s,requestDuration:e.value-a,navigationEntry:n}}return Object.assign(e,{attribution:t})}(t);e(n)}),t)}((e=>{const{dnsDuration:t,connectionDuration:n,requestDuration:i,waitingDuration:r,cacheDuration:o}=e.attribution,s=this.buildInitialValues(e);this.addIfPresent(s,"dns_duration",t),this.addIfPresent(s,"connection_duration",n),this.addIfPresent(s,"request_duration",i),this.addIfPresent(s,"waiting_duration",r),this.addIfPresent(s,"cache_duration",o);const a=this.buildInitialContext(e);this.pushMeasurement(s,a)}),{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}buildInitialValues(e){const t=e.name.toLowerCase();return{[t]:e.value,delta:e.delta}}buildInitialContext(e){var t;const n=null!==(t=Re(yi,Ae))&&void 0!==t?t:Te;return{id:e.id,rating:e.rating,navigation_type:e.navigationType,navigation_entry_id:n}}pushMeasurement(e,t){this.corePushMeasurement({type:"web-vitals",values:e},{context:t})}addIfPresent(e,t,n){n&&(e[t]=n)}}class xi extends rt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-web-vitals",this.version=B}initialize(){this.logDebug("Initializing");this.intializeWebVitalsInstrumentation().initialize()}intializeWebVitalsInstrumentation(){var e;return(null===(e=this.config)||void 0===e?void 0:e.trackWebVitalsAttribution)?new Ei(this.api.pushMeasurement,this.config.webVitalsInstrumentation):new an(this.api.pushMeasurement,this.config.webVitalsInstrumentation)}}class ki extends rt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-session",this.version=B}sendSessionStartEvent(e){var t,n;const i=e.session;if(i&&i.id!==(null===(t=this.notifiedSession)||void 0===t?void 0:t.id)){if(this.notifiedSession&&this.notifiedSession.id===(null===(n=i.attributes)||void 0===n?void 0:n.previousSession))return this.api.pushEvent("session_extend",{},void 0,{skipDedupe:!0}),void(this.notifiedSession=i);this.notifiedSession=i,this.api.pushEvent(Ve,{},void 0,{skipDedupe:!0})}}createInitialSession(e,t){var n,i,r,o,s,a;let u,l,c=e.fetchUserSession();if(t.persistent&&t.maxSessionPersistenceTime&&c){const e=y();c.lastActivity<e-t.maxSessionPersistenceTime&&(Qe.removeUserSession(),c=null)}if(Ke(c)){const e=null==c?void 0:c.sessionId;l=Ge({sessionId:e,isSampled:c.isSampled||!1,started:null==c?void 0:c.started});const r=null==c?void 0:c.sessionMeta,o=Object.assign(Object.assign({},null===(n=t.session)||void 0===n?void 0:n.overrides),null==r?void 0:r.overrides);l.sessionMeta=Object.assign(Object.assign({},t.session),{id:e,attributes:Object.assign(Object.assign(Object.assign({},null===(i=t.session)||void 0===i?void 0:i.attributes),null==r?void 0:r.attributes),{isSampled:l.isSampled.toString()}),overrides:o}),u=qe}else{const e=null!==(o=null===(r=t.session)||void 0===r?void 0:r.id)&&void 0!==o?o:function(e){var t,n,i,r;return{id:null!==(r=null===(i=null===(n=null===(t=V.config)||void 0===t?void 0:t.sessionTracking)||void 0===n?void 0:n.generateSessionId)||void 0===i?void 0:i.call(n))&&void 0!==r?r:$e(),attributes:e}}().id;l=Ge({sessionId:e,isSampled:We()});const n=null===(s=t.session)||void 0===s?void 0:s.overrides;l.sessionMeta=Object.assign({id:e,attributes:Object.assign({isSampled:l.isSampled.toString()},null===(a=t.session)||void 0===a?void 0:a.attributes)},n?{overrides:n}:{}),u=Ve}return{initialSession:l,lifecycleType:u}}registerBeforeSendHook(e){var t;const{updateSession:n}=new e;null===(t=this.transports)||void 0===t||t.addBeforeSendHooks((e=>{var t,i,r;n();const o=null===(t=e.meta.session)||void 0===t?void 0:t.attributes;if(o&&"true"===(null==o?void 0:o.isSampled)){let t=JSON.parse(JSON.stringify(e));const n=null===(i=t.meta.session)||void 0===i?void 0:i.attributes;return null==n||delete n.isSampled,0===Object.keys(null!=n?n:{}).length&&(null===(r=t.meta.session)||void 0===r||delete r.attributes),t}return null}))}initialize(){this.logDebug("init session instrumentation");const e=this.config.sessionTracking;if(null==e?void 0:e.enabled){const t=et(e);this.registerBeforeSendHook(t);const{initialSession:n,lifecycleType:i}=this.createInitialSession(t,e);t.storeUserSession(n);const r=n.sessionMeta;this.notifiedSession=r,this.api.setSession(r),i===Ve&&this.api.pushEvent(Ve,{},void 0,{skipDedupe:!0}),i===qe&&this.api.pushEvent(qe,{},void 0,{skipDedupe:!0})}this.metas.addListener(this.sendSessionStartEvent.bind(this))}}class Oi extends rt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-view",this.version=B}sendViewChangedEvent(e){var t,n,i,r;const o=e.view;o&&o.name!==(null===(t=this.notifiedView)||void 0===t?void 0:t.name)&&(this.api.pushEvent("view_changed",{fromView:null!==(i=null===(n=this.notifiedView)||void 0===n?void 0:n.name)&&void 0!==i?i:Te,toView:null!==(r=o.name)&&void 0!==r?r:Te},void 0,{skipDedupe:!0}),this.notifiedView=o)}initialize(){this.metas.addListener(this.sendViewChangedEvent.bind(this))}}const Ii=/^00-[a-f0-9]{32}-[a-f0-9]{16}-[0-9]{1,2}$/;function Ci(e=[]){for(const t of e)if("traceparent"===t.name){if(!Ii.test(t.description))continue;const[,e,n]=t.description.split("-");if(null!=e&&null!=n)return{traceId:e,spanId:n};break}}function Pi(e=[],t){return e.some((e=>e&&null!=t.match(e)))}function Li(e,t={}){for(const[n,i]of Object.entries(t)){const t=e[n];return null!=t&&(p(i)?i.includes(t):t===i)}return!0}function ji(e){const{connectEnd:t,connectStart:n,decodedBodySize:i,domainLookupEnd:r,domainLookupStart:o,duration:s,encodedBodySize:a,fetchStart:u,initiatorType:l,name:c,nextHopProtocol:d,redirectEnd:f,redirectStart:p,renderBlockingStatus:v,requestStart:m,responseEnd:g,responseStart:h,responseStatus:b,secureConnectionStart:w,transferSize:y,workerStart:S}=e;return{name:c,duration:_i(s),tcpHandshakeTime:_i(t-n),dnsLookupTime:_i(r-o),tlsNegotiationTime:_i(m-w),responseStatus:_i(b),redirectTime:_i(f-p),requestTime:_i(h-m),responseTime:_i(g-h),fetchTime:_i(g-u),serviceWorkerTime:_i(u-S),decodedBodySize:_i(i),encodedBodySize:_i(a),cacheHitStatus:function(){let e="fullLoad";0===y?i>0&&(e="cache"):null!=b?304===b&&(e="conditionalFetch"):a>0&&y<a&&(e="conditionalFetch");return e}(),renderBlockingStatus:_i(v),protocol:d,initiatorType:l,visibilityState:document.visibilityState,ttfb:_i(h-m)}}function Ai(e){const{activationStart:t,domComplete:n,domContentLoadedEventEnd:i,domContentLoadedEventStart:r,domInteractive:o,fetchStart:s,loadEventEnd:a,loadEventStart:u,responseStart:l,type:c}=e,d=function(){var e;if(null!=(null===(e=performance.timing)||void 0===e?void 0:e.domLoading))return performance.timing.domLoading-performance.timeOrigin;return null}();return Object.assign(Object.assign({},ji(e)),{pageLoadTime:_i(n-s),documentParsingTime:_i(d?o-d:null),domProcessingTime:_i(n-o),domContentLoadHandlerTime:_i(i-r),onLoadTime:_i(a-u),ttfb:_i(Math.max(l-(null!=t?t:0),0)),type:c})}function _i(e){return null==e?Te:"number"==typeof e?Math.round(e).toString():e.toString()}const Mi={initiatorType:["xmlhttprequest","fetch"]};var Ri=function(e,t,n,i){return new(n||(n=Promise))((function(r,o){function s(e){try{u(i.next(e))}catch(e){o(e)}}function a(e){try{u(i.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((i=i.apply(e,t||[])).next())}))};class Di extends rt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-performance",this.version=B}initialize(){"PerformanceObserver"in window?function(e){if("complete"===document.readyState)e();else{const t=()=>{"complete"===document.readyState&&(e(),document.removeEventListener("readystatechange",t))};document.addEventListener("readystatechange",t)}}((()=>Ri(this,void 0,void 0,(function*(){const e=this.api.pushEvent,t=this.getIgnoreUrls(),{faroNavigationId:n}=yield function(e,t){let n;const i=new Promise((e=>{n=e}));return new PerformanceObserver((i=>{var r;const[o]=i.getEntries();if(null==o||Pi(t,o.name))return;const s=o.toJSON();let a=Ci(null==s?void 0:s.serverTiming);const u=null!==(r=Re(yi,Ae))&&void 0!==r?r:Te,l=Object.assign(Object.assign({},Ai(s)),{faroNavigationId:$e(),faroPreviousNavigationId:u});De(yi,l.faroNavigationId,Ae),e("faro.performance.navigation",l,void 0,{spanContext:a,timestampOverwriteMs:performance.timeOrigin+s.startTime}),n(l)})).observe({type:"navigation",buffered:!0}),i}(e,t);null!=n&&function(e,t,n){const i=V.config.trackResources;new PerformanceObserver((r=>{const o=r.getEntries();for(const r of o){if(Pi(n,r.name))return;const o=r.toJSON();let s=Ci(null==o?void 0:o.serverTiming);if(null==i&&Li(o,Mi)||i){const n=Object.assign(Object.assign({},ji(o)),{faroNavigationId:e,faroResourceId:$e()});t("faro.performance.resource",n,void 0,{spanContext:s,timestampOverwriteMs:performance.timeOrigin+o.startTime})}}})).observe({type:"resource",buffered:!0})}(n,e,t)})))):this.logDebug("performance observer not supported. Disable performance instrumentation.")}getIgnoreUrls(){var e;return null===(e=this.transports.transports)||void 0===e?void 0:e.flatMap((e=>e.getIgnoreUrls()))}}class Ni extends rt{constructor(e={}){super(),this.options=e,this.name="@grafana/faro-web-sdk:instrumentation-console",this.version=B,this.errorSerializer=L}initialize(){var e,t,n,i;this.options=Object.assign(Object.assign({},this.options),this.config.consoleInstrumentation);const r=(null===(e=this.options)||void 0===e?void 0:e.serializeErrors)||!!(null===(t=this.options)||void 0===t?void 0:t.errorSerializer);this.errorSerializer=r?null!==(i=null===(n=this.options)||void 0===n?void 0:n.errorSerializer)&&void 0!==i?i:k:L,P.HT.filter((e=>{var t,n;return!(null!==(n=null===(t=this.options)||void 0===t?void 0:t.disabledLevels)&&void 0!==n?n:Ni.defaultDisabledLevels).includes(e)})).forEach((e=>{console[e]=(...t)=>{var n,i;try{if(e!==P.$b.ERROR||(null===(n=this.options)||void 0===n?void 0:n.consoleErrorAsLog))if(e===P.$b.ERROR&&(null===(i=this.options)||void 0===i?void 0:i.consoleErrorAsLog)){const{value:n,type:i,stackFrames:r}=ut(t,this.errorSerializer);this.api.pushLog(n?[Ni.consoleErrorPrefix+n]:t,{level:e,context:{value:null!=n?n:"",type:null!=i?i:"",stackFrames:(null==r?void 0:r.length)?k(r):""}})}else this.api.pushLog(t,{level:e});else{const{value:e,type:n,stackFrames:i}=ut(t,this.errorSerializer);if(e&&!n&&!i)return void this.api.pushError(new Error(Ni.consoleErrorPrefix+e));this.api.pushError(new Error(Ni.consoleErrorPrefix+e),{type:n,stackFrames:i})}}catch(e){this.logError(e)}finally{this.unpatchedConsole[e](...t)}}}))}}function Ui(e={}){const t=[new ct,new xi,new ki,new Oi];return!1!==e.enablePerformanceInstrumentation&&t.unshift(new Di),!1!==e.captureConsole&&t.push(new Ni({disabledLevels:e.captureConsoleDisabledLevels})),t}function zi(e){var t,n;const{page:i,generatePageId:r}=null!==(t=null==e?void 0:e.pageTracking)&&void 0!==t?t:{},o=[Ee,Ie({generatePageId:r,initialPageMeta:i}),...null!==(n=e.metas)&&void 0!==n?n:[]];return d(window.k6)?[...o,xe]:o}function Fi({geoLocationTracking:e,sessionTracking:t}){var n;const i={};return null!=(null==e?void 0:e.enabled)&&(i.geoLocationTrackingEnabled=e.enabled),h(i)?{}:{session:Object.assign(Object.assign({},null!==(n=null==t?void 0:t.session)&&void 0!==n?n:{}),{overrides:i})}}function Bi(e){const t=function(e){var t;const n=[],i=N(e.unpatchedConsole,e.internalLoggerLevel);e.transports?((e.url||e.apiKey)&&i.error('if "transports" is defined, "url" and "apiKey" should not be defined'),n.push(...e.transports)):e.url?n.push(new it({url:e.url,apiKey:e.apiKey})):i.error('either "url" or "transports" must be defined');const{app:r,batching:o,beforeSend:s,consoleInstrumentation:a,ignoreErrors:u,sessionTracking:l,trackResources:c,trackWebVitalsAttribution:d,user:f,view:p,geoLocationTracking:v,dedupe:m=!0,eventDomain:g=Q,globalObjectKey:h=X,instrumentations:b=Ui(),internalLoggerLevel:w=R,isolate:y=!1,logArgsSerializer:S=L,metas:T=zi(e),paused:E=!1,preventGlobalExposure:x=!1,unpatchedConsole:k=D,webVitalsInstrumentation:O}=e;return{app:r,batching:Object.assign(Object.assign({},Z),o),dedupe:m,globalObjectKey:h,instrumentations:b,internalLoggerLevel:w,isolate:y,logArgsSerializer:S,metas:T,parseStacktrace:he,paused:E,preventGlobalExposure:x,transports:n,unpatchedConsole:k,beforeSend:s,eventDomain:g,ignoreErrors:u,ignoreUrls:(null!==(t=e.ignoreUrls)&&void 0!==t?t:[]).concat([/\/collect(?:\/[\w]*)?$/]),sessionTracking:Object.assign(Object.assign(Object.assign({},ye),l),Fi({geoLocationTracking:v,sessionTracking:l})),user:f,view:p,trackResources:c,trackWebVitalsAttribution:d,consoleInstrumentation:a,webVitalsInstrumentation:O}}(e);if(t)return J(t)}Ni.defaultDisabledLevels=[P.$b.DEBUG,P.$b.TRACE,P.$b.LOG],Ni.consoleErrorPrefix="console.error: ";var $i=n(8531),Vi=n(6159);const qi=new Map([["dev",{environment:"dev",appName:"grafana-metricsdrilldown-app-dev",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/8c57b32175ba39d35dfaccee7cd793c7"}],["ops",{environment:"ops",appName:"grafana-metricsdrilldown-app-ops",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/d65ab91eb9c5e8c51b474d9313ba28f4"}],["prod",{environment:"prod",appName:"grafana-metricsdrilldown-app-prod",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/0f4f1bbc97c9e2db4fa85ef75a559885"}]]);var Hi=n(4137),Wi=n(5176);let Gi=null;const Ki=()=>Gi,Ji=e=>Gi=e;function Xi(){if(Ki())return;const e=function(){const e=(0,Vi.u)();if(e&&qi.has(e))return qi.get(e)}();if(!e)return;const{environment:t,faroUrl:n,appName:i}=e,{apps:r,bootData:o}=$i.config,s=r[Hi.s_].version,a=o.user.email;Ji(Bi({url:n,app:{name:i,release:s,version:Wi.t,environment:t},user:{email:a},instrumentations:[...Ui({captureConsole:!1})],isolate:!0,beforeSend:e=>{var t,n;return(null!==(n=null===(t=e.meta.page)||void 0===t?void 0:t.url)&&void 0!==n?n:"").includes(Hi.Gy)?e:null}}))}},6159:(e,t,n)=>{"use strict";n.d(t,{u:()=>r});const i=[{regExp:/localhost/,environment:"local"},{regExp:/grafana-dev\.net/,environment:"dev"},{regExp:/grafana-ops\.net/,environment:"ops"},{regExp:/grafana\.net/,environment:"prod"}];function r(){var e,t;if(!(null===(t=window)||void 0===t||null===(e=t.location)||void 0===e?void 0:e.host))return null;const n=i.find((({regExp:e})=>e.test(window.location.host)));return n?n.environment:null}},2445:(e,t,n)=>{"use strict";n.d(t,{v:()=>p});var i=n(5438),r=n(6530),o=n(6159);function s(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function a(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function u(e,t,n){return function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}(e,a(e,t,"set"),n),n}function l(e,t,n){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return n}var c=new WeakMap,d=new WeakSet;function f(e,t){var n;"prod"!==function(e,t){return t.get?t.get.call(e):t.value}(n=this,a(n,c,"get"))&&console[e](...t)}const p=new class{trace(){var e;l(this,d,f).call(this,"trace",[]),null===(e=(0,r.n1)())||void 0===e||e.api.pushLog([],{level:i.$b.TRACE})}debug(...e){var t;l(this,d,f).call(this,"debug",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:i.$b.DEBUG})}info(...e){var t;l(this,d,f).call(this,"info",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:i.$b.INFO})}log(...e){var t;l(this,d,f).call(this,"log",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:i.$b.LOG})}warn(...e){var t;l(this,d,f).call(this,"warn",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:i.$b.WARN})}error(e,t){var n;l(this,d,f).call(this,"error",[e]),t&&l(this,d,f).call(this,"error",["Error context",t]),null===(n=(0,r.n1)())||void 0===n||n.api.pushError(e,{context:t})}constructor(){var e,t;s(e=this,t=d),t.add(e),function(e,t,n){s(e,t),t.set(e,n)}(this,c,{writable:!0,value:void 0}),u(this,c,(0,o.u)())}}},5176:(e,t,n)=>{"use strict";n.d(t,{t:()=>i});const i="946548122146823e85bf98cdf38a77ff48b88a3e"},6660:function(e,t,n){var i;!function(r,o){"use strict";var s="function",a="undefined",u="object",l="string",c="major",d="model",f="name",p="type",v="vendor",m="version",g="architecture",h="console",b="mobile",w="tablet",y="smarttv",S="wearable",T="embedded",E="Amazon",x="Apple",k="ASUS",O="BlackBerry",I="Browser",C="Chrome",P="Firefox",L="Google",j="Huawei",A="LG",_="Microsoft",M="Motorola",R="Opera",D="Samsung",N="Sharp",U="Sony",z="Xiaomi",F="Zebra",B="Facebook",$="Chromium OS",V="Mac OS",q=" Browser",H=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},W=function(e,t){return typeof e===l&&-1!==G(t).indexOf(G(e))},G=function(e){return e.toLowerCase()},K=function(e,t){if(typeof e===l)return e=e.replace(/^\s\s*/,""),typeof t===a?e:e.substring(0,500)},J=function(e,t){for(var n,i,r,a,l,c,d=0;d<t.length&&!l;){var f=t[d],p=t[d+1];for(n=i=0;n<f.length&&!l&&f[n];)if(l=f[n++].exec(e))for(r=0;r<p.length;r++)c=l[++i],typeof(a=p[r])===u&&a.length>0?2===a.length?typeof a[1]==s?this[a[0]]=a[1].call(this,c):this[a[0]]=a[1]:3===a.length?typeof a[1]!==s||a[1].exec&&a[1].test?this[a[0]]=c?c.replace(a[1],a[2]):o:this[a[0]]=c?a[1].call(this,c,a[2]):o:4===a.length&&(this[a[0]]=c?a[3].call(this,c.replace(a[1],a[2])):o):this[a]=c||o;d+=2}},X=function(e,t){for(var n in t)if(typeof t[n]===u&&t[n].length>0){for(var i=0;i<t[n].length;i++)if(W(t[n][i],e))return"?"===n?o:n}else if(W(t[n],e))return"?"===n?o:n;return t.hasOwnProperty("*")?t["*"]:e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,m],[/opios[\/ ]+([\w\.]+)/i],[m,[f,R+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[m,[f,R+" GX"]],[/\bopr\/([\w\.]+)/i],[m,[f,R]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[m,[f,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[m,[f,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,m],[/quark(?:pc)?\/([-\w\.]+)/i],[m,[f,"Quark"]],[/\bddg\/([\w\.]+)/i],[m,[f,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[f,"UC"+I]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[m,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[f,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[m,[f,"Smart Lenovo "+I]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+I],m],[/\bfocus\/([\w\.]+)/i],[m,[f,P+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[f,R+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[f,R+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[f,"MIUI"+q]],[/fxios\/([\w\.-]+)/i],[m,[f,P]],[/\bqihoobrowser\/?([\w\.]*)/i],[m,[f,"360"]],[/\b(qq)\/([\w\.]+)/i],[[f,/(.+)/,"$1Browser"],m],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1"+q],m],[/samsungbrowser\/([\w\.]+)/i],[m,[f,D+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[m,[f,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[f,"Sogou Mobile"],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[f,m],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[f],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[m,f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,B],m],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[f,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[f,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,C+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[f,"Android "+I]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[m,X,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],m],[/(wolvic|librewolf)\/([\w\.]+)/i],[f,m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[f,P+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[f,[m,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[f,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,G]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",G]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,G]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[d,[v,D],[p,w]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[d,[v,D],[p,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[d,[v,x],[p,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[d,[v,x],[p,w]],[/(macintosh);/i],[d,[v,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[d,[v,N],[p,b]],[/(?:honor)([-\w ]+)[;\)]/i],[d,[v,"Honor"],[p,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[d,[v,j],[p,w]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[d,[v,j],[p,b]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[d,/_/g," "],[v,z],[p,b]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[d,/_/g," "],[v,z],[p,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[d,[v,"OPPO"],[p,b]],[/\b(opd2\d{3}a?) bui/i],[d,[v,"OPPO"],[p,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[d,[v,"Vivo"],[p,b]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[d,[v,"Realme"],[p,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[d,[v,M],[p,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[d,[v,M],[p,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[d,[v,A],[p,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[d,[v,A],[p,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[d,[v,"Lenovo"],[p,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[d,/_/g," "],[v,"Nokia"],[p,b]],[/(pixel c)\b/i],[d,[v,L],[p,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[d,[v,L],[p,b]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[d,[v,U],[p,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[d,"Xperia Tablet"],[v,U],[p,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[d,[v,"OnePlus"],[p,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[d,[v,E],[p,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[d,/(.+)/g,"Fire Phone $1"],[v,E],[p,b]],[/(playbook);[-\w\),; ]+(rim)/i],[d,v,[p,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[d,[v,O],[p,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[d,[v,k],[p,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[d,[v,k],[p,b]],[/(nexus 9)/i],[d,[v,"HTC"],[p,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[v,[d,/_/g," "],[p,b]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[d,[v,"TCL"],[p,w]],[/(itel) ((\w+))/i],[[v,G],d,[p,X,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[d,[v,"Acer"],[p,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[d,[v,"Meizu"],[p,b]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[d,[v,"Ulefone"],[p,b]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[d,[v,"Energizer"],[p,b]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[d,[v,"Cat"],[p,b]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[d,[v,"Smartfren"],[p,b]],[/droid.+; (a(?:015|06[35]|142p?))/i],[d,[v,"Nothing"],[p,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[v,d,[p,b]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[v,d,[p,w]],[/(surface duo)/i],[d,[v,_],[p,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[d,[v,"Fairphone"],[p,b]],[/(u304aa)/i],[d,[v,"AT&T"],[p,b]],[/\bsie-(\w*)/i],[d,[v,"Siemens"],[p,b]],[/\b(rct\w+) b/i],[d,[v,"RCA"],[p,w]],[/\b(venue[\d ]{2,7}) b/i],[d,[v,"Dell"],[p,w]],[/\b(q(?:mv|ta)\w+) b/i],[d,[v,"Verizon"],[p,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[d,[v,"Barnes & Noble"],[p,w]],[/\b(tm\d{3}\w+) b/i],[d,[v,"NuVision"],[p,w]],[/\b(k88) b/i],[d,[v,"ZTE"],[p,w]],[/\b(nx\d{3}j) b/i],[d,[v,"ZTE"],[p,b]],[/\b(gen\d{3}) b.+49h/i],[d,[v,"Swiss"],[p,b]],[/\b(zur\d{3}) b/i],[d,[v,"Swiss"],[p,w]],[/\b((zeki)?tb.*\b) b/i],[d,[v,"Zeki"],[p,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[v,"Dragon Touch"],d,[p,w]],[/\b(ns-?\w{0,9}) b/i],[d,[v,"Insignia"],[p,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[d,[v,"NextBook"],[p,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[v,"Voice"],d,[p,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[v,"LvTel"],d,[p,b]],[/\b(ph-1) /i],[d,[v,"Essential"],[p,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[d,[v,"Envizen"],[p,w]],[/\b(trio[-\w\. ]+) b/i],[d,[v,"MachSpeed"],[p,w]],[/\btu_(1491) b/i],[d,[v,"Rotor"],[p,w]],[/(shield[\w ]+) b/i],[d,[v,"Nvidia"],[p,w]],[/(sprint) (\w+)/i],[v,d,[p,b]],[/(kin\.[onetw]{3})/i],[[d,/\./g," "],[v,_],[p,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[d,[v,F],[p,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[d,[v,F],[p,b]],[/smart-tv.+(samsung)/i],[v,[p,y]],[/hbbtv.+maple;(\d+)/i],[[d,/^/,"SmartTV"],[v,D],[p,y]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[v,A],[p,y]],[/(apple) ?tv/i],[v,[d,x+" TV"],[p,y]],[/crkey/i],[[d,C+"cast"],[v,L],[p,y]],[/droid.+aft(\w+)( bui|\))/i],[d,[v,E],[p,y]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[d,[v,N],[p,y]],[/(bravia[\w ]+)( bui|\))/i],[d,[v,U],[p,y]],[/(mitv-\w{5}) bui/i],[d,[v,z],[p,y]],[/Hbbtv.*(technisat) (.*);/i],[v,d,[p,y]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[v,K],[d,K],[p,y]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,y]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[v,d,[p,h]],[/droid.+; (shield) bui/i],[d,[v,"Nvidia"],[p,h]],[/(playstation [345portablevi]+)/i],[d,[v,U],[p,h]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[d,[v,_],[p,h]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[d,[v,D],[p,S]],[/((pebble))app/i],[v,d,[p,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[d,[v,x],[p,S]],[/droid.+; (glass) \d/i],[d,[v,L],[p,S]],[/droid.+; (wt63?0{2,3})\)/i],[d,[v,F],[p,S]],[/droid.+; (glass) \d/i],[d,[v,L],[p,S]],[/(pico) (4|neo3(?: link|pro)?)/i],[v,d,[p,S]],[/; (quest( \d| pro)?)/i],[d,[v,B],[p,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[v,[p,T]],[/(aeobc)\b/i],[d,[v,E],[p,T]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[d,[p,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[d,[p,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,b]],[/(android[-\w\. ]{0,9});.+buil/i],[d,[v,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[f,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[f,m],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,m],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[f,[m,X,Z]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[m,X,Z],[f,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,V],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,m],[/\(bb(10);/i],[m,[f,O]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[f,P+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[f,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,$],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,m],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,m]]},Y=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof Y))return new Y(e,t).getResult();var n=typeof r!==a&&r.navigator?r.navigator:o,i=e||(n&&n.userAgent?n.userAgent:""),h=n&&n.userAgentData?n.userAgentData:o,y=t?function(e,t){var n={};for(var i in e)t[i]&&t[i].length%2==0?n[i]=t[i].concat(e[i]):n[i]=e[i];return n}(Q,t):Q,S=n&&n.userAgent==i;return this.getBrowser=function(){var e,t={};return t[f]=o,t[m]=o,J.call(t,i,y.browser),t[c]=typeof(e=t[m])===l?e.replace(/[^\d\.]/g,"").split(".")[0]:o,S&&n&&n.brave&&typeof n.brave.isBrave==s&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[g]=o,J.call(e,i,y.cpu),e},this.getDevice=function(){var e={};return e[v]=o,e[d]=o,e[p]=o,J.call(e,i,y.device),S&&!e[p]&&h&&h.mobile&&(e[p]=b),S&&"Macintosh"==e[d]&&n&&typeof n.standalone!==a&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[d]="iPad",e[p]=w),e},this.getEngine=function(){var e={};return e[f]=o,e[m]=o,J.call(e,i,y.engine),e},this.getOS=function(){var e={};return e[f]=o,e[m]=o,J.call(e,i,y.os),S&&!e[f]&&h&&h.platform&&"Unknown"!=h.platform&&(e[f]=h.platform.replace(/chrome os/i,$).replace(/macos/i,V)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===l&&e.length>500?K(e,500):e,this},this.setUA(i),this};Y.VERSION="1.0.40",Y.BROWSER=H([f,m,c]),Y.CPU=H([g]),Y.DEVICE=H([d,v,p,h,b,y,w,S,T]),Y.ENGINE=Y.OS=H([f,m]),typeof t!==a?(e.exports&&(t=e.exports=Y),t.UAParser=Y):n.amdO?(i=function(){return Y}.call(t,n,t,e))===o||(e.exports=i):typeof r!==a&&(r.UAParser=Y);var ee=typeof r!==a&&(r.jQuery||r.Zepto);if(ee&&!ee.ua){var te=new Y;ee.ua=te.getResult(),ee.ua.get=function(){return te.getUA()},ee.ua.set=function(e){te.setUA(e);var t=te.getResult();for(var n in t)ee.ua[n]=t[n]}}}("object"==typeof window?window:this)},6089:t=>{"use strict";t.exports=e},7781:e=>{"use strict";e.exports=t},8531:e=>{"use strict";e.exports=n},2007:e=>{"use strict";e.exports=i},3241:e=>{"use strict";e.exports=r},1308:e=>{"use strict";e.exports=o},8146:e=>{"use strict";e.exports=s},5959:e=>{"use strict";e.exports=a},8398:e=>{"use strict";e.exports=u},1159:e=>{"use strict";e.exports=l},7694:e=>{"use strict";e.exports=c},1269:e=>{"use strict";e.exports=d},2533:e=>{"use strict";e.exports=JSON.parse('{"id":"grafana-metricsdrilldown-app"}')}},h={};function b(e){var t=h[e];if(void 0!==t)return t.exports;var n=h[e]={id:e,loaded:!1,exports:{}};return g[e].call(n.exports,n,n.exports,b),n.loaded=!0,n.exports}b.m=g,b.amdO={},b.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return b.d(t,{a:t}),t},p=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,b.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var n=Object.create(null);b.r(n);var i={};f=f||[null,p({}),p([]),p(p)];for(var r=2&t&&e;"object"==typeof r&&!~f.indexOf(r);r=p(r))Object.getOwnPropertyNames(r).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,b.d(n,i),n},b.d=(e,t)=>{for(var n in t)b.o(t,n)&&!b.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},b.f={},b.e=e=>Promise.all(Object.keys(b.f).reduce(((t,n)=>(b.f[n](e,t),t)),[])),b.u=e=>e+".js?_cache="+{78:"aef19aea7ae38d61f33b",256:"91a505d2def3511566fa",513:"bd6b649be2e9bf43de55",601:"4787f18fd19f3b2ec3ea",871:"d98efe39b170214658e1",944:"641a2488269c99381577"}[e],b.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),b.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),v={},m="grafana-metricsdrilldown-app:",b.l=(e,t,n,i)=>{if(v[e])v[e].push(t);else{var r,o;if(void 0!==n)for(var s=document.getElementsByTagName("script"),a=0;a<s.length;a++){var u=s[a];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==m+n){r=u;break}}r||(o=!0,(r=document.createElement("script")).charset="utf-8",r.timeout=120,b.nc&&r.setAttribute("nonce",b.nc),r.setAttribute("data-webpack",m+n),r.src=e,0!==r.src.indexOf(window.location.origin+"/")&&(r.crossOrigin="anonymous"),r.integrity=b.sriHashes[i],r.crossOrigin="anonymous"),v[e]=[t];var l=(t,n)=>{r.onerror=r.onload=null,clearTimeout(c);var i=v[e];if(delete v[e],r.parentNode&&r.parentNode.removeChild(r),i&&i.forEach((e=>e(n))),t)return t(n)},c=setTimeout(l.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=l.bind(null,r.onerror),r.onload=l.bind(null,r.onload),o&&document.head.appendChild(r)}},b.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},b.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),b.p="public/plugins/grafana-metricsdrilldown-app/",b.sriHashes={78:"sha256-ALMGkiXKxLqFgwRMyxfLgZ2PIg6athvGFIfVabJ0BZM=",256:"sha256-lEy7Ed5n2fJBC4eHRmm504TbyTHyjYXk8RtA1W5asRw=",513:"sha256-MqaoB8q9TcZVYb7hfJkjgxw5MEAVqvMSBdQPWOR28bU=",601:"sha256-90fBMdwTzGR/E4qIdySdeMYSEf+M9O1IOCXAXk5jjIQ=",871:"sha256-S45dqb0OmEPyKcH7HerU9S7x2r2cjjx6g4pIyAQ30jU=",944:"sha256-m4Bf+QaNH0TUun0+Ik3LvWDdsiOnJcFxSCVHynSjBx0="},(()=>{b.b=document.baseURI||self.location.href;var e={231:0};b.f.j=(t,n)=>{var i=b.o(e,t)?e[t]:void 0;if(0!==i)if(i)n.push(i[2]);else{var r=new Promise(((n,r)=>i=e[t]=[n,r]));n.push(i[2]=r);var o=b.p+b.u(t),s=new Error;b.l(o,(n=>{if(b.o(e,t)&&(0!==(i=e[t])&&(e[t]=void 0),i)){var r=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;s.message="Loading chunk "+t+" failed.\n("+r+": "+o+")",s.name="ChunkLoadError",s.type=r,s.request=o,i[1](s)}}),"chunk-"+t,t)}};var t=(t,n)=>{var i,r,[o,s,a]=n,u=0;if(o.some((t=>0!==e[t]))){for(i in s)b.o(s,i)&&(b.m[i]=s[i]);if(a)a(b)}for(t&&t(n);u<o.length;u++)r=o[u],b.o(e,r)&&e[r]&&e[r][0](),e[r]=0},n=self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var w={};return(()=>{"use strict";b.r(w),b.d(w,{plugin:()=>T});var e=b(1308),t=b.n(e);b.p=t()&&t().uri?t().uri.slice(0,t().uri.lastIndexOf("/")+1):"public/plugins/grafana-metricsdrilldown-app/";var n=b(7781),i=b(2007),r=b(5959),o=b.n(r),s=b(4137),a=b(2445);const u="Grafana Metrics Drilldown",l=`Open in ${u}`,c=`Open current query in the ${u} view`,d=[{targets:[n.PluginExtensionPoints.DashboardPanelMenu,n.PluginExtensionPoints.ExploreToolbarAction],title:l,description:c,icon:"gf-prometheus",category:"metrics-drilldown",path:f(s.bw.Drilldown),configure:e=>{if(void 0===e)return;if("pluginId"in e&&"timeseries"!==e.pluginId)return;const t=e.targets.filter(v);if(!(null==t?void 0:t.length))return;const{datasource:n,expr:i}=t[0];if(!i||"prometheus"!==(null==n?void 0:n.type))return;const r=function(e){let t;const n=[],i=e.trim(),r=i.match(/^([a-zA-Z_:][a-zA-Z0-9_:]*)\s*(?:\{([^}]*)\})?/);if(r){const e=r[1],o=r[2];"("===i[r[0].length]&&g.has(e)||(t=e),void 0!==o&&m(o,n)}if(!t){const e=/([a-zA-Z_:][a-zA-Z0-9_:]*)\s*(?:\{([^}]*)\})?/g;let r;for(;null!==(r=e.exec(i));){const e=r[1],i=r[2];if(!g.has(e)){t=e,i&&m(i,n);break}}}return{metric:t,labelFilters:n,query:e}}(i),o="timeRange"in e&&"object"==typeof e.timeRange&&null!==e.timeRange&&"from"in e.timeRange&&"to"in e.timeRange?e.timeRange:void 0,a=function(e,t){const n=new URLSearchParams(null==t?void 0:t.toString());return e.forEach((([e,t])=>{t&&n.append(e,t)})),n}([[p.Metric,r.metric],[p.TimeRangeFrom,null==o?void 0:o.from],[p.TimeRangeTo,null==o?void 0:o.to],[p.DatasourceId,n.uid],...r.labelFilters.map((e=>[p.Filters,`${e.label}${e.op}${e.value}`]))]);return{path:f(s.bw.Drilldown,a)}}}];function f(e,t){return`${s.Gy}/${e}${t?`?${t.toString()}`:""}`}const p={TimeRangeFrom:"from",TimeRangeTo:"to",Metric:"metric",DatasourceId:"var-ds",Filters:"var-filters"};function v(e){return"expr"in e}function m(e,t){const n=e.split(","),i=/^\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*([=!~]+)\s*"((?:[^"\\]|\\.)*)"\s*$/;n.forEach((n=>{if(""===n.trim())return;const r=n.match(i);if(r){const e=r[3].replace(/\\(.)/g,"$1");t.push({label:r[1],op:r[2],value:e})}else a.v.warn(`[Metrics Drilldown] Could not parse label part: "${n}" for labels: ${e}`)}))}const g=new Set(["rate","increase","sum","avg","count","max","min","stddev","stdvar","topk","bottomk","quantile","histogram_quantile","label_replace","label_join","vector","scalar","time","timestamp","month","year","day_of_month","day_of_week","days_in_month","hour","minute","by","without","on","ignoring","group_left","group_right"]);function h(e,t,n,i,r,o,s){try{var a=e[o](s),u=a.value}catch(e){return void n(e)}a.done?t(u):Promise.resolve(u).then(i,r)}function y(e){return function(){var t=this,n=arguments;return new Promise((function(i,r){var o=e.apply(t,n);function s(e){h(o,i,r,s,a,"next",e)}function a(e){h(o,i,r,s,a,"throw",e)}s(void 0)}))}}const S=(0,r.lazy)(y((function*(){const{wasmSupported:e}=yield Promise.all([b.e(944),b.e(601)]).then(b.bind(b,2601)),{default:t}=yield b.e(944).then(b.bind(b,6944));return e()&&(yield t()),Promise.all([b.e(944),b.e(256),b.e(513)]).then(b.bind(b,9617))}))),T=(new n.AppPlugin).setRootPage((e=>o().createElement(r.Suspense,{fallback:o().createElement(i.LoadingPlaceholder,{text:""})},o().createElement(S,e))));for(const e of d)T.addLink(e)})(),w})()));
//# sourceMappingURL=module.js.map