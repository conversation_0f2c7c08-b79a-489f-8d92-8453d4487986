#!/bin/bash
# Check Grafana status specifically

echo "🔍 Checking Grafana status..."
docker compose logs grafana --tail 20

echo
echo "🔍 Checking Loki status..."
docker compose logs loki --tail 10

echo
echo "🔍 Testing Grafana connection..."
for i in {1..5}; do
    echo -n "Attempt $i: "
    if curl -s -f http://localhost:3000 >/dev/null 2>&1; then
        echo "✅ Grafana is accessible!"
        break
    else
        echo "⏳ Not ready yet, waiting..."
        sleep 2
    fi
done

echo
echo "🔍 Testing metrics endpoint..."
curl -s http://localhost:8000/admin/plugins/security_monitor/api/prometheus | head -10
