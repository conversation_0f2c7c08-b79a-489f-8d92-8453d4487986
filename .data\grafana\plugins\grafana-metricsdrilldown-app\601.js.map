{"version": 3, "file": "601.js?_cache=4787f18fd19f3b2ec3ea", "mappings": "kLAmLA,MAAMA,EAAS,2BAER,SAASC,EAA8EC,EAAUC,IACtGC,EAAAA,EAAAA,mBAAkB,GAAGJ,IAASE,IAASC,EACzC,CAGO,SAASE,EACdC,EACAC,EACAC,GAEA,GAAIF,EAAWG,SAAWF,EAAWE,OACnC,IAAK,MAAMC,KAAaH,EACtB,IAAK,MAAMI,KAAaL,EAClBI,EAAUE,MAAQD,EAAUC,KAC1BF,EAAUG,QAAUF,EAAUE,OAChCZ,EAAqB,uBAAwB,CAC3Ca,MAAOJ,EAAUE,IACjBG,OAAQ,UACRC,MAAO,eACPC,wBAAyBT,SAAAA,SAM9B,GAAIF,EAAWG,OAASF,EAAWE,OACxC,IAAK,MAAMC,KAAaH,EAAY,CAClC,IAAIW,GAAgB,EACpB,IAAK,MAAMP,KAAaL,EACtB,GAAII,EAAUE,MAAQD,EAAUC,IAAK,CACnCM,GAAgB,EAChB,KACF,CAEGA,GACHjB,EAAqB,uBAAwB,CAC3Ca,MAAOJ,EAAUE,IACjBG,OAAQ,UACRC,MAAO,gBAGb,MAEA,IAAK,MAAML,KAAaL,EAAY,CAClC,IAAIa,GAAgB,EACpB,IAAK,MAAMT,KAAaH,EACtB,GAAIG,EAAUE,MAAQD,EAAUC,IAAK,CACnCO,GAAgB,EAChB,KACF,CAEGA,GACHlB,EAAqB,uBAAwB,CAAEa,MAAOH,EAAUC,IAAKG,OAAQ,QAASC,MAAO,gBAEjG,CAEJ,C,iBC3OO,SAASI,EAA2BC,G,IAC1BA,EAAf,MAAMC,EAAwB,QAAfD,EAAAA,EAAME,OAAO,UAAbF,IAAAA,OAAAA,EAAAA,EAAiBC,OAEhC,IAAKA,EACH,OAAO,KAGT,MAAME,EAAOC,OAAOD,KAAKF,GACzB,OAAoB,IAAhBE,EAAKf,OACA,KAGFa,EAAOE,EAAK,GACrB,C,+LCDO,MAAME,GAAaC,EAAAA,EAAAA,UACxB,CAACC,EAAqBC,EAAgBC,EAAY,SAChD,GAAe,iBAAXD,EACF,OAAOE,EAAiBH,EAAQ,OAGlC,GAAe,0BAAXC,EACF,OAAOE,EAAiBH,EAAQ,QAGnB,aAAXC,GACFG,EAAoBJ,GAGtB,MAAMK,EAAWC,I,IAYbC,EAXF,IACE,GAAe,aAAXN,EACF,OAAOO,EAAsBR,EAAQM,EAEzC,CAAE,MAAOG,GACPC,QAAQC,MAAMF,GAEdR,EAASW,EAAAA,UAAUC,MACrB,CACA,MAAMN,EAAeO,EAAAA,cAAcC,IAAId,G,IAErCM,E,IACKtB,EAAP,OAAoB,QAAbA,GAD4C,QAAjDsB,EAAmB,QAAnBA,EAAAA,EAAaS,cAAbT,IAAAA,OAAAA,EAAAA,EAAAA,KAAAA,EAAsBD,EAAUX,OAAO,IAAI,GAAM,UAAjDY,IAAAA,EAAAA,GAA0DU,EAAAA,EAAAA,iBAAgBX,EAAUX,OAAO,IAAI,GAAM,IAC1FM,UAANhB,IAAAA,EAAAA,EAAiB,CAAC,EAGrBiC,EAAclB,EAAOmB,KAAKb,IAAe,CAC7CrB,MAAOoB,EAAQC,GACfA,UAAWA,MAcb,OAXAY,EAAYE,MAAK,CAACC,EAAGC,SACHC,IAAZF,EAAEpC,YAAmCsC,IAAZD,EAAErC,MACtBqC,EAAErC,MAAQoC,EAAEpC,MAEd,IAGS,QAAdiB,GACFgB,EAAYM,UAGPN,EAAYC,KAAI,EAAGb,eAAgBA,GAAU,IAEtD,CAACN,EAAqBC,EAAgBC,EAAY,SAChD,MAAMuB,EAAiBzB,EAAOnB,OAAS,EAAImB,EAAO,GAAGL,OAAO,GAAG+B,OAAO,GAAK,EACrEC,EACJ3B,EAAOnB,OAAS,EACZmB,EAAOA,EAAOnB,OAAS,GAAGc,OAAO,GAAG+B,OAAO1B,EAAOA,EAAOnB,OAAS,GAAGc,OAAO,GAAG+B,OAAO7C,OAAS,GAC/F,EAIN,MADY,GAFOmB,EAAOnB,OAAS,GAAIW,EAAAA,EAAAA,GAA2BQ,EAAO,IAAM,MAC7DA,EAAOnB,OAAS,GAAIW,EAAAA,EAAAA,GAA2BQ,EAAOA,EAAOnB,OAAS,IAAM,MACpD4C,KAAkBE,KAAiB3B,EAAOnB,UAAUoB,KAAUC,GAC9F,IAIRE,EAAuBJ,IAC3B,IAAK4B,IACH,OAIF,MAAMC,GAASC,EAAAA,EAAAA,qBAAoB,CAAEC,OAAQ/B,IAC7C,IAAK6B,EACH,OAIF,MACMG,EADeH,EAAOlC,OAAOsC,QAAQC,GAAMA,EAAEC,OAASC,EAAAA,UAAUC,SAC1ClB,KAAKnB,GAAW,IAAIsC,aAAatC,EAAO0B,UAEpE,IACE,MAAMa,EAAWC,EAAAA,gBAAgBC,OAAO,CAAEC,YAAa,KAAOC,WAAWX,GACzEY,EAAWL,EAASM,QACtB,CAAE,MAAOpC,GACPC,QAAQC,MAAMF,GACdmC,OAAWrB,CACb,GAGF,IAAIqB,EAEG,MAAMpC,EAAwB,CAACR,EAAqB8C,KACzD,IAAKlB,IACH,MAAM,IAAImB,MAAM,2CAElB,IAAKH,EACH,MAAM,IAAIG,MAAM,qCAGlB,MAAMC,EAAQhD,EAAOiD,QAAQH,GAC7B,OAAIF,EAASM,cAAcF,GAAOG,WACxBP,EAASM,cAAcF,GAAOI,iBAAiBvE,OAGlD,CAAC,EAGGsB,EAAmB,CAACH,EAAqBE,KACpD,MAAMmD,EAAe,IAAIrD,GAYzB,OAXAqD,EAAajC,MAAK,CAACC,EAAGC,KACpB,MAAMgC,GAAS9D,EAAAA,EAAAA,GAA2B6B,GACpCkC,GAAS/D,EAAAA,EAAAA,GAA2B8B,GAC1C,OAAKgC,GAAWC,GAGaA,QAAtBD,EAAAA,aAAAA,EAAAA,EAAQE,cAAcD,UAAtBD,IAAAA,EAAAA,EAFE,E,IAEFA,CAAkC,IAEzB,SAAdpD,GACFmD,EAAa7B,UAER6B,CAAY,EAGRzB,EAAgB,KAC3B,MAAM6B,EAAiC,iBAAhBC,YAMvB,OAJKD,IACHpF,EAAAA,EAAAA,GAAqB,qBAAsB,CAAC,GAGvCoF,CAAO,C", "sources": ["webpack://grafana-metricsdrilldown-app/./interactions.ts", "webpack://grafana-metricsdrilldown-app/./services/levels.ts", "webpack://grafana-metricsdrilldown-app/./services/sorting.ts"], "sourcesContent": ["import { type AdHocVariableFilter } from '@grafana/data';\nimport { reportInteraction } from '@grafana/runtime';\n\nimport { type LabelBreakdownSortingOption as BreakdownSortByOption } from 'Breakdown/SortByScene';\nimport { type SortingOption as MetricsReducerSortByOption } from 'WingmanDataTrail/ListControls/MetricsSorter/MetricsSorter';\n\nimport { type BreakdownLayoutType } from './Breakdown/types';\nimport { type ActionViewType } from './MetricScene';\n\n// prettier-ignore\nexport type Interactions = {\n  // User selected a label to view its breakdown.\n  label_selected: {\n    label: string;\n    cause: (\n      // By clicking the \"select\" button on that label's breakdown panel\n      | 'breakdown_panel'\n      // By clicking on the label selector at the top of the breakdown\n      | 'selector'\n    );\n    otel_resource_attribute?: boolean;\n  };\n  // User changed a label filter\n  label_filter_changed: {\n    label: string;\n    action: 'added' | 'removed' | 'changed';\n    cause: 'breakdown' | 'adhoc_filter';\n    otel_resource_attribute?: boolean;\n  };\n  // User changed the breakdown layout\n  breakdown_layout_changed: { layout: BreakdownLayoutType };\n  // A metric exploration has started due to one of the following causes\n  exploration_started: {\n    cause: (\n      // a bookmark was clicked from the home page\n      | 'bookmark_clicked'\n      // a recent exploration was clicked from the home page\n      | 'recent_clicked'\n      // \"new exploration\" was clicked from the home page\n      | 'new_clicked'\n      // the page was loaded (or reloaded) from a URL which matches one of the recent explorations\n      | 'loaded_local_recent_url'\n      // the page was loaded from a URL which did not match one of the recent explorations, and is assumed shared\n      | 'loaded_shared_url'\n      // the exploration was opened from the dashboard panel menu and is embedded in a drawer\n      | 'dashboard_panel'\n    );\n  };\n  // A user has changed a bookmark\n  bookmark_changed: {\n    action: (\n      // Toggled on or off from the bookmark icon\n      | 'toggled_on'\n      | 'toggled_off'\n      // Deleted from the homepage bookmarks list\n      | 'deleted'\n    );\n  };\n  // User changes metric explore settings\n  settings_changed: { stickyMainGraph?: boolean };\n  // User clicks on tab to change the action view\n  metric_action_view_changed: { \n    view: ActionViewType \n\n    // The number of related logs\n    related_logs_count?: number\n  };\n  // User clicks on one of the action buttons associated with a selected metric\n  selected_metric_action_clicked: {\n    action: (\n      // Opens the metric queries in Explore\n      | 'open_in_explore'\n      // Clicks on the share URL button\n      | 'share_url'\n      // Deselects the current selected metrics by clicking the \"Select new metric\" button\n      | 'unselect'\n      // When in embedded mode, clicked to open the exploration from the embedded view\n      | 'open_from_embedded'\n    );\n  };\n  // User clicks on one of the action buttons associated with related logs\n  related_logs_action_clicked: {\n    action: (\n      // Opens Logs Drilldown\n      | 'open_logs_drilldown'\n      // Logs data source changed\n      | 'logs_data_source_changed'\n    );\n  };\n  // User selects a metric\n  metric_selected: {\n    from: (\n      // By clicking \"Select\" on a metric panel when on the no-metric-selected metrics list view\n      | 'metric_list'\n      // By clicking \"Select\" on a metric panel when on the related metrics tab\n      | 'related_metrics'\n    );\n    // The number of search terms activated when the selection was made\n    searchTermCount: number | null;\n  };\n  // User opens/closes the prefix filter dropdown\n  prefix_filter_clicked: {\n    from: (\n      // By clicking \"Select\" on a metric panel when on the no-metric-selected metrics list view\n      | 'metric_list'\n      // By clicking \"Select\" on a metric panel when on the related metrics tab\n      | 'related_metrics'\n    )\n    action: (\n      // Opens the dropdown\n      | 'open'\n      // Closes the dropdown\n      | 'close'\n    )\n  };\n  // User types in the quick search bar\n  quick_search_used: {};\n  sorting_changed: {\n    // By clicking on the sort by variable in the metrics reducer\n    from: 'metrics-reducer',\n    // The sort by option selected\n    sortBy: MetricsReducerSortByOption\n  } | {\n    // By clicking on the sort by component in the label breakdown\n    from: 'label-breakdown',\n    // The sort by option selected\n    sortBy: BreakdownSortByOption\n  };\n  wasm_not_supported: {},\n  missing_otel_labels_by_truncating_job_and_instance: {\n    metric?: string;\n  },\n  deployment_environment_migrated: {},\n  otel_experience_used: {},\n  otel_experience_toggled: {\n    value: ('on'| 'off')\n  },\n  native_histogram_examples_closed: {},\n  native_histogram_example_clicked: {\n    metric: string;\n  },\n  // User toggles the Wingman sidebar\n  metrics_sidebar_toggled: {\n    action: (\n      // Opens the sidebar section\n      | 'opened'\n      // Closes the sidebar section\n      | 'closed'\n    ),\n    section?: string\n  },\n  // User clicks into the prefix filter section of the sidebar\n  sidebar_prefix_filter_section_clicked: {},\n  // User applies any prefix filter from the sidebar\n  sidebar_prefix_filter_applied: {\n    // Number of prefix filters applied (optional)\n    filter_count?: number;\n  },\n  // User clicks into the suffix filter section of the sidebar\n  sidebar_suffix_filter_section_clicked: {},\n  // User applies any suffix filter from the sidebar\n  sidebar_suffix_filter_applied: {\n    // Number of suffix filters applied (optional)\n    filter_count?: number;\n  },\n  // User selects a rules filter from the Wingman sidebar\n  sidebar_rules_filter_selected: {\n    filter_type: (\n      | 'non_rules_metrics'\n      | 'recording_rules'\n    )\n  },\n  // User applies a label filter from the sidebar\n  sidebar_group_by_label_filter_applied: {\n    // The label that was applied (optional)\n    label?: string;\n  }\n};\n\nconst PREFIX = 'grafana_explore_metrics_';\n\nexport function reportExploreMetrics<E extends keyof Interactions, P extends Interactions[E]>(event: E, payload: P) {\n  reportInteraction(`${PREFIX}${event}`, payload);\n}\n\n/** Detect the single change in filters and report the event, assuming it came from manipulating the adhoc filter */\nexport function reportChangeInLabelFilters(\n  newFilters: AdHocVariableFilter[],\n  oldFilters: AdHocVariableFilter[],\n  otel?: boolean\n) {\n  if (newFilters.length === oldFilters.length) {\n    for (const oldFilter of oldFilters) {\n      for (const newFilter of newFilters) {\n        if (oldFilter.key === newFilter.key) {\n          if (oldFilter.value !== newFilter.value) {\n            reportExploreMetrics('label_filter_changed', {\n              label: oldFilter.key,\n              action: 'changed',\n              cause: 'adhoc_filter',\n              otel_resource_attribute: otel ?? false,\n            });\n          }\n        }\n      }\n    }\n  } else if (newFilters.length < oldFilters.length) {\n    for (const oldFilter of oldFilters) {\n      let foundOldLabel = false;\n      for (const newFilter of newFilters) {\n        if (oldFilter.key === newFilter.key) {\n          foundOldLabel = true;\n          break;\n        }\n      }\n      if (!foundOldLabel) {\n        reportExploreMetrics('label_filter_changed', {\n          label: oldFilter.key,\n          action: 'removed',\n          cause: 'adhoc_filter',\n        });\n      }\n    }\n  } else {\n    for (const newFilter of newFilters) {\n      let foundNewLabel = false;\n      for (const oldFilter of oldFilters) {\n        if (oldFilter.key === newFilter.key) {\n          foundNewLabel = true;\n          break;\n        }\n      }\n      if (!foundNewLabel) {\n        reportExploreMetrics('label_filter_changed', { label: newFilter.key, action: 'added', cause: 'adhoc_filter' });\n      }\n    }\n  }\n}\n", "import { type DataFrame } from '@grafana/data';\n\nexport function getLabelValueFromDataFrame(frame: DataFrame) {\n  const labels = frame.fields[1]?.labels;\n\n  if (!labels) {\n    return null;\n  }\n\n  const keys = Object.keys(labels);\n  if (keys.length === 0) {\n    return null;\n  }\n\n  return labels[keys[0]];\n}\n", "import { OutlierDetector, type OutlierOutput } from '@bsull/augurs/outlier';\nimport {\n  doStandardCalcs,\n  fieldReducers,\n  FieldType,\n  outerJoinDataFrames,\n  ReducerID,\n  type DataFrame,\n} from '@grafana/data';\nimport { memoize } from 'lodash';\n\nimport { reportExploreMetrics } from '../interactions';\nimport { getLabelValueFromDataFrame } from './levels';\n\nexport const sortSeries = memoize(\n  (series: DataFrame[], sortBy: string, direction = 'asc') => {\n    if (sortBy === 'alphabetical') {\n      return sortSeriesByName(series, 'asc');\n    }\n\n    if (sortBy === 'alphabetical-reversed') {\n      return sortSeriesByName(series, 'desc');\n    }\n\n    if (sortBy === 'outliers') {\n      initOutlierDetector(series);\n    }\n\n    const reducer = (dataFrame: DataFrame) => {\n      try {\n        if (sortBy === 'outliers') {\n          return calculateOutlierValue(series, dataFrame);\n        }\n      } catch (e) {\n        console.error(e);\n        // ML sorting panicked, fallback to stdDev\n        sortBy = ReducerID.stdDev;\n      }\n      const fieldReducer = fieldReducers.get(sortBy);\n      const value =\n        fieldReducer.reduce?.(dataFrame.fields[1], true, true) ?? doStandardCalcs(dataFrame.fields[1], true, true);\n      return value[sortBy] ?? 0;\n    };\n\n    const seriesCalcs = series.map((dataFrame) => ({\n      value: reducer(dataFrame),\n      dataFrame: dataFrame,\n    }));\n\n    seriesCalcs.sort((a, b) => {\n      if (a.value !== undefined && b.value !== undefined) {\n        return b.value - a.value;\n      }\n      return 0;\n    });\n\n    if (direction === 'asc') {\n      seriesCalcs.reverse();\n    }\n\n    return seriesCalcs.map(({ dataFrame }) => dataFrame);\n  },\n  (series: DataFrame[], sortBy: string, direction = 'asc') => {\n    const firstTimestamp = series.length > 0 ? series[0].fields[0].values[0] : 0;\n    const lastTimestamp =\n      series.length > 0\n        ? series[series.length - 1].fields[0].values[series[series.length - 1].fields[0].values.length - 1]\n        : 0;\n    const firstValue = series.length > 0 ? getLabelValueFromDataFrame(series[0]) : '';\n    const lastValue = series.length > 0 ? getLabelValueFromDataFrame(series[series.length - 1]) : '';\n    const key = `${firstValue}_${lastValue}_${firstTimestamp}_${lastTimestamp}_${series.length}_${sortBy}_${direction}`;\n    return key;\n  }\n);\n\nconst initOutlierDetector = (series: DataFrame[]) => {\n  if (!wasmSupported()) {\n    return;\n  }\n\n  // Combine all frames into one by joining on time.\n  const joined = outerJoinDataFrames({ frames: series });\n  if (!joined) {\n    return;\n  }\n\n  // Get number fields: these are our series.\n  const joinedSeries = joined.fields.filter((f) => f.type === FieldType.number);\n  const points = joinedSeries.map((series) => new Float64Array(series.values));\n\n  try {\n    const detector = OutlierDetector.dbscan({ sensitivity: 0.4 }).preprocess(points);\n    outliers = detector.detect();\n  } catch (e) {\n    console.error(e);\n    outliers = undefined;\n  }\n};\n\nlet outliers: OutlierOutput | undefined = undefined;\n\nexport const calculateOutlierValue = (series: DataFrame[], data: DataFrame): number => {\n  if (!wasmSupported()) {\n    throw new Error('WASM not supported, fall back to stdDev');\n  }\n  if (!outliers) {\n    throw new Error('Initialize outlier detector first');\n  }\n\n  const index = series.indexOf(data);\n  if (outliers.seriesResults[index].isOutlier) {\n    return -outliers.seriesResults[index].outlierIntervals.length;\n  }\n\n  return 0;\n};\n\nexport const sortSeriesByName = (series: DataFrame[], direction: string) => {\n  const sortedSeries = [...series];\n  sortedSeries.sort((a, b) => {\n    const valueA = getLabelValueFromDataFrame(a);\n    const valueB = getLabelValueFromDataFrame(b);\n    if (!valueA || !valueB) {\n      return 0;\n    }\n    return valueA?.localeCompare(valueB) ?? 0;\n  });\n  if (direction === 'desc') {\n    sortedSeries.reverse();\n  }\n  return sortedSeries;\n};\n\nexport const wasmSupported = () => {\n  const support = typeof WebAssembly === 'object';\n\n  if (!support) {\n    reportExploreMetrics('wasm_not_supported', {});\n  }\n\n  return support;\n};\n"], "names": ["PREFIX", "reportExploreMetrics", "event", "payload", "reportInteraction", "reportChangeInLabelFilters", "newFilters", "oldFilters", "otel", "length", "old<PERSON><PERSON>er", "newFilter", "key", "value", "label", "action", "cause", "otel_resource_attribute", "foundOldLabel", "found<PERSON><PERSON><PERSON><PERSON><PERSON>", "getLabelValueFromDataFrame", "frame", "labels", "fields", "keys", "Object", "sortSeries", "memoize", "series", "sortBy", "direction", "sortSeriesByName", "initOutlierDetector", "reducer", "dataFrame", "fieldReducer", "calculateOutlierValue", "e", "console", "error", "ReducerID", "stdDev", "fieldReducers", "get", "reduce", "doStandardCalcs", "seriesCalcs", "map", "sort", "a", "b", "undefined", "reverse", "firstTimestamp", "values", "lastTimestamp", "wasmSupported", "joined", "outerJoinDataFrames", "frames", "points", "filter", "f", "type", "FieldType", "number", "Float64Array", "detector", "OutlierDetector", "dbscan", "sensitivity", "preprocess", "outliers", "detect", "data", "Error", "index", "indexOf", "seriesResults", "isOutlier", "outlierIntervals", "sortedSeries", "valueA", "valueB", "localeCompare", "support", "WebAssembly"], "sourceRoot": ""}