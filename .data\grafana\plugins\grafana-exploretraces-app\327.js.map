{"version": 3, "file": "327.js", "mappings": "g2CAyBA,MAsIMA,EAAaC,IAA0B,CAC3CC,UAAWC,EAAAA,GAAG;aACHF,EAAMG,OAAOC,KAAKC;IAE7BC,UAAWJ,EAAAA,GAAG;kBACEF,EAAMO,QAAQ;IAE9BC,YAAaN,EAAAA,GAAG;kBACAF,EAAMO,QAAQ;MAI1BE,EAAAA,W,MAAwB,aAAOC,EAAkBC,GACrD,UACQC,EAAaF,EAAUC,GAI7BE,EAAAA,gBAAgBC,QAClB,CAAE,MAAOC,GACPC,QAAQC,MAAM,kCAAmCF,EACnD,CACF,I,gBAVqCL,EAAkBC,G,gCAAjDF,GAYAS,EACO,CACTC,UAAW,2BACXC,OAAQ,yBACRC,OAAQ,yBACRC,OAAQ,8BAICV,EAAAA,W,MAAe,aAAOF,EAAkBC,GACnD,MAAMY,GAAWC,EAAAA,EAAAA,iBAAgBC,MAAM,CACrCC,IAAK,gBAAgBhB,aACrBiB,OAAQ,OACRhB,SAKF,aAF2BiB,EAAAA,EAAAA,eAAcL,IAErBZ,IACtB,I,gBAVmCD,EAAkBC,G,gCAAxCC,GAYb,EAnLkB,EAAGiB,aACnB,MAAMC,GAAIC,EAAAA,EAAAA,YAAWhC,IACf,QAAEiC,EAAO,OAAEC,EAAM,SAAEC,GAAaL,EAAOM,MACtCC,EAAOC,IAAYC,EAAAA,EAAAA,UAAgB,CACxCjB,QAAQa,aAAAA,EAAAA,EAAUb,SAAU,GAC5BD,OAAQ,GACRmB,YAAaC,QAAQN,aAAAA,EAAAA,EAAUK,eAwBjC,OACE,kBAACE,MAAAA,CAAIC,cAAaxB,EAAkBC,WAElC,kBAACwB,EAAAA,SAAQA,CAACC,MAAM,qBACZZ,GACA,oCACE,kBAACS,MAAAA,CAAII,UAAWf,EAAE7B,WAAW,wCAC7B,kBAAC6C,EAAAA,OAAMA,CACLD,UAAWf,EAAExB,UACbyC,QAAQ,UACRC,QAAS,IACPvC,EAAsBoB,EAAOM,KAAKc,GAAI,CACpCjB,SAAS,EACTC,QAAQ,EACRC,cAGL,kBAOJF,GACC,oCACE,kBAACS,MAAAA,CAAII,UAAWf,EAAE7B,WAAW,oCAC7B,kBAAC6C,EAAAA,OAAMA,CACLD,UAAWf,EAAExB,UACbyC,QAAQ,cACRC,QAAS,IACPvC,EAAsBoB,EAAOM,KAAKc,GAAI,CACpCjB,SAAS,EACTC,QAAQ,EACRC,cAGL,oBAQP,kBAACS,EAAAA,SAAQA,CAACC,MAAM,eAAeC,UAAWf,EAAEtB,aAE1C,kBAAC0C,EAAAA,MAAKA,CAACN,MAAM,UAAUO,YAAY,qDACjC,kBAACC,EAAAA,YAAWA,CACVC,MAAO,GACPX,cAAaxB,EAAkBE,OAC/B6B,GAAG,UACHK,MAAOlB,aAAAA,EAAAA,EAAOhB,OACdmC,aAAcnB,EAAMG,YACpBiB,YAAa,sBACbC,SArEcC,IACtBrB,EAAS,OACJD,GAAAA,CACHhB,OAAQsC,EAAMC,OAAOL,MAAMM,S,EAmErBC,QA7EY,IACpBxB,EAAS,OACJD,GAAAA,CACHhB,OAAQ,GACRmB,aAAa,QA8EX,kBAACW,EAAAA,MAAKA,CAACN,MAAM,UAAUO,YAAY,GAAGN,UAAWf,EAAExB,WACjD,kBAACwD,EAAAA,MAAKA,CACJT,MAAO,GACPJ,GAAG,UACHP,cAAaxB,EAAkBG,OAC/BuB,MAAO,UACPU,MAAOlB,aAAAA,EAAAA,EAAOf,OACdmC,YAAa,oCACbC,SA5EcC,IACtBrB,EAAS,OACJD,GAAAA,CACHf,OAAQqC,EAAMC,OAAOL,MAAMM,S,KA6EzB,kBAACnB,MAAAA,CAAII,UAAWf,EAAExB,WAChB,kBAACwC,EAAAA,OAAMA,CACLiB,KAAK,SACLrB,cAAaxB,EAAkBI,OAC/B0B,QAAS,IACPvC,EAAsBoB,EAAOM,KAAKc,GAAI,CACpCjB,UACAC,SACAC,SAAU,CACRb,OAAQe,EAAMf,OACdkB,aAAa,GAIfyB,eAAgB5B,EAAMG,iBAClB0B,EACA,CACE7C,OAAQgB,EAAMhB,UAIxB8C,SAAU1B,SAASJ,EAAMf,SAAYe,EAAMG,cAAgBH,EAAMhB,SAClE,wB", "sources": ["webpack://grafana-exploretraces-app/./components/AppConfig/AppConfig.tsx"], "sourcesContent": ["import React, { useState, ChangeEvent } from 'react';\nimport { Button, Field, Input, useStyles2, FieldSet, SecretInput } from '@grafana/ui';\nimport { PluginConfigPageProps, AppPluginMeta, PluginMeta, GrafanaTheme2 } from '@grafana/data';\nimport { FetchResponse, getBackendSrv, locationService } from '@grafana/runtime';\nimport { css } from '@emotion/css';\nimport { lastValueFrom, Observable } from 'rxjs';\n\nexport type JsonData = {\n  apiUrl?: string;\n  isApiKeySet?: boolean;\n};\n\ntype State = {\n  // The URL to reach our custom API.\n  apiUrl: string;\n  // Tells us if the API key secret is set.\n  // Set to `true` ONLY if it has already been set and haven't been changed.\n  // (We unfortunately need an auxiliary variable for this, as `secureJsonData` is never exposed to the browser after it is set)\n  isApiKeySet: boolean;\n  // An secret key for our custom API.\n  apiKey: string;\n};\n\ninterface Props extends PluginConfigPageProps<AppPluginMeta<JsonData>> {}\n\nconst AppConfig = ({ plugin }: Props) => {\n  const s = useStyles2(getStyles);\n  const { enabled, pinned, jsonData } = plugin.meta;\n  const [state, setState] = useState<State>({\n    apiUrl: jsonData?.apiUrl || '',\n    apiKey: '',\n    isApiKeySet: Boolean(jsonData?.isApiKeySet),\n  });\n\n  const onResetApiKey = () =>\n    setState({\n      ...state,\n      apiKey: '',\n      isApiKeySet: false,\n    });\n\n  const onChangeApiKey = (event: ChangeEvent<HTMLInputElement>) => {\n    setState({\n      ...state,\n      apiKey: event.target.value.trim(),\n    });\n  };\n\n  const onChangeApiUrl = (event: ChangeEvent<HTMLInputElement>) => {\n    setState({\n      ...state,\n      apiUrl: event.target.value.trim(),\n    });\n  };\n\n  return (\n    <div data-testid={testIds.appConfig.container}>\n      {/* ENABLE / DISABLE PLUGIN */}\n      <FieldSet label=\"Enable / Disable\">\n        {!enabled && (\n          <>\n            <div className={s.colorWeak}>The plugin is currently not enabled.</div>\n            <Button\n              className={s.marginTop}\n              variant=\"primary\"\n              onClick={() =>\n                updatePluginAndReload(plugin.meta.id, {\n                  enabled: true,\n                  pinned: true,\n                  jsonData,\n                })\n              }\n            >\n              Enable plugin\n            </Button>\n          </>\n        )}\n\n        {/* Disable the plugin */}\n        {enabled && (\n          <>\n            <div className={s.colorWeak}>The plugin is currently enabled.</div>\n            <Button\n              className={s.marginTop}\n              variant=\"destructive\"\n              onClick={() =>\n                updatePluginAndReload(plugin.meta.id, {\n                  enabled: false,\n                  pinned: false,\n                  jsonData,\n                })\n              }\n            >\n              Disable plugin\n            </Button>\n          </>\n        )}\n      </FieldSet>\n\n      {/* CUSTOM SETTINGS */}\n      <FieldSet label=\"API Settings\" className={s.marginTopXl}>\n        {/* API Key */}\n        <Field label=\"API Key\" description=\"A secret key for authenticating to our custom API\">\n          <SecretInput\n            width={60}\n            data-testid={testIds.appConfig.apiKey}\n            id=\"api-key\"\n            value={state?.apiKey}\n            isConfigured={state.isApiKeySet}\n            placeholder={'Your secret API key'}\n            onChange={onChangeApiKey}\n            onReset={onResetApiKey}\n          />\n        </Field>\n\n        {/* API Url */}\n        <Field label=\"API Url\" description=\"\" className={s.marginTop}>\n          <Input\n            width={60}\n            id=\"api-url\"\n            data-testid={testIds.appConfig.apiUrl}\n            label={`API Url`}\n            value={state?.apiUrl}\n            placeholder={`E.g.: http://mywebsite.com/api/v1`}\n            onChange={onChangeApiUrl}\n          />\n        </Field>\n\n        <div className={s.marginTop}>\n          <Button\n            type=\"submit\"\n            data-testid={testIds.appConfig.submit}\n            onClick={() =>\n              updatePluginAndReload(plugin.meta.id, {\n                enabled,\n                pinned,\n                jsonData: {\n                  apiUrl: state.apiUrl,\n                  isApiKeySet: true,\n                },\n                // This cannot be queried later by the frontend.\n                // We don't want to override it in case it was set previously and left untouched now.\n                secureJsonData: state.isApiKeySet\n                  ? undefined\n                  : {\n                      apiKey: state.apiKey,\n                    },\n              })\n            }\n            disabled={Boolean(!state.apiUrl || (!state.isApiKeySet && !state.apiKey))}\n          >\n            Save API settings\n          </Button>\n        </div>\n      </FieldSet>\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  colorWeak: css`\n    color: ${theme.colors.text.secondary};\n  `,\n  marginTop: css`\n    margin-top: ${theme.spacing(3)};\n  `,\n  marginTopXl: css`\n    margin-top: ${theme.spacing(6)};\n  `,\n});\n\nconst updatePluginAndReload = async (pluginId: string, data: Partial<PluginMeta<JsonData>>) => {\n  try {\n    await updatePlugin(pluginId, data);\n\n    // Reloading the page as the changes made here wouldn't be propagated to the actual plugin otherwise.\n    // This is not ideal, however unfortunately currently there is no supported way for updating the plugin state.\n    locationService.reload();\n  } catch (e) {\n    console.error('Error while updating the plugin', e);\n  }\n};\n\nconst testIds = {\n  appConfig: {\n    container: 'data-testid ac-container',\n    apiKey: 'data-testid ac-api-key',\n    apiUrl: 'data-testid ac-api-url',\n    submit: 'data-testid ac-submit-form',\n  },\n};\n\nexport const updatePlugin = async (pluginId: string, data: Partial<PluginMeta>) => {\n  const response = getBackendSrv().fetch({\n    url: `/api/plugins/${pluginId}/settings`,\n    method: 'POST',\n    data,\n  }) as unknown as Observable<FetchResponse>;\n\n  const dataResponse = await lastValueFrom(response);\n\n  return dataResponse.data;\n};\n\nexport default AppConfig;\n"], "names": ["getStyles", "theme", "colorWeak", "css", "colors", "text", "secondary", "marginTop", "spacing", "marginTopXl", "updatePluginAndReload", "pluginId", "data", "updatePlugin", "locationService", "reload", "e", "console", "error", "testIds", "container", "<PERSON><PERSON><PERSON><PERSON>", "apiUrl", "submit", "response", "getBackendSrv", "fetch", "url", "method", "lastValueFrom", "plugin", "s", "useStyles2", "enabled", "pinned", "jsonData", "meta", "state", "setState", "useState", "isApiKeySet", "Boolean", "div", "data-testid", "FieldSet", "label", "className", "<PERSON><PERSON>", "variant", "onClick", "id", "Field", "description", "SecretInput", "width", "value", "isConfigured", "placeholder", "onChange", "event", "target", "trim", "onReset", "Input", "type", "secureJsonData", "undefined", "disabled"], "sourceRoot": ""}