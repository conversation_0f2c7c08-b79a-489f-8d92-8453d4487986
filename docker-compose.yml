services:
  ctfd:
    build: .
    user: root
    restart: always
    ports:
      - "8000:8000"
    environment:
      - UPLOAD_FOLDER=/var/uploads
      - DATABASE_URL=mysql+pymysql://ctfd:ctfd@db/ctfd
      - REDIS_URL=redis://cache:6379
      - WORKERS=1
      - LOG_FOLDER=/var/log/CTFd
      - ACCESS_LOG=-
      - ERROR_LOG=-
      - REVERSE_PROXY=true
      - PLUGIN_WHITELIST=web_desktop,challenges,dynamic_challenges,flags,ctfd-whale,security_monitor
    volumes:
      - ./.data/CTFd/logs:/var/log/CTFd
      - ./.data/CTFd/uploads:/var/uploads
      - .:/opt/CTFd:ro
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - db
    networks:
        default:
        internal:
        frp:
            ipv4_address: *********
    mem_limit: 450M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 450M

  nginx:
    image: nginx:1.17
    restart: always
    volumes:
      - ./conf/nginx/security.conf:/etc/nginx/conf.d/default.conf:ro
      - ./.data/nginx/logs:/var/log/nginx
    ports:
      - 82:80
    depends_on:
      - ctfd
    networks:
        default:
        internal:
    mem_limit: 450M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  db:
    image: mariadb:10.4.12
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=ctfd
      - MYSQL_USER=ctfd
      - MYSQL_PASSWORD=ctfd
      - MYSQL_DATABASE=ctfd
    volumes:
      - ./.data/mysql:/var/lib/mysql
    networks:
        internal:
    command: [mysqld, --character-set-server=utf8mb4, --collation-server=utf8mb4_unicode_ci, --wait_timeout=28800, --log-warnings=0]
    mem_limit: 450M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  cache:
    image: redis:4
    restart: always
    volumes:
    - ./.data/redis:/data
    networks:
        internal:
    mem_limit: 450M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
  
  frps:
    image: glzjin/frp:latest
    restart: always
    volumes:
      - ./frps:/conf/
    entrypoint:
        - /usr/local/bin/frps
        - -c
        - /conf/frps.ini
    ports:
      - "10000-10100:10000-10100"
      - "6490:6490"
    networks:
        frp:
          ipv4_address: *********
        default:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 250M

  frpc:    
    image: glzjin/frp:latest
    restart: always
    volumes:
      - ./frpc:/conf/
    entrypoint:
        - /usr/local/bin/frpc
        - -c
        - /conf/frpc.ini
    networks:
        frp:
            ipv4_address: *********
        frp-containers:
    mem_limit: 250M
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 250M

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    restart: always
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./.data/prometheus:/prometheus
    ports:
      - "9090:9090"
    networks:
      - internal
      - default
    mem_limit: 256M
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    restart: always
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - ./.data/grafana:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - internal
      - default
    mem_limit: 256M

  # Log aggregation
  loki:
    image: grafana/loki:2.9.0
    restart: always
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yaml:/etc/loki/local-config.yaml:ro
      - ./.data/loki:/loki
    user: "0"
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - internal
      - default
    mem_limit: 256M

  promtail:
    image: grafana/promtail:2.9.0
    restart: always
    volumes:
      - ./monitoring/promtail-config.yaml:/etc/promtail/config.yml:ro
      - ./.data/CTFd/logs:/var/log/CTFd:ro
      - ./.data/nginx/logs:/var/log/nginx:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - internal
      - default
    mem_limit: 128M

networks:
    default:
    internal:
        internal: true
    frp:
        attachable: true
        driver: bridge
        ipam:
            config:
                - subnet: *********/16
    frp-containers:
        driver: overlay
        internal: false
        attachable: true
        ipam:
            config:
                - subnet: *********/16
