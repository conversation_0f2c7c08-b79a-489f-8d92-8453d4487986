#!/bin/bash
# Comprehensive Security Monitor diagnostic

echo "=== Security Monitor Real-Time Data Check ==="
echo

# 1. Check what metrics are actually exposed
echo "📊 Current Prometheus Metrics:"
curl -s http://localhost:8000/admin/plugins/security_monitor/api/prometheus 2>/dev/null | grep -E "^ctfd_|^# HELP" || echo "No metrics found"

echo
echo "🗄️ Database Event Counts:"
docker compose exec db mysql -uctfd -pctfd ctfd -e "
SELECT event_type, COUNT(*) as count 
FROM security_events 
GROUP BY event_type;

SELECT 'Total Events:', COUNT(*) FROM security_events;
SELECT 'Events in last hour:', COUNT(*) FROM security_events WHERE timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR);
" 2>/dev/null || echo "Could not query database"

echo
echo "🔄 Testing Real-Time Event Collection:"
echo "  Generating test events..."

# Create a unique identifier for our test
TEST_ID=$(date +%s)

# Make some requests that should trigger events
echo "  - Testing login endpoint..."
curl -s -X POST http://localhost:8000/login \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "name=test_${TEST_ID}&password=wrong" > /dev/null 2>&1

echo "  - Testing rapid requests..."
for i in {1..10}; do
    curl -s http://localhost:8000/ > /dev/null 2>&1 &
done
wait

# Wait for events to be processed
sleep 3

echo
echo "📈 Checking if new events were recorded:"
docker compose exec db mysql -uctfd -pctfd ctfd -e "
SELECT * FROM security_events 
ORDER BY id DESC 
LIMIT 5;
" 2>/dev/null || echo "Could not check recent events"

echo
echo "📋 Grafana Dashboards:"
curl -s http://localhost:3000/api/search -u admin:admin 2>/dev/null | jq -r '.[] | select(.type=="dash-db") | "\(.title) (uid: \(.uid))"' || echo "Could not list dashboards"

echo
echo "🔍 Checking Prometheus scrape status:"
curl -s http://localhost:9090/api/v1/targets | jq '.data.activeTargets[] | select(.labels.job=="ctfd") | {job: .labels.job, health: .health, lastScrape: .lastScrape, lastError: .lastError}' 2>/dev/null || echo "Could not check targets"
