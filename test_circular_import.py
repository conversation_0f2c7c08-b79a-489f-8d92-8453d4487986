#!/usr/bin/env python3
"""
Test script to check if the circular import issue is fixed
by examining the import structure without actually importing
"""

import ast
import os

def check_imports_in_file(filepath):
    """Check what modules a file imports"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        
        return imports
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return []

def main():
    print("🔍 Checking for circular import issues in security_monitor plugin...")
    
    # Check what routes.py imports
    routes_file = "CTFd/plugins/security_monitor/routes.py"
    init_file = "CTFd/plugins/security_monitor/__init__.py"
    
    if not os.path.exists(routes_file):
        print(f"❌ {routes_file} not found")
        return
    
    if not os.path.exists(init_file):
        print(f"❌ {init_file} not found")
        return
    
    print(f"\n📁 Checking imports in {routes_file}...")
    routes_imports = check_imports_in_file(routes_file)
    
    # Check if routes.py imports platform_metrics from __init__.py
    problematic_import = "CTFd.plugins.security_monitor"
    has_circular_import = any(imp == problematic_import for imp in routes_imports)
    
    print(f"Imports in routes.py: {routes_imports}")
    
    if has_circular_import:
        print("❌ CIRCULAR IMPORT DETECTED!")
        print(f"   routes.py imports from {problematic_import}")
        print(f"   but {problematic_import}/__init__.py imports routes.py")
    else:
        print("✅ NO CIRCULAR IMPORT DETECTED!")
        print("   routes.py does not import from CTFd.plugins.security_monitor")
    
    print(f"\n📁 Checking imports in {init_file}...")
    init_imports = check_imports_in_file(init_file)
    print(f"Imports in __init__.py: {init_imports}")
    
    # Check if __init__.py imports routes
    imports_routes = any("routes" in imp for imp in init_imports)
    if imports_routes:
        print("✅ __init__.py imports routes (this is expected)")
    else:
        print("❌ __init__.py does not import routes (this might be a problem)")
    
    print("\n🎯 SUMMARY:")
    if not has_circular_import:
        print("✅ The circular import issue appears to be FIXED!")
        print("   routes.py no longer imports platform_metrics from __init__.py")
    else:
        print("❌ The circular import issue still EXISTS!")
        print("   routes.py still imports from __init__.py")

if __name__ == "__main__":
    main()
