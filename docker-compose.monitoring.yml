version: '3'

services:
  # Loki for log aggregation
  loki:
    image: grafana/loki:2.9.0
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yaml:/etc/loki/local-config.yaml
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring
    mem_limit: 256m
    
  # Promtail to ship logs
  promtail:
    image: grafana/promtail:2.9.0
    volumes:
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - ./monitoring/promtail-config.yaml:/etc/promtail/config.yml
      - .data/CTFd/logs:/var/log/CTFd:ro
      - .data/nginx/logs:/var/log/nginx:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring
    mem_limit: 128m

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - monitoring
    mem_limit: 256m

volumes:
  loki-data:
  grafana-data:

networks:
  monitoring:
    driver: bridge
