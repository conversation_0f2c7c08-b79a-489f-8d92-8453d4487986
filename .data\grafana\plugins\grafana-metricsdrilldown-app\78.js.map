{"version": 3, "file": "78.js?_cache=aef19aea7ae38d61f33b", "mappings": "mNAOe,SAASA,GAAM,MAAEC,IAC9B,MAAOC,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,GAWnD,OATAC,EAAAA,EAAAA,YAAU,KACHH,IACCD,EAAMK,MAAMC,SACdC,EAAAA,EAAAA,MAAgBC,eAAeR,GAEjCE,GAAiB,GACnB,GACC,CAACF,EAAOC,IAENA,EAKH,kBAACQ,EAAAA,GAAsBA,CAACC,MAAOV,EAAOW,2BAA2B,EAAMC,iBAAiB,GACtF,kBAACZ,EAAMa,UAAS,CAACC,MAAOd,KALnB,IAQX,C", "sources": ["webpack://grafana-metricsdrilldown-app/./pages/TrailWingman.tsx"], "sourcesContent": ["import { UrlSyncContextProvider } from '@grafana/scenes';\nimport React, { useEffect, useState } from 'react';\n\nimport { getTrailStore } from 'TrailStore/TrailStore';\n\nimport type { DataTrail } from 'DataTrail';\n\nexport default function Trail({ trail }: { trail: DataTrail }) {\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      if (trail.state.metric) {\n        getTrailStore().setRecentTrail(trail);\n      }\n      setIsInitialized(true);\n    }\n  }, [trail, isInitialized]);\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return (\n    <UrlSyncContextProvider scene={trail} createBrowserHistorySteps={true} updateUrlOnInit={true}>\n      <trail.Component model={trail} />\n    </UrlSyncContextProvider>\n  );\n}\n"], "names": ["Trail", "trail", "isInitialized", "setIsInitialized", "useState", "useEffect", "state", "metric", "getTrailStore", "setRecentTrail", "UrlSyncContextProvider", "scene", "createBrowserHistorySteps", "updateUrlOnInit", "Component", "model"], "sourceRoot": ""}