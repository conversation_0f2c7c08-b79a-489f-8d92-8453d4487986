{"version": 3, "file": "389.js", "mappings": "wHAAA,IAAIA,EAAM,CACT,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,IACX,aAAc,IACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,OAAQ,GACR,UAAW,GACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,EACX,aAAc,EACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,WAAY,KACZ,cAAe,KACf,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,KACX,aAAc,KACd,UAAW,IACX,OAAQ,KACR,UAAW,KACX,WAAY,KACZ,cAAe,KACf,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,IACb,eAAgB,IAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,QAAS,GACT,WAAY,GACZ,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,QAAS,KACT,aAAc,KACd,gBAAiB,KACjB,WAAY,KACZ,UAAW,IACX,aAAc,IACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,IACb,eAAgB,IAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,GACX,aAAc,GACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,MAIf,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOG,EAAoBF,EAC5B,CACA,SAASC,EAAsBF,GAC9B,IAAIG,EAAoBC,EAAEN,EAAKE,GAAM,CACpC,IAAIK,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAEE,KAAO,mBACHF,CACP,CACA,OAAOP,EAAIE,EACZ,CACAD,EAAeS,KAAO,WACrB,OAAOC,OAAOD,KAAKV,EACpB,EACAC,EAAeW,QAAUR,EACzBS,EAAOC,QAAUb,EACjBA,EAAeE,GAAK,I,0MC9RpB,MCcaY,EAAe,EAAGC,QAAQ,OAAQC,SAAQC,WAAU,MAC/D,MAAMC,GAAQC,EAAAA,EAAAA,cACR,EAAEC,EAAC,EAAEC,GDdY,EAACC,EAFW,MAGnC,MAAOC,EAAeC,IAAoBC,EAAAA,EAAAA,UAAwB,CAAEL,EAAG,KAAMC,EAAG,OAahF,OAXAK,EAAAA,EAAAA,YAAU,KACR,MAAMC,GAAsBC,EAAAA,EAAAA,WAAUC,IACpCL,EAAiB,CAAEJ,EAAGS,EAAMC,QAAST,EAAGQ,EAAME,SAAU,GACvDT,GAGH,OAFAU,OAAOC,iBAAiB,YAAaN,GAE9B,KACLK,OAAOE,oBAAoB,YAAaP,EAAoB,CAC7D,GACA,CAACL,IAEGC,CAAa,ECAHY,GACXC,GAASC,EAAAA,EAAAA,YAAWC,EAAWlB,EAAGC,EAAGJ,GAC3C,OAAO,kBAACsB,EAAAA,EAAGA,CAACC,IAAKtB,EAAMuB,O,gJAA6BC,UAAWN,EAAOO,IAAK3B,OAAQA,EAAQD,MAAOA,G,EAGpGD,EAAa8B,YAAc,eAE3B,MAAMN,EAAY,CAACpB,EAAsB2B,EAAqBC,EAAqB7B,KACjF,MAAM,WAAE8B,EAAU,YAAEC,GAAgBhB,OAC9BiB,EAAcH,GAAQA,EAAOE,EAC7BE,EAAaL,GAAQA,EAAOE,EAC5BI,EAA2B,OAAhBF,EAAuBG,EAAqBH,GAxBtC,GACA,GAuByF,EAC1GI,EACW,OAAfH,EAAsBE,EAAqBF,GAxBnB,EACA,GAuB2E,EAErG,MAAO,CACLP,KAAKW,EAAAA,EAAAA,KAAI,CACP,qCAAsC,CACpCC,UAAW,UAAUJ,oBAA2BE,MAChDG,gBAAiB,SACjBC,WAAY,yBAEd,iBAAkB,CAChBC,QAASzC,EAAU,QAAU,UAGlC,EAOGmC,EAAuB,CAACO,EAAeC,EAAeC,IAC5CF,GAASE,EAAMD,GAASA,EC3C3BE,EAAa,EAAGC,UAASC,gBAAeC,WAAUC,cAC7D,MAAM9B,GAASC,EAAAA,EAAAA,YAAWC,EAAW4B,GAErC,OACE,kBAACC,MAAAA,CAAIzB,UAAWN,EAAOgC,UAAWC,cCnBxB,2BDoBR,kBAACC,EAAAA,MAAKA,CAACC,UAAU,SAASC,WAAW,SAASC,IAAK,GACjD,kBAAC3D,EAAYA,CAACC,MAAOkD,QAAAA,EAAY,MACb,iBAAZF,GAAyB,kBAACW,EAAAA,KAAIA,CAACC,cAAe,SAAUC,QAAQ,MAAMb,GAC1D,iBAAZA,GAAyBA,EAEhCC,GACC,kBAACG,MAAAA,CAAIzB,UAAWN,EAAOyC,QACrB,kBAACP,EAAAA,MAAKA,CAACG,IAAK,GAAKD,WAAY,UAC3B,kBAACM,EAAAA,KAAIA,CAACC,KAAK,gBACX,kBAACL,EAAAA,KAAIA,CAACC,cAAe,SAAUC,QAAQ,QACpCZ,MAAAA,EAYjB,SAAS1B,EAAUpB,EAAsBgD,GACvC,MAAO,CACLE,WAAWd,EAAAA,EAAAA,KAAI,CACbvC,MAAO,OACP2C,QAAS,OACTsB,eAAgB,eAChBC,cAAe,SACff,QAASA,GAAoB,IAE/BW,QAAQvB,EAAAA,EAAAA,KAAI,CACV4B,aAAchE,EAAMiE,QAAQ,KAGlC,CAfArB,EAAWlB,YAAc,aE9BlB,MAAMwC,UAAwBC,EAAAA,I,YACT,EAAGC,YAC3B,MAAM,QAAEvB,EAAO,cAAEC,EAAa,SAAEC,EAAQ,QAAEC,GAAYoB,EAAM7D,WAC5D,OAAO,kBAACqC,EAAUA,CAACC,QAASA,EAASC,cAAeA,EAAeC,SAAUA,EAAUC,QAASA,G,KAFpFqB,e,EADHH,G,oGCAN,MAAMI,UAA0BH,EAAAA,K,6GACrC,CADWG,EACGD,aAAY,EAAGD,YAC3B,MAAMpE,GAAQC,EAAAA,EAAAA,aACRiB,GAASC,EAAAA,EAAAA,YAAWC,IACpB,UAAEmD,GAAcH,EAAM7D,WAE5B,OACE,kBAAC0C,MAAAA,CAAIzB,UAAWN,EAAOgC,UAAWC,cFfxB,6BEgBR,kBAACqB,EAAAA,EAAaA,CACZC,UAAWzE,EAAM0E,OAAOC,UAAU3E,EAAM0E,OAAOE,WAAWC,WAC1DC,eAAgB9E,EAAM0E,OAAOC,UAAU3E,EAAM0E,OAAOE,WAAWC,UAAW,IAC1EE,aAAc/E,EAAMgF,MAAMC,OAAOC,SAEhCX,KAAAA,IAOX,MAAMY,GAASC,EAAAA,EAAAA,WAAU,CACvB,KAAM,CACJC,QAAS,GAEX,OAAQ,CACNA,QAAS,KAIb,SAASjE,IACP,MAAO,CACL8B,WAAWd,EAAAA,EAAAA,KAAI,CACbkD,MAAO,sBAEPC,cAAeJ,EACfK,eAAgB,QAChBC,wBAAyB,UACzBC,kBAAmB,QACnBC,kBAAmB,cAGzB,CC3CO,MAAMC,UAAwBzB,EAAAA,K,6GACnC,CADWyB,EACGvB,aAAY,EAAGD,YAC3B,MAAM,QAAEvB,GAAYuB,EAAM7D,WAC1B,OACE,kBAACsF,EAAAA,MAAKA,CAACC,MAAO,cAAeC,SAAU,QAAS5C,cHXxC,2BGYLN,EAAAA,ICJF,MAAMmD,EAAUC,IACrB,MAAM/E,GAASC,EAAAA,EAAAA,YAAWC,IACpB,YAAE8E,EAAW,oBAAEC,GAAwBF,EAE7C,OACE,kBAACG,EAAAA,MAAKA,CAAC5E,UAAWN,EAAOmF,aACvB,kBAACC,EAAAA,MAAKA,CACJC,YAAY,SACZC,OAAQ,kBAAC5C,EAAAA,KAAIA,CAACC,KAAM,WACpB4C,MAAOP,EACPQ,SAAUP,EACVnH,GAAG,qB,EAMX,SAASoC,EAAUpB,GACjB,MAAO,CACLqG,aAAajE,EAAAA,EAAAA,KAAI,CACf4B,aAAchE,EAAMiE,QAAQ,KAGlC,C,8lDCKO,MAAM0C,UAAwBxC,EAAAA,GAmF3ByC,kBAAAA,CAAmBC,GACrBA,EAASC,QAAUD,EAASC,OAAOC,OAAS,EAC9CC,KAAKC,cAAcJ,GAEnBG,KAAKE,MAAMC,KAAKC,SAAS,CACvBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAM,IAAIjD,EAAgB,CACxBrB,QAAS,0BACTG,QAAS,aAMrB,CAEQuE,aAAAA,CAAcC,EAAiBC,GACrC,MAAMC,EAAcF,EAAKV,OAAOa,QAC9B,CAACC,EAAKd,K,IACQA,EAAAA,EAAZ,MAAMe,EAA2D,QAArDf,EAAAA,EAAOgB,OAAOC,MAAMC,GAAMA,EAAEC,OAASC,EAAAA,UAAUC,gBAA/CrB,IAAAA,GAA8D,QAA9DA,EAAAA,EAAwDsB,cAAxDtB,IAAAA,OAAAA,EAAAA,EAAiEW,GAC7E,OAAKI,GAGAD,EAAIC,KACPD,EAAIC,GAAO,IAEbD,EAAIC,GAAKQ,KAAKvB,GACPc,GANEA,CAMC,GAEZ,CAAC,GAGGU,EAAY,GAClB,IAAK,MAAMT,KAAOH,EAAa,CAC7B,MAAMa,EAASb,EAAYG,GAAKW,MAAK,CAACC,EAAGC,K,IAAMD,E,OAAM,QAANA,EAAAA,EAAE5E,YAAF4E,IAAAA,OAAAA,EAAAA,EAAQE,cAAcD,EAAE7E,QAAU,CAAC,IAC5E+E,EC1JH,OADsBC,ED2JQN,EAAO,ICzJvCM,CACHf,OAAQe,EAAMf,OAAOjJ,KAAKiK,GAAkB,OACvCA,GAAAA,CACHC,OAAQD,EAAMC,aDuJdR,EAAOS,MAAM,EAAGT,EAAOxB,QAAQkC,SAASJ,GAAUD,EAAUd,OAAOO,KAAKQ,EAAMf,OAAO,MACrFQ,EAAUD,MAAKa,EAAAA,EAAAA,eAAcN,EAAW,GAC1C,CC9JG,IAAwBC,ED+J3B,OAAOP,CACT,CAEQrB,aAAAA,CAAcO,GACpB,MAAM2B,EAA+B,GACrC,IAAIZ,EAASf,EAAKV,OAEdE,KAAKE,MAAMO,UACbc,EAASvB,KAAKO,cAAcC,EAAM4B,GAAmBpC,MAAMqC,iBAG7D,IAAK,IAAIC,EAAa,EAAGA,EAAaf,EAAOxB,OAAQuC,IAAc,CAMjE,GAAY,IALSf,EAAOe,GAEHxB,OACtByB,QAAQvB,GAAMA,EAAEC,OAASC,EAAAA,UAAUC,SACnCR,QAAO,CAAC6B,EAAKxB,IAAMwB,EAAMxB,EAAEe,OAAOpB,QAAO,CAAC8B,EAAMC,IAAMD,GAAQC,GAAK,IAAI,IAAM,GAAG,GAEjF,SAGF,MAAMC,EAAc3C,KAAKE,MAAM0C,eAAepC,EAAMe,EAAOe,GAAaA,GACxEH,EAAYd,KAAKsB,EACnB,CAEA3C,KAAKE,MAAMC,KAAKC,SAAS,CAAEC,SAAU8B,GACvC,CApJA,YAAmBjC,GACjB2C,MAAM3C,GAoER,OAAQf,uBAAuB2D,IAC7B9C,KAAKI,SAAS,CAAElB,YAAa4D,EAAIC,cAActD,OAAQ,IAGzD,OAAQuD,gCAA+BC,EAAAA,EAAAA,WAAU/D,I,IAIrCsB,EAHV,MAAMA,EAAO0C,EAAAA,GAAWC,QAAQnD,MAC1BH,EAAW,OACZW,EAAKN,MAAMM,MAAI,CAClBV,OAAuB,QAAfU,EAAAA,EAAKN,MAAMM,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBV,OAAOyC,OAAOa,EAA8BlE,MAEvEc,KAAKJ,mBAAmBC,EAAAA,GACvB,MA7EDG,KAAKqD,sBAAqB,KACxB,MAAM7C,EAAO0C,EAAAA,GAAWC,QAAQnD,MAEhCA,KAAKsD,MAAMC,IACT/C,EAAKgD,kBAAkBhD,I,IACjBA,EAA0CA,EAqBnCA,EApB4BA,EAY1BA,EAbb,IAAa,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,OAAiB,QAATlD,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaE,WAC9E,GAAgC,IAA5BnD,EAAKA,KAAKV,OAAOC,SAAyB,QAATS,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaE,UACrE3D,KAAKE,MAAMC,KAAKC,SAAS,CACvBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAM,IAAIjD,EAAgB,CACxBrB,QAAS+H,EAAAA,GACT9H,cAAe+H,EAAAA,GACf7H,QAAS,mBAKZ,IAAa,QAATwE,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,KAAM,C,IAGvClD,EAFV,MAAMX,EAAW,OACZW,EAAKA,MAAI,CACZV,OAAiB,QAATU,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWV,OAAOyC,OAAOa,EAA8BpD,KAAKE,MAAMhB,gBAE5Ec,KAAKJ,mBAAmBC,GACxBG,KAAK8D,aAAa,IAAIC,EAAAA,GAA4B,CAAEjE,OAAQU,EAAKA,KAAKV,UAAW,EACnF,OACK,IAAa,QAATU,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAapL,MAAO,C,IAM/BmI,EAAAA,EAAAA,EALnBR,KAAKE,MAAMC,KAAKC,SAAS,CACvBC,SAAU,CACR,IAAI2D,EAAAA,GAAmB,CACrB3D,SAAU,CACR,IAAIzB,EAAgB,CAClB/C,QAAuC,QAA9B2E,EAAgB,QAAhBA,EAAAA,EAAKA,KAAKyD,cAAVzD,IAAAA,GAAqB,QAArBA,EAAAA,EAAmB,UAAnBA,IAAAA,OAAAA,EAAAA,EAAuB3E,eAAvB2E,IAAAA,EAAAA,EAAkC,wCAMvD,MACER,KAAKE,MAAMC,KAAKC,SAAS,CACvBC,SAAU,CACR,IAAI2D,EAAAA,GAAmB,CACrB3D,SAAU,CACR,IAAI/C,EAAkB,CACpBC,UAAW,IAAM2G,EAAkB,UAM/C,KAIJlE,KAAKwD,kBAAiB,CAACW,EAAUC,K,IAEKD,EADhCA,EAASjF,cAAgBkF,EAAUlF,aACrCc,KAAKgD,6BAAiD,QAApBmB,EAAAA,EAASjF,mBAATiF,IAAAA,EAAAA,EAAwB,GAC5D,IAGE3D,EAAKN,MAAMM,MACbR,KAAKC,cAAcO,EAAKN,MAAMM,KAChC,GAEJ,EAgGF,SAASpG,IACP,MAAO,CACL8B,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTuB,cAAe,SACfsH,SAAU,IAGhB,CArBE,EAvJW1E,EAuJGtC,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,EAAI,YAAEjB,GAAgB9B,EAAM7D,WAC9BW,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAAC8C,EAAMA,CAACE,YAAaA,QAAAA,EAAe,GAAIC,oBAAqB/B,EAAM+B,sBACnE,kBAACgB,EAAK9C,UAAS,CAACD,MAAO+C,I,IAgBxB,MAAM+D,EAAqBI,IAChC,MAAMpK,GAASC,EAAAA,EAAAA,YAAWoK,GAE1B,OACE,kBAACtI,MAAAA,CAAIzB,UAAWN,EAAOgC,WACpB,IAAIsI,MAAMF,IAASzM,KAAI,CAAC4M,EAAGC,IAC1B,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAOyK,cAAe9D,IAAK6D,GACzC,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAO0K,QACrB,kBAAC3I,MAAAA,CAAIzB,UAAWN,EAAO4E,OACrB,kBAAC+F,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAO6K,QACrB,kBAACF,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAO8K,OACpB,IAAIR,MAAM,IAAI3M,KAAI,CAAC4M,EAAGC,IACrB,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAO+K,UAAWpE,IAAK6D,GACrC,kBAACG,EAAAA,EAAQA,CAACC,MAAO,QAIvB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOgL,OACpB,IAAIV,MAAM,IAAI3M,KAAI,CAAC4M,EAAGC,IACrB,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAOiL,UAAWtE,IAAK6D,GACrC,kBAACG,EAAAA,EAAQA,CAACC,MAAO,W,EAUjC,SAASP,EAAkBvL,GACzB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACT4J,oBAAqBC,EAAAA,GACrBC,aAAc,QACdC,OAAQvM,EAAMiE,QAAQ,GACtBuI,UAAWxM,EAAMiE,QAAQ,KAE3B0H,eAAevJ,EAAAA,EAAAA,KAAI,CACjBqK,gBAAiBzM,EAAM0E,OAAOE,WAAW8H,QACzCC,OAAQ,aAAa3M,EAAM0E,OAAOE,WAAWC,YAC7C7B,QAAS,QAEX4I,QAAQxJ,EAAAA,EAAAA,KAAI,CACVI,QAAS,OACTsB,eAAgB,kBAElBgC,OAAO1D,EAAAA,EAAAA,KAAI,CACTvC,MAAO,UAETkM,QAAQ3J,EAAAA,EAAAA,KAAI,CACVvC,MAAO,SAETmM,OAAO5J,EAAAA,EAAAA,KAAI,CACTI,QAAS,OACTuB,cAAe,SACfD,eAAgB,eAChB8I,UAAW,SAEbX,WAAW7J,EAAAA,EAAAA,KAAI,CACbvC,MAAO,OACPC,OAAQ,SAEVoM,OAAO9J,EAAAA,EAAAA,KAAI,CACTI,QAAS,OACTsB,eAAgB,iBAElBqI,WAAW/J,EAAAA,EAAAA,KAAI,CACbvC,MAAO,SAGb,CAEO,MAAMuK,EAAiClE,GAA0B2G,IACtE,MAAMC,EAAU5G,aAAAA,EAAAA,EAAa6G,OAC7B,IAAKD,EACH,OAAO,EAGT,MAAME,EAAQ,IAAIC,OAAOH,EAAS,KAElC,OAAOD,EAAU/E,OAAOoF,MAAMlF,KAAQA,EAAEI,QAAiB5I,OAAOuJ,OAAOf,EAAEI,QAAQL,MAAMzC,GAAU0H,EAAMG,KAAK7H,MAAQ,EExSzG8H,EAAkB,IACtBC,EAAAA,GAAcC,aAClBC,UAAU,SAAU,CAAEC,YAAY,IAClCC,qBAAqB,YAAaC,EAAAA,UAAUC,MAC5CF,qBAAqB,WAAY,CAAEG,KAAMC,EAAAA,aAAaC,SACtDL,qBAAqB,cAAe,IACpCA,qBAAqB,YAAa,GAClCA,qBAAqB,YAAa,GAClCA,qBAAqB,YAAa,QAClCM,cAAcC,IACbA,EAAUC,2BAA2B,gCAAgCC,cAAc,CACjFN,KAAM,QACNO,WAAY,kBAEdH,EAAUC,2BAA2B,gCAAgCC,cAAc,CACjFN,KAAM,QACNO,WAAY,UAEdH,EAAUC,2BAA2B,0BAA0BC,cAAc,CAC3EN,KAAM,QACNO,WAAY,cACZ,IAEHZ,UAAU,UAAW,CAAEK,KAAMQ,EAAAA,mBAAmBC,QCjB9C,SAASC,GAAqB,OAAEC,EAAM,WAAEC,EAAU,aAAEC,EAAY,cAAEC,IAEvE,IAAIC,EAAU,GAAGC,EAAAA,KAEF,SAAXL,EACFI,GAAW,oBACS,WAAXJ,IACTI,GAAW,oBAGTF,IACFE,GAAW,OAAOF,KAGhBD,GAAcA,IAAeK,EAAAA,KAC/BF,GAAW,OAAOH,YAIpB,IAAIM,EAAW,SACf,OAAQP,GACN,IAAK,SACHO,EAAW,SACX,MACF,IAAK,WACHA,EAAW,oCAKf,IAAIC,EAAe,GAWnB,OAVIP,GAAcA,IAAeK,EAAAA,IAC/BE,EAAa1G,KAAKmG,GAGL,aAAXD,GAAyBG,GAC3BK,EAAa1G,KAAK,UAKb,IAAIsG,QAAcG,KAFTC,EAAahI,OAAS,MAAMgI,EAAaC,KAAK,SAAW,IAG3E,CAEO,SAASC,EAAmBV,EAAwBW,GACzD,MAAO,CACLC,MAAO,IACPC,MAAOd,EAAqB,CAAEC,SAAQC,WAAYU,EAAQR,eAAe,IACzEW,UAAW,UACXC,UAAW,QACXC,MAAO,IACPC,KAAM,GACNb,QAAS,GAEb,C,cC3DO,MAODc,GAF8BC,KAAKC,MALV,KAOuD,CACpF,CAAEC,KAAM,IAAKC,aAJQ,MAIeC,WAAY,IAChD,CAAEF,KAAM,IAAKC,aANS,KAMeC,WAAY,IACjD,CAAEF,KAAM,IAAKC,aARW,IAQeC,WAAY,IACnD,CAAEF,KAAM,IAAKC,aAVW,IAUeC,WAAY,KACnD,CAAEF,KAAM,KAAMC,aAZe,IAYgBC,WAAY,KACzD,CAAEF,KAAM,KAAMC,aAAc,EAAGC,WAAY,OAchCC,EAAkBC,IAE7B,MAAOC,EAAaC,IAAiBC,EAAAA,EAAAA,WACnCV,GACA,EAAGI,gBAAgBO,IAAUA,EAAQX,EAAW1I,OAAS,GAAK8I,EAAeG,IAG/E,GAA+B,MAA3BC,EAAYH,WAEd,MAAO,IAAGO,EAAAA,EAAAA,OAAOL,EAAWC,EAAYJ,aAAc,KAAKI,EAAYL,OAGzE,MACMU,EAAoB,GADLZ,KAAKa,MAAMP,EAAWC,EAAYJ,gBACXI,EAAYL,OAClDY,EAAiBd,KAAKe,MAAM,EAAYP,EAAcL,aAAgBI,EAAYH,YAClFY,EAAsB,GAAGF,IAAiBN,EAAcN,OAC9D,OAA0B,IAAnBY,EAAuBF,EAAoB,GAAGA,KAAqBI,GAAqB,EAGpFC,EAAsB,CAACC,EAAoBC,KACtD,MAAMC,EAAiB5G,EAAAA,GAAW6G,aAAaH,GACzCI,EAAOF,EAAe5J,MAAMT,MAAMuK,KAAKC,OACvCC,EAAKJ,EAAe5J,MAAMT,MAAMyK,GAAGD,OAEnCE,GAAMnB,EAAAA,EAAAA,UAASkB,EAAKF,EAAM,KAEhC,MAAO,GADUtB,KAAKa,MAAMY,EAAIC,aAAeP,QAAAA,EAAc,MAAQ,IAChD,E,+yBCtDhB,MAAMQ,UAAwBC,EAAAA,GAM3BC,eAAAA,GACN,MAAMC,EAAOb,EAAoB3J,KAAMA,KAAKE,MAAMuK,eAClDzK,KAAKI,SAAS,CACZsK,QAAS1K,KAAKE,MAAMwK,QAAQ7S,KAAKuQ,GACxB,OACFA,GAAAA,CACHoC,aAKiBtH,EAAAA,GAAW6G,aAAa/J,MAChCwD,kBAAiB,CAACW,EAAUC,KACzC,GAAID,EAAS1E,MAAMuK,OAAS5F,EAAU3E,MAAMuK,MAAQ7F,EAAS1E,MAAMyK,KAAO9F,EAAU3E,MAAMyK,GAAI,CAC5F,MAAMS,EAAUhB,EAAoB3J,KAAMA,KAAKE,MAAMuK,eACrDzK,KAAKI,SAAS,CACZsK,QAAS1K,KAAKE,MAAMwK,QAAQ7S,KAAKuQ,GACxB,OACFA,GAAAA,CACHoC,KAAMG,OAId,IAEJ,CA9BAC,WAAAA,CAAY1K,GACV2C,MAAM3C,GACNF,KAAKqD,qBAAqBrD,KAAKuK,gBAAgBM,KAAK7K,MACtD,ECGK,MAAM8K,EAAqB,EAChCC,cACAC,WAAW,OAEX,MAAM9Q,GAASC,EAAAA,EAAAA,YAAWC,EAAW4Q,GAErC,OAAKD,EAKH,kBAACE,EAAAA,QAAOA,CAACC,QAAS,aAChB,kBAACtO,EAAAA,KAAIA,CAACC,KAAM,cAAesO,KAAK,KAAK3Q,UAAWN,EAAOkR,sBALlD,I,EAULhR,EAAY,CAACpB,EAAsBgS,KAChC,CACLI,oBAAoBhQ,EAAAA,EAAAA,KAAI,CACtBvC,MAAO,GAAGmS,MACVlS,OAAQ,GAAGkS,MACXvF,gBAAiBzM,EAAM0E,OAAO2N,QAAQC,KACtCC,KAAMvS,EAAM0E,OAAO2N,QAAQC,KAC3BvN,aAAc,MACdvC,QAAS,mB,eC/Bf,MAKagQ,GAAuB,CAClCC,EACA1G,EACA2G,MAEAC,EAAAA,EAAAA,mBAV4B,EAACF,EAA0B1G,IAChD,GAAG6G,GAAAA,GAAcC,QAAQ,KAAM,QAAQJ,KAAQ1G,IASpC+G,CAAsBL,EAAM1G,GAAS2G,EAAW,EAGvDK,GAAoB,CAC/BC,eAAgB,iBAChBC,KAAM,OACNC,OAAQ,UASGC,GAAsB,CACjC,CAACJ,GAAkBC,gBAAiB,CAClCI,oBAAqB,sBACrBC,2BAA4B,6BAC5BC,iCAAkC,mCAClCC,kCAAmC,oCACnCC,uCAAwC,yCACxCC,oBAAqB,sBACrBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,WAAY,aACZC,wBAAyB,0BACzBC,6BAA8B,+BAC9BC,wCAAyC,0CACzCC,0BAA2B,4BAC3BC,wBAAyB,2BAE3B,CAAClB,GAAkBE,MAAO,CACxBiB,qBAAsB,uBACtBC,kBAAmB,oBACnBC,uBAAwB,yBACxBC,2BAA4B,6BAC5BC,eAAgB,iBAChBC,uBAAwB,0BAE1B,CAACxB,GAAkBG,QAAS,CAC1BsB,eAAgB,iBAChBC,0BAA2B,4BAC3BC,gBAAiB,kBACjBC,yBAA0B,2BAC1BC,yBAA0B,2BAC1BC,sBAAuB,0BCCdC,GAAuB,IAC3BzH,EAAAA,GAAc0H,UAClBxH,UAAU,SAAU,CAAEyH,MAAM,IAC5BzH,UAAU,QAAS,CAClBqC,KAAM,IACNqF,UAAW,aAEZ1H,UAAU,QAAS,CAClB2H,OAAQ,QACRC,MAAO,KAER5H,UAAU,YAAa,CAAE9G,MAAO,UAG9B,SAAS2O,GAAkBC,EAAgBC,EAAoBC,GACpE,IAAKD,EACH,MAAO,GAET,GAAID,EAAS,EACX,MAAO,IAGT,MAAMG,EAAWF,EAAQ5F,KAAKa,MAAM8E,KAAYE,GAAc,GAC9D,OAAKC,GAAYC,MAAMD,GACd,GAELA,GAAY,EACP,GAAGA,EAASE,QAAQ,MAEtB,IAAe,IAAXF,GAAiBE,QAAQ,MACtC,C,eCzFO,MAEMC,GAAuD,CAClE,CACErQ,MAAO,aACPmB,MAAO,oBACP8C,OAAQ,CAAE1B,IAAK,kBAAmB+N,SAAU,IAAKnP,MAAO,KACxDoP,YAAa,sDAEf,CACEvQ,MAAO,YACPmB,MAAO,OACP8C,OAAQ,CAAE1B,IAAK,GAAI+N,SAAU,GAAInP,OAAO,GACxCoP,YAAa,gF,yHCDV,MAAMC,WAA2B3R,EAAAA,G,kBAAjC,YACL,QAAO4R,WAAU,K,IAGA,EAFf,MAAMC,EAAWC,GAAmBjP,M,IAErB,EAAf,MAAMoB,EAA8D,QAArD,EAA4C,QAA5C,EAAApB,KAAKE,MAAM2B,MAAMf,OAAOC,MAAMC,GAAMA,EAAEI,gBAAtC,eAA+CA,cAA/C,QAAyD,CAAC,EACzE,GAAIpB,KAAKE,MAAMgP,UACb,IAAK9N,EAAOpB,KAAKE,MAAMgP,UACrB,YAGF,GAAmC,IAA/B1W,OAAOD,KAAK6I,GAAQrB,OACtB,O,IAIc,EAAlB,MAAMoP,EAA+B,QAAnB,EAAAnP,KAAKE,MAAMgP,gBAAX,QAAuB1W,OAAOD,KAAK6I,GAAQ,GACvD3B,EAAQ2P,GAAcpP,KAAKE,MAAM2B,MAAO7B,KAAKE,MAAMgP,UAEzDG,GAAaL,EAAUG,EAAW1P,GAElCO,KAAKE,MAAM6O,QAAQ,CAAEI,aAAY,G,EAGnC,GAvBWL,GAuBGzR,aAAY,EAAGD,Y,IACfA,EACEA,EACA0E,EAAAA,EAFF1E,EAAZ,MAAMyD,EAA2B,QAArBzD,EAAW,QAAXA,EAAAA,EAAM8C,aAAN9C,IAAAA,OAAAA,EAAAA,EAAa8R,gBAAb9R,IAAAA,EAAAA,EAAyB,GAC/B0E,EAAmB,QAAX1E,EAAAA,EAAM8C,aAAN9C,IAAAA,OAAAA,EAAAA,EAAayE,MAAMf,OAAOyB,QAAOrJ,GAAgB,SAAXA,EAAE+H,O,IACxCa,EAAd,MAAMrC,EAAiC,QAAzBqC,EAAAA,SAAU,QAAVA,EAAAA,EAAQ,UAARA,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYV,cAAZU,IAAAA,OAAAA,EAAAA,EAAqBjB,UAArBiB,IAAAA,EAAAA,EAA6B,GAG3C,OAFqBwN,GAAmBL,GAAmB7R,GAAQyD,EAAKpB,EAAMoM,QAAQ,KAAM,KASrF,qCALH,kBAAC0D,EAAAA,OAAMA,CAAC7S,QAAQ,UAAUyO,KAAK,KAAKI,KAAK,OAAOwD,QAAS3R,EAAM2R,QAASS,KAAM,eAAe,iBAK1F,IAIJ,MAAMH,GAAe,CAACL,EAAgC1Q,EAAemB,KAI1E,MAAMgQ,EAAoBT,EAAS9O,MAAMyH,QAAQpF,QAAQvB,GDxDzB,iBCwD+BA,EAAEH,KAA8BG,EAAEH,MAAQvC,IAIzGoR,QAAQC,UAAU,KAAM,IAExBX,EAAS5O,SAAS,CAChBuH,QAAS,IACJ8H,EACH,CACE5O,IAAKvC,EACLsQ,SAAU,IACVnP,MAAOA,KAGX,EAGS6P,GAAqB,CAAClS,EAA6ByD,EAAapB,IAC1DwP,GAAmB7R,GACpB8C,MAAMyH,QAAQ5G,MAAMC,GAAMA,EAAEH,MAAQA,GAAOG,EAAEvB,QAAUA,IC3E5DmQ,GAA4B/N,I,IAOlBgO,EANrB,MAAMA,EAAgBhO,EAAMf,OAAOC,MAAMC,GAAiB,aAAXA,EAAEnE,OAC3CiT,EAAiBjO,EAAMf,OAAOC,MAAMC,GAAiB,cAAXA,EAAEnE,OAElD,IAAIkT,EAAgB,EAChBC,EAAqB,EAEzB,IAAK,IAAItL,EAAI,EAAGA,IAAKmL,SAAqB,QAArBA,EAAAA,EAAe9N,cAAf8N,IAAAA,OAAAA,EAAAA,EAAuB9P,SAAU,GAAI2E,IAAK,CAC7D,MAAMuL,IAAQH,aAAAA,EAAAA,EAAgB/N,OAAO2C,KAAM,KAAMmL,aAAAA,EAAAA,EAAe9N,OAAO2C,KAAM,GACzEgE,KAAKwH,IAAID,GAAQvH,KAAKwH,IAAIH,GAAiB,KAC7CA,EAAgBE,EAChBD,EAAqBtL,EAEzB,CAEA,MAAO,CAAEqL,gBAAeC,qBAAoB,EAGjCG,GAAgC5I,IAC3C,GAAe,aAAXA,EAGJ,MAAO,CAAEa,MAAO,iBAAkBnH,KAAM,OAAQ,E,mcCT3C,MAAMmP,WAA+BjT,EAAAA,GASlCkT,WAAAA,GACN,MAAM,MAAExO,GAAU7B,KAAKE,MACvBF,KAAKI,SAAS,MAAKwP,GAAyB/N,KAE5C7B,KAAKsD,MAAMC,IACTvD,KAAKwD,kBAAiB,CAACW,EAAUC,KAC/B,GAAID,EAAStC,QAAUuC,EAAUvC,MAAO,CACtC,MAAM,MAAEA,GAAUsC,EAClBnE,KAAKI,SAAS,MAAKwP,GAAyB/N,IAC9C,KAGN,CAEQyO,YAAAA,GACN,OAAOtQ,KAAKE,MAAM2B,MAAMhF,IAC1B,CAEQ0T,QAAAA,GACN,MAAMC,EAAaxQ,KAAKE,MAAM2B,MAAMf,OAAOC,MAAMC,GAAiB,UAAXA,EAAEnE,OACzD,OAAO2T,aAAAA,EAAAA,EAAYzO,OAAO/B,KAAKE,MAAM8P,oBAAsB,EAC7D,CAEQS,cAAAA,GACN,MAAMzB,EAAWC,GAAmBjP,MAC9B0Q,EAAY1Q,KAAKsQ,eACnBI,GACFrB,GAAaL,EAAU0B,EAAW1Q,KAAKuQ,WAE3C,CArCA3F,WAAAA,CAAY1K,GACV2C,MAAM,MACD3C,IAGLF,KAAKqD,sBAAqB,IAAMrD,KAAKqQ,eACvC,EAwEF,SAASjW,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTuB,cAAe,SACfsH,SAAU,EACVvL,OAAQ,SAEV6X,qBAAqBvV,EAAAA,EAAAA,KAAI,CACvBI,QAAS,OACTuB,cAAe,SACfsH,SAAU,EACVsB,OAAQ,aAAa3M,EAAM0E,OAAOG,UAAU8H,SAC5C/H,WAAY5E,EAAM0E,OAAOE,WAAW8H,QACpC1J,QAAS,MACTgB,aAAchE,EAAMiE,QAAQ,GAC5B2T,SAAU,OACV9X,OAAQ,UAEV+X,iBAAiBzV,EAAAA,EAAAA,KAAI,CACnBwV,SAAU,OACVE,WAAY,OACZC,UAAW,WAEbtR,OAAOrE,EAAAA,EAAAA,KAAI,CACT2V,UAAW,SACXC,MAAOhY,EAAM0E,OAAOG,UAAUyN,KAC9B2F,SAAU,SACVC,WAAY,SACZC,SAAU,SACVC,aAAc,aAEhBtS,OAAO1D,EAAAA,EAAAA,KAAI,CACT0V,WAAY,MAGlB,C,mcA3EE,GAxCWV,GAwCG/S,aAAY,EAAGD,YAC3B,MAAM,cAAE2S,EAAa,mBAAEC,EAAkB,MAAEqB,GAAUjU,EAAM7D,WACrDW,GAASC,EAAAA,EAAAA,YAAWC,IACpBqF,EAAQrC,EAAMmT,W,IACRnT,EAAZ,MAAMyD,EAA4B,QAAtBzD,EAAAA,EAAM8C,MAAM2B,MAAMhF,YAAlBO,IAAAA,EAAAA,EAA0B,GAChCkU,EAAehC,GAAmBL,GAAmB7R,GAAQyD,EAAKpB,EAAMoM,QAAQ,KAAM,KAE5F,OACE,kBAAC5P,MAAAA,CAAIzB,UAAWN,EAAOgC,WACpB,kBAACmV,EAAMhU,UAAS,CAACD,MAAOiU,IACzB,kBAACpV,MAAAA,CAAIzB,UAAWN,EAAOyW,0BACFY,IAAlBxB,QAAsDwB,IAAvBvB,GAC9B,oCACE,kBAAC5T,EAAAA,MAAKA,CAACG,IAAK,EAAGO,eAAgB,gBAAiBR,WAAY,UAC1D,kBAACL,MAAAA,CAAIzB,UAAWN,EAAO4E,OAAO,uBAC5BwS,GACA,kBAAC/B,EAAAA,OAAMA,CACLpE,KAAK,KACLzO,QAAQ,UACR8S,KAAM,cACNjE,KAAK,OACLwD,QAAS,IAAM3R,EAAMqT,kBACtB,mBAKL,kBAACxU,MAAAA,CAAIzB,UAAWN,EAAO2W,kBACO,IAA1BnI,KAAKwH,IAAIH,IAAsBrB,QAA0B,IAAlBqB,EAAsB,EAAI,GAAG,KAExE,kBAAC9T,MAAAA,CAAIzB,UAAWN,EAAOuF,OAAQA,KAAAA,IC9EtC,MAAM+R,GAAgB,YAChBC,GAAiB,UAkBxBC,GAAgBC,GACbA,EAAG9U,MAAQ,oBAGpB,SAAS+F,GACPgP,EACAC,EACAC,EACAvK,GAEA,MAAO,CAAC/G,EAAiBqB,KACvB,MAAMkQ,EAAmBlQ,EAAMhF,KAAO+U,EAAO/P,EAAMhF,WAAQ0U,EAErDS,EAAW,IAAIC,EAAAA,GAAc,CACjCzR,M,EAAM,MACDA,G,EAAAA,CACHV,OAAQ,CACN,MACK+B,K,mWAMX,GAAIkQ,EAAkB,CACpB,MAAM5R,EAAO4R,EAAiB7R,MAAMC,KAGpC,OAFAA,EAAKC,SAAS,CAAEyB,UAChB1B,EAAKD,MAAMmR,MAAMjR,SAAS,CAAE8R,MAAOF,IAC5BD,CACT,CAEA,MAAMV,EAAQc,GAAe5K,GAAQ6K,SAASP,EAAShQ,IAAQwQ,QAAQL,GAEjEM,EAAUR,EAAUjQ,GACtByQ,GACFjB,EAAMkB,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCtS,KAAM,IAAIiQ,GAAuB,CAAEvO,QAAOwP,MAAOA,EAAMqB,YAMzD,OAJI7Q,EAAMhF,OACR+U,EAAO/P,EAAMhF,MAAQ2V,GAGhBA,CAAQ,CAEnB,CAEO,SAASL,GAAe5K,GAC7B,OAAOlB,EAAAA,GAAcsM,WAClBpM,UAAU,SAAU,CAAEC,YAAY,IAClCD,UAAU,UAAW,CAAEK,KAAMQ,GAAAA,GAAmBC,QAChDuL,OAAO,GACP7L,cAAcC,IACbA,EAAU6L,oBAAoB,SAASC,0BAA0B,gBAAiBC,EAAAA,cAAcC,QAChGhM,EACG6L,oBAAoB,YACpB3L,cAAc,CACbN,KAAM,QACNO,WAAuB,aAAXI,EAAwBiK,GAAgB,oBAErDyB,aAAa,eAChBjM,EACG6L,oBAAoB,aACpB3L,cAAc,CACbN,KAAM,QACNO,WAAuB,aAAXI,EAAwBkK,GAAiB,kBAEtDwB,aAAa,cAAc,GAEpC,CChGO,SAASC,KACd,MAAO,CACL/K,MAAO,IACPC,MAAO,IAAIR,EAAAA,sCACXS,UAAW,UACXC,UAAW,QACXC,MAAO,IACPC,KAAM,GACNb,QAAS,GAEb,C,yHCEO,MAAMwL,WAAkChW,EAAAA,GAC7C,aAAmB,UAAEiW,IACnBvQ,MAAM,CAAEuQ,cAGV,QAAOC,sBAAqB,KAC1B,MAAMC,EAAiBC,GAAuBvT,MAC9CsT,EAAelT,SAAS,CAAEgT,UAAWpT,KAAKE,MAAMkT,YAC3CI,GAAoBF,EAAepT,MAAMuT,aAC5CH,EAAeI,cAAc,cAG/BlI,GAAqBO,GAAkBC,eAAgBG,GAAoBH,eAAeU,oBAAqB,CAC7G0G,UAAWpT,KAAKE,MAAMkT,UACtB7L,OCgLGoM,GDhLoB3T,MCgLKzG,WAAWkG,OD/KvC,GAZJ,EA0CF,SAASrF,GAAUpB,GACjB,MAAO,CACL4a,SAASxY,EAAAA,EAAAA,KAAI,CACXI,QAAS,OACTe,IAAK,OACLD,WAAY,WAEdiD,aAAanE,EAAAA,EAAAA,KAAI,CACf4V,MAAOhY,EAAM0E,OAAO4N,KAAKzN,UACzB+S,SAAU5X,EAAM6a,WAAWC,UAAUlD,SACrCpV,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ8W,OAGzB,CAzCE,GAlBWZ,GAkBG9V,aAAY,EAAGD,YAC3B,MAAM,UAAEgW,GAAcG,GAAuBnW,GAAO7D,WAC9CW,GAASC,EAAAA,EAAAA,YAAWC,IAEpB4Z,EAAiC,UAApBZ,aAAAA,EAAAA,EAAWnS,MACxBgT,EAAUD,EACZ,oGACAzC,EAEJ,OACE,kBAACtV,MAAAA,CAAIzB,UAAWN,EAAO0Z,SACrB,kBAACrE,EAAAA,OAAMA,CACL7S,QAAQ,YACRyO,KAAK,KACLI,KAAK,QACL2I,SAAUF,EACVxE,KAAM,OACNT,QAAS3R,EAAMiW,mBACfY,QAASA,GAERD,EAAa,0BAA4B,6B,eEhD7C,MAAMG,GACXC,GACkC,CAClC,CACEC,MAAOC,EAAAA,UAAUC,YACjB3F,SAAU,IAAO4F,GACRA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,GACIA,EAAK3I,KAAKgK,IACf,GAAmB,aAAfA,EAAMhF,KAAqB,CAC7B,MAAM6X,EAAe7S,EAAMf,OAAOC,MAAMe,GAAgC,YAAfA,EAAMjF,OAC3D6X,IAGFA,EAAaC,OAAOC,MAAQ,CAC1B,CACE9V,MAAO,aACP+V,IAAK,kBACL9F,QAAUpV,I,IAEiBA,EAAAA,EAAAA,EADzBA,EAAMvB,EAAE0c,kBACR,MAAMC,EAAiC,QAAdpb,EAAAA,EAAMvB,EAAE4c,cAARrb,IAAAA,GAA6B,QAA7BA,EAAAA,EAAgBsb,qBAAhBtb,IAAAA,GAA4C,QAA5CA,EAAAA,EAA+Bsb,qBAA/Btb,IAAAA,OAAAA,EAAAA,EAA8Cub,KACvE,IAAKH,IAAuD,IAAnCA,EAAiBI,QAAQ,KAChD,OAEF,MAAMC,EAAUL,EAAiBM,MAAM,KAAK,GACvCD,GAAuB,KAAZA,IAGhBhB,SAAAA,EAAYgB,GAAQ,IAK9B,CAEA,OAAOvT,CAAK,SAQXyT,GAAgC,IAAqC,CAChF,CACEjB,MAAOC,EAAAA,UAAUC,YACjB3F,SAAU,IAAO4F,GACRA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,GACIA,EAAK+B,QAAQV,GAAyB,aAAfA,EAAMhF,Y,mzBCNvC,MAAM0Y,WAAiBpY,EAAAA,GA+HpBqY,UAAAA,GACN,MAA+C,aAAxC7B,GAAkB3T,MAAME,MAAMT,KACvC,CAEQ4Q,WAAAA,GACN,MAAM9I,EAASoM,GAAkB3T,MAAME,MAAMT,MAE7CO,KAAKI,SAAS,CACZ8R,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAI7H,EAAgB,CACzBI,cAAezK,KAAKwV,aAAe,GAAK,GACxCE,WAAYC,EAAAA,GACZjL,QAAS,CAAC1K,KAAKwV,aAAetC,KAAwBjL,EAAmBV,MAE3EqO,gBAAiB5V,KAAKwV,aAClB,IAAIF,MACJ,IAAInB,GAAyB0B,GAAa7V,UAEhDqR,MAAOrR,KAAK8V,eAEhB,CAEQA,WAAAA,GACN,MAAMvO,EAASoM,GAAkB3T,MAAME,MAAMT,M,IAET,EADpC,OAAIO,KAAKwV,aVjMN,SAA8B5L,EAAoBmM,GACvD,MAAMC,EAASzC,GAAuB3J,GAChCyH,EAAQvD,KACXmI,gBAAe,GAEf1P,UAAU,gBAAiB,MAC3BmM,QAwCH,OAvCArB,EAAMjR,SAAS,CACb8V,mBAAoB,CAACC,EAAUC,KAG7BA,EAAQC,cAAiBC,I,IAUHC,EACFA,EAQeD,EACHA,EAnB9B,GAAoB,IAAhBA,EAAKvW,OAEP,YADAiW,EAAO5V,SAAS,CAAEgT,eAAW7B,IAG/B,MAAMgF,EAAeD,EAAK,GAEpBE,EAAoC,CAAEvV,KAAM,SAAUwV,IAAKF,GAQjE,GANAC,EAAaE,UAAY,CACvB1M,KAAMtB,KAAKe,QAAqB,QAAd8M,EAAAA,EAAard,SAAbqd,IAAAA,OAAAA,EAAAA,EAAgBvM,OAAQ,GAAK,KAC/CE,GAAIxB,KAAKe,QAAqB,QAAd8M,EAAAA,EAAard,SAAbqd,IAAAA,OAAAA,EAAAA,EAAgBrM,KAAM,GAAK,MAIzCsM,EAAaE,UAAU1M,OAASwM,EAAaE,UAAUxM,GACzD,OAGF,MAAMyM,EAAQvI,KAA4B,QAATkI,EAAAA,EAAK,GAAGnd,SAARmd,IAAAA,OAAAA,EAAAA,EAAWtM,OAAQ,GAAK,EAAG+L,GACtDa,EAAMxI,IAA2B,QAATkI,EAAAA,EAAK,GAAGnd,SAARmd,IAAAA,OAAAA,EAAAA,EAAWpM,KAAM,EAAG6L,GAClDS,EAAaxN,SAAW,CAAEgB,KAAM2M,EAAOzM,GAAI0M,GAE3CZ,EAAOa,sBAAsBL,GACxBhD,GAAoBwC,EAAO9V,MAAMuT,aACpCuC,EAAOtC,cAAc,cAGvBlI,GAAqBO,GAAkBC,eAAgBG,GAAoBH,eAAeU,oBAAqB,CAC7G0G,UAAWoD,EACXjP,OAAQ,YACR,CACH,IAGE,IAAIuP,EAAAA,GAAgB,CACzBza,UAAW,MACXgE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAMkR,MAId,CU4Ia0F,CAAqB/W,KAAyB,QAAnB,EAAAA,KAAKE,MAAM6V,gBAAX,QAAuB,IAGpD/V,KAAKgX,uBAAuBzP,EACrC,CAEQyP,sBAAAA,CAAuB/V,GAC7B,MAAMoQ,EAAQjL,IAAkB6P,gBAAe,GAAMgB,eAAe,eASpE,MARa,SAAThW,EACFoQ,EAAM5K,qBAAqB,YAAa,UACtB,WAATxF,GACToQ,EAAM5K,qBAAqB,YAAa,WAAWyQ,SAAS,CAC1D/P,WAAY,gBACZP,KAAM,UAGH,IAAIkQ,EAAAA,GAAgB,CACzBza,UAAW,MACXgE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAMkR,EAAMqB,YAIpB,CAEQyE,wBAAAA,CAAyBjX,G,IAKlBA,EAAAA,EACAA,EAAAA,EALb,IAAKsT,GAAoBtT,EAAMuT,YAC7B,OAGF,MAAM2D,EAAsB,QAAflX,EAAAA,EAAMkT,iBAANlT,IAAAA,GAAoB,QAApBA,EAAAA,EAAiBuW,WAAjBvW,IAAAA,OAAAA,EAAAA,EAAsBhH,EAC7Bme,EAAsB,QAAfnX,EAAAA,EAAMkT,iBAANlT,IAAAA,GAAoB,QAApBA,EAAAA,EAAiBuW,WAAjBvW,IAAAA,OAAAA,EAAAA,EAAsB/G,EAE7B0I,GAAQyV,EAAAA,EAAAA,kBAAiB,CAC7B,CACEC,MAAMH,aAAAA,EAAAA,EAAMpN,OAAQ,EACpBwN,MAAMJ,aAAAA,EAAAA,EAAMpN,OAAQ,EACpByN,MAAML,aAAAA,EAAAA,EAAMlN,KAAM,EAClBwN,SAASN,aAAAA,EAAAA,EAAMlN,KAAM,EACrByN,KAAMN,aAAAA,EAAAA,EAAMrN,KACZ4N,KAAMP,aAAAA,EAAAA,EAAMnN,GACZ2N,UAAU,EACVC,YAAa,IACbC,UAAW,EACXC,UAAW,QACXhH,MAAOS,GACPnG,KAAM,0BAKV,OAFAzJ,EAAMhF,KAAO,SAEN,CAACgF,EACV,CA5MA+I,WAAAA,CAAY1K,GACV2C,MAAM,IACJkT,SAAU,GACVzD,QAAS,GACTvH,aAAa,GACV7K,IAGLF,KAAKqD,sBAAqB,KACxBrD,KAAKqQ,cACL,MAAM7P,EAAO0C,EAAAA,GAAWC,QAAQnD,MAC1BgW,EAASzC,GAAuBvT,MAChC0W,EAAYxT,EAAAA,GAAW6G,aAAa/J,MAE1CA,KAAKsD,MAAMC,IACT/C,EAAKgD,kBAAkByU,I,IACQA,EAEzBA,EAyEOA,EAzEX,GAFAjY,KAAKI,SAAS,CAAE2K,aAAyB,QAAZkN,EAAAA,EAAQzX,YAARyX,IAAAA,OAAAA,EAAAA,EAAc/X,SAAUuD,EAAAA,aAAaE,aAElD,QAAZsU,EAAAA,EAAQzX,YAARyX,IAAAA,OAAAA,EAAAA,EAAc/X,SAAUuD,EAAAA,aAAaC,KACvC,GACiC,IAA/BuU,EAAQzX,KAAKV,OAAOC,QACc,IAAlCkY,EAAQzX,KAAKV,OAAO,GAAGC,QACvBmY,GAAoBD,GAEpBjY,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAM,IAAIjD,EAAgB,CACxBrB,QAAS+H,EAAAA,GACT7H,SAAU,iBAMf,CACL,IAAIga,EAAiC,GACrC,GAAI/V,KAAKwV,aAAc,C,IACEhV,EACOyX,EAA9B,GADAlC,EAAWoC,IAA2B,QAAf3X,EAAAA,EAAKN,MAAMM,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBV,SAAU,IAC9CkW,EAAO9V,MAAMkT,YAAyB,QAAZ6E,EAAAA,EAAQzX,YAARyX,IAAAA,OAAAA,EAAAA,EAAc/X,SAAUuD,EAAAA,aAAaC,KAAM,C,IAInDlD,EAAAA,EAFpB,MAAM4X,EAAcpY,KAAKmX,yBAAyBnB,EAAO9V,OAErDkY,KAA+B,QAAf5X,EAAAA,EAAKN,MAAMM,YAAXA,IAAAA,GAA4B,QAA5BA,EAAAA,EAAiB4X,mBAAjB5X,IAAAA,OAAAA,EAAAA,EAA8BT,SAChDS,EAAKJ,SAAS,CACZI,KAAM,SACDA,EAAKN,MAAMM,MAAI,CAClB4X,YAAaA,KAIrB,CAEA,GAAIrC,aAAAA,EAAAA,EAAUhW,OAAQ,C,IAyBfiW,EAxBL,MAAM,YAAEqC,EAAW,UAAEC,GAAcC,GAAuBxC,GACpD3C,EAAiC,CAAEnS,KAAM,SFiC1D,SAAqC2I,GAC1C,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAeC,EAAAA,GAAuB7O,GAClE,KAAMoF,aAAoB0J,EAAAA,IACxB,MAAM,IAAIrgB,MAAM,wCAElB,OAAO2W,CACT,EErCkB2J,CAA4B3Y,MAAM4Y,cAAcP,GFuC3D,SAA4CzO,GACjD,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAeK,EAAAA,GAA+BjP,GAC1E,KAAMoF,aAAoB0J,EAAAA,IACxB,MAAM,IAAIrgB,MAAM,gDAElB,OAAO2W,CACT,CE5CkB8J,CAAmC9Y,MAAM4Y,cACvCxK,GAAkBkK,EAAY,EAAGvC,EAAU,KAG7C3C,EAAUpK,SAAW,CAAEgB,KAAMqO,EAAanO,GAAI,IAC9CkJ,EAAUqD,IAAM,CACdvd,EAAG,CACD8Q,KAA0C,IAApC0M,EAAUxW,MAAMT,MAAMuK,KAAKC,OACjCC,GAAsC,IAAlCwM,EAAUxW,MAAMT,MAAMyK,GAAGD,QAE/B9Q,EAAG,CAAE6Q,KAAMsO,EAAY,GAAKpO,GAAI6L,EAAShW,OAAS,KAGpDC,KAAKI,SAAS,CACZkS,QAAS,CACP,IAAIa,GAA0B,CAC5BC,kBAIqB,QAAtB4C,EAAAA,EAAO9V,MAAMkT,iBAAb4C,IAAAA,OAAAA,EAAAA,EAAwBhN,WAA4C,SAAhCgN,EAAO9V,MAAMkT,UAAUnS,MAC9D+U,EAAO5V,SAAS,CAAEgT,aAEtB,CACF,CAGApT,KAAKI,SAAS,CACZ2V,WACA1E,MAAOrR,KAAK8V,eAEhB,MACqB,QAAZmC,EAAAA,EAAQzX,YAARyX,IAAAA,OAAAA,EAAAA,EAAc/X,SAAUuD,EAAAA,aAAasV,SAC9C/Y,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBza,UAAW,SACXgE,SAAU,CACR,IAAI/C,EAAkB,CACpBC,UAAW,IAAM2G,EAAkB,SAK7C,KAIJlE,KAAKsD,MAAMC,IACTyS,EAAOxS,kBAAiB,CAACW,EAAUC,K,IAC7B5D,EAAJ,IAAmB,QAAfA,EAAAA,EAAKN,MAAMM,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBN,SAAUuD,EAAAA,aAAaC,SACrCsV,EAAAA,EAAAA,SAAQ7U,EAASiP,UAAWhP,EAAUgP,YAAcjP,EAASsP,aAAerP,EAAUqP,aACrFzT,KAAKwV,aAAc,CACrB,MAAM4C,EAAcpY,KAAKmX,yBAAyBhT,GAClD3D,EAAKJ,SAAS,CACZI,KAAM,SACDA,EAAKN,MAAMM,MAAI,CAClB4X,YAAaA,KAGnB,CAEJ,IACF,GAGN,EAkFA,GA/MW7C,GA+MGlY,aAAY,EAAGD,YAC3B,MAAM,MAAEiU,EAAK,QAAEiB,EAAO,YAAEvH,GAAgB3N,EAAM7D,YACtCkG,MAAO8H,GAAWoM,GAAkBvW,GAAO7D,WAC7CW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,IAAKiX,EACH,OAGF,MAsBM4H,EAPG,aADC1R,EAEG,qDAEA,GAMb,OACE,kBAACtL,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAOgf,iBACrB,kBAACjd,MAAAA,CAAIzB,UAAWN,EAAOif,gBACrB,kBAACld,MAAAA,CAAIzB,UAAWN,EAAOkf,mBACrB,kBAACC,EAAAA,gBAAeA,CACdxc,KAAM,UAAU0K,IAChB+R,QAAS,CAAC,CAAExa,MAAO,GAAIW,MAAO,aAC9BA,MAAO,aAET,kBAAC8Z,OAAAA,KAlCM,MACf,OAAQhS,GACN,IAAK,SACH,MAAO,cACT,IAAK,OACH,MAAO,YACT,IAAK,WACH,MAAO,wBACT,QACE,MAAO,GACX,EAwBesK,KAERoH,GAAY,kBAAChd,MAAAA,CAAIzB,UAAWN,EAAO+e,UAAWA,IAEjD,kBAAChd,MAAAA,CAAIzB,UAAWN,EAAOoY,SACpBvH,GAAe,kBAACD,EAAkBA,CAACC,aAAa,EAAMC,SAAU,KAChEsH,aAAAA,EAAAA,EAASza,KAAKkN,GAAW,kBAACA,EAAO1H,UAAS,CAACD,MAAO2H,EAAQlE,IAAKkE,EAAO7E,MAAMW,UAGjF,kBAACwQ,EAAMhU,UAAS,CAACD,MAAOiU,I,IAMzB,MAAM8G,GAAerY,GACnBA,EAAOjI,KAAK2hB,GAAMC,WAAWD,EAAE1Y,OAAO,GAAGjE,QAAO2E,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IAG/D6W,GAA0BxC,IACrC,MAAM2D,EAAiBhR,KAAKa,MAAMwM,EAAShW,OAAS,GACpD,IAAIuY,EAAYvC,EAAShW,OAAS2Z,EAAiB,EAKnD,OAJIpB,EAAY,IACdA,EAAY,GAGP,CACLD,YAAajK,GAAkBkK,EAAY,EAAGvC,GAC9CuC,YACD,EAGH,SAASle,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbvC,MAAO,OACP2C,QAAS,OACTuB,cAAe,SACf4I,OAAQ,aAAa3M,EAAM0E,OAAOiI,OAAOgU,OACzC5b,aAAc,MACdH,WAAY5E,EAAM0E,OAAOE,WAAW8H,QAEpC,iBAAkB,CAChBlK,QAAS,QAEX,yBAA0B,CACxBoe,YAAa,eAEf,cAAe,CACbjU,OAAQ,yBAGZuT,iBAAiB9d,EAAAA,EAAAA,KAAI,CACnBvC,MAAO,OACP2C,QAAS,OACTuB,cAAe,MACff,QAAS,MACTO,IAAK,MACLO,eAAgB,gBAChBR,WAAY,aACZwU,WAAY9X,EAAM6a,WAAWgG,iBAE/BV,gBAAgB/d,EAAAA,EAAAA,KAAI,CAClBI,QAAS,OACTuB,cAAe,SACfR,IAAK,QAEP6c,mBAAmBhe,EAAAA,EAAAA,KAAI,CACrBI,QAAS,OACTc,WAAY,WAEdgW,SAASlX,EAAAA,EAAAA,KAAI,CACXI,QAAS,OACTe,IAAK,MACLD,WAAY,WAEd2c,UAAU7d,EAAAA,EAAAA,KAAI,CACZI,QAAS,OACTwV,MAAOhY,EAAM0E,OAAO4N,KAAKzN,UACzB+S,SAAU,OACVE,WAAY,IAEZ,QAAS,CACPgJ,OAAQ,WAIhB,C,cCpXO,MAAMC,GAAyB,EAAGC,kBACvC,MAAM,OAAEC,IAAWC,EAAAA,GAAAA,MACZjG,EAASkG,IAAc5gB,EAAAA,EAAAA,UAAS,YAYvC,OAAO,kBAAC6gB,EAAAA,cAAaA,CAAC1d,QAAS,SAAU8S,KAAM,YAAayE,QAASA,EAASlF,QAV9D,KACVsL,UAAUC,YACZD,UAAUC,UAAUC,UAAUN,EHoD7B,SAA8BD,GAEnC,OAG8BjY,EAJfyY,EAAAA,GAAWC,YAAYT,GAK/BU,EAAAA,QAAQC,UAAUC,EAAAA,GAAoB7Y,GADxC,IAAyBA,CAFhC,CGvD6C8Y,CAAqBb,IAC5DG,EAAW,WACXW,YAAW,KACTX,EAAW,WAAW,GACrB,KACL,G,qzBChBJ,MAAMY,GAAyB,CAC7B,mBACA,2BACA,kBACA,iBACA,wBACA,kCASIC,GAAa,CAAC,cAAe,WAAY,OAAQ,SAEhD,SAASC,IAAwB,QAAE3B,EAAO,MAAE7Z,EAAK,SAAEC,I,IAyCbD,EAxC3C,MAAMvF,GAASC,EAAAA,EAAAA,YAAWC,IAEpB8gB,GAAMC,EAAAA,EAAAA,UACV,IACE3iB,OAAOuJ,OACLuX,EAAQ3Y,QAAO,CAACC,EAAKwa,KACnB,GAAIA,EAAK9c,MAAO,CACd,MAAMA,EAAQ8c,EAAK9c,MAAM0D,MAAMoZ,EAAK9c,MAAM6W,QAAQ,KAAO,GAGzD,GAAI4F,GAAuBM,SAASD,EAAK9c,OAAQ,C,IACjCsC,EAAd,MAAM0a,EAA0B,QAAlB1a,EAAAA,EAAiB,mBAAjBA,IAAAA,EAAAA,EAAsB,CAAEtC,MAAO,cAAegb,QAAS,IACrEgC,EAAMhC,QAAQjY,KAAK,SAAK+Z,GAAAA,CAAM9c,WAC9BsC,EAAiB,YAAI0a,CACvB,MAAO,GAAIF,EAAK9c,MAAMid,WAAW,aAAc,C,IAC/B3a,EAAd,MAAM0a,EAAuB,QAAf1a,EAAAA,EAAc,gBAAdA,IAAAA,EAAAA,EAAmB,CAAEtC,MAAO,WAAYgb,QAAS,IAC/DgC,EAAMhC,QAAQjY,KAAK,SAAK+Z,GAAAA,CAAM9c,WAC9BsC,EAAc,SAAI0a,CACpB,MACE,GAAIF,EAAK9c,MAAMid,WAAW,SAAU,C,IACpB3a,EAAd,MAAM0a,EAAmB,QAAX1a,EAAAA,EAAU,YAAVA,IAAAA,EAAAA,EAAe,CAAEtC,MAAO,OAAQgb,QAAS,IACvDgC,EAAMhC,QAAQjY,KAAK,SAAK+Z,GAAAA,CAAM9c,WAC9BsC,EAAU,KAAI0a,CAChB,KAAO,C,IACS1a,EAAd,MAAM0a,EAAoB,QAAZ1a,EAAAA,EAAW,aAAXA,IAAAA,EAAAA,EAAgB,CAAEtC,MAAO,QAASgb,QAAS,IACzDgC,EAAMhC,QAAQjY,KAAK+Z,GACnBxa,EAAW,MAAI0a,CACjB,CAEJ,CACA,OAAO1a,CAAG,GACT,CAAC,IACJY,MAAK,CAACC,EAAGC,IAAMsZ,GAAW7F,QAAQ1T,EAAEnD,OAAS0c,GAAW7F,QAAQzT,EAAEpD,UACtE,CAACgb,I,IAOwC7Z,EAJ3C,OACE,kBAACxD,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACkD,EAAAA,MAAKA,CAACd,MAAM,qBACX,kBAACkd,EAAAA,OAAMA,CACL/b,MAA6B,MAAtBA,aAAAA,EAAAA,EAAOgc,aAA8C,QAAzBhc,EAAAA,SAAe,QAAfA,EAAAA,EAAOgc,kBAAPhc,IAAAA,OAAAA,EAAAA,EAAmB4V,MAAM,YAAzB5V,IAAAA,EAAAA,EAAuC,GAC1EF,YAAa,sBACb+Z,QAAS4B,EACTxb,SAAWxG,GAAMwG,EAASxG,EAAErB,KAAKqB,GAAuBA,EAAEuG,QAAOuI,KAAK,MACtE0T,SAAS,EACTC,aAAAA,EACAC,aAAAA,EACApc,OAAQ,kBAAC5C,EAAAA,KAAIA,CAACC,KAAK,eAK7B,CAEA,MAAMzC,GAAY,KACT,CACL8B,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTqgB,SAAU,QAEV,UAAW,CACThjB,MAAO,Y,mcC/CR,MAAMijB,WAAsB3e,EAAAA,GAwBzB4e,oBAAAA,GACN,MAAO,CACL,IAAOvH,GACEA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,GACIA,EAAK3I,KAAK8Z,I,IAoCXqK,EAnCJ,MAAMlb,EAAS6Q,EAAG7Q,OACZkb,EAAYlb,EAAOC,MAAMC,GAAiB,cAAXA,EAAEnE,OAEjCyc,EAAkC,CACtCrY,KAAMgb,EAAAA,qBAAqBC,OAC3BC,cAAgBld,IACd,MAAMuB,EAAOvB,EAAM4C,MACbua,EAAe5b,aAAAA,EAAAA,EAAMM,OAAOC,MAAMC,GAAiB,kBAAXA,EAAEnE,OAC1Cwf,EAAc7b,aAAAA,EAAAA,EAAMM,OAAOC,MAAMC,GAAiB,WAAXA,EAAEnE,OACzCuY,EAAUgH,aAAAA,EAAAA,EAAcra,OAAO9C,EAAMqd,UACrCC,EAASF,aAAAA,EAAAA,EAAata,OAAO9C,EAAMqd,UAEzC,IAAKlH,EACH,OAAOnW,EAAMQ,MAGf,MAAM5C,EAAOoC,EAAMQ,MAASR,EAAMQ,MAAmB,2BACrD,OACE,kBAACxD,MAAAA,CAAIzB,UAAW,qBACd,kBAACyB,MAAAA,CACCzB,UAAW,YACXsE,MAAOjC,EACPkS,QAAS,KACP/O,KAAK8D,aAAa,IAAI0Y,EAAAA,GAAiB,CAAEpH,UAASmH,YAAW,EAAK,GAGnE1f,GAEH,kBAAC4f,EAAAA,KAAIA,CAACvH,KAAMlV,KAAK0c,iBAAiBtH,EAASmH,GAASvH,OAAQ,SAAUlW,MAAO,mBAC3E,kBAAClC,EAAAA,KAAIA,CAACC,KAAM,oBAAqBsO,KAAM,Q,GASjD,OAHI6Q,SAAiB,QAAjBA,EAAAA,EAAWrH,cAAXqH,IAAAA,OAAAA,EAAAA,EAAmBW,UACrBX,EAAUrH,OAAOgI,OAAOC,YAActD,G,6WAEjC,OACF3H,GAAAA,CACH7Q,U,OAOd,CAuBQ+b,WAAAA,CAAYrc,G,IAK4BA,EAAAA,EAJ9C,IACEA,aAAAA,EAAAA,EAAMN,SAAUuD,EAAAA,aAAasV,UAC7BvY,aAAAA,EAAAA,EAAMN,SAAUuD,EAAAA,aAAaqZ,aAC5Btc,aAAAA,EAAAA,EAAMN,UACNM,aAAAA,EAAAA,EAAMN,SAAUuD,EAAAA,aAAaE,YAAyB,QAAXnD,EAAAA,EAAKV,cAALU,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBT,UAkBhE,IAAIS,aAAAA,EAAAA,EAAMN,SAAUuD,EAAAA,aAAaC,OAAQlD,aAAAA,EAAAA,EAAMN,SAAUuD,EAAAA,aAAaE,UACpE,GAA2B,IAAvBnD,EAAKV,OAAOC,QAA0C,IAA1BS,EAAKV,OAAO,GAAGC,OAAc,CAC3D,GAA6B,UAAzBC,KAAKE,MAAM6c,WAAyB/c,KAAKE,MAAMmR,MACjD,OAEFrR,KAAKI,SAAS,CACZ2c,UAAW,QACX1L,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAM,IAAIjD,EAAgB,CACxBrB,QAAS+H,EAAAA,GACT9H,cAAe+H,EAAAA,GACf7H,QAAS,eAMrB,KAAoC,SAAzBgE,KAAKE,MAAM6c,WACpB/c,KAAKI,SAAS,CACZ2c,UAAW,OACX1L,MAAO,IAAIyF,EAAAA,GAAgB,CACzBza,UAAW,MACXgE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAMkG,EAAAA,GAAc2W,QACjB/G,gBAAe,GACflP,cAAckW,GACNA,EACJpK,oBAAoB,UACpBC,0BAA0B,UAAU,GACpCD,oBAAoB,gBACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,aACpBC,0BAA0B,QAAS,OAEvCJ,mBA3Df,CAME,GAA6B,YAAzB1S,KAAKE,MAAM6c,UACb,OAEF/c,KAAKI,SAAS,CACZ2c,UAAW,UACX1L,MAAO,IAAIyF,EAAAA,GAAgB,CACzBza,UAAW,MACXgE,SAAU,CACR,IAAI/C,EAAkB,CACpBC,UAAW2G,SAMrB,CA6CF,CAtKA0G,WAAAA,CAAY1K,GACV2C,MAAM,IACJka,UAAW,SACR7c,IA2EP,QAAQwc,oBAAmB,CAACtH,EAAiBmH,KAC3C,MACM7G,EAAawH,GADWC,GAAyBnd,OAGjD0W,EAAYxT,EAAAA,GAAW6G,aAAa/J,MAAME,MAAMT,MAChD2d,EAAeC,KAAKC,UAAU,CAClC,iBAAoB,CAClBC,OAAOC,EAAAA,EAAAA,YAAW9G,EAAUD,KAC5B/L,QAAS,CAAC,CAAEvC,MAAO,UAAWE,UAAW,UAAWD,MAAOgN,EAASM,eACpE+H,YAAa,CACXC,MAAO,CACLnB,WAGJ7G,gB,IAGWf,EAAf,MAAMgJ,EAAyB,QAAhBhJ,EAAAA,EAAAA,OAAOiJ,iBAAPjJ,IAAAA,EAAAA,EAAoB,GACnC,OAAO+F,EAAAA,QAAQC,UAAU,GAAGgD,YAAkB,CAAEE,MAAOT,EAAcU,cAAe,GAAI,IAwE1F,QAAOpe,YAAYqe,IACjB,MAAM/O,EAAWgP,GAA2Bhe,MACxCgP,EAASuB,aAAewN,IAC1B/O,EAAS4J,cAAcmF,GAEvBvS,GACEO,GAAkBC,eAClBG,GAAoBH,eAAegB,0BACnC,CACE+Q,YAGN,IA9KA/d,KAAKqD,sBAAqB,KACxBrD,KAAKI,SAAS,CACZ8R,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BG,gBAAiB5V,KAAK+b,2BAG1B,MAAMkC,EAAY/a,EAAAA,GAAWC,QAAQnD,MAErCA,KAAK6c,YAAYoB,EAAU/d,MAAMM,MACjCR,KAAKsD,MAAMC,IACT0a,EAAUza,kBAAkBhD,IAC1BR,KAAK6c,YAAYrc,EAAKA,KAAK,IAC7B,GAGN,EAkKA,GAxLWsb,GAwLGze,aAAY,EAAGD,YAC3B,MAAM,MAAEiU,GAAUjU,EAAM7D,WAClBW,EAASE,IAAUnB,EAAAA,EAAAA,cACnB+V,EAAWgP,GAA2B5gB,IACtC,WAAE8gB,GAAe3K,GAAuBnW,GAAO7D,W,IAWpC2kB,EATjB,GAAK7M,EAIL,OACE,kBAACpV,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO0K,QACrB,kBAAC3I,MAAAA,CAAIzB,UAAWN,EAAO2U,aAAa,wDACpC,kBAACoM,GAAuBA,CACtB3B,QAAyCpgB,QAAhCglB,EAAAA,aAAAA,EAAAA,EAAYrmB,KAAKqB,IAAMilB,EAAAA,EAAAA,UAASjlB,YAAhCglB,IAAAA,EAAAA,EAAuC,GAChDze,MAAOuP,EAASuB,WAChB7Q,SAAUtC,EAAMsC,YAGpB,kBAAC2R,EAAMhU,UAAS,CAACD,MAAOiU,I,IAMhC,MAAMjX,GAAapB,IACV,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,WAET,sBAAuB,CACrBA,QAAS,OACT3C,MAAO,QAGT,qBAAsB,CACpB2C,QAAS,OACTe,IAAK,MACLO,eAAgB,gBAChBR,WAAY,SACZzD,MAAO,OAEP4I,EAAG,CACDzF,QAAS,EACT4U,SAAU,EAEV,SAAU,CACRhT,WAAY5E,EAAM0E,OAAOE,WAAWC,aAK1C,aAAc,CACZmT,MAAOhY,EAAM0E,OAAO4N,KAAK8S,KACzBC,OAAQ,UACRC,SAAU,QACVnN,SAAU,SACVC,aAAc,WAEd,SAAU,CACRmN,eAAgB,gBAItB1P,aAAazT,EAAAA,EAAAA,KAAI,CACfwV,SAAU5X,EAAM6a,WAAW2K,GAAG5N,SAC9B5U,QAAS,GAAGhD,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,SAElD2H,QAAQxJ,EAAAA,EAAAA,KAAI,CACVI,QAAS,OACTsB,eAAgB,gBAChBR,WAAY,aACZC,IAAK,WAKL2H,GAAoB,KACxB,MAAMhK,GAASC,EAAAA,EAAAA,YAAWoK,IAE1B,OACE,kBAACtI,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO4E,OACrB,kBAAC+F,EAAAA,EAAQA,CAACC,MAAO,EAAGjM,MAAO,MAE5B,IAAI2L,MAAM,IAAI3M,KAAI,CAAC4M,EAAGC,IACrB,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAOukB,IAAK5d,IAAK6D,GAC9B,IAAIF,MAAM,IAAI3M,KAAI,CAAC4M,EAAGia,IACrB,kBAACnF,OAAAA,CAAK/e,UAAWN,EAAOykB,QAAS9d,IAAK6d,GACpC,kBAAC7Z,EAAAA,EAAQA,CAACC,MAAO,U,EAS/B,SAASP,GAAkBvL,GACzB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbtC,OAAQ,OACRD,MAAO,OACP+lB,SAAU,WACVnZ,gBAAiBzM,EAAM0E,OAAOE,WAAW8H,QACzCC,OAAQ,aAAa3M,EAAM0E,OAAOiI,OAAOgU,OACzC3d,QAAS,QAEX8C,OAAO1D,EAAAA,EAAAA,KAAI,CACT4B,aAAc,SAEhByhB,KAAKrjB,EAAAA,EAAAA,KAAI,CACP4B,aAAc,MACdxB,QAAS,OACTsB,eAAgB,iBAElB6hB,SAASvjB,EAAAA,EAAAA,KAAI,CACXvC,MAAO,QAGb,C,yHC9UO,MAAMgmB,WAAmB1hB,EAAAA,GAOtBkT,WAAAA,G,IAEJkD,EADFvT,KAAKsD,MAAMC,IAC+B,QAAxCgQ,EAAAA,GAAuBvT,MAAME,MAAMgS,aAAnCqB,IAAAA,OAAAA,EAAAA,EAA0C/P,kBAAiB,KACzDxD,KAAK8e,YAAY,KAIrB9e,KAAKsD,MAAMC,IACTgQ,GAAuBvT,MAAMwD,kBAAiB,CAACW,EAAUC,K,IACnDD,EAA8BC,GAAhB,QAAdD,EAAAA,EAAS+N,aAAT/N,IAAAA,OAAAA,EAAAA,EAAgBjE,MAAMW,QAAuB,QAAfuD,EAAAA,EAAU8N,aAAV9N,IAAAA,OAAAA,EAAAA,EAAiBlE,MAAMW,MACvDb,KAAK8e,YACP,KAIJ9e,KAAKsD,MAAMC,IACToQ,GAAkB3T,MAAMwD,kBAAiB,CAACW,EAAUC,KAC9CD,EAAS1E,QAAU2E,EAAU3E,OAC/BO,KAAK8e,YACP,KAIJ9e,KAAK8e,YACP,CAEQA,UAAAA,GACN9e,KAAKI,SAAS,CAAED,KAAM,IAAI2b,GAAc,CAAC,IAC3C,CAlCAlR,WAAAA,CAAY1K,GACV2C,M,uUAAM,IAAK3C,IAEXF,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,ECbK,SAAS+e,GAAcxF,GAC5B,GAAIA,EAAK2E,WACP,IAAK,MAAMzc,KAAK8X,EAAK2E,WAAY,C,IAEOzc,EADtC,GAAc,kBAAVA,EAAEZ,IACJ,OAAOme,SAASvd,EAAEhC,MAAMwf,WAAyB,QAAbxd,EAAAA,EAAEhC,MAAMyf,aAARzd,IAAAA,OAAAA,EAAAA,EAAe0d,YAAa,IAAK,GAEzE,CAGF,MAAM,IAAI9mB,MAAM,2BAClB,CAEO,SAAS+mB,GAAe7F,GAC7B,GAAIA,EAAK2E,WACP,IAAK,MAAMzc,KAAK8X,EAAK2E,WAAY,C,IAEOzc,EADtC,GAAc,mBAAVA,EAAEZ,IACJ,OAAOme,SAASvd,EAAEhC,MAAMwf,WAAyB,QAAbxd,EAAAA,EAAEhC,MAAMyf,aAARzd,IAAAA,OAAAA,EAAAA,EAAe0d,YAAa,IAAK,GAEzE,CAGF,MAAM,IAAI9mB,MAAM,4BAClB,C,yHDuBE,GArCWwmB,GAqCGxhB,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,GAAS/C,EAAM7D,WACvB,OAAO4G,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,G,IE9CnC,MAAMkf,GAuCXC,OAAAA,CAAQ/F,GAENvZ,KAAKuf,KAAO7W,KAAK8W,IAAIT,GAAcxF,GAAOvZ,KAAKuf,MAC/Cvf,KAAKyf,MAAQ/W,KAAKgX,IAAIN,GAAe7F,GAAOvZ,KAAKyf,OACjDzf,KAAK2f,MAAMte,KAAKkY,EAClB,CAEAqG,QAAAA,CAASC,GACPA,EAAK7J,OAAShW,KACdA,KAAKK,SAASgB,KAAKwe,EACrB,CAEAC,OAAAA,CAAQvG,GACN,OAAOwF,GAAcxF,GAAQvZ,KAAKuf,MAAQH,GAAe7F,GAAQvZ,KAAKyf,KACxE,CAEAM,iBAAAA,CAAkBxG,GAChB,MAAM1c,EAAOmjB,GAASzG,GAEtB,IAAK,MAAM0G,KAASjgB,KAAKK,SACvB,GAAI4f,EAAMpjB,OAASA,EACjB,OAAOojB,EAIX,OAAO,IACT,CAtDArV,WAAAA,EAAY,KACV/N,EAAI,YACJqjB,EAAW,cACXC,EAAa,MACbR,EAAK,KACLJ,EAAI,MACJE,EAAK,QACLW,IAjBFvjB,GAAAA,KAAAA,YAAAA,GACAqjB,GAAAA,KAAAA,mBAAAA,GACAC,GAAAA,KAAAA,qBAAAA,GACAR,GAAAA,KAAAA,aAAAA,GACAJ,GAAAA,KAAAA,YAAAA,GACAE,GAAAA,KAAAA,aAAAA,GACApf,GAAAA,KAAAA,gBAAAA,GACA2V,GAAAA,KAAAA,cAAAA,GACAoK,GAAAA,KAAAA,eAAAA,GAmBEpgB,KAAKnD,KAAOA,EACZmD,KAAKkgB,YAAcA,EACnBlgB,KAAKmgB,cAAgBA,EACrBngB,KAAK2f,MAAQA,EACb3f,KAAKuf,KAAOA,EACZvf,KAAKyf,MAAQA,EACbzf,KAAKK,SAAW,GAChBL,KAAKgW,OAAS,KACdhW,KAAKogB,QAAUA,CACjB,EA+BK,SAASC,GAAW7G,G,IACDA,EAK6B8G,EAAAA,EALrD,MAAMA,EAA8B,QAAZ9G,EAAAA,EAAE0E,kBAAF1E,IAAAA,OAAAA,EAAAA,EAAczY,MAAMU,GAAgB,iBAAVA,EAAEZ,M,IAKrCyf,EAAAA,EACE9G,EAENA,EAPX,OAAO,IAAI6F,GAAS,CAClBE,KAAMR,GAAcvF,GACpBiG,MAAOL,GAAe5F,GACtB3c,KAAMmjB,GAASxG,GACf0G,YAA8F,QAAjFI,EAAkC,QAAlCA,EAAAA,aAAAA,EAAAA,EAAiB7gB,MAAM8gB,mBAAvBD,IAAAA,EAAAA,EAAsCA,SAAsB,QAAtBA,EAAAA,EAAiB7gB,aAAjB6gB,IAAAA,GAA6B,QAA7BA,EAAAA,EAAwBpB,aAAxBoB,IAAAA,OAAAA,EAAAA,EAA+BE,oBAArEF,IAAAA,EAAAA,EAAqF,GAClGH,cAAqB,QAAN3G,EAAAA,EAAE3c,YAAF2c,IAAAA,EAAAA,EAAU,GACzBmG,MAAO,CAACnG,GACR4G,QAAkB,QAAT5G,EAAAA,EAAEpE,eAAFoE,IAAAA,EAAAA,EAAa,IAE1B,CAEA,SAASwG,GAASxG,GAChB,IAAIiH,EAAU,GACd,IAAK,MAAMhf,KAAK+X,EAAE0E,YAAc,GAChB,iBAAVzc,EAAEZ,KAA0BY,EAAEhC,MAAM8gB,cACtCE,EAAUhf,EAAEhC,MAAM8gB,aAItB,MAAO,GAAGE,KAAWjH,EAAE3c,MACzB,CChBA,SAAS6jB,GAAeC,GACtBA,EAAEpB,KAAOqB,OAAOC,iBAChBF,EAAElB,MAAQmB,OAAOE,iBAEjB,IAAK,MAAMC,KAAKJ,EAAEtgB,SAChBqgB,GAAeK,EAEnB,C,mcC3CA,MAAMC,GAAe,mBAEd,MAAMC,WAA0B9jB,EAAAA,GAiB9BkT,WAAAA,G,IAEH,EADFrQ,KAAKsD,MAAMC,IACO,QAAhB,EAAAvD,KAAKE,MAAMgS,aAAX,eAAkB1O,kBAAkBtD,I,IAC9BA,EAA8CA,EAK9CA,EAA2CA,EAL/C,IAAc,QAAVA,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYA,SAAUuD,EAAAA,aAAasV,UAAqB,QAAV7Y,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYA,SAAUuD,EAAAA,aAAaE,WAKrF,IAAc,QAAVzD,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYA,SAAUuD,EAAAA,aAAaC,OAAkB,QAAVxD,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYJ,OAAOC,QAAQ,C,IAC1DG,EAAd,MAAM2B,EAAkB,QAAV3B,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYJ,OAAO,GAAGgB,OAAO,GAAGiB,OAAO,GACrD,GAAIF,EAAO,CACT,MACMqf,EDpEX,SAAqBC,GAC1B,MAAMD,EAAO,IAAI7B,GAAS,CACxBxiB,KAAM,OACNqjB,YAAa,GACbC,cAAe,GACfZ,KAAMqB,OAAOE,iBACbrB,MAAOmB,OAAOC,iBACdlB,MAAO,GACPS,QAAS,KAGX,GAAIe,GAAUA,EAAOphB,OAAS,EAC5B,IAAK,MAAM2d,KAASyD,EAAQ,C,IACtBzD,EAAJ,GAA+B,KAAb,QAAdA,EAAAA,EAAM0D,gBAAN1D,IAAAA,OAAAA,EAAAA,EAAgB3d,QAClB,MAAM,IAAI1H,MAAM,mCAGlB,MAAMgpB,EAAiBrC,SAAStB,EAAM4D,mBAAqB,IAAK,IAE1DC,EAAK7D,EAAM0D,SAAS,GAE1BG,EAAG5B,MAAMne,MAAK,CAACggB,EAAIC,IAAO1C,GAAcyC,GAAMzC,GAAc0C,KAG5D,IAAIC,EAAoBR,EAExBR,GAAeQ,GACf,IAAK,MAAM3H,KAAQgI,EAAG5B,MAAO,CAM3B,IAJApG,EAAKnE,QAAUsI,EAAM0C,QACrB7G,EAAK+H,kBAAoB,IAAGtC,SAASzF,EAAK+H,kBAAmB,IAAMD,GAGzC,OAAnBK,EAAQ1L,SACT0L,EAAQ5B,QAAQvG,IAGpBmI,EAAUA,EAAQ1L,OAIpB,MAAMiK,EAAQyB,EAAQ3B,kBAAkBxG,GACxC,GAAI0G,EAAO,CACTA,EAAMX,QAAQ/F,GAEdmI,EAAUzB,EACV,QACF,CAGA,MAAM0B,EAAUtB,GAAW9G,GAC3BoI,EAAQvB,QAAU1C,EAAM0C,QACxBsB,EAAQ9B,SAAS+B,GACjBD,EAAUC,CACZ,CACF,CAGF,OAAOT,CACT,CCSyBU,CADIvE,KAAKwE,MAAMhgB,IAE5Bqf,EAAK7gB,SAASmB,MAAK,CAACC,EAAGC,IAAMogB,GAAWpgB,GAAKogB,GAAWrgB,KAExDzB,KAAKI,SAAS,CACZ2hB,SAAS,EACTb,OACA7P,MAAO,IAAIyF,EAAAA,GAAgB,CACzBhe,OAAQ,OACRkpB,KAAM,OACN3hB,SAAUL,KAAKiiB,UAAUf,MAG/B,CACF,OArBElhB,KAAKI,SAAS,CAAE2hB,SAAS,GAqB3B,IAGN,CAEQE,SAAAA,CAAUf,GAChB,OAAOA,EAAK7gB,SAASxI,KAAKooB,GACjB,IAAI3f,EAAAA,GAAc,CACvBxH,OAAQ,IACRD,MAAO,OACPqpB,UAAW,QACX/hB,KAAMH,KAAKmiB,SAASlC,MAG1B,CAEQkC,QAAAA,CAASjB,GACf,MAAMxK,EAAYxT,EAAAA,GAAW6G,aAAa/J,MACpCgK,EAAO0M,EAAUxW,MAAMT,MAAMuK,KAC7BE,EAAKwM,EAAUxW,MAAMT,MAAMyK,GAE3BkK,EAAYyB,GAAa7V,MAE/B,OAAOqG,EAAAA,GAAc8a,SAClB/O,SAAS,iBAAiB8O,EAAKhB,gBAAgB4B,GAAWZ,kBAC1D3a,UAAU,uBAA8B,CAAC6O,EAAiBmH,KAClD,CACLzd,MAAO,aACPoW,KAAM,IACNnG,QAAS,IAAMqF,EAAUgB,EAASmH,GAClCtC,OAAQ,CAAC,EACTjF,OAAQ,YAGX3C,QACC,IAAIJ,EAAAA,GAAc,CAChBzR,KAAM,CACJN,MAAOuD,EAAAA,aAAaC,KACpBgT,UAAW,CACT1M,OACAE,KACAuM,IAAK,CAAEzM,OAAME,OAEfpK,OAAQ,CACN,MACKE,KAAKoiB,UAAUlB,SAM3BxO,OACL,CAEQ0P,SAAAA,CAAUlB,GAChB,MAAMxD,EAAQ1d,KAAKqiB,SAASnB,EAAMF,IAC5BsB,EAAY5E,EAAM,GAAGwC,YAAc,IAAMxC,EAAM,GAAGyC,cAExD,OAAOoC,EAAAA,EAAAA,iBAAgB,CACrB1lB,KAAM,SAASylB,IACfna,MAAO,SAASma,IAChBxhB,OAAQ,CACN,CACEjE,KAAM,aACNoE,KAAMC,EAAAA,UAAUshB,MAChBzgB,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAEupB,cAE7B,CACE5lB,KAAM,UACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAEknB,WAE7B,CACEvjB,KAAM,SACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAEypB,UAE7B,CACE9lB,KAAM,eACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAE0pB,gBAE7B,CACE/lB,KAAM,cACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAEgnB,eAE7B,CACErjB,KAAM,gBACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAEinB,iBAE7B,CACEtjB,KAAM,WACNoE,KAAMC,EAAAA,UAAUC,OAChBY,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAE8P,YAE7B,CACEnM,KAAM,YACNoE,KAAMC,EAAAA,UAAUC,OAChBY,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAE2pB,aAE7B,CACEhmB,KAAM,aACNoE,KAAMC,EAAAA,UAAUC,OAChBY,OAAQ2b,EAAM7lB,KAAKqB,GAAMA,EAAE4pB,gBAInC,CAEQT,QAAAA,CAASxC,EAAgB8C,GAC/B,MAAMI,EAAelD,EAAKF,MAAMhf,QAC9B,CAACC,EAAKmgB,K,IAAOA,EAAAA,E,MAAqE,WAAzD,QAAZA,EAAAA,EAAE7C,kBAAF6C,IAAAA,GAAoC,QAApCA,EAAAA,EAAchgB,MAAMU,GAAgB,WAAVA,EAAEZ,aAA5BkgB,IAAAA,OAAAA,EAAAA,EAA+CthB,MAAM8gB,aAA0B3f,EAAM,EAAIA,CAAG,GACzG,GAIF,IAAIiiB,EAAY,KACZF,IAAW3B,KACb6B,EACEhD,EAAKF,MAAMhf,QAAO,CAACC,EAAKmgB,IAAMngB,EAAMoe,SAAS+B,EAAEO,kBAAmB,KAAK,GAAKzB,EAAKF,MAAM5f,OAAS,KAGpG,MAAMgC,EAAS,CACb,CAGE0gB,WAAY5C,EAAKF,MAAM3d,OAAO,GAAGnK,KAAKqB,IAAO,CAC3C8pB,QAAS,WACT5C,QAASlnB,EAAEkc,QACXuN,OAAQzpB,EAAEypB,WAEZvC,QAASP,EAAKO,QACduC,OAAQ9C,EAAKF,MAAM,GAAGgD,OACtBC,aAAcD,EACdzC,YAAaL,EAAKK,YAClBC,cAAeN,EAAKM,cACpB2C,WAAYC,EAAe,EAAI,EAAc,EAC7C/Z,SAAU6W,EAAKF,MAAMhf,QAAO,CAACC,EAAKmgB,IAAMngB,EAAMoe,SAAS+B,EAAEkC,cAAe,KAAK,GAAKpD,EAAKF,MAAM5f,OAAS,IACtG8iB,cAIJ,IAAK,MAAM5C,KAASJ,EAAKxf,SACvB0B,EAAOV,QAAQrB,KAAKqiB,SAASpC,EAAOJ,EAAKF,MAAM,GAAGgD,SAEpD,OAAO5gB,CACT,CA7LA6I,WAAAA,CAAY1K,GACV2C,MAAM,IACJqP,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BoL,WAAYC,EAAAA,GACZjL,QAAS,CAACwY,GAAWhjB,EAAMqH,WAE7BqO,gBAAiBuN,EAAAA,KAEnBpB,SAAS,GACN7hB,IAGLF,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAiSF,SAASkjB,GAAW3b,GAClB,IAAI6b,EACAC,EAAiB,GACrB,OAAQ9b,GACN,IAAK,SACH6b,EAAc,iBACdC,EAAiB,iBACjB,MACF,IAAK,WACHD,EAAc,cAAcE,EAAAA,KAC5BD,EAAiB,cAAcE,EAAAA,KAC/B,MACF,QACEH,EAAc,gBAIlB,MAAO,CACLjb,MAAO,IACPC,MAAO,IAAIR,EAAAA,MACTyb,EAAetjB,OAAS,MAAMsjB,IAAmB,aACxCD,oGACX/a,UAAW,UACXC,UAAW,MACXC,MAAO,IACPC,KAAM,GACNb,QAAS,GAEb,CA5IE,GAhMWsZ,GAgMG5jB,aAAY,EAAGD,Y,IAWvB8U,EA6FCA,EAvGL,MAAM,KAAEgP,EAAI,QAAEa,EAAO,MAAE1Q,EAAK,MAAEa,GAAU9U,EAAM7D,WACxCW,EAASE,IAAUnB,EAAAA,EAAAA,cACnBD,GAAQC,EAAAA,EAAAA,aAER+gB,EAAcmD,GAAyB/f,IACvC,MAAEqC,GAAUua,EAAYrG,oBAAoBpa,WAE5CgO,EAAS9H,EAEf,IAKIoP,EALA2U,EAAYzB,KAAYb,aAAAA,EAAAA,EAAM7gB,SAASN,SACvCmS,SAAiB,QAAjBA,EAAAA,EAAOhS,MAAMM,YAAb0R,IAAAA,OAAAA,EAAAA,EAAmBhS,SAAUuD,EAAAA,aAAaC,OAC5C8f,GAAY,GAId,IAAIC,EAAW,GACf,OAAQlc,GACN,IAAK,OACHsH,EACE,oCACE,kBAAC5S,MAAAA,KAAI,+EACL,kBAACA,MAAAA,KAAI,uFAGTwnB,EAAW,SACX,MACF,IAAK,SACH5U,EACE,oCACE,kBAAC5S,MAAAA,KAAI,8EACL,kBAACA,MAAAA,KAAI,uFAGTwnB,EAAW,QACX,MACF,IAAK,WACH5U,EACE,oCACE,kBAAC5S,MAAAA,KAAI,uFACL,kBAACA,MAAAA,KAAI,uFAGTwnB,EAAW,OAIf,MAAMC,EAAUC,GAAqBpc,GAE/Bqc,EACJ,oCACE,kBAACpnB,EAAAA,KAAIA,CAACC,cAAe,SAAUC,QAAQ,MACpCkH,EAAAA,IAEH,kBAACpH,EAAAA,KAAIA,CAACC,cAAe,SAAUC,QAAQ,QACrC,kBAACT,MAAAA,CAAIzB,UAAWN,EAAO2pB,UAAU,2BACNJ,EAAS,2FACnBA,EAAS,oDAG5B,kBAACrnB,EAAAA,MAAKA,CAACG,IAAK,GAAKD,WAAY,UAC3B,kBAACM,EAAAA,KAAIA,CAACC,KAAK,gBACX,kBAACL,EAAAA,KAAIA,CAACC,cAAe,SAAUC,QAAQ,QAAO,mDAKhD,kBAACT,MAAAA,CAAIzB,UAAWN,EAAO4pB,iBAAiB,kBAEtC,kBAAC7nB,MAAAA,CAAIzB,UAAWN,EAAO6K,QACrB,kBAACgf,EAAAA,WAAUA,CACTvU,KAAK,oBACLjE,KAAK,QACLJ,KAAM,KACN6J,OAAQ,SACRE,KACE,yGAGD,GAAGwO,EAAQM,oBAOtB,OACE,kBAAC5nB,EAAAA,MAAKA,CAACC,UAAW,SAAUE,IAAK,GAC/B,kBAACN,MAAAA,CAAIzB,UAAWN,EAAO2U,aAAcA,GACpC2U,GACC,kBAACpnB,EAAAA,MAAKA,CAACC,UAAW,SAAUE,IAAK,GAC/B,kBAACsI,EAAAA,EAAQA,CACPC,MAAO,EACPhM,OAAQ,IACR2E,UAAWzE,EAAM0E,OAAOE,WAAWC,UACnCC,eAAgB9E,EAAM0E,OAAOE,WAAW8H,YAK5C8d,GAAatC,GAAQA,EAAK7gB,SAASN,OAAS,GAC5C,kBAAC9D,MAAAA,CAAIzB,UAAWN,EAAO+pB,eAAgB5S,GAAS,kBAACA,EAAMhU,UAAS,CAACD,MAAOiU,MAGzEa,SAAiB,QAAjBA,EAAAA,EAAOhS,MAAMM,YAAb0R,IAAAA,OAAAA,EAAAA,EAAmBhS,SAAUuD,EAAAA,aAAaC,QAASwd,aAAAA,EAAAA,EAAM7gB,SAASN,SACjE,kBAACnE,EAAUA,CAACC,QAAS+nB,EAAe5nB,QAAS,S,IAqCvD,MAAM5B,GAAapB,IACV,CACL6V,aAAazT,EAAAA,EAAAA,KAAI,CACfwV,SAAU5X,EAAM6a,WAAW2K,GAAG5N,SAC9B5U,QAAS,GAAGhD,EAAMiE,QAAQ,SAE5BgnB,eAAe7oB,EAAAA,EAAAA,KAAI,CACjBI,QAAS,OACTuB,cAAe,SACfR,IAAKvD,EAAMiE,QAAQinB,GAEnB,oCAAqC,CACnC/S,SAAU,OACV,yCAA0C,CACxC3V,QAAS,SAIb,2EAA4E,CAC1EA,QAAS,QAIX,mBAAoB,CAClBA,QAAS,QAIX,qCAAsC,CACpC,wBAAyB,CACvB6iB,OAAQ,SAGZ,+BAAgC,CAC9BA,OAAQ,qBAGZwF,UAAUzoB,EAAAA,EAAAA,KAAI,CACZkjB,SAAU,QACVxE,OAAQ,WAEV/U,QAAQ3J,EAAAA,EAAAA,KAAI,CACV+oB,WAAYnrB,EAAMiE,QAAQ,KAE5B6mB,iBAAiB1oB,EAAAA,EAAAA,KAAI,CACnBI,QAAS,OACTsB,eAAgB,gBAChBR,WAAY,aAKlB,SAASwlB,GAAWZ,GAClB,IAAIpc,EAAQoc,EAAKvB,MAAM5f,OACvB,IAAK,MAAMkgB,KAASiB,EAAK7gB,SACvByE,GAASgd,GAAW7B,GAEtB,OAAOnb,CACT,C,eC5ZO,SAASsf,IAAgB,QAAE9K,EAAO,gBAAE+K,EAAe,MAAE5kB,EAAK,SAAEC,EAAQ,QAAE4kB,GAAU,EAAK,MAAElnB,IAC5F,MAAMlD,GAASC,EAAAA,EAAAA,YAAWC,IACpBpB,GAAQC,EAAAA,EAAAA,cACR,SAAE2X,GAAa5X,EAAM6a,YAEpB0Q,EAAaC,IAAkBjrB,EAAAA,EAAAA,UAAiB,KAEhDkrB,EAAgBC,IAAqBnrB,EAAAA,EAAAA,UAAiB,GACvDorB,GAAoBC,EAAAA,EAAAA,QAAuB,OAE3C,QAAEjd,GAAYsH,GAAmB7R,GAAO7D,YACtCkG,MAAO8H,GAAWoM,GAAkBvW,GAAO7D,WAC7CsrB,EAActd,GAEpBud,EAAAA,GAAAA,GAAkB,CAChBC,IAAKJ,EACLK,SAAU,KACR,MAAMC,EAAUN,EAAkBO,QAC9BD,GACFP,EAAkBO,EAAQE,YAC5B,IAIJ,MAAMC,GAAejK,EAAAA,EAAAA,UAAQ,KAC3B,IAAIkK,EAAoB,EACxB,OAAOhB,EACJ9hB,QAAQ+iB,IAEP,IAAIC,IAAWjM,EAAQvY,MAAM5I,GAAMA,EAAEsH,QAAU6lB,IAG/C,OAAI3d,EAAQ5G,MAAMC,GAAMA,EAAEH,MAAQykB,IAAsB,MAAftkB,EAAE4N,UAAmC,OAAf5N,EAAE4N,cAM7DjH,EAAQ5G,MAAMC,GAAgB,oBAAVA,EAAEH,QACxB0kB,EAASA,GAAiB,aAAPD,GAA4B,oBAAPA,GAKtB,SAAhBT,GAA0C,WAAhBA,IAC5BU,EAASA,GAAiB,WAAPD,GAGdC,EAAM,IAEd1tB,KAAK6Y,IAAe,CACnBpS,MAAOoS,EAAU7E,QAAQ2Z,EAAAA,GAAW,IAAI3Z,QAAQ4Z,EAAAA,GAAe,IAC/Dna,KAAMoF,EACNjR,MAAOiR,MAERnO,QAAQmjB,IACP,MAAMpa,EAAOoa,EAAOpnB,OAASonB,EAAOpa,MAAQ,GACtCqa,GAAYC,EAAAA,EAAAA,aAAYta,EAAMsF,GAAU/X,MAC9C,OAAIwsB,EAAoBM,EA7DD,GACA,IA4D+DlB,IACpFY,GAAqBM,EA9DA,IA+Dd,EAGT,GACA,GACH,CAACtB,EAAiB/K,EAAS3R,EAASkd,EAAajU,EAAU6T,IAExDoB,GAAmB1K,EAAAA,EAAAA,UAAQ,KAC/B,MAAM2K,EAAMxM,EAAQ/W,QAAQ+iB,IAAQF,EAAarkB,MAAMglB,I,IAAoBT,E,OAAbS,EAAGtmB,SAAkB,QAAR6lB,EAAAA,EAAG7lB,aAAH6lB,IAAAA,OAAAA,EAAAA,EAAU7J,WAAW,MAChG,OAAOuK,GAAgBF,EAAKvB,EAAY,GACvC,CAACA,EAAajL,EAAS8L,IAEpBa,EAA4B3M,GACzBA,EACJ/W,QAAQ+iB,I,IAAmCA,E,OAA3BY,EAAAA,GAAkB7K,SAAiB,QAARiK,EAAAA,EAAG7lB,aAAH6lB,IAAAA,OAAAA,EAAAA,EAAU7J,WAAQ,IAC7D5jB,KAAKytB,I,IAAiBA,E,MAAT,CAAEhnB,MAAe,QAARgnB,EAAAA,EAAGhnB,aAAHgnB,IAAAA,OAAAA,EAAAA,EAAUzZ,QAAQ2Z,EAAAA,GAAW,IAAI3Z,QAAQ4Z,EAAAA,GAAe,IAAKhmB,MAAO6lB,EAAG7lB,MAAO,KAIzGjG,EAAAA,EAAAA,YAAU,K,IACmC8f,EAAtB+K,EAArB,MAAM8B,EAAiC,QAAlB9B,EAAAA,EAAgB,UAAhBA,IAAAA,EAAAA,EAAgC,QAAV/K,EAAAA,EAAQ,UAARA,IAAAA,OAAAA,EAAAA,EAAY7Z,MACnD0mB,IACG7B,GAAa7kB,GAASA,IAAUoI,EAAAA,IACnCnI,EAASymB,GAAc,GAE3B,IAGF,MAAMC,EAAgB9B,EAAU,CAAC,CAAEhmB,MAAOuJ,EAAAA,GAAKpI,MAAOoI,EAAAA,KAAS,GACzDwe,EAAuB/B,EAAUzc,EAAAA,GAAM,GAE7C,OACE,kBAACzI,EAAAA,MAAKA,CAACd,MAAM,YACX,kBAACrC,MAAAA,CAAI8oB,IAAKJ,EAAmBnqB,UAAWN,EAAOgC,WAC5CkpB,EAAarlB,OAAS,GACrB,kBAACumB,EAAAA,iBAAgBA,CAAChN,QAAS,IAAI8M,KAAkBhB,GAAe3lB,MAAOA,EAAOC,SAAUA,IAE1F,kBAAC8b,EAAAA,OAAMA,CACL/b,MAAOA,GAASwmB,EAAyBJ,GAAkB3f,MAAMhN,GAAMA,EAAEuG,QAAUA,IAASA,EAAQ,KACpGF,YAAa,mBACb+Z,QAAS2M,EAAyBJ,GAClCnmB,SAAW6mB,I,IACWA,EAApB,MAAMC,EAA6B,QAAfD,EAAAA,aAAAA,EAAAA,EAAU9mB,aAAV8mB,IAAAA,EAAAA,EAAmBF,EACvC3mB,EAAS8mB,EAAY,EAEvBhsB,UAAWN,EAAOusB,OAClB9K,aAAAA,EACA+K,cAAe,CAACjnB,GAAiBsF,aAChB,iBAAXA,GACFyf,EAAe/kB,EACjB,EAEFknB,YAAa,IAAMnC,EAAe,IAClC5I,aAAAA,KAKV,CAEA,SAASxhB,GAAUpB,GACjB,MAAO,CACLytB,QAAQrrB,EAAAA,EAAAA,KAAI,CACVkjB,SAAUtlB,EAAMiE,QAAQ,MAE1Bf,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,KAGzB,CAEO,MAAM+oB,GAAkB,CAAC1M,EAAyClR,KACvE,GAAuB,IAAnBkR,EAAQvZ,OACV,MAAO,GAGT,GAAqB,IAAjBqI,EAAMrI,OACR,OAAOuZ,EAAQtX,MAAM,EAAG4kB,EAAAA,IAG1B,MAAMC,EAAiBze,EAAM4b,cAC7B,OAAO1K,EACJ/W,QAAQukB,MACHA,EAAIrnB,OAASqnB,EAAIrnB,MAAMM,OAAS,IAC3B+mB,EAAIrnB,MAAMukB,cAAc3I,SAASwL,KAI3C7kB,MAAM,EAAG4kB,EAAAA,GAAW,E,yHC7JlB,MAAMG,WAAuB5pB,EAAAA,GAC3B6pB,QAAAA,EAAS,MAAE5pB,IAChB,MAAM,OAAE6pB,EAAM,QAAE3N,GAAYlc,EAAM7D,WAElC,OACE,kBAAC6F,EAAAA,MAAKA,CAACd,MAAM,QACX,kBAACgoB,EAAAA,iBAAgBA,CAAChN,QAASA,EAAS7Z,MAAOwnB,EAAQvnB,SAAUtC,EAAM8pB,iBAGzE,C,kBATK,YAWL,QAAOA,kBAAkBD,IACvBjnB,KAAKI,SAAS,CAAE6mB,WAChBzb,GAAqBO,GAAkBC,eAAgBG,GAAoBH,eAAeS,oBAAqB,CAC7G0a,OAAQF,GACR,G,EAGJ,GAlBWF,GAkBG1pB,aAAY,EAAGD,YAC3B,MAAM,QAAEgqB,EAAO,QAAE9N,EAAO,OAAE2N,GAAW7pB,EAAM7D,WAErC6P,EAAQkQ,EAAQ+N,WAAWlvB,GAAMA,EAAEsH,QAAUwnB,IACnD,IAAe,IAAX7d,EACF,OAAO,KAGT,MAAM+d,EAASC,EAAQhe,GAEvB,OAAO,kBAAC+d,EAAO9pB,UAAS,CAACD,MAAO+pB,G,ICxC7B,MAAMG,GAAmB,IACvBjhB,EAAAA,GAAcC,aAClBC,UAAU,SAAU,CAAEC,YAAY,IAClCD,UAAU,UAAW,CAAEK,KAAMQ,EAAAA,mBAAmBC,QAChDZ,qBAAqB,cAAe,I,mzBCoBlC,MAAM8gB,WAAiCpqB,EAAAA,GAC5CyN,WAAAA,CAAY1K,GACV2C,MAAM,SAAK3C,GAAAA,CAAOwK,QAAS,MAK7B,QAAQ2F,eAAc,KACpBrQ,KAAKsD,MAAMC,IACTvD,KAAKwD,kBAAiB,KACpBxD,KAAKwnB,aACLxnB,KAAKynB,YAAY,IACnB,IAIJ,QAAiBD,cAAa,KAC5B,MAAMhnB,EAAO0C,EAAAA,GAAWC,QAAQnD,MAC1B0nB,EAAcxkB,EAAAA,GAAWykB,WAAWnnB,EAAMonB,IAEhD,GAAIA,GAAcF,GAAc,CAC9B,MAAMhd,EAAUgd,EAAYxnB,MAAMwK,QAAQ7S,KAAKgwB,GAAO,SACjDA,GAAAA,CACHzf,MAAOpI,KAAKE,MAAMkI,UAGhBiV,KAAKC,UAAU5S,KAAa2S,KAAKC,UAAUtd,KAAKE,MAAMwK,UACxD1K,KAAKI,SAAS,CAAEsK,WAEpB,KAGF,QAAiB+c,cAAa,KAC5B,MAAM,QAAE/c,EAAO,MAAEod,EAAK,WAAEC,EAAU,KAAE9mB,EAAO,gBAAmBjB,KAAKE,MAC7DwW,EAAYxT,EAAAA,GAAW6G,aAAa/J,MAE1C,IAAK0W,IAAchM,IAAYod,EAC7B,OAEF,MAAME,EAAM,CACV/N,OAAQ,iBACRhZ,OACAyJ,UACAgM,UAAW,MAAKA,EAAUxW,MAAMT,OAChCiW,WAAY,CAAEuS,IAAKH,GACnBjT,IAAK/a,OAAOouB,SAAShT,KACrBld,GAAI,GAAGqlB,KAAKC,UAAU5S,KACtB5L,MAAO,GAAGipB,IACVI,S,yEAEE9K,KAAKC,UAAU0K,KAAS3K,KAAKC,UAAUtd,KAAKE,MAAMkW,UACpDpW,KAAKI,SAAS,CAAEgW,QAAS4R,GAC3B,IAhDAhoB,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAmDF,SAAS4nB,GAAczvB,GACrB,OAAOA,aAAamS,EAAAA,EACtB,C,ybClEO,MAAM8d,GAAiC,uBACxCC,GAAmB,6CACnBC,GAAyC,yBACzCC,GAAuC,iBAStC,MAAMC,WAAkBrrB,EAAAA,GA8C7BsrB,OAAAA,CAAQC,GACF1oB,KAAKE,MAAMC,MACbH,KAAKE,MAAMC,KAAKsoB,QAAQC,EAE5B,CAEAC,QAAAA,CAASC,GACH5oB,KAAKE,MAAMC,MACbH,KAAKE,MAAMC,KAAKwoB,SAASC,EAE7B,CAvDAhe,WAAAA,CAAY1K,GACV2C,MAAM3C,GACNF,KAAKqD,sBAAqB,KACxB,MAAMulB,EAAyB,CAC7B,CACEtd,KAAM,aACNrK,KAAM,SAER,CACEqK,KAAM,UACNud,cAAe,UACf3T,KAAM4T,GAAe9oB,MACrB+O,QAAS,IAAMga,OAInB/oB,KAAKI,SAAS,CACZD,KAAM,IAAI6oB,EAAAA,GAAa,CACrBJ,YAIJ,MACMd,EAAQ5K,GADWC,GAAyBnd,OAG5CipB,EAA2B,IAAI1B,GAAyB,CAC5Dnf,MAAOpI,KAAKE,MAAMkI,MAClB0f,U,QAGFmB,EAAyBC,WACzBlpB,KAAKI,SAAS,CAAE6oB,6BAChBjpB,KAAKsD,MAAMC,IACT0lB,aAAAA,EAAAA,EAA0BzlB,kBAAiB,M,SAoFN2lB,GAA9BC,GAAAA,MAAAA,KAAAA,U,CAnFLA,CAA8BppB,KAAK,KAIvCipB,EAAyB7oB,U,yUAAS,IAC7B6oB,EAAyB/oB,O,WAAK,CACjC6nB,WAAY/nB,KAAKE,MAAM6nB,a,kVAG7B,EAcA,GA1DWS,GA0DGnrB,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,GAAS/C,EAAM7D,WAEvB,OAAI4G,EACK,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,IAGzB,wCAIX,MAAM2oB,GAAkB1rB,IACtB,MACMsY,EAAawH,GADMC,GAAyB/f,IAE5CsZ,EAAYxT,EAAAA,GAAW6G,aAAa3M,GAAO8C,MAAMT,MACjD+K,EfyFD,SAAwBZ,G,IAETpJ,EADpB,MAAMA,EAAO0C,EAAAA,GAAWC,QAAQyG,GAAO1J,MAAMM,KACvC6oB,EAAc7oB,SAAa,QAAbA,EAAAA,EAAM8oB,eAAN9oB,IAAAA,OAAAA,EAAAA,EAAe+oB,QAAQ,GAC3C,OAAOF,EAAc,EAAgC7e,UAAO+G,CAC9D,Ce7FeiY,CAAepsB,GAEtBggB,EAAeC,KAAKC,UAAU,CAClC,iBAAoB,CAClBC,OAAOC,EAAAA,EAAAA,YAAW9G,EAAUD,KAC5B/L,QAAS,CAAC,CAAEvC,MAAO,IAAKuN,aAAYtN,MAAOhL,EAAM8C,MAAMkI,MAAOoC,Y,IAGnDmK,EAAf,MAAMgJ,EAAyB,QAAhBhJ,EAAAA,EAAAA,OAAOiJ,iBAAPjJ,IAAAA,EAAAA,EAAoB,GAEnC,OADmB+F,EAAAA,QAAQC,UAAU,GAAGgD,YAAkB,CAAEE,MAAOT,EAAcU,cAAe,GAC/E,EAGbiL,GAAiB,KACrBvd,GAAqBO,GAAkBC,eAAgBG,GAAoBH,eAAea,wBAAwB,EAGvG4c,GAAAA,W,MAAuB,cAAOC,GACzC,MAAMtT,EAAUsT,EAAoBxpB,MAAMkW,QAG1C,YAAgC7E,IAA5BoY,EAAAA,yBACYA,EAAAA,EAAAA,yBAAwB,CACpCtB,oBACAjS,YAGWwT,WAAW,QAIOrY,IAA7BsY,EAAAA,gCACyCC,EAAAA,GAAAA,iBACzCD,EAAAA,EAAAA,0BAAyB,CACvBxB,oBACAjS,cAIS,QARf,CAYF,I,gBA1B2CsT,G,gCAA9BD,G,SA4BEL,K,OAAAA,GAAf,cAA6CD,GAC3C,MAAMF,EAA2BE,EAAKjpB,MAAM+oB,yBAC5C,GAAIA,EAA0B,C,IAEFE,EAD1B,MAAM/K,QAAaqL,GAAqBR,G,IACdE,EAA1B,MAAMY,EAAgD,QAA5BZ,EAAe,QAAfA,EAAAA,EAAKjpB,MAAMC,YAAXgpB,IAAAA,OAAAA,EAAAA,EAAiBjpB,MAAM0oB,aAAvBO,IAAAA,EAAAA,EAAgC,GACpDa,EAAiCD,EAAkBhpB,MACtD2nB,GAASA,EAAKpd,OAAS8c,K,IA6BpBe,EAxBFA,EAIAA,EAIAA,EAVA/K,IACG4L,EAwBCA,IACa,QAAfb,EAAAA,EAAKjpB,MAAMC,YAAXgpB,IAAAA,GAAAA,EAAiBR,SACfoB,EAAkBxnB,QACfmmB,IAK2B,IAJ1B,CACEJ,GACAC,GACAH,IACA/M,SAASqN,EAAKpd,WA/BT,QAAf6d,EAAAA,EAAKjpB,MAAMC,YAAXgpB,IAAAA,GAAAA,EAAiBV,QAAQ,CACvBnd,KAAMgd,GACNrnB,KAAM,YAEO,QAAfkoB,EAAAA,EAAKjpB,MAAMC,YAAXgpB,IAAAA,GAAAA,EAAiBV,QAAQ,CACvBnd,KAAMid,GACNtnB,KAAM,UAEO,QAAfkoB,EAAAA,EAAKjpB,MAAMC,YAAXgpB,IAAAA,GAAAA,EAAiBV,QAAQ,CACvBnd,KAAM8c,GACNS,cAAe,cACf9Z,QAAU3W,IACJgmB,EAAKrP,SACPqP,EAAKrP,QAAQ3W,GAGfoT,GACEO,GAAkBC,eAClBG,GAAoBH,eAAec,6BAA4B,KAmB3E,CACF,KAjDesc,MAAAA,KAAAA,U,ozBC3HR,SAASa,GACdrgB,EACAoF,EACA8C,GAEA,MACMvK,EADmB4V,GAAyBvT,GAClB+J,oBAAoBpD,WAC9CnI,EAAQH,EAAmBV,EAAQyH,EAAS3M,gBAC5CuP,EAA2C,CAAC,EAElD,OAAO,IAAImV,GAAe,CACxBmD,WAAY,CChCN/T,IACN,MAAMgU,EAAS,IAAIC,IAEbC,EAAWlU,EAASmU,iBAAiBvmB,EAAAA,IAA8BpK,IACvE,MAAMmG,EAASnG,EAAM4wB,QAAQzqB,OAE7BA,SAAAA,EAAQmC,SAASuX,IACfA,EAAE1Y,OAAOkB,MAAM,GAAGC,SAASjB,IACzBmpB,EAAOK,IAAIhR,EAAErR,MAAiBO,KAAKgX,OAAO1e,EAAEe,OAAOQ,QAAQG,GAAMA,KAAI,GACrE,IAYV,SAA8ByT,EAAuBuJ,GAEnD,MAAMpZ,EAAapD,EAAAA,GAAWunB,eAAetU,GAAWhe,GAAMA,aAAauyB,EAAAA,KAE3E,IAAK,MAAM/J,KAAKra,EACdqa,EAAEgK,wBAEFhK,EAAEvgB,SAAS,CACTwqB,aAAaC,EAAAA,EAAAA,QAAMC,EAAAA,EAAAA,WAAUnK,EAAEzgB,MAAM0qB,aAAc,CAAEG,SAAU,CAAErL,UAGvE,CApBMsL,CAAqB7U,EAAUzN,KAAKgX,OAAOyK,EAAOpoB,UAAU,IAG9D,MAAO,KACLsoB,EAASY,aAAa,CACvB,GDgBD/Y,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAI7H,EAAgB,CACzBI,cAAe,GACfiL,WAAYC,EAAAA,GACZjL,QAAS,CAACtC,KAEZwN,gBAAiB,IACZzB,GAAyB0B,GAAajM,IACzC,IAAO4K,GACEA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,IACHA,EAAKyB,SAASR,IAAMypB,EAAAA,EAAAA,aAAY,CAAEppB,MAAOL,EAAEX,OAAO,GAAIqqB,SAAU,CAACC,EAAAA,UAAU1L,SACpElf,EAAKgB,MAAK,CAACC,EAAGC,K,IACXA,EAAAA,EAAuCD,EAAAA,EAA/C,QAAyB,QAAjBC,EAAAA,EAAEZ,OAAO,GAAGZ,aAAZwB,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB2pB,aAAnB3pB,IAAAA,OAAAA,EAAAA,EAA0Bge,MAAO,KAAuB,QAAjBje,EAAAA,EAAEX,OAAO,GAAGZ,aAAZuB,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB4pB,aAAnB5pB,IAAAA,OAAAA,EAAAA,EAA0Bie,MAAO,EAAE,WAO9FpG,QAAS,CACP,CAAE7Z,MAAO,SAAUnB,MAAO,UAC1B,CAAEmB,MAAO,OAAQnB,MAAO,QACxB,CAAEmB,MAAO,OAAQnB,MAAO,SAE1B2oB,OAAQ,OACRG,QAAS,CACP,IAAItQ,EAAAA,GAAgB,CAClBza,UAAW,SACXgE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB4hB,UAAW,IACX/hB,MAAkB,aAAXoH,EAAwB+f,KAAmBgE,QAAQ,KAAOhE,MAAoB5U,aAI3F,IAAI/S,EAAgB,CAClBQ,KAAM,IAAI6D,EAAAA,GAAmB,CAC3BunB,gBAAiBlmB,EAAAA,GACjBmmB,SAAU,QACVC,QAAQ,EACRprB,SAAU,KAEZI,SAAS,EACTmC,eAAgBA,GAAegP,EAAQxC,GAAeJ,EAAUzH,EAAQuK,KAE1E,IAAInS,EAAgB,CAClBQ,KAAM,IAAI6D,EAAAA,GAAmB,CAC3BunB,gBAAiB,MACjBC,SAAU,QACVC,QAAQ,EACRprB,SAAU,KAEZI,SAAS,EACTmC,eAAgBA,GAAegP,EAAQxC,GAAeJ,EAAUzH,EAAQuK,OAIhF,CAEO,SAASlP,GACdgP,EACAC,EACA7C,EACAzH,EACAuK,GAEA,MAAO,CAACtR,EAAiBqB,K,IAMNrB,EALjB,MAAMuR,EAAmBlQ,EAAMhF,KAAO+U,EAAO/P,EAAMhF,WAAQ0U,EAErDS,EAAW,IAAIC,EAAAA,GAAc,CACjCzR,KAAM,SACDA,GAAAA,CACH4X,YAA6B,QAAhB5X,EAAAA,EAAK4X,mBAAL5X,IAAAA,OAAAA,EAAAA,EAAkB+B,QAAQd,GAAMA,EAAE0G,QAAUtG,EAAMsG,QAC/DrI,OAAQ,CACN,SACK+B,GAAAA,CACHf,OAAQe,EAAMf,OAAOU,MAAK,CAACC,EAAGC,K,IAAsCA,EAAhCD,EAAAA,E,OAAQ,QAARA,EAAAA,EAAEL,cAAFK,IAAAA,GAAgB,QAAhBA,EAAAA,EAAUiqB,cAAVjqB,IAAAA,OAAAA,EAAAA,EAAkBE,eAAsB,QAARD,EAAAA,EAAEN,cAAFM,IAAAA,OAAAA,EAAAA,EAAUgqB,SAAU,MAAO,CAAC,W,IAOtG3Z,EADF,GAAIA,EAEF,OAD2B,QAA3BA,EAAAA,EAAiB7R,MAAMC,YAAvB4R,IAAAA,GAAAA,EAA6B3R,SAAS,CAAE8R,MAAOF,IACxCD,EAGT,MAAM3J,EAAQlF,EAAAA,GAAWyoB,YACvB3c,EACA1H,EAAqB,CACnBC,SACAE,aAAc,GAAGuH,EAAS3M,kBAAkBupB,GAAiBxc,GAAcvN,MAC3E6F,eAAe,KAIb2J,GAAoB,aAAX9J,EAAwB+f,KAAmBgE,QAAQ,KAAOllB,KACtEgM,SAASP,EAAShQ,EAAOmN,EAAS3M,iBAClCwpB,QAAQ,IAAIrD,GAAU,CAAEpgB,QAAO2f,WAAY3Y,GAAcvN,MACzDwQ,QAAQL,GAELM,EAAUR,EAAUjQ,GACtByQ,GACFjB,EAAMkB,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCtS,KAAMkR,EAAMqB,UAMd,OAJI7Q,EAAMhF,OACR+U,EAAO/P,EAAMhF,MAAQ2V,GAGhBA,CAAQ,CAEnB,CE1IO,SAASsZ,IAAsB,YAAEjd,EAAW,KAAEkd,IACnD,MACM7xB,GAgBWlB,GAjBHC,EAAAA,EAAAA,aAkBP,CACL+yB,UAAU5wB,EAAAA,EAAAA,KAAI,CACZI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,GACnBX,WAAY,SACZN,QAAS,GAAGhD,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,SAElDgvB,UAAU7wB,EAAAA,EAAAA,KAAI,CACZI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,GACnBX,WAAY,WAEdwqB,KAAK1rB,EAAAA,EAAAA,KAAI,CACPI,QAAS,eACT3C,MAAOG,EAAMiE,QAAQ,GACrBnE,OAAQE,EAAMiE,QAAQ,IACtBc,aAAc/E,EAAMiE,QAAQ,QAjBlC,IAAmBjE,EAdjB,OACE,kBAACiD,MAAAA,CAAIzB,UAAWN,EAAO8xB,UACrB,kBAAC/vB,MAAAA,CAAIzB,UAAWN,EAAO+xB,UAAWpd,GACjCkd,EAAKhsB,OAAS,GACbgsB,EAAKl0B,KAAKivB,GACR,kBAAC7qB,MAAAA,CAAIzB,UAAWN,EAAO+xB,SAAUprB,IAAKimB,EAAIxoB,OACxC,kBAACrC,MAAAA,CAAIzB,UAAWN,EAAO4sB,IAAKoF,MAAO,CAAEzmB,gBAAiBqhB,EAAI9V,SAC1D,kBAAC/U,MAAAA,KAAK6qB,EAAIxoB,UAKtB,C,yHCWO,MAAM6tB,WAAiChvB,EAAAA,GAcpCkT,WAAAA,GACN,MAAMrB,EAAW5M,GAAmBpC,MAEpCgP,EAASxL,kBAAiB,KACxBxD,KAAKosB,QAAQpd,EAAS,IAGxBuE,GAAuBvT,MAAMwD,kBAAiB,KAC5CxD,KAAKosB,QAAQpd,EAAS,IAGxBhP,KAAKosB,QAAQpd,EACf,CAEQqd,gCAAAA,GACN,MAAMrd,EAAW5M,GAAmBpC,MACpCgP,EAAS4J,cAAc0T,EAAAA,GAAwB,IAC/CtsB,KAAKosB,QAAQpd,EACf,CAEQud,mBAAAA,CAAoBhC,GAC1B/e,GACEO,GAAkBC,eAClBG,GAAoBH,eAAeM,iCACnCie,EAEJ,CAlCA3f,WAAAA,CAAY1K,GACV2C,M,uUAAM,IACD3C,IAPP,QAAUssB,sBAAsB,IAAIC,EAAAA,GAAyBzsB,KAAM,CACjE0sB,cAAe,CAACC,EAAAA,GAAaC,EAAAA,IAC7BP,iCAAkCrsB,KAAKqsB,iCAAiCxhB,KAAK7K,SAuC/E,QAAQosB,WAAWpd,IACjBhP,KAAKI,SAAS,CACZD,KAAM8pB,GAAkBjqB,KAAMgP,GAAWnN,GAAqB,CAC5D,IAAIiN,GAAmB,CAAEjN,QAAOqN,SAAUF,EAAS3M,eAAgB0M,QAAS/O,KAAKusB,0BAEnF,IAGJ,QAAO7sB,YAAW,CAACD,EAAeotB,KAChC,MAAM7d,EAAW5M,GAAmBpC,MAChCgP,EAAS3M,iBAAmB5C,IAC9BuP,EAAS4J,cAAcnZ,OAAO8R,GAAYsb,GAE1CrhB,GACEO,GAAkBC,eAClBG,GAAoBH,eAAeK,2BACnC,CACE5L,QAAShB,IAGf,IAnDAO,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAqIF,SAAS5F,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbiJ,SAAU,EACV7I,QAAS,OACT0mB,UAAW,OACXnlB,cAAe,WAEjBmO,SAAS9P,EAAAA,EAAAA,KAAI,CACXiJ,SAAU,EACV7I,QAAS,OACTsxB,WAAY9zB,EAAMiE,QAAQ,KAE5B8vB,UAAU3xB,EAAAA,EAAAA,KAAI,CACZiJ,SAAU,EACV7I,QAAS,OACTc,WAAY,MACZC,IAAKvD,EAAMiE,QAAQ,KAErB+vB,eAAe5xB,EAAAA,EAAAA,KAAI,CACjBiJ,SAAU,EACV7I,QAAS,OACTsB,eAAgB,aAElBmwB,OAAO7xB,EAAAA,EAAAA,KAAI,CACT8xB,YAAal0B,EAAMiE,QAAQ,KAE7BwD,SAASrF,EAAAA,EAAAA,KAAI,CACXvC,MAAO,SAETs0B,cAAc/xB,EAAAA,EAAAA,KAAI,CAChBI,QAAS,OACTsB,eAAgB,YAChBswB,aAAc,OACdv0B,MAAO,OACPkE,cAAe,QAGrB,C,yHAtHE,GAjEWovB,GAiEG9uB,aAAY,EAAGD,YAC3B,MAAMqD,EAAU2B,GAAmBhF,GAAOiF,eACpCgrB,EAAe5sB,EAAQ4a,SAASmK,EAAAA,KAAc8H,EAAAA,GAAoBjS,SAAS5a,GAAW8sB,EAAAA,GAAOC,EAAAA,IAC5FP,EAAOQ,IAAYl0B,EAAAA,EAAAA,UAAS8zB,IAC7B,KAAEltB,GAAS/C,EAAM7D,WACjBW,GAASC,EAAAA,EAAAA,YAAWC,KAEpB,WAAE8jB,GAAe3K,GAAuBnW,GAAO7D,WAC/Cm0B,EAAaT,IAAUO,EAAAA,GAAW/H,EAAAA,GAAgBD,EAAAA,GACxD,IAAImI,EAAqBzP,aAAAA,EAAAA,EAAY3b,QAAQqrB,GAASA,EAAKvS,SAASqS,KAChET,IAAUM,EAAAA,KACZI,EAAqBA,aAAAA,EAAAA,EAAoBE,OAAOP,EAAAA,KAGlD,MAAMtT,EAAcmD,GAAyB/f,IACrCqC,MAAO8H,GAAWyS,EAAYrG,oBAAoBpa,WAapDsV,EAZiB,CAACtH,IACtB,OAAQA,GACN,IAAK,OACH,MAAO,+DACT,IAAK,SACH,MAAO,6DACT,IAAK,WACH,MAAO,oDACT,QACE,MAAM,IAAIlP,MAAM,wBACpB,EAEkBy1B,CAAevmB,GAEnC,OACE,kBAACtL,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAAC4vB,GAAqBA,CACpBjd,YAAaA,EACbkd,KACa,aAAXxkB,EACI,GACA,CACE,CAAEjJ,MAAO,OAAQ0S,MAAO,SACxB,CAAE1S,MAAO,QAAS0S,MAAO,UAKnC,kBAAC/U,MAAAA,CAAIzB,UAAWN,EAAO6yB,WACpBY,aAAAA,EAAAA,EAAoB5tB,SACnB,kBAAC9D,MAAAA,CAAIzB,UAAWN,EAAOizB,cACrB,kBAAClxB,MAAAA,CAAIzB,UAAWN,EAAO+yB,OACrB,kBAAC7tB,EAAAA,MAAKA,CAACd,MAAM,SACX,kBAACgoB,EAAAA,iBAAgBA,CACfhN,QAASyU,GAAuB,CAACP,EAAAA,GAAUD,EAAAA,KAC3C9tB,MAAOwtB,EACPvtB,SAAU+tB,MAKhB,kBAACxxB,MAAAA,CAAIzB,UAAWN,EAAOuG,SACrB,kBAAC2jB,GAAeA,CACd9K,QAASyU,GAAuBJ,GAChCtJ,gBAAiB4I,IAAUO,EAAAA,GAAWlB,EAAAA,GAA0BgB,EAAAA,GAChE7tB,MAAOgB,EACPf,SAAUtC,EAAMsC,SAChBtC,MAAOA,MAKd+C,aAAgB4mB,IACf,kBAAC9qB,MAAAA,CAAIzB,UAAWN,EAAO8yB,eACrB,kBAAC7sB,EAAK6mB,SAAQ,CAAC5pB,MAAO+C,MAI5B,kBAAClE,MAAAA,CAAIzB,UAAWN,EAAOgR,SAAU/K,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,K,ICrKjE,MAAM6tB,WAAuB7wB,EAAAA,GAW1BkT,WAAAA,GACNrQ,KAAK8e,YACP,CAEQA,UAAAA,GACN9e,KAAKI,SAAS,CAAED,KAAM,IAAIgsB,GAAyB,CAAC,IACtD,CAZAvhB,WAAAA,CAAY1K,GACV2C,M,uUAAM,IAAK3C,IALb,QAAUssB,sBAAsB,IAAIC,EAAAA,GAAyBzsB,KAAM,CACjE0sB,cAAe,CAACE,EAAAA,OAMhB5sB,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAUA,GAnBWguB,GAmBG3wB,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,GAAS/C,EAAM7D,WACvB,OAAO4G,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,G,mBCpCnC,SAAS8tB,GAAgB7a,G,IAY1BA,EAGAA,EAUkBA,EACFA,EAzBpB,IAAI8a,EAAW,GAEf,IAAK9a,EACH,MAAO,KAGLA,EAAUhL,QACZ8lB,GAAY9a,EAAUhL,OAGxB,MAAMY,EAAW,IACK,QAAlBoK,EAAAA,EAAUpK,gBAAVoK,IAAAA,OAAAA,EAAAA,EAAoBpJ,KAAKjK,SAC3BiJ,EAAS3H,KAAK,eAAe+R,EAAUpK,SAASgB,SAE5B,QAAlBoJ,EAAAA,EAAUpK,gBAAVoK,IAAAA,OAAAA,EAAAA,EAAoBlJ,GAAGnK,SACzBiJ,EAAS3H,KAAK,eAAe+R,EAAUpK,SAASkB,MAE9ClB,EAASjJ,SACPmuB,EAASnuB,SACXmuB,GAAY,QAEdA,GAAYllB,EAAShB,KAAK,SAG5B,MAAMmmB,EAAmC,QAAnB/a,EAAAA,EAAUsD,iBAAVtD,IAAAA,OAAAA,EAAAA,EAAqBpJ,KACrCokB,EAAiC,QAAnBhb,EAAAA,EAAUsD,iBAAVtD,IAAAA,OAAAA,EAAAA,EAAqBlJ,GACzC,MAAO,IAAIgkB,SACTC,GAAiBC,EAAc,KAAqB,IAAhBD,MAA6C,IAAdC,IAA6B,IAEpG,C,mzBChBO,SAASC,GACdzkB,EACAoF,EACA8C,EACAvK,G,IAKwB/G,EAHxB,MAAMkW,EAAYxT,EAAAA,GAAW6G,aAAaH,GACpCpJ,EAAO0C,EAAAA,GAAWC,QAAQyG,GAC1B8G,EAAY1B,EAAS3M,eACrBisB,EAAiC,QAAf9tB,EAAAA,EAAKN,MAAMM,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBV,OAAOiB,MAAMwtB,GAAMA,EAAE1xB,OAAS6T,IACjE8d,EAA2B,GAC3BxS,EAAYsS,aAAAA,EAAAA,EAAiBxtB,OAAOC,MAAMC,GAAiB,UAAXA,EAAEnE,OAClDgT,EAAgBye,aAAAA,EAAAA,EAAiBxtB,OAAOC,MAAMC,GAAiB,aAAXA,EAAEnE,OACtDiT,EAAiBwe,aAAAA,EAAAA,EAAiBxtB,OAAOC,MAAMC,GAAiB,cAAXA,EAAEnE,OAI7D,GAAImf,GAAanM,GAAiBC,EAChC,IAAK,IAAIpL,EAAI,EAAGA,EAAIsX,EAAUja,OAAOhC,OAAQ2E,IACtCsX,EAAUja,OAAO2C,KAAQmL,EAAc9N,OAAO2C,IAAOoL,EAAe/N,OAAO2C,KAIhF8pB,EAAYntB,KAAK,CACfxE,KAAMmf,EAAUja,OAAO2C,GAAGmH,QAAQ,KAAM,IACxC9L,OAAQ,EACRe,OAAQ,CACN,CACEjE,KAAM,QACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ,CAAC,WAAY,cACrB4S,OAAQ,CAAC,GAEX,SACK9E,GAAAA,CACH9N,OAAQ,CAAC8N,EAAc9N,OAAO2C,IAC9BtD,OAAQ,CACN,CAACsP,GAAYsL,EAAUja,OAAO2C,IAEhCiQ,OAAQ,CACNja,YAAa,cAGjB,SACKoV,GAAAA,CACH/N,OAAQ,CAAC+N,EAAe/N,OAAO2C,SAOzC,OAAO,IAAI/E,EAAgB,CACzBuS,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAID,EAAAA,GAAc,CACvBzR,KAAM,CACJkW,UAAWA,EAAUxW,MAAMT,MAC3BS,MAAOuD,EAAAA,aAAaC,KACpB5D,OAAQ0uB,KAGZ5Y,gBAAiB,CACf,IAAOpB,GACEA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,IACHA,EAAKyB,SAASR,IAAMypB,EAAAA,EAAAA,aAAY,CAAEppB,MAAOL,EAAEX,OAAO,GAAIqqB,SAAU,CAACC,EAAAA,UAAU1L,SACpElf,EAAKgB,MAAK,CAACC,EAAGC,K,IACXA,EAAAA,EAAuCD,EAAAA,EAA/C,QAAyB,QAAjBC,EAAAA,EAAEZ,OAAO,GAAGZ,aAAZwB,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB2pB,aAAnB3pB,IAAAA,OAAAA,EAAAA,EAA0Bge,MAAO,KAAuB,QAAjBje,EAAAA,EAAEX,OAAO,GAAGZ,aAAZuB,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB4pB,aAAnB5pB,IAAAA,OAAAA,EAAAA,EAA0Bie,MAAO,EAAE,WAO9Fvf,KAAM,IAAI6D,EAAAA,GAAmB,CAC3BunB,gBAAiBlmB,EAAAA,GACjBmmB,SAAU,QACVC,QAAQ,EACRprB,SAAU,KAEZuC,eAAgBA,GAjE+B,CAAC,EAiET6rB,GAAU3c,EAAWvK,IAEhE,CAEA,MAAMknB,GAAY9c,GACTA,EAAG9U,MAAQ,oBAGpB,SAAS+F,GACPgP,EACAC,EACAC,EACAvK,GAEA,MAAO,CAAC/G,EAAiBqB,KACvB,MAAMkQ,EAAmBlQ,EAAMhF,KAAO+U,EAAO/P,EAAMhF,WAAQ0U,EAErDS,EAAW,IAAIC,EAAAA,GAAc,CACjCzR,KAAM,SACDA,GAAAA,CACHV,OAAQ,CACN,MACK+B,Q,IAOTkQ,EADF,GAAIA,EAEF,OAD2B,QAA3BA,EAAAA,EAAiB7R,MAAMC,YAAvB4R,IAAAA,GAAAA,EAA6B3R,SAAS,CAAE8R,MAAOF,IACxCD,EAGT,MAAMV,EAAQc,GAAe5K,GAAQ6K,SAASP,EAAShQ,IAAQwQ,QAAQL,GAEjEM,EAAUR,EAAUjQ,GACtByQ,GACFjB,EAAMkB,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCtS,KAAMkR,EAAMqB,UAMd,OAJI7Q,EAAMhF,OACR+U,EAAO/P,EAAMhF,MAAQ2V,GAGhBA,CAAQ,CAEnB,CCnIO,MAAMkc,WAA+BvxB,EAAAA,I,uOAC1C,CADWuxB,GACGrxB,aAAY,EAAGD,WACtBA,EAAM8C,MAAMwQ,UAKf,kBAACnB,EAAAA,OAAMA,CAAC7S,QAAQ,YAAYyO,KAAK,KAAKI,KAAK,QAAQwD,QAAS,IAAM3R,EAAM8C,MAAM6O,WAAW,WAJlF,OC6BN,MAAM4f,WAAkCxxB,EAAAA,GAcrCkT,WAAAA,GACN,MAAMrB,EAAW5M,GAAmBpC,MAEpCgP,EAAS4J,cAAc/Q,EAAAA,IAEvB7H,KAAK4uB,aAEL5f,EAASxL,kBAAiB,CAACW,EAAUC,KAC/BD,EAAS1E,QAAU2E,EAAU3E,OAC/BO,KAAKosB,QAAQpd,EACf,IAGF6f,GAAyB7uB,MAAMwD,kBAAiB,KAC9CxD,KAAK4uB,aACL5uB,KAAKosB,QAAQpd,EAAS,IAGxBuE,GAAuBvT,MAAMwD,kBAAiB,CAACW,EAAUC,MAClD4U,EAAAA,EAAAA,SAAQ7U,EAASiP,UAAWhP,EAAUgP,aACzCpT,KAAK4uB,aACL5uB,KAAKosB,QAAQpd,GACf,IAGF9L,EAAAA,GAAW6G,aAAa/J,MAAMwD,kBAAiB,KAC7CxD,KAAK4uB,YAAY,IAGnB5uB,KAAKosB,QAAQpd,EACf,CAMQ4f,UAAAA,GACN,MAAMtb,EAAiBC,GAAuBvT,MACxC8J,EAAiB5G,EAAAA,GAAW6G,aAAa/J,MACzCgK,EAAOF,EAAe5J,MAAMT,MAAMuK,KAAKC,OACvCC,EAAKJ,EAAe5J,MAAMT,MAAMyK,GAAGD,OACnC6kB,EAAgBD,GAAyB7uB,MAAME,MAAMT,MACrDkuB,EAAqB3tB,KAAK+uB,sBAAsBD,GAEtD9uB,KAAKI,SAAS,CACZ8R,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BoL,WAAYC,EAAAA,GACZjL,QAAS,CAACwY,GAAWlZ,EAAME,EAAI+jB,GAAgB3a,EAAepT,MAAMkT,eAEtEwC,gBAAiB,CACf,IAAOpB,GACEA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,IACH,MAAMwuB,EAAgBC,GAA0BzuB,GAChD,OAAOhI,OAAO02B,QAAQF,GACnBzsB,QAAO,EAAEmO,EAAWjM,MAAQkpB,EAAmBtS,SAAS3K,KACxD7Y,KAAI,EAAE6Y,EAAWnP,KAAY4tB,GAAsBze,EAAWnP,KAC9DC,MAAK,CAACC,EAAGC,KACR,MAAM0tB,EAAWxf,GAAyBnO,GACpC4tB,EAAWzf,GAAyBlO,GAC1C,OAAOgH,KAAKwH,IAAImf,EAAStf,eAAiBrH,KAAKwH,IAAIkf,EAASrf,cAAc,GAC1E,SAOlB,CAEQsc,gCAAAA,GACN,MAAMrd,EAAW5M,GAAmBpC,MACpCgP,EAAS4J,cAAc/Q,EAAAA,IACvB7H,KAAKosB,QAAQpd,EACf,CAEQud,mBAAAA,CAAoBhC,GAC1B/e,GACEO,GAAkBC,eAClBG,GAAoBH,eAAeO,kCACnCge,EAEJ,CA3FA3f,WAAAA,CAAY1K,GACV2C,M,uUAAM,IACD3C,IAPP,QAAUssB,sBAAsB,IAAIC,EAAAA,GAAyBzsB,KAAM,CACjE0sB,cAAe,CAACC,EAAAA,GAAa2C,EAAAA,IAC7BjD,iCAAkCrsB,KAAKqsB,iCAAiCxhB,KAAK7K,SA2C/E,QAAQ+uB,yBAAyBD,GACN,sBAAlBA,EAAwC,CAAC,WAAY,mBAAqB,KAoDnF,QAAQ1C,WAAWpd,IACjB,MAAMugB,EAAmBpS,GAAyBnd,M3BvI/C,IACL8R,EACAvK,E2BsIEvH,KAAKI,SAAS,CACZD,KACE6O,EAASwgB,eAAiBxgB,EAASuB,aAAe1I,EAAAA,I3BzIxDiK,E2B2IajQ,GACC,IAAI6sB,GAAuB,CACzBhe,UAAW7O,EAAMhF,KACjBkS,QAAS,IAAM/O,KAAKN,SAASmC,EAAMhF,MAAQ,M3B7I3D0K,E2B+IYgoB,EAAiBE,oB3B3ItB,IAAI9vB,EAAgB,CACzBQ,KAAM,IAAI6D,EAAAA,GAAmB,CAC3BunB,gBAAiBlmB,EAAAA,GACjBmmB,SAAU,QACVnrB,SAAU,KAEZuC,eAAgBA,GAR+B,CAAC,EAQT8O,GAAcI,EAAWvK,M2BuIxD8mB,GACEruB,KACAgP,GACCnN,GAAqB,CACpB,IAAIiN,GAAmB,CACrBjN,QACAqN,SAAUF,EAAS3M,eACnB0M,QAAS/O,KAAKusB,wBAGlBgD,EAAiBE,sBAEzB,IAGJ,QAAO/vB,YAAW,CAACD,EAAeotB,KACfzqB,GAAmBpC,MAC3B4Y,cAAcnZ,OAAO8R,GAAYsb,GAE1CrhB,GACEO,GAAkBC,eAClBG,GAAoBH,eAAeQ,uCACnC,CAAE/M,SAAM,IA3HVO,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAoLK,SAASkjB,GAAWlZ,EAAcE,EAAYwlB,GACnD,MACMC,EAAY,IADN3mB,EAAAA,GAAAA,UAASkB,EAAKF,EAAM,KACPI,eACzB,MAAO,CACLjC,MAAO,IACPC,MAAO,IAAIR,EAAAA,iBAA+B8nB,KAC1CllB,KAAMmlB,EACNtnB,UAAW,UACXC,UAAW,QACXC,MAAO,IACPC,KAAM,GACNb,QAAS,GAEb,CAnEE,GA1IWgnB,GA0IGtxB,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,GAAS/C,EAAM7D,WACjByV,EAAW5M,GAAmBhF,GAC9BmyB,EAAmBpS,GAAyB/f,IAC5C,WAAE8gB,GAAe3K,GAAuBnW,GAAO7D,WAC/CW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAAC4vB,GAAqBA,CACpBjd,YAAY,qGACZkd,KAAM,CACJ,CACEztB,MAAO,WACP0S,MAC2C,aAAzCue,EAAiBE,oBACbje,IACAoe,EAAAA,EAAAA,YAAWC,cAAcC,eAAe,oBAEhD,CACExxB,MAAO,YACP0S,MAC2C,aAAzCue,EAAiBE,oBACbhe,IACAme,EAAAA,EAAAA,YAAWC,cAAcC,eAAe,qBAKpD,kBAAC7zB,MAAAA,CAAIzB,UAAWN,EAAO6yB,WACpB7O,aAAAA,EAAAA,EAAYne,SACX,kBAAC9D,MAAAA,CAAIzB,UAAWN,EAAOizB,cACrB,kBAAC/I,GAAeA,CACd9K,QAASyU,GAAuB7P,GAChCmG,gBAAiBiJ,EAAAA,GACjB7tB,MAAOuP,EAAS3M,eAChB3C,SAAUtC,EAAMsC,SAChB4kB,SAAS,EACTlnB,MAAOA,KAIZ+C,aAAgB4mB,IACf,kBAAC9qB,MAAAA,CAAIzB,UAAWN,EAAO8yB,eACrB,kBAAC7sB,EAAK6mB,SAAQ,CAAC5pB,MAAO+C,MAI5B,kBAAClE,MAAAA,CAAIzB,UAAWN,EAAOgR,SAAU/K,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,K,IAqBxE,MAAM8uB,GAA6B1tB,GAC1BA,EAAOZ,QAAO,CAACC,EAAkCd,KACtD,MAAMiwB,EAAcjwB,EAAOgB,OAAOC,MAAMe,GAAyB,WAAfA,EAAMb,OAClD+uB,EAAiBx3B,OAAOD,MAAKw3B,aAAAA,EAAAA,EAAa3uB,SAAU,CAAC,GAAGL,MAAMF,IAASA,EAAI0a,WAAW,QAI5F,OAHIyU,IACFpvB,EAAIovB,GAAkB,IAAKpvB,EAAIovB,IAAmB,GAAKlwB,IAElDc,CAAG,GACT,CAAC,GAGAuuB,GAAwB,CAACze,EAAmBnP,KAChD,MAAM0uB,EAAsB,CAC1BpzB,KAAM6T,EACNvI,MAAOuI,EACP5P,OAAQ,GACRf,OAAQ,GAGJmwB,EAAwB,CAC5BrzB,KAAM,QACNoE,KAAMC,EAAAA,UAAUwhB,OAChB3gB,OAAQ,GACR4S,OAAQ,CAAC,EACTvT,OAAQ,CAAE,CAACsP,GAAYA,IAEnBb,EAAuB,CAC3BhT,KAAM,WACNoE,KAAMC,EAAAA,UAAUC,OAChBY,OAAQ,GACR4S,OAAQ,CAAC,GAEL7E,EAAwB,CAC5BjT,KAAM,YACNoE,KAAMC,EAAAA,UAAUC,OAChBY,OAAQ,GACR4S,OAAQ,CAAC,GAGL5S,EAASR,EAAOZ,QAAO,CAACC,EAA8BiB,K,IAE9CkuB,EADZ,MAAMA,EAAcluB,EAAMf,OAAOC,MAAMe,GAAyB,WAAfA,EAAMb,OACjDkvB,EAAMJ,SAAmB,QAAnBA,EAAAA,EAAa3uB,cAAb2uB,IAAAA,OAAAA,EAAAA,EAAsBrf,GAIlC,OAHIyf,IACFvvB,EAAIuvB,GAAO,IAAKvvB,EAAIuvB,IAAQ,GAAKJ,IAE5BnvB,CAAG,GACT,CAAC,GAEEwvB,EAAgBC,GAAoB9uB,EAAQ,WAAYQ,GACxDuuB,EAAiBD,GAAoB9uB,EAAQ,YAAaQ,GAchE,OAZAkuB,EAASlwB,OAASvH,OAAOD,KAAKwJ,GAAQhC,OAEtCvH,OAAO02B,QAAQntB,GAAQE,SAAQ,EAAExC,EAAOqB,M,IAGpCA,EAGAA,EALFovB,EAAenuB,OAAOV,KAAK5B,GAC3BoQ,EAAc9N,OAAOV,M,QACnBP,EAAAA,EAAOC,MAAMe,I,IAAUA,E,MAAkC,gBAAtB,QAAZA,EAAAA,EAAMV,cAANU,IAAAA,OAAAA,EAAAA,EAA4B,YAAkB,WAArEhB,IAAAA,OAAAA,EAAAA,EAAwEiB,OAAO,IAAKquB,GAEtFtgB,EAAe/N,OAAOV,M,QACpBP,EAAAA,EAAOC,MAAMe,I,IAAUA,E,MAAkC,iBAAtB,QAAZA,EAAAA,EAAMV,cAANU,IAAAA,OAAAA,EAAAA,EAA4B,YAAmB,WAAtEhB,IAAAA,OAAAA,EAAAA,EAAyEiB,OAAO,IAAKuuB,EAAAA,IAGzFL,EAASnvB,OAAS,CAACovB,EAAgBrgB,EAAeC,GAC3CmgB,CAAQ,EAGjB,SAASI,GAAoB9uB,EAAqBgvB,EAAkBxuB,GAElE,MAAMyuB,EAAkBh4B,OAAOuJ,OAAOA,GAAQpB,QAAO,CAAC8vB,EAAO3vB,KAC3D,MAAMgB,EAAQhB,EAAOC,MAAMe,I,IAAUA,E,OAAY,QAAZA,EAAAA,EAAMV,cAANU,IAAAA,OAAAA,EAAAA,EAA4B,eAAM,IAAIyuB,IAAW,IACtF,OAAOE,IAAS3uB,aAAAA,EAAAA,EAAOC,OAAO,KAAM,EAAE,GACrC,GAEH,IAAI0uB,EAAQlvB,EAAOZ,QAAO,CAAC+vB,EAAc7uB,K,IAEnCC,EADJ,MAAMA,EAAQD,EAAMf,OAAOC,MAAMC,GAAiB,WAAXA,EAAEC,OACzC,OAAIa,SAAa,QAAbA,EAAAA,EAAOV,cAAPU,IAAAA,OAAAA,EAAAA,EAA6B,eAAM,IAAIyuB,WAClCzuB,EAAMC,OAAO,GAEf2uB,CAAY,GAClB,GAOH,OAAID,EAAQD,GAME,IAAVC,GAAyB,IAAVA,EALU,IAApBD,EAAwB,EAAIA,EAS9BC,CACT,CAEA,SAASr2B,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbiJ,SAAU,EACV7I,QAAS,OACT0mB,UAAW,OACXnlB,cAAe,WAEjBmO,SAAS9P,EAAAA,EAAAA,KAAI,CACXiJ,SAAU,EACV7I,QAAS,OACTsxB,WAAY9zB,EAAMiE,QAAQ,KAE5B8vB,UAAU3xB,EAAAA,EAAAA,KAAI,CACZiJ,SAAU,EACV7I,QAAS,OACTc,WAAY,MACZC,IAAKvD,EAAMiE,QAAQ,KAErB+vB,eAAe5xB,EAAAA,EAAAA,KAAI,CACjBiJ,SAAU,EACV7I,QAAS,OACTsB,eAAgB,aAElBqwB,cAAc/xB,EAAAA,EAAAA,KAAI,CAChBI,QAAS,OACTsB,eAAgB,YAChBswB,aAAc,OACdv0B,MAAO,OACPkE,cAAe,WAGrB,C,yHC7WO,MAAM4zB,WAAwBxzB,EAAAA,GAW3BkT,WAAAA,GACN,MACM9I,EADYoM,GAAkB3T,MACXuQ,WAEnBqgB,EAAkBrd,GAAuBvT,MAC/C,IAAK4wB,EAAgB1wB,MAAMkT,UAAW,CACpC,MAAMA,EAAYjD,GAA6B5I,GAC3C6L,GACFwd,EAAgBxwB,SAAS,CAAEgT,aAE/B,CAEApT,KAAK8e,YACP,CAEQA,UAAAA,GACN9e,KAAKI,SAAS,CAAED,KAAM,IAAIwuB,GAA0B,CAAC,IACvD,CAvBA/jB,WAAAA,CAAY1K,GACV2C,M,uUAAM,IAAK3C,IALb,QAAUssB,sBAAsB,IAAIC,EAAAA,GAAyBzsB,KAAM,CACjE0sB,cAAe,CAACE,EAAAA,OAMhB5sB,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAqBA,GA9BW2wB,GA8BGtzB,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,GAAS/C,EAAM7D,WACvB,OAAO4G,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,G,IC/BnC,MAAM0wB,GAAiD,CAC5D,CAAEn2B,YAgDJ,SAA8B+J,GAC5B,MAAO,WACT,EAlDuChF,MAAO,YAAaqxB,SNqBpD,WACL,OAAO,IAAIxwB,EAAAA,GAAc,CACvBH,KAAM,IAAI6tB,GAAe,CAAC,IAE9B,GMxBE,CAAEtzB,YAAaipB,GAAsBlkB,MAAO,YAAaqxB,ShB+ZpD,SAA6BvpB,GAClC,OAAO,IAAIjH,EAAAA,GAAc,CACvBH,KAAM,IAAI8gB,GAAkB,CAAE1Z,YAElC,GgBlaE,CAAE7M,YAkDJ,SAA+B+J,GAC7B,MAAO,YACT,EApDwChF,MAAO,aAAcqxB,SDgCtD,WACL,OAAO,IAAIxwB,EAAAA,GAAc,CACvBH,KAAM,IAAIwwB,GAAgB,CAAC,IAE/B,GCnCE,CACEj2B,YA+DJ,SAA2B6M,GACzB,MAAkB,WAAXA,EAAsB,iBAA8B,aAAXA,EAAwB,cAAgB,QAC1F,EAhEI9H,MAAO,YACPqxB,SpB0BG,WACL,OAAO,IAAIxwB,EAAAA,GAAc,CACvBH,KAAM,IAAI0e,GAAW,CAAC,IAE1B,IoBxBO,MAAMkS,WAAqB5zB,EAAAA,IA4C3B,SAASwmB,GAAqBpc,GACnC,OAAQA,GACN,IAAK,OACH,MAAO,oBACT,IAAK,SACH,MAAO,oBACT,IAAK,WACH,MAAO,qBAEb,CAMA,SAASnN,GAAUpB,GACjB,MAAO,CACLsZ,SAASlX,EAAAA,EAAAA,KAAI,CACX,CAACpC,EAAMg4B,YAAYC,GAAGj4B,EAAMg4B,YAAYjvB,OAAOmvB,KAAM,CACnDtS,SAAU,WACVa,MAAO,EACP0R,IAAK,EACLC,OAAQ,KAIhB,C,uOArEE,CADWL,GACG1zB,aAAY,EAAGD,Y,IAOP2f,EAAAA,EAAAA,EANpB,MAAMsU,EAAc9d,GAAuBnW,GACrClD,GAASC,EAAAA,EAAAA,YAAWC,IACpB4f,EAAcmD,GAAyB/f,IACvC,WAAEqW,GAAe4d,EAAY93B,YAC3BkG,MAAO8H,GAAWyS,EAAYrG,oBAAoBpa,WAEpD+3B,EAA4B,QAAdvU,EADF7Z,EAAAA,GAAWC,QAAQ/F,GAAO7D,WACdiH,YAAVuc,IAAAA,GAAsB,QAAtBA,EAAAA,EAAgBjd,cAAhBid,IAAAA,GAA2B,QAA3BA,EAAAA,EAAyB,UAAzBA,IAAAA,OAAAA,EAAAA,EAA6Bhd,OAEjD,OACE,kBAACwxB,EAAAA,IAAGA,KACF,kBAACt1B,MAAAA,CAAIzB,UAAWN,EAAOoY,SACrB,kBAAClW,EAAAA,MAAKA,CAACG,IAAK,GACV,kBAACwd,GAAsBA,CAACC,YAAaA,MAIzC,kBAACwX,EAAAA,QAAOA,KACLX,GAAuBh5B,KAAI,CAAC45B,EAAKroB,IAE9B,kBAACsoB,EAAAA,IAAGA,CACF7wB,IAAKuI,EACL9K,MAAOmzB,EAAI/2B,YAAY6M,GACvB0f,OAAQxT,IAAege,EAAIhyB,MAC3BkyB,YAAa,IAAMN,EAAY3d,cAAc+d,EAAIhyB,OACjDmyB,QAAuB,cAAdH,EAAIhyB,MAAwB6xB,OAAc/f,OAGzD,IC3BH,MAAMsgB,WAAqB10B,EAAAA,GAoDxBkT,WAAAA,GACNrQ,KAAKI,SAAS,CACZ8R,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAI7H,EAAgB,CACzBI,cAAqC,aAAtBzK,KAAKE,MAAMqH,OAAwB,GAAK,GACvDmO,WAAYC,EAAAA,GACZjL,QAAS,CAAuB,aAAtB1K,KAAKE,MAAMqH,OAAwB2L,KAAwBjL,EAAmBjI,KAAKE,MAAMqH,WAErGqO,gBACwB,aAAtB5V,KAAKE,MAAMqH,OACP,IAAI+N,MACJ,IAAInB,GAAyB0B,GAAa7V,UAElDqR,MAAOrR,KAAK8V,YAAY9V,KAAKE,MAAMqH,SAEvC,CAEQuO,WAAAA,CAAYvO,GAClB,OAAO,IAAIuP,EAAAA,GAAgB,CACzBza,UAAW,MACXgE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAiB,aAAXoH,EAAwBvH,KAAK8xB,sBAAwB9xB,KAAK+xB,oBAAoBxqB,OAI5F,CAEQwqB,mBAAAA,CAAoBxqB,GAC1B,MAAM8J,EAAQjL,IAAkB6P,gBAAe,GAAMgB,eAAe,eAUpE,MATe,SAAX1P,EACF8J,EAAM5K,qBAAqB,YAAa,UACpB,WAAXc,GACT8J,EAAMe,SAAS,eAAe3L,qBAAqB,YAAa,WAAWyQ,SAAS,CAClF/P,WAAY,gBACZP,KAAM,UAIHyK,EAAMqB,OACf,CAEQof,mBAAAA,GACN,OAAOhkB,KACJsE,SAAS,yBACT6D,gBAAe,GACfgB,eAAe,eACfvE,OACL,CAnGA9H,WAAAA,CAAY1K,GACV2C,M,uUAAM,EACJkI,aAAa,GACV7K,IAGLF,KAAKqD,sBAAqB,KACxBrD,KAAKqQ,cACL,MAAM7P,EAAO0C,EAAAA,GAAWC,QAAQnD,MAEhCA,KAAKsD,MAAMC,IACT/C,EAAKgD,kBAAkBhD,I,IACQA,EAEzBA,EAkBOA,EApBXR,KAAKI,SAAS,CAAE2K,aAAsB,QAATvK,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaE,aAElD,QAATnD,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,KACJ,IAA5BlD,EAAKA,KAAKV,OAAOC,QAA+C,IAA/BS,EAAKA,KAAKV,OAAO,GAAGC,QAAgBmY,GAAoB1X,GAC3FR,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBH,KAAM,IAAIjD,EAAgB,CACxBnB,SAAU,aAOpBiE,KAAKI,SAAS,CACZiR,MAAOrR,KAAK8V,YAAY9V,KAAKE,MAAMqH,WAGrB,QAAT/G,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAasV,SAC3C/Y,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBza,UAAW,SACX21B,UAAWC,GACXn5B,OAAQm5B,GACR5xB,SAAU,CACR,IAAI/C,EAAkB,CACpBC,UAAW,IAAM2G,EAAkB,SAK7C,IACF,GAGN,EA2FF,SAAS9J,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACb82B,KAAM,EACNr5B,MAAO,OACP2C,QAAS,OACTuB,cAAe,SACf4I,OAAQ,aAAa3M,EAAM0E,OAAOiI,OAAOgU,OACzC5b,aAAc,MACdH,WAAY5E,EAAM0E,OAAOE,WAAW8H,QACpConB,WAAY,MAEZ,yBAA0B,CACxBlT,YAAa,eAGf,mBAAoB,CAClBpe,QAAS,UAGb22B,eAAe/2B,EAAAA,EAAAA,KAAI,CACjBI,QAAS,OACTc,WAAY,SACZsiB,SAAU,WACVuS,IAAK,MACL5R,KAAM,MACN6R,OAAQ,IAEVgB,WAAWh3B,EAAAA,EAAAA,KAAI,CACbijB,OAAQ,UACR2T,UAAWC,GAEX,iCAAoC,CAClCn5B,OAAQm5B,GACR9gB,SAAU,UAGZ,SAAU,CACRvT,WAAY5E,EAAM0E,OAAOE,WAAWC,UACpCw0B,MAAO,CACL5sB,gBAAiB,UACjBE,OAAQ,oBACR0Y,OAAQ,cAIdiU,aAAal3B,EAAAA,EAAAA,KAAI,CACfI,QAAS,UAEX+2B,kBAAkBn3B,EAAAA,EAAAA,KAAI,CACpBwjB,SAAU,WACVuS,IAAK,MACL1R,MAAO,MACP2R,OAAQ,IAGd,C,6jBA/FE,GAtGWS,GAsGGx0B,aAAY,EAAGD,YAC3B,MAAM,MAAEiU,EAAK,YAAEtG,GAAgB3N,EAAM7D,WAC/BW,GAASC,EAAAA,EAAAA,YAAWC,IACpBm1B,EAAmBpS,GAAyB/f,GAE5Co1B,EAAe,KACnBhnB,GAAqBO,GAAkBG,OAAQC,GAAoBD,OAAOsB,eAAgB,CACxFjG,OAAQnK,EAAM8C,MAAMqH,OACpB2gB,SAAU,UAEZqH,EAAiBkD,uBAAuBr1B,EAAM8C,MAAMqH,OAAO,EAG7D,GAAK8J,EAIL,OACE,kBAACpV,MAAAA,CAAIzB,WAAWY,EAAAA,EAAAA,KAAI,CAAClB,EAAOgC,UAAWhC,EAAOk4B,YAAarjB,QAASyjB,GAClE,kBAACv2B,MAAAA,CAAIzB,UAAWN,EAAOi4B,eACrB,kBAAC9Y,EAAAA,gBAAeA,CACd7e,UAAWN,EAAOo4B,YAClBz1B,KAAM,UAAUO,EAAM8C,MAAMqH,SAC5B+R,QAAS,CAAC,CAAExa,MAAO,GAAIW,MAAO,aAC9BC,SAAU,IAAM8yB,IAChB/yB,MAAO,kBAGVsL,GACC,kBAAC9O,MAAAA,CAAIzB,UAAWN,EAAOq4B,kBACrB,kBAACznB,EAAkBA,CAACC,aAAa,EAAMC,SAAU,MAGrD,kBAACqG,EAAMhU,UAAS,CAACD,MAAOiU,I,IC5GzB,MAAMqhB,WAA6Bv1B,EAAAA,GAYhCkT,WAAAA,GAEN,MACMsiB,EADS,IAAIC,gBAAgB94B,OAAOouB,SAAS2K,QACtBC,IAAI,cAC7BH,GAAiB9B,GAAuB9vB,MAAM2B,GAAMA,EAAEjD,QAAUkzB,KAClE3yB,KAAKI,SAAS,CAAEqT,WAAYkf,IAG9B3yB,KAAK8e,aAEL,MACMiU,EADc5V,GAAyBnd,MACV2T,oBACnC3T,KAAKsD,MAAMC,IACTwvB,EAAevvB,kBAAiB,CAACW,EAAUC,KACzC,GAAID,EAAS1E,QAAU2E,EAAU3E,MAAO,CACtC,MAAM2T,EAAYjD,GAA6BhM,EAAS1E,OACpD2T,GACFpT,KAAKI,SAAS,CAAEgT,cAElBpT,KAAKgzB,kBAAkB7uB,EAAS1E,OAChCO,KAAK8e,YACP,MAIJ9e,KAAKsD,MAAMC,IACTvD,KAAKwD,kBAAiB,CAACW,EAAUC,K,IAETD,EAAAA,EADtB,MAAMuS,EAAYxT,EAAAA,GAAW6G,aAAa/J,MACpCizB,EAAkC,QAAlB9uB,EAAAA,EAASiP,iBAATjP,IAAAA,GAA6B,QAA7BA,EAAAA,EAAoBuS,iBAApBvS,IAAAA,OAAAA,EAAAA,EAA+B6F,KAEjDipB,GAAiBA,EAAgBvc,EAAUxW,MAAMT,MAAMuK,KAAKC,QAC9DjK,KAAKI,SAAS,CAAEgT,eAAW7B,KAIxByH,EAAAA,EAAAA,SAAQ7U,EAASiP,UAAWhP,EAAUgP,aACtBhR,GAAmBpC,MAC3B4Y,cAAc/Q,EAAAA,IACzB7H,KAAKgzB,kBAAkBD,EAAexiB,YACxC,KAIJvQ,KAAKsD,MAAMC,IACT2vB,GAAsBlzB,MAAMwD,kBAAiB,KAC3CxD,KAAKmzB,kBAAkB,KAI3BnzB,KAAKsD,MAAMC,IACTya,GAA2Bhe,MAAMwD,kBAAiB,KAChDxD,KAAKgzB,kBAAkBD,EAAexiB,WAAQ,KAIlDvQ,KAAKgzB,kBAAkBD,EAAexiB,YACtCvQ,KAAKmzB,kBACP,CAEArU,UAAAA,GACE,MACMvX,EADmB4V,GAAyBnd,MAClB2T,oBAAoBpD,WAC9C6iB,EAAgBvC,GAAuB9vB,MAAM2B,GAAMA,EAAEjD,QAAUO,KAAKE,MAAMuT,aAEhFzT,KAAKI,SAAS,CACZD,KAAMkzB,GACJ9rB,EACA6rB,EAAgB,CAACA,aAAAA,EAAAA,EAAetC,SAASvpB,SAA6BgK,UAI5CA,IAA1BvR,KAAKE,MAAMuT,YACbzT,KAAK0T,cAAc,YAEvB,CAEcyf,gBAAAA,G,sBAAd,Y,IAOEG,EANA,MAAMA,QAAWC,EAAAA,EAAAA,oBAAmBT,IAAIU,EAAAA,GAAqB,CAAEC,cAAe,CAAEh0B,MAAO,KAElF6zB,IAIQ,QAAbA,EAAAA,EAAGI,kBAAHJ,IAAAA,GAAAA,EAAAA,KAAAA,GAAkBK,MAAMC,IACtB,IAAIr7B,EAA0B,GAE5BA,EADE,SAAUq7B,EACL,EAA4BpzB,KAE5BozB,EAET,MAAM1V,EAAa3lB,EAAKV,KAAKg8B,GAAMA,EAAEvoB,OACjC4S,IAAe,EAAKhe,MAAMge,YAC5B,EAAK9d,SAAS,CAAE8d,cAClB,IAEJ,E,mLAEAzD,WAAAA,GACE,MAAO,CACLhH,WAAYzT,KAAKE,MAAMuT,WACvBL,UAAWpT,KAAKE,MAAMkT,UAAYiK,KAAKC,UAAUtd,KAAKE,MAAMkT,gBAAa7B,EAE7E,CAEAuiB,aAAAA,CAAc/xB,GACZ,GAAiC,iBAAtBA,EAAO0R,YAChB,GAAIzT,KAAKE,MAAMuT,aAAe1R,EAAO0R,WAAY,CAC/C,MAAM2f,EAAgBvC,GAAuB9vB,MAAM2B,GAAMA,EAAEjD,QAAUsC,EAAO0R,aACxE2f,GACFpzB,KAAK0T,cAAc0f,EAAc3zB,MAErC,OAC+B,OAAtBsC,EAAO0R,YAChBzT,KAAK0T,cAAc,aAGrB,GAAgC,iBAArB3R,EAAOqR,UAAwB,CACxC,MAAMoD,EAAe6G,KAAKwE,MAAM9f,EAAOqR,YAClC4F,EAAAA,EAAAA,SAAQxC,EAAcxW,KAAKE,MAAMkT,YACpCpT,KAAKI,SAAS,CAAEgT,UAAWoD,GAE/B,CACF,CAEAK,qBAAAA,CAAsBL,GACpBxW,KAAK+zB,SAASC,6BAA4B,KACxCh0B,KAAKI,SAAS,CAAEgT,UAAWoD,GAAe,GAE9C,CAEO9C,aAAAA,CAAcD,GACnB,MAAM,KAAEtT,GAASH,KAAKE,MAChBkzB,EAAgBvC,GAAuB9vB,MAAM2B,GAAMA,EAAEjD,QAAUgU,IAE/DlM,EADmB4V,GAAyBnd,MAClB2T,oBAAoBpD,WAEhDpQ,EAAKD,MAAMG,SAASN,OAAS,GAC3BqzB,IACFjzB,EAAKC,SAAS,CACZC,SAAU,IAAIF,EAAKD,MAAMG,SAAS2B,MAAM,EAAG,GAAIoxB,EAActC,SAASvpB,MAExEiE,GAAqBO,GAAkBC,eAAgBG,GAAoBH,eAAeI,oBAAqB,CAC7G6nB,UAAWj0B,KAAKE,MAAMuT,WACtBygB,UAAWzgB,IAEbzT,KAAKI,SAAS,CAAEqT,WAAY2f,EAAc3zB,QAGhD,CAEQuzB,iBAAAA,CAAkBzrB,G,IAERyW,EADhB,MAAM5K,EAAYpT,KAAKE,MAAMkT,U,IACb4K,EAAhB,MAAMD,EAA+D,QAArDC,EAAyC,QAAzCA,EAAAA,GAA2Bhe,MAAMuQ,kBAAjCyN,IAAAA,OAAAA,EAAAA,EAA6CvC,kBAA7CuC,IAAAA,EAAAA,EAA2D,GAE3Ehe,KAAKI,SAAS,CACZ8R,MAAO,IAAIuD,EAAAA,GAAqB,CAC9BvD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BoL,WAAYC,EAAAA,GACZjL,QAAS,CAACwY,GAAW3b,EAAQwW,EAAS3K,IACtC+gB,WAAYC,GAAuBhhB,KAErCwC,gBAAiB,IAAIuN,EAAAA,MAA2CkR,OAGtE,CA7KA,YAAmBn0B,G,IAETA,EADR2C,MAAM,IACJ1C,KAAgB,QAAVD,EAAAA,EAAMC,YAAND,IAAAA,EAAAA,EAAc,IAAI4W,EAAAA,GAAgB,CAAEzW,SAAU,MACjDH,IALP,QAAU6zB,WAAW,IAAIO,EAAAA,GAAyBt0B,KAAM,CAAEzH,KAAM,CAAC,aAAc,gBAQ7EyH,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAwKA,GAlLW0yB,GAkLJr1B,aAAY,EAAGD,YACpB,MAAM,KAAE+C,GAAS/C,EAAM7D,WACjBW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,oCACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAO4E,OACrB,kBAACmM,EAAAA,QAAOA,CAACC,QAAS,kBAACqpB,GAAAA,MAAsBC,UAAW,cAAeC,aAAAA,GACjE,kBAAClb,OAAAA,CAAK/e,UAAWN,EAAOw6B,MAAM,sBACT,kBAAC93B,EAAAA,KAAIA,CAACC,KAAM,mBAIrC,kBAACsD,EAAK9C,UAAS,CAACD,MAAO+C,I,IAM/B,MAAMo0B,GAAoB,KACxB,MAAMr6B,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACgC,EAAAA,MAAKA,CAACC,UAAW,SAAUE,IAAK,GAC/B,kBAACN,MAAAA,CAAIzB,UAAWN,EAAO+Z,QAAQnV,OAAO,0BACtC,kBAACya,OAAAA,CAAK/e,UAAWN,EAAO+Z,QAAQgF,UAAU,oFAG1C,kBAAChd,MAAAA,CAAIzB,UAAWN,EAAO+Z,QAAQ3I,MAC7B,kBAACrP,MAAAA,KACC,kBAACsd,OAAAA,CAAK/e,UAAWN,EAAO+Z,QAAQtW,WAAW,QAAW,yFAGxD,kBAAC1B,MAAAA,KACC,kBAACsd,OAAAA,CAAK/e,UAAWN,EAAO+Z,QAAQtW,WAAW,UAAa,iEAG1D,kBAAC1B,MAAAA,KACC,kBAACsd,OAAAA,CAAK/e,UAAWN,EAAO+Z,QAAQtW,WAAW,YAAe,2FAK9D,kBAAC1B,MAAAA,CAAIzB,UAAWN,EAAO+Z,QAAQ0gB,QAC7B,kBAAC5Q,EAAAA,WAAUA,CACTvU,KAAK,oBACLjE,KAAK,QACLJ,KAAM,KACN6J,OAAQ,SACRE,KACE,gIAEFnG,QAAS,IACPvD,GAAqBO,GAAkBG,OAAQC,GAAoBD,OAAO0B,2BAE7E,yBAQT,SAASxT,GAAUpB,GACjB,MAAO,CACL8F,OAAO1D,EAAAA,EAAAA,KAAI,CACTkD,MAAO,QACP9C,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ8W,KACnBnD,SAAU5X,EAAM6a,WAAWC,UAAUlD,SACrCgkB,cAAe57B,EAAMiE,QAAQ8W,KAC7BzX,WAAY,WAEdo4B,MAAMt5B,EAAAA,EAAAA,KAAI,CACRkD,MAAO,OACP+f,OAAQ,YAEVpK,QAAS,CACP3V,MAAO,UACPQ,OAAO1D,EAAAA,EAAAA,KAAI,CACTwV,SAAU,OACVE,WAAY,MAEdmI,UAAU7d,EAAAA,EAAAA,KAAI,CACZ4B,aAAchE,EAAMiE,QAAQinB,KAE9B5Y,MAAMlQ,EAAAA,EAAAA,KAAI,CACRkD,MAAO,OACP0S,MAAOhY,EAAM0E,OAAO4N,KAAKzN,UAEzB5B,IAAK,CACHe,aAAchE,EAAMiE,QAAQ8W,QAGhCpW,WAAWvC,EAAAA,EAAAA,KAAI,CACbkD,MAAO,YACP0S,MAAOhY,EAAM0E,OAAO4N,KAAK5F,UAE3BivB,QAAQv5B,EAAAA,EAAAA,KAAI,CACV4B,aAAchE,EAAMiE,QAAQ8W,QAIpC,CAEA,MAAM8gB,GAAoB,IACb5C,IAAqB4C,GAAoB,GAAK,EAEpD,SAAS3R,GAAWjiB,EAAsB8c,EAAiB3K,GAChE,MAAMmR,EAA0B,KAAZxG,EAAiB,aAAaA,KAAa,GAC/D,IAAI+W,EAAY,GAChB,OAAQ7zB,GACN,IAAK,SACH6zB,EAAY,qBACZ,MACF,IAAK,WACH,GAAI1hB,EAAW,C,IAETA,EAGAA,EAJJ,MAAMpK,EAAW,IACK,QAAlBoK,EAAAA,EAAUpK,gBAAVoK,IAAAA,OAAAA,EAAAA,EAAoBpJ,KAAKjK,SAC3BiJ,EAAS3H,KAAK,eAAe+R,EAAUpK,SAASgB,SAE5B,QAAlBoJ,EAAAA,EAAUpK,gBAAVoK,IAAAA,OAAAA,EAAAA,EAAoBlJ,GAAGnK,SACzBiJ,EAAS3H,KAAK,eAAe+R,EAAUpK,SAASkB,MAE9ClB,EAASjJ,SACX+0B,GAAa,MAAQ9rB,EAAShB,KAAK,QAEvC,CACK8sB,EAAU/0B,SACb+0B,EAAY,iBAAiBvR,EAAAA,MAInC,MAAO,CACLpb,MAAO,IACPC,MAAO,IAAIR,EAAAA,KAAmBktB,KAAavQ,IAC3Clc,UAAW,UACXC,UAAW,QACXC,MAAO,IACPC,KAAM,GACNb,QAAS,GAEb,CAEA,SAASysB,GAAuBhhB,G,IACPA,EACFA,EADrB,MAAM+a,EAAoD,MAAnC/a,SAAoB,QAApBA,EAAAA,EAAWsD,iBAAXtD,IAAAA,OAAAA,EAAAA,EAAsBpJ,OAAQ,GAC/CokB,EAAgD,MAAjChb,SAAoB,QAApBA,EAAAA,EAAWsD,iBAAXtD,IAAAA,OAAAA,EAAAA,EAAsBlJ,KAAM,GACjD,OAAOikB,GAAiBC,EACpB,IAAI2G,EAAAA,GAAe,CACjB/qB,KAAMmkB,EAAczf,QAAQ,GAC5BxE,GAAIkkB,EAAY1f,QAAQ,GACxBjP,MAAO,CACLuK,MAAMgrB,EAAAA,EAAAA,UAAS7G,GACfjkB,IAAI8qB,EAAAA,EAAAA,UAAS5G,GACb3X,IAAK,CAAEzM,MAAMgrB,EAAAA,EAAAA,UAAS7G,GAAgBjkB,IAAI8qB,EAAAA,EAAAA,UAAS5G,YAGvD7c,CACN,CAEA,SAAS8hB,GAAgB9rB,EAAwBlH,GAC/C,MAAM40B,EAEA,IAAIpD,GADG,SAAXtqB,EACqB,CAAEA,OAAQ,UACV,CACfA,OAAQ,SAGV2tB,EAEA,IAAIrD,GADG,aAAXtqB,EACqB,CACfA,OAAQ,UAEO,CAAEA,OAAQ,aAEjC,OAAO,IAAIuP,EAAAA,GAAgB,CACzBza,UAAW,SACX6tB,WAAY,CACV,IAAIiL,EAAAA,GAAAA,GAAqB,CACvBt0B,IAAK,sBACLu0B,KAAMC,EAAAA,oBAAoBC,aAG9Bj1B,SAAU,CACR,IAAIyW,EAAAA,GAAgB,CAClBza,UAAW,MACXk5B,QAAS,UACTl1B,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB4hB,UAAW2S,GACX7C,UAAW6C,GACXh8B,MAAO,MACPsH,KAAM,IAAIoV,GAAS,CAAC,KAEtB,IAAIuB,EAAAA,GAAgB,CAClBza,UAAW,SACX6lB,UAAW2S,GACX7C,UAAW6C,GACXx0B,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB4hB,UAAW+P,GACXD,UAAWC,GACXn5B,OAAQm5B,GAER9xB,KAAM80B,IAER,IAAI30B,EAAAA,GAAc,CAChB4hB,UAAW+P,GACXD,UAAWC,GACXn5B,OAAQm5B,GAERsD,QAAS,OAETp1B,KAAM+0B,UAMhB,IAAI50B,EAAAA,GAAc,CAChBi1B,QAAS,UACTp1B,KAAM,IAAI4wB,GAAa,CAAC,QAEtB1wB,GAAY,KAGtB,CAEA,MAAMg0B,GAA0B,CAC9B,IAAO7f,GACEA,EAAOC,MACZ5c,EAAAA,GAAAA,MAAK2I,GACIA,EAAK3I,KAAK8Z,G,6WAAQ,OACpBA,GAAAA,CACH7Q,OAAQ6Q,EAAG7Q,OAAOyB,QAAQvB,IAAOA,EAAEnE,KAAK0e,WAAW,sBAK3D,CACEvjB,GAAI,SACJshB,QAAS,CACPxY,OAAQ,CAAC,EACTU,KAAM,CACJ,CACEM,MAAO,WACP0zB,MAAM,MAKd,CACEx9B,GAAI,WACJshB,QAAS,CACPmc,YAAa,CACX,aAAc,EACd/J,OAAQ,EACR,gBAAiB,EACjB,aAAc,EACdgK,SAAU,EACV,UAAW,EACX,mBAAoB,EACpB,2BAA4B,EAC5B,iBAAkB,EAClB,kBAAmB,EACnB,wBAAyB,GACzB,iCAAkC,O,yHClenC,MAAMC,WAA4Bx4B,EAAAA,GA+B/B2Y,WAAAA,GACN,MAAMzE,EAAQhL,EAAAA,GAAc8a,SAASlL,gBAAe,GAIpD,OAHIjW,KAAKE,MAAMqc,QACblL,EAAM9K,UAAU,gBAAwBvG,KAAKE,MAAMqc,QAE9ClL,CACT,CApCAzG,WAAAA,CAAY1K,GACV2C,M,uUAAM,EACJqP,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BoL,WAAYC,EAAAA,GACZjL,QAAS,CAAC,CAAEvC,MAAO,IAAKC,MAAOlI,EAAMkV,QAAS/M,UAAW,eAExDnI,IAGLF,KAAKqD,sBAAqB,KACxB,MAAM7C,EAAO0C,EAAAA,GAAWC,QAAQnD,MAEhCA,KAAKsD,MAAMC,IACT/C,EAAKgD,kBAAkBhD,I,IACjBA,EAIOA,GAJE,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,KACpC1D,KAAKI,SAAS,CACZiR,MAAOrR,KAAK8V,cAAcpD,WAEV,QAATlS,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAasV,SAC3C/Y,KAAKI,SAAS,CACZiR,MAAO,IAAI/T,EAAkB,CAC3BC,UAAW2G,MAGjB,IACF,GAGN,EAUA,GAvCWyxB,GAuCGt4B,aAAY,EAAGD,YAC3B,MAAM,MAAEiU,GAAUjU,EAAM7D,WAClBW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,GAAKiX,EAIL,OACE,kBAACpV,MAAAA,CAAIzB,UAAWN,EAAO07B,gBACrB,kBAACvkB,EAAMhU,UAAS,CAACD,MAAOiU,I,IAMhC,MAAMnN,GAAoB,KACxB,MAAMhK,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO0K,QACrB,kBAACC,EAAAA,EAAQA,CAACC,MAAO,EAAGjM,MAAO,KAC3B,kBAACgM,EAAAA,EAAQA,CAACC,MAAO,EAAGjM,MAAO,MAE7B,kBAACgM,EAAAA,EAAQA,CAACC,MAAO,EAAGjM,MAAO,QAC3B,kBAACoD,MAAAA,CAAIzB,UAAWN,EAAOrC,KACrB,kBAACgN,EAAAA,EAAQA,CAACC,MAAO,IACjB,kBAACD,EAAAA,EAAQA,CAACC,MAAO,EAAGhM,OAAQ,MAG9B,kBAACmD,MAAAA,CAAIzB,UAAWN,EAAOqf,MACrB,kBAACA,OAAAA,CAAK/e,UAAWN,EAAO27B,UACtB,kBAAChxB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACyU,OAAAA,CAAK/e,UAAWN,EAAO47B,MACtB,kBAACjxB,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOqf,MACrB,kBAACA,OAAAA,CAAK/e,UAAWN,EAAO67B,UACtB,kBAAClxB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACyU,OAAAA,CAAK/e,UAAWN,EAAO87B,MACtB,kBAACnxB,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOqf,MACrB,kBAACA,OAAAA,CAAK/e,UAAWN,EAAO+7B,UACtB,kBAACpxB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACyU,OAAAA,CAAK/e,UAAWN,EAAOg8B,MACtB,kBAACrxB,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOqf,MACrB,kBAACA,OAAAA,CAAK/e,UAAWN,EAAOi8B,UACtB,kBAACtxB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACyU,OAAAA,CAAK/e,UAAWN,EAAOk8B,MACtB,kBAACvxB,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOqf,MACrB,kBAACA,OAAAA,CAAK/e,UAAWN,EAAOm8B,UACtB,kBAACxxB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACyU,OAAAA,CAAK/e,UAAWN,EAAOo8B,MACtB,kBAACzxB,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOqf,MACrB,kBAACA,OAAAA,CAAK/e,UAAWN,EAAOq8B,UACtB,kBAAC1xB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACyU,OAAAA,CAAK/e,UAAWN,EAAOs8B,MACtB,kBAAC3xB,EAAAA,EAAQA,CAACC,MAAO,M,EAO3B,SAAS1K,GAAUpB,GACjB,MAAO,CACL48B,gBAAgBx6B,EAAAA,EAAAA,KAAI,CAClBI,QAAS,OACT1C,OAAQ,OAER,mBAAoB,CAClB0C,QAAS,UAGbU,WAAWd,EAAAA,EAAAA,KAAI,CACbtC,OAAQ,oBACRD,MAAO,oBACP+lB,SAAU,WACVnZ,gBAAiBzM,EAAM0E,OAAOE,WAAW8H,QACzCC,OAAQ,aAAa3M,EAAM0E,OAAOiI,OAAOgU,OACzC3d,QAAS,QAEX4I,QAAQxJ,EAAAA,EAAAA,KAAI,CACV4B,aAAc,OACdxB,QAAS,OACTsB,eAAgB,kBAElBjF,KAAKuD,EAAAA,EAAAA,KAAI,CACPwK,UAAW,OACX5I,aAAc,SAEhBuc,MAAMne,EAAAA,EAAAA,KAAI,CACRI,QAAS,SAEXq6B,UAAUz6B,EAAAA,EAAAA,KAAI,CACZvC,MAAO,QAETi9B,MAAM16B,EAAAA,EAAAA,KAAI,CACR+oB,WAAY,KACZtrB,MAAO,QAETk9B,UAAU36B,EAAAA,EAAAA,KAAI,CACZvC,MAAO,QAETm9B,MAAM56B,EAAAA,EAAAA,KAAI,CACR+oB,WAAY,MACZtrB,MAAO,QAETo9B,UAAU76B,EAAAA,EAAAA,KAAI,CACZvC,MAAO,MACPsrB,WAAY,OAEd+R,MAAM96B,EAAAA,EAAAA,KAAI,CACR+oB,WAAY,MACZtrB,MAAO,QAETs9B,UAAU/6B,EAAAA,EAAAA,KAAI,CACZvC,MAAO,MACPsrB,WAAY,OAEdiS,MAAMh7B,EAAAA,EAAAA,KAAI,CACR+oB,WAAY,MACZtrB,MAAO,QAETw9B,UAAUj7B,EAAAA,EAAAA,KAAI,CACZvC,MAAO,MACPsrB,WAAY,QAEdmS,MAAMl7B,EAAAA,EAAAA,KAAI,CACR+oB,WAAY,MACZtrB,MAAO,QAET09B,UAAUn7B,EAAAA,EAAAA,KAAI,CACZvC,MAAO,MACPsrB,WAAY,QAEdqS,MAAMp7B,EAAAA,EAAAA,KAAI,CACR+oB,WAAY,MACZtrB,MAAO,QAGb,C,yHCnNO,MAAM49B,WAAyBt5B,EAAAA,GAS5BkT,WAAAA,GACNrQ,KAAK8e,aAEoB3B,GAAyBnd,MAEjCwD,kBAAiB,CAACW,EAAUC,KACvCD,EAASiR,UAAYhR,EAAUgR,SAAWjR,EAASoY,SAAWnY,EAAUmY,SAC1Evc,KAAK8e,aACLtT,GAAqBO,GAAkBC,eAAgBG,GAAoBH,eAAeY,WAAY,CACpGwI,QAASjR,EAASiR,QAClBmH,OAAQpY,EAASoY,SAErB,GAEJ,CAEQuC,UAAAA,GACN,MAAMyQ,EAAmBpS,GAAyBnd,MAE9CuvB,EAAiBrvB,MAAMkV,QACzBpV,KAAKI,SAAS,CACZD,KAAM,IAAIw1B,GAAoB,CAC5BvgB,QAASma,EAAiBrvB,MAAMkV,QAChCmH,OAAQgT,EAAiBrvB,MAAMqc,WAInCvc,KAAKI,SAAS,CACZD,KAAM,IAAIjD,EAAgB,CACxBrB,QAAS,uBAIjB,CAzCA+O,WAAAA,CAAY1K,GACV2C,M,uUAAM,IACD3C,IAGLF,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAqCA,GA5CWy2B,GA4CGp5B,aAAY,EAAGD,YAC3B,MAAM,KAAE+C,GAAS/C,EAAM7D,WACvB,OAAO4G,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,G,8BCpDnC,MAAMu2B,WAA8Bhe,EAAAA,ICJpC,SAASie,GAA0BhvB,GACxC,MAAMivB,EAAOjvB,EACVpF,QAAQvB,GAAMA,EAAEH,KAAOG,EAAE4N,UAAY5N,EAAEvB,QACvC5H,KAAK0K,GAOV,SAAsBA,GACpB,IAAI4tB,EAAM5tB,EAAO9C,MAWjB,OAVI,CAAC,0CAA2C,iCAAiC4b,SAAS9Y,EAAO1B,OAanG,SAAkBpB,GAChB,OAAgB,MAATA,GAA2B,KAAVA,IAAiBgP,MAAMmS,OAAOnhB,EAAMgc,WAAW1V,QACzE,CAdM8wB,CAAS1G,KAAS,CAAC,SAAU,OAAQ,cAAe,YAAa,WAAY,gBAAiB,iBAAkB,wBAAwB9U,SAAS9Y,EAAO1B,QAIvI,iBAARsvB,GAAqBA,EAAI5U,WAAW,MAAS4U,EAAI2G,SAAS,OACnE3G,EAAM,IAAIA,OAIP,GAAG5tB,EAAO1B,MAAM0B,EAAOqM,WAAWuhB,GAC3C,CApBqB4G,CAAax0B,KAC7ByF,KAAK,MAGR,OAAO4uB,EAAK72B,OAAS62B,EAAO,MAC9B,E,6GDHE,CADWF,GACJr5B,aAAY,EAAGD,YACpB,MAAM,MAAEqC,GAAUrC,EAAM7D,WASxB,OANAy9B,EAAAA,GAAAA,IAAS,KACFv3B,GACHrC,EAAMwb,cAAcjK,GAAqB,GAAGlP,MAC9C,IAIA,kBAAC6mB,EAAAA,iBAAgBA,CACfhN,QAAS3K,GACTlP,MAAOA,EACPC,SAAWgD,GAActF,EAAMwb,cAAclW,OAAI6O,GAAW,I,IEN7D,MAAM0lB,WAA6B95B,EAAAA,GAShCkT,WAAAA,GACNrQ,KAAKk3B,yBAEL,MAAMC,EAAgBjE,GAAsBlzB,MAC5CA,KAAKsD,MAAMC,IACT4zB,EAAc3zB,kBAAiB,CAACW,EAAUC,KACpCD,EAAS1E,QAAU2E,EAAU3E,QAC/BO,KAAKo3B,cACLp3B,KAAKk3B,yBACP,IAGN,CAEQA,sBAAAA,GACN,MAAMC,EAAgBjE,GAAsBlzB,MAGtCq3B,GAAMrC,EAAAA,EAAAA,YACNhrB,GAAOgrB,EAAAA,EAAAA,UAASqC,GAAKC,SAAS,EAAG,UACjCC,EAAmB,IAAIxC,EAAAA,GAAe,CAC1C/qB,KAAMA,EAAKwtB,cACXttB,GAAImtB,EAAIG,gBAGJC,EAAgB,IAAIntB,EAAAA,GAAiB,CACzCG,cAAe,EACfiL,WAAY,CAAEuS,IAAKyP,OAAOP,EAAcj3B,MAAMT,QAC9C00B,WAAYoD,EACZ7sB,QAAS,CAAC,CACRvC,MAAO,qBACPC,MAAO,cACPC,UAAW,UACXC,UAAW,QACXC,MAAO,EACPC,KAAM,EACNb,QAAS,OAIb3H,KAAKsD,MAAMC,IACTk0B,EAAcj0B,kBAAkBtD,I,IAC1BA,EACcA,EAAAA,EAAAA,GADJ,QAAVA,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYA,SAAUuD,EAAAA,aAAapL,UACX,QAAV6H,EAAAA,EAAMM,YAANN,IAAAA,GAAkB,QAAlBA,EAAAA,EAAY+D,cAAZ/D,IAAAA,GAAuB,QAAvBA,EAAAA,EAAqB,UAArBA,IAAAA,OAAAA,EAAAA,EAAyBrE,UAAW,IAGxCwf,SAAS,oCACnBrb,KAAKI,SAAS,CAAEu3B,UAAU,IAE9B,KAIJF,EAAcvO,UAChB,CAEOkO,WAAAA,GACLp3B,KAAKI,SAAS,CACZu3B,UAAU,GAEd,CApEA/sB,WAAAA,GACE/H,MAAM,CACJ80B,UAAU,IAGZ33B,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EAiEF,MAGa43B,GAAqE,EAAGC,eACnF,MAAM,SAAEF,GAAaE,EAASt+B,WAE9B,OAAKo+B,EAKH,kBAAC94B,EAAAA,MAAKA,CACJE,SAAS,UACTD,MAbsB,kCAetB,kBAACg5B,IAAAA,KAduB,8NAgBtB,kBAAC/T,EAAAA,WAAUA,CACTvU,KAAK,oBACLjE,KAAK,OACLJ,KAAK,KACL6J,OAAO,SACPE,KAAK,oEACN,wBAhBE,IAgBF,E,66BCtCT,MAGM6iB,GAAmB,GAFPC,2BAEqB3iB,MAAM,KAAK,eAE3C,MAAM4iB,WAAyB96B,EAAAA,GAiB7BkT,WAAAA,GACArQ,KAAKE,MAAMg4B,UACdl4B,KAAKI,SAAS,CAAE83B,SA4Rb,IAAIxF,GAAqB,CAAC,KAzR/B1yB,KAAKsD,MAAMC,IACTvD,KAAKsqB,iBAAiB9N,EAAAA,IAAmB7iB,IACvCqG,KAAKm4B,yBAAyBx+B,EAAM4wB,QAAQnV,SAC5CpV,KAAKI,SAAS,CAAEgV,QAASzb,EAAM4wB,QAAQnV,QAASmH,OAAQ5iB,EAAM4wB,QAAQhO,QAAS,KAI/Evc,KAAKE,MAAMkV,SACbpV,KAAKm4B,yBAAyBn4B,KAAKE,MAAMkV,SAGrBlS,EAAAA,GAAWsV,eAAe4f,EAAAA,GAAgBp4B,MAClDwD,kBAAkBW,IAC1BA,EAAS1E,OACX44B,aAAaC,QAAQC,EAAAA,GAAmBp0B,EAAS1E,MAAMgc,WACzD,IAGEzb,KAAKE,MAAMu3B,gBACRz3B,KAAKE,MAAMu3B,cAAce,UAC5Bx4B,KAAKE,MAAMu3B,cAAcvO,WAG/B,CAEAzO,WAAAA,GACE,MAAO,CAAErF,QAASpV,KAAKE,MAAMkV,QAASmH,OAAQvc,KAAKE,MAAMqc,OAC3D,CAEAuX,aAAAA,CAAc/xB,GACZ,MAAM02B,EAA8C,CAAC,GAEjD12B,EAAOqT,SAAWrT,EAAOwa,UAC3Bkc,EAAYrjB,QAAUrT,EAAOqT,QAAWrT,EAAOqT,aAAqB7D,EACpEknB,EAAYlc,OAASxa,EAAOwa,OAAUxa,EAAOwa,YAAoBhL,GAGnEvR,KAAKI,SAASq4B,EAChB,CAEO9kB,iBAAAA,GACL,MAAM3E,EAAW9L,EAAAA,GAAWsV,eAAeoU,EAAAA,GAAY5sB,MACvD,KAAMgP,aAAoB0J,EAAAA,IACxB,MAAM,IAAIrgB,MAAM,6BAOlB,OAJK2W,EAASuB,YACZvB,EAAS4J,cAAc,QAGlB5J,CACT,CAWOygB,iBAAAA,GACL,OAAOzvB,KAAK2T,oBAAoBpD,UAClC,CAEOmoB,WAAAA,GACL14B,KAAKI,SAAS,CAAEgV,aAAS7D,EAAWgL,YAAQhL,GAC9C,CAEQ4mB,wBAAAA,CAAyB/iB,GAC/B,MACM0S,EAAQ5K,GADWC,GAAyBnd,OAG5C0nB,EAAc,IAAIpd,EAAAA,GAAiB,CACvCoL,WAAY,CAAEuS,IAAKH,GACnBpd,QAAS,CAAC,CACRvC,MAAO,IACPC,MAAOgN,EACP/M,UAAW,cAIT4gB,EAA2B,IAAI1B,GAAyB,CAC5Dnf,MAAOgN,EACPnU,KAAM,QACN6mB,QACA5V,MAAOwV,IAGTuB,EAAyBC,WACzBlpB,KAAKI,SAAS,CAAE6oB,6BAChBjpB,KAAKsD,MAAMC,IACT0lB,EAAyBzlB,kBAAiB,KACxCxD,KAAK24B,yBAAyB,KAIlCjR,EAAYwB,WAEZlpB,KAAKsD,MAAMC,IACTmkB,EAAYlkB,kBAAkBtD,I,IACxBA,EAA2CA,EAAAA,EAA/C,IAAc,QAAVA,EAAAA,EAAMM,YAANN,IAAAA,OAAAA,EAAAA,EAAYA,SAAUuD,EAAAA,aAAaC,OAAkB,QAAVxD,EAAAA,EAAMM,YAANN,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYJ,cAAZI,IAAAA,OAAAA,EAAAA,EAAoBH,QAAS,EAAG,C,IACpDG,EAAAA,EAAzB,MAAM04B,EAAuC,QAApB14B,EAAAA,EAAMM,KAAKV,OAAO,UAAlBI,IAAAA,GAA4B,QAA5BA,EAAAA,EAAsBY,cAAtBZ,IAAAA,OAAAA,EAAAA,EAA8Ba,MAAMC,GAAiB,gBAAXA,EAAEnE,OAEjE+7B,GAAoBA,EAAiB72B,OAAO,IAC9CknB,EAAyB7oB,SAAS,SAC7B6oB,EAAyB/oB,OAAK,CACjC6nB,WAAY,GAAG6Q,EAAiB72B,OAAO,OAG7C,MAIJknB,EAAyB7oB,SAAS,SAC7B6oB,EAAyB/oB,OAAK,CACjC6nB,WAAY3S,IAEhB,CAEcujB,uBAAAA,G,sBAAd,YACE,MAAM,yBAAE1P,GAA6B,EAAK/oB,MAC1C,IAAK+oB,EACH,OAGF,MAAM7K,QAAaqL,GAAqBR,GACpC7K,GACF,EAAKhe,SAAS,CAAEy4B,kBAAmBza,GAEvC,E,mLAtJA,YAAmBle,G,IAEHA,EACAA,EACFA,EA2SQ44B,EAAoBC,EA9SxCl2B,MAAM,IACJsxB,WAA4B,QAAhBj0B,EAAAA,EAAMi0B,kBAANj0B,IAAAA,EAAAA,EAAoB,IAAI60B,EAAAA,GAAe,CAAC,GACpDiE,WAA4B,QAAhB94B,EAAAA,EAAM84B,kBAAN94B,IAAAA,EAAAA,GA4SM44B,EA5S6B54B,EAAM44B,UA4SfC,EA5S0B74B,EAAM64B,eA6SnE,IAAIE,EAAAA,GAAiB,CAC1BC,UAAW,CACT,IAAIC,EAAAA,GAAmB,CACrBt8B,KAAMu7B,EAAAA,GACN95B,MAAO,cACPmB,MAAOq5B,EACPM,SAAU,UAEZ,IAAI1C,GAAsB,CACxB75B,KAAMyyB,EAAAA,GACN7vB,MAAOkP,GAAqB,GAAGlP,QAEjC,IAAI45B,EAAAA,GAAqB,CACvBC,oBAAqB,aACrBC,KAAMC,GAAAA,GAAaC,UACnB58B,KAAM8vB,EAAAA,GACNjX,WAAYC,EAAAA,GACZwR,OAAQ,WACRxf,QAASoxB,QAAAA,EAAkB,GAC3BW,kBAAkB,EAClBC,kBAAmBhD,KAErB,IAAIje,EAAAA,GAAe,CACjB7b,KAAM+vB,EAAAA,GACN2M,KAAMC,GAAAA,GAAaI,eAErB,IAAIlhB,EAAAA,GAAe,CACjB7b,KAAMg9B,EAAAA,EACNC,cAAc,IAEhB,IAAIphB,EAAAA,GAAe,CACjB7b,KAAMk9B,EAAAA,GACND,cAAc,IAEhB,IAAIphB,EAAAA,GAAe,CACjB7b,KAAM4b,EAAAA,GACNqhB,cAAc,EACdP,KAAMC,GAAAA,GAAaI,eAErB,IAAIlhB,EAAAA,GAAe,CACjB7b,KAAMgc,EAAAA,GACNihB,cAAc,EACdP,KAAMC,GAAAA,GAAaI,mBAtVrB7M,SAAwB,QAAd7sB,EAAAA,EAAM6sB,gBAAN7sB,IAAAA,EAAAA,EAAkB,CAAC,IAAI85B,EAAAA,GAAgB,CAAC,GAAI,IAAIC,EAAAA,GAAmB,CAAC,IAC9E95B,KAAM,IAAI+5B,GAAsB,CAAC,GACjCC,YAAa,IAAI1D,GAAiB,CAAC,GACnCgB,cAAe,IAAIR,IAChB/2B,IAVP,QAAU6zB,WAAW,IAAIO,EAAAA,GAAyBt0B,KAAM,CAAEzH,KAAM,CAAC,gBAAiB,UAAW,SAAU,aA0EvG,QAAOk6B,0BAA0BlrB,IAC/B,MAAMyH,EAAWhP,KAAK2T,oBACjBpM,GAAUyH,EAASuB,aAAehJ,GAIvCyH,EAAS4J,cAAcrR,OAAQgK,GAAW,EAAK,IAnE/CvR,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EA4IA,GA3JWi4B,GA2JJ56B,aAAY,EAAGD,YACpB,MAAM,KAAE+C,GAAS/C,EAAM7D,WACjBW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OAAO,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAOkgC,eAAe,IAAEj6B,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,IAAS,QAIpF,MAAM+5B,WAA8B/8B,EAAAA,IAgM3C,SAAS/C,GAAUpB,GACjB,MAAO,CACLohC,eAAeh/B,EAAAA,EAAAA,KAAI,CACjBkD,MAAO,gBACP+F,SAAU,EACV7I,QAAS,OACT0mB,UAAW,OACXnlB,cAAe,WAEjBb,WAAWd,EAAAA,EAAAA,KAAI,CACbkD,MAAO,YACP+F,SAAU,EACV7I,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,GACnBilB,UAAW,OACXnlB,cAAe,SACff,QAAS,KAAKhD,EAAMiE,QAAQ,MAAMjE,EAAMiE,QAAQ,MAAMjE,EAAMiE,QAAQ,KACpEkU,SAAU,OACV6gB,UAAW,SAEbqI,cAAcj/B,EAAAA,EAAAA,KAAI,CAChBI,QAAS,OACTsB,eAAgB,gBAChBR,WAAY,SACZg+B,aAAc,aAAathC,EAAM0E,OAAOiI,OAAOgU,OAC/Cib,cAAe57B,EAAMiE,QAAQ,GAC7BD,aAAchE,EAAMiE,QAAQ,GAE5B,GAAM,CACJ6c,OAAQ,KAGZygB,qBAAqBn/B,EAAAA,EAAAA,KAAI,CACvBI,QAAS,OACTsB,eAAgB,WAChBP,IAAKvD,EAAMiE,QAAQ,OAErBkD,MAAM/E,EAAAA,EAAAA,KAAI,CACRkD,MAAO,OACP+F,SAAU,EACV7I,QAAS,OACTuB,cAAe,SACfR,IAAKvD,EAAMiE,QAAQ,KAErBic,iBAAiB9d,EAAAA,EAAAA,KAAI,CACnBkD,MAAO,kBACPmH,gBAAiBzM,EAAM0E,OAAOE,WAAW48B,OACzCh/B,QAAS,OACTuB,cAAe,SACf6hB,SAAU,SACVuS,IAAK,EACLC,OAAQ,EACRp1B,QAAS,GAAGhD,EAAMiE,QAAQ,SAC1BV,IAAKvD,EAAMiE,QAAQ,KAErBw9B,iBAAiBr/B,EAAAA,EAAAA,KAAI,CACnBkD,MAAO,kBACPsS,SAAU,OACV5U,QAAS,KAAKhD,EAAMiE,QAAQ,KAC5BnE,OAAQ,OACR0C,QAAS,OACTc,WAAY,SACZQ,eAAgB,aAChBgU,WAAY9X,EAAM6a,WAAW6mB,iBAC7B9b,SAAU,WACVa,OAAQ,EACR5mB,MAAO,SAETk0B,UAAU3xB,EAAAA,EAAAA,KAAI,CACZkD,MAAO,WACP9C,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,GACnBm0B,OAAQ,EACRuJ,SAAU,SAEZxR,MAAM/tB,EAAAA,EAAAA,KAAI,CACRkD,MAAO,OACP,YAAa,CACX0S,MAAOhY,EAAM0E,OAAO4N,KAAK8S,QAG7Bwc,WAAYx/B,EAAAA,GAAG;iBACFpC,EAAMiE,QAAQ,GAAK;;MAGhC49B,mBAAoBz/B,EAAAA,GAAG;eACZpC,EAAM0E,OAAO4N,KAAKzN;mBACd7E,EAAM6a,WAAWC,UAAUlD;MAE1CqD,SAAS7Y,EAAAA,EAAAA,KAAI,CACXkD,MAAO,UACPsS,SAAU,OACVkqB,WAAY,OACZjiC,MAAO,QACPkY,UAAW,WAEbgqB,UAAU3/B,EAAAA,EAAAA,KAAI,CACZkD,MAAO,WACP6lB,WAAYnrB,EAAMiE,QAAQ,KAE5B0K,SAASvM,EAAAA,EAAAA,KAAI,CACXkD,MAAO,UACPsH,UAAW5M,EAAMiE,QAAQ,GACzBzB,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,KAGzB,CA1SE,GADWi9B,GACJ78B,aAAY,EAAGD,YACpB,MAAMmyB,EAAmBpS,GAAyB/f,IAC5C,SAAE2vB,EAAQ,SAAEmL,EAAQ,YAAEiC,EAAW,QAAE/kB,EAAO,cAAEqiB,EAAa,kBAAEoB,EAAiB,yBAAE5P,GAA6BsG,EAAiBh2B,YAC5H,SAAEo+B,IAAaF,aAAAA,EAAAA,EAAel+B,aAAc,CAChDo+B,UAAU,GAENz9B,GAASC,EAAAA,EAAAA,YAAWC,KACnB4gC,EAAaC,GAAkBC,IAAAA,UAAe,GAE/CC,EAAaj4B,EAAAA,GAAWsV,eAAe4f,EAAAA,GAAgB7I,GACvD6L,EAAkBnsB,GAAmBsgB,GACrC8L,EAAwBxM,GAAyBU,GAEvD,SAAS+L,IACP,MAAMphC,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAO0gC,YACrB,kBAACW,KAAAA,KAAG,6BA3LEvD,SA4LN,kBAAC/7B,MAAAA,CAAIzB,UAAWN,EAAO2gC,oBAAoB,gBAAc9C,IAG/D,CAEA,MAAM5O,EACJ,kBAACqS,EAAAA,KAAIA,CAAC52B,OAAQ,kBAAC02B,EAAAA,OACb,kBAACr/B,MAAAA,CAAIzB,UAAWN,EAAOivB,MACpBxU,EAAAA,OAAO8mB,sBACN,kBAACD,EAAAA,KAAKE,KAAI,CACRp9B,MAAM,gBACNq9B,UAAU,gBACVnsB,KAAM,sBACNqF,IAAI,4DACJG,OAAO,SACPjG,QAAS,IACPvD,GAAqBO,GAAkBG,OAAQC,GAAoBD,OAAOyB,4BAIhF,kBAAC6tB,EAAAA,KAAKE,KAAI,CACRp9B,MAAM,gBACNq9B,UAAU,gBACVnsB,KAAM,oBACNqF,IAAI,+EACJG,OAAO,SACPjG,QAAS,IACPvD,GAAqBO,GAAkBG,OAAQC,GAAoBD,OAAO2B,2BAoBpF,OACE,oCACE,kBAAC5R,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAOgf,iBACpBye,GAAYF,GAAiB,kBAACG,GAAoBA,CAACC,SAAUJ,IAC9D,kBAACr7B,EAAAA,MAAKA,CAACG,IAAK,EAAGO,eAAgB,gBAAiBklB,KAAM,QACpD,kBAAC5lB,EAAAA,MAAKA,CAACG,IAAK,EAAGD,WAAY,SAAU0lB,KAAM,QACxCmZ,GACC,kBAAC/+B,EAAAA,MAAKA,CAACG,IAAK,EAAGD,WAAY,UACzB,kBAACL,MAAAA,CAAIzB,UAAWN,EAAOugC,iBAAiB,eACxC,kBAACU,EAAW99B,UAAS,CAACD,MAAO+9B,MAKnC,kBAACl/B,MAAAA,CAAIzB,UAAWN,EAAO6yB,UACrB,kBAAC6O,EAAAA,SAAQA,CAACC,QAAS1S,EAAM2S,gBAAiB,IAAMb,GAAgBD,IAC9D,kBAACzrB,EAAAA,OAAMA,CAAC7S,QAAQ,YAAY8S,KAAK,eAAc,YAE7C,kBAAC5S,EAAAA,KAAIA,CAACpC,UAAWN,EAAO6gC,SAAUl+B,KAAMm+B,EAAc,WAAa,aAAc7vB,KAAK,SAGzF4hB,EAASl1B,KAAKkkC,GACb,kBAACA,EAAQ1+B,UAAS,CAACwD,IAAKk7B,EAAQ77B,MAAMW,IAAKzD,MAAO2+B,QAIxD,kBAAC3/B,EAAAA,MAAKA,CAACG,IAAK,EAAGD,WAAY,SAAU0lB,KAAM,QACzC,kBAAC5lB,EAAAA,MAAKA,CAACG,IAAK,EAAGD,WAAY,UACzB,kBAACL,MAAAA,CAAIzB,UAAWN,EAAOugC,iBAAiB,WACvCY,GAAyB,kBAACA,EAAsBh+B,UAAS,CAACD,MAAOi+B,KAEnED,GACC,kBAACn/B,MAAAA,KACC,kBAACm/B,EAAgB/9B,UAAS,CAACD,MAAOg+B,OAK1C,kBAACn/B,MAAAA,CAAIzB,UAAWN,EAAOiG,MAAO+3B,GAAY,kBAACA,EAAS76B,UAAS,CAACD,MAAO86B,MAEtEiC,GAAe/kB,GACd,kBAAC4mB,EAAAA,OAAMA,CAAC7wB,KAAM,KAAM8wB,QAAS,IAAM1M,EAAiBmJ,eAClD,kBAACz8B,MAAAA,CAAIzB,UAAWN,EAAOmgC,cACrB,kBAAC6B,KAAAA,KAAG,cAAY9mB,GACd,kBAACnZ,MAAAA,CAAIzB,UAAWN,EAAOqgC,qBACpBtR,GAA4B4P,GAC3B,kBAACtpB,EAAAA,OAAMA,CACL7S,QAAQ,YACRyO,KAAK,KACLqE,KAAK,cACLT,QAhEiB3W,KAC7BygC,aAAAA,EAAAA,EAAmB9pB,UACrB8pB,EAAkB9pB,QAAQ3W,GAG5BoT,GACEO,GAAkBC,eAClBG,GAAoBH,eAAee,yCAGrC+N,YAAW,IAAMyU,EAAiBmJ,eAAe,IAAI,GAwDpCtQ,IAGL,kBAAC+T,EAAAA,WAAUA,CACTt/B,KAAK,QACLkS,QAAS,IAAMwgB,EAAiBmJ,cAChCzkB,QAAQ,eACR9I,KAAK,SAIb,kBAACgvB,EAAY98B,UAAS,CAACD,MAAO+8B,K,ICjWnC,MAAMiC,GAAqBn9B,IAChC,MAAM,MAAEmK,EAAK,KAAEnI,EAAI,MAAE3C,EAAK,WAAE+9B,EAAU,MAAE58B,EAAK,WAAE68B,EAAU,IAAEznB,GAAQ5V,EAC7D/E,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAAC6B,MAAAA,CAAI4E,IAAKuI,GACG,IAAVA,GACC,kBAACnN,MAAAA,CAAIzB,UAAWN,EAAOqiC,WACrB,kBAAChjB,OAAAA,KAAM8iB,GACP,kBAAC9iB,OAAAA,CAAK/e,UAAWN,EAAOoiC,YAAaA,IAIzC,kBAACrgC,MAAAA,CACCzB,UAAWN,EAAOukB,IAClB5d,IAAKuI,EACL2F,QAAS,KACPvD,GAAqBO,GAAkBE,KAAME,GAAoBF,KAAKkB,kBAAmB,CACvFlM,OACAmI,QACA3J,UAEF+8B,EAAAA,gBAAgBn7B,KAAKwT,EAAI,GAG3B,kBAAC5Y,MAAAA,CAAIzB,UAAW,YAAa8D,GAE7B,kBAACrC,MAAAA,CAAIzB,UAAWN,EAAO6K,QACrB,kBAACwU,OAAAA,CAAK/e,UAAWN,EAAOuiC,YAAah9B,GACrC,kBAAC7C,EAAAA,KAAIA,CAACpC,UAAWN,EAAOwiC,WAAY7/B,KAAK,cAAcsO,KAAK,S,EAOtE,SAAS/Q,GAAUpB,GACjB,MAAO,CACLujC,WAAWnhC,EAAAA,EAAAA,KAAI,CACb4V,MAAOhY,EAAM0E,OAAO4N,KAAKzN,UACzBrC,QAAS,OACTsB,eAAgB,gBAChBR,WAAY,SACZN,QAAS,KAAKhD,EAAMiE,QAAQ,MAAMjE,EAAMiE,QAAQ,MAAMjE,EAAMiE,QAAQ,OAEtEq/B,YAAYlhC,EAAAA,EAAAA,KAAI,CACd0e,OAAQ,eAEV2E,KAAKrjB,EAAAA,EAAAA,KAAI,CACPI,QAAS,OACTsB,eAAgB,gBAChBR,WAAY,SACZC,IAAKvD,EAAMiE,QAAQ,GACnBjB,QAAS,GAAGhD,EAAMiE,QAAQ,QAASjE,EAAMiE,QAAQ,KAEjD,UAAW,CACTwI,gBAAiBzM,EAAMuB,OAASvB,EAAM0E,OAAOE,WAAWC,UAAY7E,EAAM0E,OAAOE,WAAW8H,QAC5F2Y,OAAQ,UACR,YAAa,CACXE,eAAgB,gBAItBxZ,QAAQ3J,EAAAA,EAAAA,KAAI,CACVI,QAAS,OACTc,WAAY,WAEdmgC,YAAYrhC,EAAAA,EAAAA,KAAI,CACd4V,MAAO,UACPhV,QAAS,KAAKhD,EAAMiE,QAAQ,KAC5BpE,MAAO,gBAET6jC,YAAYthC,EAAAA,EAAAA,KAAI,CACdijB,OAAQ,UACRvE,OAAQ,KAAK9gB,EAAMiE,QAAQ,SAAUjE,EAAMiE,QAAQ,OAGzD,CClFO,MAAM0/B,GAAuB19B,I,IAgC7Ba,EA/BL,MAAM,OAAEA,EAAM,KAAEmB,GAAShC,EACnB/E,GAASC,EAAAA,EAAAA,YAAWC,IAEpBq0B,EAAY9c,I,IAETirB,EADP,MAAMA,EAAcjrB,EAAG7Q,OAAOC,MAAMC,GAAiB,SAAXA,EAAEnE,O,IACrC+/B,EAAP,OAAoE,QAA7DA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAax7B,cAAbw7B,IAAAA,OAAAA,EAAAA,EAAsB,yBAAyB/wB,QAAQ,KAAM,WAA7D+wB,IAAAA,EAAAA,EAAoE,wBAAwB,EAG/FC,EAAUlrB,IACd,MACMmrB,EAAS,CACb,cAAe,2BAFGrO,EAAS9c,KAG3B,aAAc,UAEhB,OAAO+I,EAAAA,QAAQC,UAAUC,EAAAA,GAAoBkiB,EAAO,EAGhDC,EAAgBprB,I,IAGlBirB,EAFF,MAAMA,EAAcjrB,EAAG7Q,OAAOC,MAAMC,GAAiB,SAAXA,EAAEnE,O,IAE1C+/B,EADF,OAMK,QALHA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAa76B,cAAb66B,IAAAA,OAAAA,EAAAA,EAAqBj8B,QAAO,CAACzH,EAAG0H,IACb,iBAAN1H,GAAmBuV,MAAMvV,GAG7B0H,EAFE1H,EAAI0H,GAGZ,UALHg8B,IAAAA,EAAAA,EAKS,CAAC,EAId,OACE,kBAAC3gC,MAAAA,CAAIzB,UAAWN,EAAOgC,WAGT,QAFX4D,EAAAA,EACE0B,MAAK,CAACC,EAAGC,IAAMq7B,EAAar7B,GAAKq7B,EAAat7B,KAC9CO,MAAM,EAAG,WAFXlC,IAAAA,OAAAA,EAAAA,EAGGjI,KAAI,CAAC8Z,EAAIvI,IACT,kBAACmQ,OAAAA,CAAK1Y,IAAKuI,GACT,kBAACgzB,GAAiBA,CAChBn7B,KAAMA,EACNmI,MAAOA,EACP9K,MAAOmwB,EAAS9c,GAChB0qB,WAAW,UACX58B,MAAOs9B,EAAaprB,GACpB2qB,WAAW,eACXznB,IAAKgoB,EAAOlrB,Q,EAQ1B,SAASvX,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbY,QAAS,GAAGhD,EAAMiE,QAAQ,SAGhC,C,yHCzDO,MAAM+/B,GAAqB/9B,IAChC,MAAM,OAAEa,EAAM,KAAEmB,GAAShC,EACnB/E,GAASC,EAAAA,EAAAA,YAAWC,IAEpB6iC,EAAWn9B,EAAO,GAAGgB,OAAOC,MAAMC,GAAiB,aAAXA,EAAEnE,OAChD,GAAIogC,GAAYA,EAASl7B,OAAQ,C,IACNk7B,EA0DpB7gB,EA1DL,MAAM8gB,EAAmBD,SACRv4B,QADQu4B,EAAAA,EAAUl7B,OAChClK,KAAI,CAAC4M,EAAGC,IAAMA,WADQu4B,IAAAA,OAAAA,EAAAA,EAErBz7B,MAAK,CAACC,EAAGC,KAAMu7B,aAAAA,EAAAA,EAAUl7B,OAAOL,KAAKu7B,aAAAA,EAAAA,EAAUl7B,OAAON,MACpD07B,EAAer9B,EAAO,GAAGgB,OAAOjJ,KAAKmJ,IACzC,O,yUAAO,IACFA,G,WAAAA,CACHe,OAAQm7B,aAAAA,EAAAA,EAAkBrlC,KAAK6M,GAAM1D,EAAEe,OAAO2C,O,yVAI5C+pB,EAAW,CAAC2O,EAAsCC,EAAmCj0B,KACzF,IAAI9K,EAAQ,GAOZ,OANI8+B,aAAAA,EAAAA,EAAmBr7B,OAAOqH,MAC5B9K,EAAQ8+B,EAAkBr7B,OAAOqH,KAE/Bi0B,aAAAA,EAAAA,EAAgBt7B,OAAOqH,MACzB9K,EAAyB,IAAjBA,EAAMyB,OAAes9B,EAAet7B,OAAOqH,GAAS,GAAG9K,MAAU++B,EAAet7B,OAAOqH,MAEzE,IAAjB9K,EAAMyB,OAAe,iCAAmCzB,CAAK,EAGhEu+B,EAAS,CACbznB,EACAiH,EACA+gB,EACAh0B,KAEA,KAAKiT,GAAgBA,EAAYta,OAAOqH,IAAWg0B,GAAsBA,EAAkBr7B,OAAOqH,IAEhG,OADAk0B,QAAQC,MAAM,oCACPC,EAAAA,GAAOC,QAGhB,MAAMX,EAAS,CACb1nB,UACAmH,OAAQF,EAAYta,OAAOqH,GAC3B,cAAe,2BAA2Bg0B,EAAkBr7B,OAAOqH,KACnE,aAAc,YAGhB,OAAOsR,EAAAA,QAAQC,UAAUC,EAAAA,GAAoBkiB,EAAO,EAGhDY,EAAc,CAACC,EAAkCv0B,IAChDu0B,GAAkBA,EAAc57B,OAI9BgH,EAAe40B,EAAc57B,OAAOqH,GAAS,KAH3C,qBAMLgT,EAAe+gB,EAAap8B,MAAMC,GAAiB,kBAAXA,EAAEnE,OAC1Cwf,EAAc8gB,EAAap8B,MAAMC,GAAiB,WAAXA,EAAEnE,OACzCwgC,EAAiBF,EAAap8B,MAAMC,GAAiB,cAAXA,EAAEnE,OAC5CugC,EAAoBD,EAAap8B,MAAMC,GAAiB,iBAAXA,EAAEnE,OAC/C8gC,EAAgBR,EAAap8B,MAAMC,GAAiB,aAAXA,EAAEnE,OAEjD,OACE,kBAACZ,MAAAA,CAAIzB,UAAWN,EAAOgC,WACpBkgB,SAAoB,QAApBA,EAAAA,EAAcra,cAAdqa,IAAAA,OAAAA,EAAAA,EAAsBvkB,KAAI,CAACud,EAAShM,IACnC,kBAACmQ,OAAAA,CAAK1Y,IAAKuI,GACT,kBAACgzB,GAAiBA,CAChBn7B,KAAMA,EACNmI,MAAOA,EACP9K,MAAOmwB,EAAS2O,EAAmBC,EAAgBj0B,GACnDizB,WAAW,QACX58B,MAAOi+B,EAAYC,EAAev0B,GAClCkzB,WAAW,WACXznB,IAAKgoB,EAAOznB,EAASiH,EAAa+gB,EAAmBh0B,QAMjE,CACA,OAAO,IAAI,EAGb,SAAShP,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbY,QAAS,GAAGhD,EAAMiE,QAAQ,SAGhC,CCzFO,MAAM2gC,GAAuB3+B,I,IAgC7Ba,EA/BL,MAAM,OAAEA,EAAM,KAAEmB,GAAShC,EACnB/E,GAASC,EAAAA,EAAAA,YAAWC,IAEpBq0B,EAAY9c,I,IAETirB,EADP,MAAMA,EAAcjrB,EAAG7Q,OAAOC,MAAMC,GAAiB,SAAXA,EAAEnE,O,IACrC+/B,EAAP,OAAoE,QAA7DA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAax7B,cAAbw7B,IAAAA,OAAAA,EAAAA,EAAsB,yBAAyB/wB,QAAQ,KAAM,WAA7D+wB,IAAAA,EAAAA,EAAoE,wBAAwB,EAG/FC,EAAUlrB,IACd,MACMmrB,EAAS,CACb,cAAe,2BAFGrO,EAAS9c,KAG3B,aAAc,YAEhB,OAAO+I,EAAAA,QAAQC,UAAUC,EAAAA,GAAoBkiB,EAAO,EAGhDY,EAAe/rB,I,IAGjBirB,EAFF,MAAMA,EAAcjrB,EAAG7Q,OAAOC,MAAMC,GAAiB,SAAXA,EAAEnE,O,IAE1C+/B,EADF,OAMK,QALHA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAa76B,cAAb66B,IAAAA,OAAAA,EAAAA,EAAqBj8B,QAAO,CAACzH,EAAG0H,IACb,iBAAN1H,GAAmBuV,MAAMvV,GAG7B0H,EAFE1H,EAAI0H,GAGZ,UALHg8B,IAAAA,EAAAA,EAKS,CAAC,EAId,OACE,kBAAC3gC,MAAAA,CAAIzB,UAAWN,EAAOgC,WAGT,QAFX4D,EAAAA,EACE0B,MAAK,CAACC,EAAGC,IAAMg8B,EAAYh8B,GAAKg8B,EAAYj8B,KAC5CO,MAAM,EAAG,WAFXlC,IAAAA,OAAAA,EAAAA,EAGGjI,KAAI,CAAC8Z,EAAIvI,IACT,kBAACmQ,OAAAA,CAAK1Y,IAAKuI,GACT,kBAACgzB,GAAiBA,CAChBn7B,KAAMA,EACNmI,MAAOA,EACP9K,MAAOmwB,EAAS9c,GAChB0qB,WAAW,UACX58B,MAAOsJ,EAAiC,IAAlB20B,EAAY/rB,IAClC2qB,WAAW,MACXznB,IAAKgoB,EAAOlrB,Q,EAQ1B,SAASvX,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbY,QAAS,GAAGhD,EAAMiE,QAAQ,SAGhC,CCzDO,MAAM4gC,GAAsB5+B,IACjC,MAAM,OAAEa,EAAM,KAAEmB,EAAI,QAAEpF,GAAYoD,EAC5B/E,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,GAAIyB,EACF,OACE,kBAACI,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO2B,SACrB,kBAACe,EAAAA,KAAIA,CAACpC,UAAWN,EAAOsV,KAAM3S,KAAK,qBAAqBsO,KAAK,OAC5DtP,IAMT,GAAIiE,GAAUA,EAAOC,OAAS,EAC5B,OAAQkB,GACN,IAAK,iBACH,OAAO,kBAAC+7B,GAAiBA,CAACl9B,OAAQA,EAAQmB,KAAMA,IAClD,IAAK,mBACH,OAAO,kBAAC07B,GAAmBA,CAAC78B,OAAQA,EAAQmB,KAAMA,IACpD,IAAK,mBACH,OAAO,kBAAC28B,GAAmBA,CAAC99B,OAAQA,EAAQmB,KAAMA,IAGxD,OAAO,kBAAChF,MAAAA,CAAIzB,UAAWN,EAAOgC,WAAW,mBAG3C,SAAS9B,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbY,QAAS,GAAGhD,EAAMiE,QAAQ,SAE5BuS,MAAMpU,EAAAA,EAAAA,KAAI,CACR0e,OAAQ,KAAK9gB,EAAMiE,QAAQ,SAAUjE,EAAMiE,QAAQ,OAErDpB,SAAST,EAAAA,EAAAA,KAAI,CACXI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,KACnB6c,OAAQ,GAAG9gB,EAAMiE,QAAQ,UACzBpE,MAAO,QAGb,CC3CO,MAAMilC,WAA4B3gC,EAAAA,IAiBzC,SAAS4gC,GAAQ98B,GACf,OAAQA,GACN,IAAK,mBACH,MAAO,aACT,IAAK,iBACH,MAAO,YAGT,QACE,MAAO,uBAEb,CAEA,SAAS7G,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbuK,OAAQ,aAAa3M,EAAMuB,OAASvB,EAAM0E,OAAOiI,OAAOq4B,OAAShlC,EAAM0E,OAAOiI,OAAOgU,OACrF5b,aAAc/E,EAAMiE,QAAQ,IAC5BD,aAAchE,EAAMiE,QAAQ,GAC5BpE,MAAO,SAETiG,OAAO1D,EAAAA,EAAAA,KAAI,CACT4V,MAAOhY,EAAMuB,OAASvB,EAAM0E,OAAO4N,KAAKzN,UAAY7E,EAAM0E,OAAO4N,KAAK5F,QACtED,gBAAiBzM,EAAMuB,OAASvB,EAAM0E,OAAOE,WAAWC,UAAY7E,EAAM0E,OAAOE,WAAW8H,QAC5Fu4B,oBAAqBjlC,EAAMiE,QAAQ,IACnCihC,qBAAsBllC,EAAMiE,QAAQ,IACpCzB,QAAS,OACTsB,eAAgB,SAChBR,WAAY,SACZsU,SAAU,SACV5U,QAAS,GAAGhD,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,OAElDkhC,WAAW/iC,EAAAA,EAAAA,KAAI,CACb+oB,WAAYnrB,EAAMiE,QAAQ,KAGhC,C,ijBApDE,CADW6gC,GACGzgC,aAAY,EAAGD,YAC3B,MAAM,OAAE0C,EAAM,MAAEhB,EAAK,KAAEmC,EAAI,QAAEpF,GAAYuB,EAAM7D,WACzCW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO4E,OACrB,kBAAClC,EAAAA,KAAIA,CAACC,KAAMkhC,GAAQ98B,GAAOkK,KAAK,OAChC,kBAACoO,OAAAA,CAAK/e,UAAWN,EAAOikC,WAAYr/B,IAEtC,kBAAC++B,GAAkBA,CAAC/9B,OAAQA,EAAQmB,KAAMA,EAAMpF,QAASA,I,ICS1D,MAAMuiC,WAAuBjhC,EAAAA,GAClCyN,WAAAA,CAAY1K,G,QACV2C,MAAM,IACJqP,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BoL,WAAYC,EAAAA,GACZjL,QAAS,E,EAAC,IAAEvC,MAAO,IAAKE,UAAW,UAAWC,UAAW,QAASC,MAAO,IAAOrI,EAAMkI,O,EAAK,CAAEi2B,UAAW,G,6VAEvGn+B,IAGLF,KAAKqD,sBAAqB,KACxB,MAAM7C,EAAO0C,EAAAA,GAAWC,QAAQnD,MAEhCA,KAAKsD,MAAMC,IACT/C,EAAKgD,kBAAkBhD,I,IACjBA,EAA0CA,EAmDnCA,EAjDPA,EzCcmB4V,EyChBvB,IAAa,QAAT5V,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,OAAiB,QAATlD,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaE,UAC9E,IACW,QAATnD,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,MACL,IAA5BlD,EAAKA,KAAKV,OAAOC,QAA+C,IAA/BS,EAAKA,KAAKV,OAAO,GAAGC,QAajD,GAAIS,EAAKA,KAAKV,OAAOC,OAAS,EAAG,C,IAa3BS,EAZX,GAAmB,mBAAfN,EAAMe,MAA6Bf,EAAMo+B,oBAC3Ct+B,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAIy9B,GAAoB,CACtBh+B,OAAQU,EAAKA,KAAKV,OAClBhB,MAAOoB,EAAMpB,MACbmC,KAAMf,EAAMe,iBAKf,IAAa,QAATT,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAaC,KAAM,C,IACtBlD,EAAAA,EAA3B,IAAIuV,EAAWoC,GAA6B,QAAjB3X,EAAS,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWV,cAAXU,IAAAA,EAAAA,EAAqB,IAChD,GAAIuV,aAAAA,EAAAA,EAAUhW,OAAQ,CACpB,MAAM,YAAEsY,GAAgBE,GAAuBxC,G,IAOqB7V,EALpEF,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAI+9B,GAAe,CACjBh2B,MAAO,CACLA,MAAO,oCAAoCiQ,KAA2B,QAAZnY,EAAAA,EAAMqC,cAANrC,IAAAA,EAAAA,EAAgB,OAE5EpB,MAAOoB,EAAMpB,MACbmC,KAAMf,EAAMe,KACZq9B,qBAAqB,QAK/B,CACF,CACF,OA7CEt+B,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAIy9B,GAAoB,CACtBjiC,SzCOWua,EyCPelW,EAAMpB,MAAMklB,czCQnD,sEAAsE5N,MyCPzDtX,MAAOoB,EAAMpB,MACbmC,KAAMf,EAAMe,iBAwCJ,QAATT,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWN,SAAUuD,EAAAA,aAAapL,MAC3C2H,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBzW,SAAU,CACR,IAAIy9B,GAAoB,CACtBjiC,QAAS0iC,GAAgB/9B,GACzB1B,MAAOoB,EAAMpB,MACbmC,KAAMf,EAAMe,YAMpBjB,KAAKI,SAAS,CACZiR,MAAO,IAAIyF,EAAAA,GAAgB,CACzBza,UAAW,SACX21B,UAAWC,GACXn5B,OAAQm5B,GACR5xB,SAAU,CACR,IAAI/C,EAAkB,CACpBC,UAAW,IAAM2G,WAK3B,IACF,GAGN,EAkBF,SAAS9J,KACP,MAAO,CACL8B,WAAWd,EAAAA,EAAAA,KAAI,CACbygB,SAAU,QACVhjB,MAAO,2BAGb,CAvBE,GAjGWulC,GAiGG/gC,aAAY,EAAGD,YAC3B,MAAM,MAAEiU,GAAUjU,EAAM7D,WAClBW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,GAAKiX,EAIL,OACE,kBAACpV,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACmV,EAAMhU,UAAS,CAACD,MAAOiU,I,IAezB,MAAMnN,GAAoB,KAC/B,MAAMhK,GAASC,EAAAA,EAAAA,YAAWoK,IAE1B,OACE,kBAACtI,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO4E,OACrB,kBAAC+F,EAAAA,EAAQA,CAACC,MAAO,EAAGjM,MAAO,OAE7B,kBAACoD,MAAAA,CAAIzB,UAAWN,EAAOskC,iBACpB,IAAIh6B,MAAM,KAAK3M,KAAI,CAAC4M,EAAGC,IACtB,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAOukB,IAAK5d,IAAK6D,GAC/B,kBAACzI,MAAAA,CAAIzB,UAAWN,EAAOukC,SACrB,kBAAC55B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAAC7I,MAAAA,CAAIzB,UAAWN,EAAOwkC,UACrB,kBAAC75B,EAAAA,EAAQA,CAACC,MAAO,S,EAS/B,SAASP,GAAkBvL,GACzB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbuK,OAAQ,aAAa3M,EAAMuB,OAASvB,EAAM0E,OAAOiI,OAAOq4B,OAAShlC,EAAM0E,OAAOiI,OAAOgU,OACrF5b,aAAc/E,EAAMiE,QAAQ,IAC5BD,aAAchE,EAAMiE,QAAQ,GAC5BpE,MAAO,SAETiG,OAAO1D,EAAAA,EAAAA,KAAI,CACT4V,MAAOhY,EAAM0E,OAAO4N,KAAKzN,UACzB4H,gBAAiBzM,EAAM0E,OAAOE,WAAWC,UACzC+S,SAAU,SACV5U,QAAS,GAAGhD,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,KAChD8T,UAAW,WAEbytB,iBAAiBpjC,EAAAA,EAAAA,KAAI,CACnBY,QAAS,QAAQhD,EAAMiE,QAAQ,OAEjCwhB,KAAKrjB,EAAAA,EAAAA,KAAI,CACPI,QAAS,OACTsB,eAAgB,kBAElB2hC,SAASrjC,EAAAA,EAAAA,KAAI,CACX0e,OAAQ,QACRjhB,MAAO,UAET6lC,UAAUtjC,EAAAA,EAAAA,KAAI,CACZvC,MAAO,SAGb,CCjNO,MAAM8lC,GAAkB,IAC7B,kBAAClkC,MAAAA,CAAImkC,MAAM,6BAA6B/lC,MAAM,KAAKC,OAAO,KAAK+lC,QAAQ,YAAYtzB,KAAK,QACtF,kBAACuzB,OAAAA,CACCvQ,EAAE,u9CACFhjB,KAAK,UACLuM,YAAY,UAKLinB,GAAiB,IAC5B,kBAACtkC,MAAAA,CAAImkC,MAAM,6BAA6B/lC,MAAM,KAAKC,OAAO,KAAK+lC,QAAQ,YAAYtzB,KAAK,QACtF,kBAACuzB,OAAAA,CACCvQ,EAAE,u9CACFhjB,KAAK,UACLuM,YAAY,U,wcCTlB,MAAMknB,GAAiBlC,IAGrBA,EAAOmC,OAAOC,EAAAA,IACdpC,EAAOmC,OAAO,OAAOxmB,EAAAA,MACrBqkB,EAAOmC,OAAO,OAAOpmB,EAAAA,MACdikB,GAGIqC,GAAsB,KACjC,MAAMC,GAAUC,EAAAA,EAAAA,wBAEhB,MAAO,CACLC,aAAc,IAAMA,GAAaF,GACjCG,eAAiBC,GAAuBD,GAAeH,EAASI,GAChEC,eAAiBD,GAAuBC,GAAeL,EAASI,GAChEE,eAAgB,IAAMA,GAAeN,GACtC,EAqBUO,GAAqBH,IAChC,IAAKA,IAAaA,EAAS1C,OACzB,OAAOliB,EAAAA,GAGT,MAAMkiB,EAAS,IAAIlK,gBAAgB4M,EAAS1C,QACtC8C,EAAcpnC,OAAOqnC,YAAY/C,EAAO5N,WAExCvnB,EAAUm1B,EAAOgD,OAAO,OAAOnT,EAAAA,MAE/B9X,EAAM6F,EAAAA,QAAQC,UAAUC,EAAAA,I,yUAAoB,IAC7CglB,G,WAAAA,CACH,CAAC,OAAOjT,EAAAA,MAAgBhlB,I,wVAG1B,OAAOkN,CAAG,EAGNkrB,GAAAA,W,MAAe,cAAOX,EAAwBY,GAClD,UACQZ,EAAQ9G,QAAQ2H,EAAAA,GAAkB5iB,KAAKC,UAAU0iB,GACzD,CAAE,MAAO5nC,GACPklC,QAAQC,MAAM,uCAAwCnlC,EACxD,CACF,I,gBAN4BgnC,EAAwBY,G,gCAA9CD,GAQOT,GAAAA,W,MAAe,cAAOF,GACjC,IACE,MAAM3/B,QAAc2/B,EAAQc,QAAQD,EAAAA,IACpC,OAAIxgC,EACK4d,KAAKwE,MAAMpiB,GAEb,EACT,CAAE,MAAOrH,GAEP,OADAklC,QAAQC,MAAM,wCAAyCnlC,GAChD,EACT,CACF,I,gBAXmCgnC,G,gCAAtBE,GAaAI,GAAAA,W,MAAiB,cAAON,GACnC,MAAMI,EA3CC,CAAE1C,OADMkC,GAAc,IAAIpM,gBAAgB94B,OAAOouB,SAAS2K,SACzCpX,YA8CxB,aAFqBgkB,GAAeL,EAASI,WAGrCD,GAAeH,EAASI,IACvB,UAEDW,GAAYf,EAASI,IACpB,EAEX,I,gBAXqCJ,G,gCAAxBM,GAaPS,GAAAA,W,MAAc,cAAOf,EAAwBI,GACjD,MAAMQ,QAAkBV,GAAaF,GACrCY,EAAU3+B,KAAKm+B,SACTO,GAAaX,EAASY,EAC9B,I,gBAJ2BZ,EAAwBI,G,gCAA7CW,GAMOZ,GAAAA,W,MAAiB,cAAOH,EAAwBI,GAC3D,MACMY,SADwBd,GAAaF,IACD78B,QAAQ89B,IAAoBC,GAAkBd,EAAUa,WAC5FN,GAAaX,EAASgB,EAC9B,I,gBAJqChB,EAAwBI,G,gCAAhDD,GAMAE,GAAAA,W,MAAiB,cAAOL,EAAwBI,GAE3D,aADwBF,GAAaF,IACpBl5B,MAAMxE,GAAM4+B,GAAkBd,EAAU99B,IAC3D,I,gBAHqC09B,EAAwBI,G,gCAAhDC,GAKAa,GAAoB,CAACd,EAAoBa,KACpD,MAAME,EAAiBvB,GAAc,IAAIpM,gBAAgB4M,EAAS1C,SAC5D0D,EAAuBxB,GAAc,IAAIpM,gBAAgByN,EAAevD,SAExE2D,EAAY,OAAO9T,EAAAA,KACnB+T,EAAel8B,MAAMwF,KAAKu2B,EAAehoC,QAAQgK,QAAOo+B,GAAKA,IAAMF,IACnEG,EAAap8B,MAAMwF,KAAKw2B,EAAqBjoC,QAAQgK,QAAOo+B,GAAKA,IAAMF,IAG7E,GAAIC,EAAa3gC,SAAW6gC,EAAW7gC,OACrC,OAAO,EAIT,MAAM8gC,EAAeH,EAAaI,OAAMjgC,GACtC2/B,EAAqBO,IAAIlgC,IAAQ0/B,EAAezN,IAAIjyB,KAAS2/B,EAAqB1N,IAAIjyB,KAExF,IAAKggC,EACH,OAAO,EAIT,MAAMG,EAAkBT,EAAeT,OAAOW,GACxCQ,EAAgBT,EAAqBV,OAAOW,GAClD,OAAIO,EAAgBjhC,SAAWkhC,EAAclhC,QAMtCihC,EAAgBF,OAAMv+B,GAAU0+B,EAAc5lB,SAAS9Y,IAAQ,ECxI3D2+B,GAAe,EAAG1B,eAC7B,IAAI,WAAE/rB,EAAU,cAAEqb,EAAa,OAAEvnB,EAAM,QAAEI,GDkBV,CAAC63B,IAChC,IAAKA,IAAaA,EAAS1C,OACzB,MAAO,CAAErpB,WAAY,GAAIqb,cAAe,GAAInnB,QAAS,GAAIJ,OAAQ,IAGnE,MAAMu1B,EAAS,IAAIlK,gBAAgB4M,EAAS1C,Q,IACzBA,EACGA,EAEPA,EACf,MAAO,CAAErpB,WAJgC0tB,QAAtBrE,EAAAA,EAAOhK,IAAIqO,EAAAA,WAAXrE,IAAAA,EAAAA,EAA2B,GAIzBhO,cAH0BsS,QAAzBtE,EAAAA,EAAOhK,IAAIsO,EAAAA,WAAXtE,IAAAA,EAAAA,EAA8B,GAGhBn1B,QAFpBm1B,EAAOgD,OAAO,OAAOnT,EAAAA,MAAe3kB,KAAKq5B,EAAAA,IAEZ95B,OADA,QAA9Bu1B,EAAAA,EAAOhK,IAAI,OAAOlG,EAAAA,aAAlBkQ,IAAAA,EAAAA,EAAmC,GACG,EC5BAwE,CAAkB9B,GACvE,MAAMtlC,GAASC,EAAAA,EAAAA,YAAWC,IA2B1B,OAJAuN,EAPuC,EAACA,EAAiBmnB,KACvD,MAAMyS,EAfuB,CAACzS,IAC9B,MAAM0S,EnDKqB,CAAC3gC,GACvB8N,GAAqB5N,MAAM2kB,GAAWA,EAAOjmB,QAAUoB,ImDNzC4gC,CAAgB3S,GACnC,IAAK0S,IAAeA,EAAWj/B,OAC7B,MAAO,GAET,MAAMA,EAASi/B,EAAWj/B,OAE1B,OAAIA,EAAO1B,KAAO0B,EAAOqM,eAA6B2C,IAAjBhP,EAAO9C,MACnC,GAAG8C,EAAO1B,OAAO0B,EAAOqM,YAAYrM,EAAO9C,QAE7C,EAAE,EAKmBiiC,CAAuB5S,GACnD,IAAI6S,EAAeh6B,EAAQ0N,MAAMgsB,EAAAA,IAEjC,OADAM,EAAeA,EAAap/B,QAAOvB,GAAKA,IAAMugC,IACvCI,EAAa35B,KAAKq5B,EAAAA,GAAiB,EAGlCO,CAA+Bj6B,EAASmnB,GAClDnnB,EAAUA,EAAQkE,QAAQ,SAAU,OACpClE,EAAUA,EAAQkE,QAAQ4Z,EAAAA,GAAe,IAAI5Z,QAAQ2Z,EAAAA,GAAW,IAAI3Z,QAAQg2B,EAAAA,GAAY,IAGtF,kBAAC5lC,MAAAA,CAAI6C,MAAO6I,GACV,kBAAC1L,MAAAA,KACC,kBAACyF,IAAAA,KAAGogC,GAAoBv6B,IAAY,OAAI,kBAAC7F,IAAAA,KAAGotB,EAAcjjB,QAAQ,IAAK,MAAS,KAAG4H,EAAW,KAEhG,kBAACxX,MAAAA,CAAIzB,UAAWN,EAAOyN,SACpBA,GAAAA,EAMT,SAASvN,KACP,MAAO,CACLuN,SAASvM,EAAAA,EAAAA,KAAI,CACXgW,aAAc,WACdD,SAAU,SACV4wB,gBAAiB,EACjBvmC,QAAS,cACTwmC,gBAAiB,aAGvB,C,gUCjDO,MAAMC,GAAY,KACvB,MAAM/nC,GAASC,EAAAA,EAAAA,YAAWC,KACpB,aAAEklC,EAAY,eAAEC,GAAmBJ,MAClCa,EAAWD,IAAgBxmC,EAAAA,EAAAA,UAAqB,KAChDiqB,EAAW0e,IAAgB3oC,EAAAA,EAAAA,WAAkB,IAC7C4oC,EAAYC,IAAiB7oC,EAAAA,EAAAA,WAAkB,IAEtDC,EAAAA,EAAAA,YAAU,MACF6oC,W,MAAiB,gBACrBH,GAAa,GACb,IACE,MAAMI,QAAwBhD,IAC9BS,EAAauC,EACf,CAAE,MAAO/E,GACPD,QAAQC,MAAM,2BAA4BA,GAC1CwC,EAAa,GACf,CAAE,QACAmC,GAAa,EACf,CACF,I,kDAXMG,EAaNA,EAAgB,GACf,IAEH,MAAME,EAAAA,W,MAAwB,cAAO/C,EAAoB7lC,GACvDA,EAAMmb,kBACNstB,GAAc,GAEd,UACQ7C,EAAeC,GACrB,MAAMgD,QAAyBlD,IAC/BS,EAAayC,EACf,CAAE,MAAOjF,GACPD,QAAQC,MAAM,2BAA4BA,EAC5C,CAAE,QACA6E,GAAc,EAChB,CACF,I,gBAbqC5C,EAAoB7lC,G,gCAAnD4oC,GAeN,OAAI/e,EAEA,kBAACvnB,MAAAA,KACC,kBAACA,MAAAA,CAAIzB,UAAWN,EAAO0K,QACrB,kBAAC69B,KAAAA,KAAG,sBAEN,kBAACxmC,MAAAA,CAAIzB,UAAWN,EAAO6nB,SACrB,kBAAC2gB,EAAAA,mBAAkBA,CAACp3B,KAAK,2BAO/B,kBAACrP,MAAAA,KACC,kBAACA,MAAAA,CAAIzB,UAAWN,EAAO0K,QACrB,kBAAC69B,KAAAA,KAAG,sBAEgB,IAArBzC,EAAUjgC,OACT,kBAAC+3B,IAAAA,CAAEt9B,UAAWN,EAAOyoC,aAAa,qDAElC,kBAAC1mC,MAAAA,CAAIzB,UAAWN,EAAO8lC,WACpBA,EAAUnoC,KAAI,CAAC2nC,EAAoB96B,IAClC,kBAACzI,MAAAA,CACCzB,UAAWN,EAAOslC,SAClB3+B,IAAK6D,EACLqK,QAAS,IFwEK,CAACywB,IAC3Bh0B,GAAqBO,GAAkBE,KAAME,GAAoBF,KAAKsB,wBACtE,MAAMsH,EAAM8qB,GAAkBH,GAC9BhD,EAAAA,gBAAgBn7B,KAAKwT,EAAI,EE3EE+tB,CAAapD,IAE5B,kBAACvjC,MAAAA,CAAIzB,UAAWN,EAAO2oC,cACrB,kBAAC3B,GAAYA,CAAC1B,SAAUA,KAE1B,kBAACvjC,MAAAA,CAAIzB,UAAWN,EAAO4oC,QACrB,kBAACvzB,EAAAA,OAAMA,CACL7S,QAAQ,YACR6O,KAAK,OACLiE,KAAK,YACL0E,SAAUiuB,EACVpzB,QAAU3W,GAAMmqC,EAAsB/C,EAAUpnC,U,EAWlE,SAASgC,GAAUpB,GACjB,MAAO,CACL4L,QAAQxJ,EAAAA,EAAAA,KAAI,CACV2V,UAAW,SACX,GAAM,CACJ+I,OAAQ,KAGZkmB,WAAW5kC,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTm/B,SAAU,OACVp+B,IAAKvD,EAAMiE,QAAQ,GACnB6c,OAAQ,GAAG9gB,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,OAC/CH,eAAgB,WAElB0iC,UAAUpkC,EAAAA,EAAAA,KAAI,CACZI,QAAS,OACTuB,cAAe,SACfD,eAAgB,gBAChBuhB,OAAQ,UACRxlB,MAAO,QACP8M,OAAQ,aAAa3M,EAAM0E,OAAOiI,OAAOq4B,SACzCjgC,aAAc/E,EAAMgF,MAAMC,OAAOC,QAEjC,UAAW,CACTuH,gBAAiBzM,EAAMuB,OAASvB,EAAM0E,OAAOE,WAAWC,UAAY7E,EAAM0E,OAAOE,WAAW8H,WAGhGm9B,cAAcznC,EAAAA,EAAAA,KAAI,CAChBY,QAAS,GAAGhD,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,UAAUjE,EAAMiE,QAAQ,OACxEkU,SAAU,WAEZxJ,SAASvM,EAAAA,EAAAA,KAAI,CACXgW,aAAc,WACdD,SAAU,SACV4wB,gBAAiB,EACjBvmC,QAAS,cACTwmC,gBAAiB,aAEnBc,QAAQ1nC,EAAAA,EAAAA,KAAI,CACVI,QAAS,OACTsB,eAAgB,aAElB6lC,aAAavnC,EAAAA,EAAAA,KAAI,CACf0e,OAAQ,GAAG9gB,EAAMiE,QAAQ,QAAQjE,EAAMiE,QAAQ,OAC/C8T,UAAW,WAEbgR,SAAS3mB,EAAAA,EAAAA,KAAI,CACXI,QAAS,OACTsB,eAAgB,SAChBgd,OAAQ,GAAG9gB,EAAMiE,QAAQ,SAG/B,CCnIO,MAAM8lC,WAAoB5lC,EAAAA,IAgFjC,SAAS/C,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,GACnBF,cAAe,SACf+c,OAAQ,OAAO9gB,EAAMiE,QAAQ,OAC7BH,eAAgB,WAElB8H,QAAQxJ,EAAAA,EAAAA,KAAI,CACVI,QAAS,OACTc,WAAY,SACZmJ,gBAAiBzM,EAAMuB,OAASvB,EAAM0E,OAAOE,WAAWC,UAAY7E,EAAM0E,OAAOE,WAAW8H,QAC5F3H,aAAc/E,EAAMiE,QAAQ,IAC5B09B,SAAU,OACV79B,eAAgB,SAChBd,QAAShD,EAAMiE,QAAQ,GACvBV,IAAKvD,EAAMiE,QAAQ,KAErB+lC,sBAAsB5nC,EAAAA,EAAAA,KAAI,CACxBI,QAAS,OACTc,WAAY,WAEdwC,OAAO1D,EAAAA,EAAAA,KAAI,CACT0e,OAAQ,SAAS9gB,EAAMiE,QAAQ,OAGjCgmC,eAAe7nC,EAAAA,EAAAA,KAAI,CACjBkB,WAAY,SACZQ,eAAgB,aAChBtB,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,KAErBimC,mBAAmB9nC,EAAAA,EAAAA,KAAI,CACrBmjB,eAAgB,YAChB,UAAW,CACTA,eAAgB,eAIpB4kB,WAAW/nC,EAAAA,EAAAA,KAAI,CACb2V,UAAW,SACX,GAAM,CACJ+I,OAAQ,QAAQ9gB,EAAMiE,QAAQ,UAIlCqB,OAAOlD,EAAAA,EAAAA,KAAI,CACTwV,SAAU,SAEZwyB,sBAAsBhoC,EAAAA,EAAAA,KAAI,CACxBkB,WAAY,SACZC,IAAKvD,EAAMiE,QAAQ,GACnBzB,QAAS,OACTsB,eAAgB,gBAChBjE,MAAO,SAETqgC,WAAW99B,EAAAA,EAAAA,KAAI,CACbI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,KAErB8vB,UAAU3xB,EAAAA,EAAAA,KAAI,CACZI,QAAS,OACTe,IAAKvD,EAAMiE,QAAQ,KAGzB,C,0HChKO,SAAeomC,GAAmBr0B,G,OAAnBq0B,GAAAA,MAAAA,KAAAA,U,UAAAA,K,eAAf,UAAkCr0B,GACvC,MAAMs0B,EAAQpgC,EAAAA,GAAWyoB,YAAY3c,EAAUwkB,EAAAA,IACzC+P,QAAoBhQ,EAAAA,EAAAA,oBAAmBT,IAAIwQ,GACjD,KAAMC,aAAuBC,EAAAA,uBAE3B,MADAlG,QAAQC,MAAM,IAAIllC,MAAM,4CAClB,IAAIA,MAAM,2CAGlB,MAAMqd,EAAa6tB,EACnB,GAAI7tB,GAAcA,EAAWge,WAAY,CACvC,MAAME,QAAgBle,EAAWge,aAEjC,OAAIlvB,MAAMi/B,QAAQ7P,GAET,CAAE/nB,SAAS,EAAM9J,QAWHxJ,EAZWq7B,EAoB7B,IAPoBr7B,EAAKgK,QAAQo+B,I,IAAMA,E,OAAM,QAANA,EAAAA,EAAEr1B,YAAFq1B,IAAAA,OAAAA,EAAAA,EAAQtlB,SAASoK,EAAAA,GAAc,OACtDltB,EAAKgK,QAAQo+B,I,IAAMA,E,OAAM,QAANA,EAAAA,EAAEr1B,YAAFq1B,IAAAA,OAAAA,EAAAA,EAAQtlB,SAASmK,EAAAA,GAAU,OAC7CjtB,EAAKgK,QAAQo+B,I,IAC3BA,EAAoCA,EACtCA,EAAiCA,EADvC,SAAc,QAANA,EAAAA,EAAEr1B,YAAFq1B,IAAAA,OAAAA,EAAAA,EAAQtlB,SAASoK,EAAAA,OAAyB,QAANkb,EAAAA,EAAEr1B,YAAFq1B,IAAAA,OAAAA,EAAAA,EAAQtlB,SAASmK,EAAAA,OACjD,QAANmb,EAAAA,EAAEr1B,YAAFq1B,IAAAA,OAAAA,EAAAA,EAAQtlB,SAASwmB,EAAAA,OAAsB,QAANlB,EAAAA,EAAEr1B,YAAFq1B,IAAAA,OAAAA,EAAAA,EAAQtlB,SAASqoB,EAAAA,OACyB,IAA5Exd,EAAAA,GAAkB2H,OAAO8V,EAAAA,IAA6BxuB,QAAQwrB,EAAEr1B,MAAa,QAfhFgyB,QAAQC,MAAM,IAAIllC,MAAM,yCACjB,CAAE0J,OAAQ,IAErB,CAMK,IAAoBxJ,EAJvB,OADA+kC,QAAQC,MAAM,IAAIllC,MAAM,uDACjB,CAAE0J,OAAQ,GAErB,EAvBsBshC,G,gLAAAA,GAAAA,MAAAA,KAAAA,U,+GDepB,CADWN,GACG1lC,aAAY,EAAGD,YAC3B,MAAM6O,E9CgBH,SAAsB7O,GAC3B,OAAO8F,EAAAA,GAAW0gC,YAAYxmC,EAAOymC,GACvC,C8ClBiBC,CAAa1mC,GACpB2mC,GAAWC,EAAAA,GAAAA,gBACX,SAAEjX,GAAa9gB,EAAK1S,WACpBW,GAASC,EAAAA,EAAAA,YAAWC,IACpBpB,GAAQC,EAAAA,EAAAA,aAERkiC,EAAajI,GAAsBjnB,GACnCg4B,EAAiBC,GAAsBj4B,GAE7C,OACE,kBAAChQ,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAACD,MAAAA,CAAIzB,UAAWN,EAAO0K,QACrB,kBAAC3I,MAAAA,CAAIzB,UAAWN,EAAO8oC,sBACpBhqC,EAAMuB,OAAS,kBAACwkC,GAAcA,MAAM,kBAACJ,GAAeA,MACrD,kBAACwF,KAAAA,CAAG3pC,UAAWN,EAAO4E,OAAO,mCAE/B,kBAAC7C,MAAAA,KACC,kBAAC67B,IAAAA,KAAE,oEACH,kBAAC77B,MAAAA,CAAIzB,UAAWN,EAAO+oC,eACrB,kBAAC1zB,EAAAA,OAAMA,CAAC7S,QAAQ,UAAUqS,QAAS,KAC/BvD,GAAqBO,GAAkBE,KAAME,GAAoBF,KAAKmB,wBACtE22B,EAASnpB,EAAAA,GAAmB,GAC3B,cAEH,kBAAChe,EAAAA,KAAIA,CAACC,KAAK,cAAcsO,KAAK,QAEhC,kBAAC4Y,EAAAA,WAAUA,CACTvU,KAAK,oBACLjE,KAAK,OACLJ,KAAM,KACN6J,OAAQ,SACRE,KACE,sFAEF1a,UAAWN,EAAOgpC,kBAClBn0B,QAAS,IAAMvD,GAAqBO,GAAkBE,KAAME,GAAoBF,KAAKoB,6BACtF,yBAOP,kBAAC40B,GAASA,MAEV,kBAAChmC,MAAAA,CAAIzB,UAAWN,EAAOipC,WACrB,kBAACV,KAAAA,KAAG,0CAGN,kBAACrmC,EAAAA,MAAKA,CAACG,IAAK,GACV,kBAACN,MAAAA,CAAIzB,UAAWN,EAAOkpC,sBACrB,kBAACnnC,MAAAA,CAAIzB,UAAWN,EAAOg/B,WACpBiC,GACC,kBAAC/+B,EAAAA,MAAKA,CAACG,IAAK,EAAGD,WAAY,UACzB,kBAACL,MAAAA,CAAIzB,UAAWN,EAAOoE,OAAO,eAC9B,kBAAC68B,EAAW99B,UAAS,CAACD,MAAO+9B,KAGhC8I,GACC,kBAAC7nC,EAAAA,MAAKA,CAACG,IAAK,EAAGD,WAAY,UACzB,kBAACL,MAAAA,CAAIzB,UAAWN,EAAOoE,OAAO,UAC9B,kBAAC2lC,EAAe5mC,UAAS,CAACD,MAAO6mC,MAKvC,kBAAChoC,MAAAA,CAAIzB,UAAWN,EAAO6yB,UACpBA,aAAAA,EAAAA,EAAUl1B,KAAKkkC,GACd,kBAACA,EAAQ1+B,UAAS,CAACwD,IAAKk7B,EAAQ77B,MAAMW,IAAKzD,MAAO2+B,S,6HE9C3D,MAAM8H,WAAa1mC,EAAAA,GAYhBkT,WAAAA,GACN,MAAMvG,EAAiB5G,EAAAA,GAAW6G,aAAa/J,MACzCikC,EAAiBC,GAAsBlkC,MAC7CikC,EAAe7jC,SAAS,CACtBijC,mBAAoBA,KAGtBnQ,GAAsBlzB,MAAMwD,kBAAkBW,IACxCA,EAAS1E,OACX44B,aAAaC,QAAQC,EAAAA,GAAmBp0B,EAAS1E,MAAMgc,WACzD,IAGFyoB,GAAsBlkC,MAAMwD,kBAAiB,CAACW,EAAUC,KACtD,GAAID,EAASwD,UAAYvD,EAAUuD,QAAS,CAC1C3H,KAAKokC,YAAYt6B,EAAgB3F,EAASwD,SAG1C0wB,aAAaC,QAAQ+L,EAAAA,GAAyBhnB,KAAKC,UAAUnZ,EAASwD,UAEtE,MAAM28B,EAAangC,EAASwD,QAAQpF,QAAQvB,IAAOoD,EAAUuD,QAAQ5G,MAAMwjC,GAAOA,EAAG1jC,MAAQG,EAAEH,QAC3FyjC,EAAWvkC,OAAS,GACtByL,GAAqBO,GAAkBE,KAAME,GAAoBF,KAAKqB,eAAgB,CACpFzM,IAAKyjC,EAAW,GAAGzjC,KAGzB,KAGFiJ,EAAetG,kBAAiB,CAACW,EAAUC,KACrCD,EAAS1E,MAAMuK,OAAS5F,EAAU3E,MAAMuK,MAAQ7F,EAAS1E,MAAMyK,KAAO9F,EAAU3E,MAAMyK,IACxFlK,KAAKokC,YAAYt6B,EAAgBm6B,EAAe/jC,MAAMyH,QACxD,IAEF3H,KAAKokC,YAAYt6B,EAAgBm6B,EAAe/jC,MAAMyH,QACxD,CAEAy8B,WAAAA,CAAYt6B,EAAoCnC,GAC9C,MAAMqC,EAAOF,EAAe5J,MAAMT,MAAMuK,KAAKC,OACvCC,EAAKJ,EAAe5J,MAAMT,MAAMyK,GAAGD,OAEnC0lB,EAAY,IADN3mB,EAAAA,GAAAA,UAASkB,EAAKF,EAAM,KACPI,eACnBo6B,EDxDH,SAAmC78B,GACxC,MAAMivB,EAAOjvB,EACVpF,QAAQvB,GAAMA,EAAEH,KAAOG,EAAE4N,UAAY5N,EAAEvB,QACvC5H,KAAK0K,GAKW,CAACA,IACpB,IAAKA,EACH,MAAO,GAGT,IAAI4tB,EAAM5tB,EAAO9C,MACjB,OAAI0wB,SAA6C,KAARA,EAChC,IAGJ0G,GAAS1wB,KAAKgqB,IAAS,CAAC,QAAQ9U,SAAS9Y,EAAO1B,MAChC,iBAARsvB,GAAqBA,EAAI5U,WAAW,MAAS4U,EAAI2G,SAAS,OACnE3G,EAAM,IAAIA,MAIP,GAAG5tB,EAAO1B,MAAM0B,EAAOqM,WAAWuhB,IAAK,EArB3B4G,CAAax0B,KAC7ByF,KAAKq5B,EAAAA,IACR,OAAOzK,EAAK72B,OAAS,MAAM62B,IAAS,EACtC,CCkD4BD,CAA0BhvB,GAElD3H,KAAKI,SAAS,CACZD,KAAM,IAAI6D,EAAAA,GAAmB,CAC3B3D,SAAU,CACR,IAAI2D,EAAAA,GAAmB,CACrBwnB,SAAU,cACVhmB,UAAW,EACXD,OAAQ,EACRlF,SAAU,CACR,IAAIoS,EAAAA,GAAiB,CACnBtS,KAAM,IAAIi+B,GAAe,CACvBh2B,MAAO,CACLA,MAAO,0CAA0Co8B,oDACjDh6B,KAAMmlB,GAER7wB,MAAO,mBACPmC,KAAM,uBAGV,IAAIwR,EAAAA,GAAiB,CACnBtS,KAAM,IAAIi+B,GAAe,CACvBh2B,MAAO,CACLA,MAAO,wBAAwBo8B,oEAC/Bh6B,KAAMmlB,GAER7wB,MAAO,gBACPmC,KAAM,uBAGV,IAAIwR,EAAAA,GAAiB,CACnBtS,KAAM,IAAIi+B,GAAe,CACvBh2B,MAAO,CACLA,MAAO,sBAAsBo8B,sCAE/B1lC,MAAO,cACPmC,KAAM,iBACNsB,OAAQiiC,aAQxB,CAlGA,YAAmBtkC,G,IAEHA,EACAA,EACFA,EA6GQ64B,EAAuCD,EAhH3Dj2B,M,uUAAM,EACJsxB,WAA4B,QAAhBj0B,EAAAA,EAAMi0B,kBAANj0B,IAAAA,EAAAA,EAAoB,IAAI60B,EAAAA,GAAe,CAAC,GACpDiE,WAA4B,QAAhB94B,EAAAA,EAAM84B,kBAAN94B,IAAAA,EAAAA,GA8GM64B,EA9G6B74B,EAAM64B,eA8GID,EA9GY54B,EAAM44B,UA+GxE,IAAIG,EAAAA,GAAiB,CAC1BC,UAAW,CACT,IAAIC,EAAAA,GAAmB,CACrBt8B,KAAMu7B,EAAAA,GACN95B,MAAO,cACPmB,MAAOq5B,EACPM,SAAU,UAEZ,IAAIC,EAAAA,GAAqB,CACvBx8B,KAAM4nC,EAAAA,GACN/uB,WAAYC,EAAAA,GACZwR,OAAQ,WACRxf,QAASoxB,EACTW,kBAAkB,QA3HpB3M,SAAwB,QAAd7sB,EAAAA,EAAM6sB,gBAAN7sB,IAAAA,EAAAA,EAAkB,CAAC,IAAI85B,EAAAA,GAAgB,CAAC,GAAI,IAAIC,EAAAA,GAAmB,CAAC,KAC3E/5B,IAGLF,KAAKqD,qBAAqBrD,KAAKqQ,YAAYxF,KAAK7K,MAClD,EA4HF,SAAS5F,GAAUpB,GACjB,MAAO,CACLkD,WAAWd,EAAAA,EAAAA,KAAI,CACb0e,OAAQ,GAAG9gB,EAAMiE,QAAQ,UACzBpE,MAAO,MAEP,4BAA6B,CAC3BA,MAAO,SAIf,ChD3JO,SAASskB,GAAyB/f,GACvC,OAAO8F,EAAAA,GAAW0gC,YAAYxmC,EAAO66B,GACvC,CAMO,SAAS1kB,GAAuBnW,GACrC,OAAO8F,EAAAA,GAAW0gC,YAAYxmC,EAAOs1B,GACvC,CAkBO,SAAS6L,GAAgB/9B,G,IACvBA,EAAAA,EAAAA,EAAP,OAAiC,QAA1BA,EAAAA,SAAU,QAAVA,EAAAA,EAAMA,YAANA,IAAAA,GAAiB,QAAjBA,EAAAA,EAAY+8B,aAAZ/8B,IAAAA,OAAAA,EAAAA,EAAmB3E,eAAnB2E,IAAAA,EAAAA,EAA8B,iCACvC,CAeO,SAAS0c,GAAclD,GAC5B,OAAO9W,EAAAA,GAAWyoB,YAAY3R,EAAawZ,EAAAA,GAC7C,CAMO,SAASzF,GAAuB7P,GACrC,OAAOA,EAAWrmB,KAAK6Y,IAAe,CAAEpS,MAAOoS,EAAWjR,MAAOiR,KACnE,CAiBO,SAAStB,GAAcvN,EAAkBsN,G,IAC/BtN,EAAf,MAAMT,EAA6C,QAApCS,EAAAA,EAAMf,OAAOC,MAAMC,GAAiB,WAAXA,EAAEC,cAA3BY,IAAAA,OAAAA,EAAAA,EAA+CT,OAE9D,IAAKA,EACH,MAAO,YAGT,MAAM7I,EAAOC,OAAOD,KAAK6I,GAAQmB,QAAQo+B,GAAY,MAANA,IAC/C,OAAoB,IAAhBpoC,EAAKwH,OACA,YAGFqB,EAAO+N,GAAa5W,EAAK,IAAIsT,QAAQ,KAAM,GACpD,CAEO,SAASzJ,GAAmBwH,GACjC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAeqhB,EAAAA,EAAajwB,GACxD,KAAMoF,aAAoB0J,EAAAA,IACxB,MAAM,IAAIrgB,MAAM,+BAElB,OAAO2W,CACT,CAEO,SAASgP,GAA2BpU,GACzC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAeuhB,EAAAA,GAAuBnwB,GAClE,KAAMoF,aAAoB0J,EAAAA,IACxB,MAAM,IAAIrgB,MAAM,wCAElB,OAAO2W,CACT,CAkBO,SAAS2E,GAAkB/J,GAChC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAeoU,EAAAA,GAAYhjB,GACvD,KAAMoF,aAAoB0J,EAAAA,IACxB,MAAM,IAAIrgB,MAAM,6BAElB,OAAO2W,CACT,CAEO,SAASC,GAAmBrF,GACjC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAemU,EAAAA,GAAa/iB,GACxD,KAAMoF,aAAoBqqB,EAAAA,IACxB,MAAM,IAAIhhC,MAAM,8BAElB,OAAO2W,CACT,CAEO,SAAS6f,GAAyBjlB,GACvC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAe8W,EAAAA,GAAoB1lB,GAC/D,KAAMoF,aAAoB0nB,IACxB,MAAM,IAAIr+B,MAAM,qCAElB,OAAO2W,CACT,CAEO,SAASk1B,GAAsBt6B,GACpC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAeisB,EAAAA,GAAiB76B,GAC5D,KAAMoF,aAAoBqqB,EAAAA,IACxB,MAAM,IAAIhhC,MAAM,kCAElB,OAAO2W,CACT,CAEO,SAASkkB,GAAsBtpB,GACpC,MAAMoF,EAAW9L,EAAAA,GAAWsV,eAAe4f,EAAAA,GAAgBxuB,GAC3D,KAAMoF,aAAoBmqB,EAAAA,IACxB,MAAM,IAAI9gC,MAAM,iCAElB,OAAO2W,CACT,CAQO,SAASwE,GAAoBie,GAClC,MAAe,eAARA,GAAgC,cAARA,CACjC,CAMO,SAASvZ,GAAoB1X,G,IAC3BA,EAAAA,EAAAA,EAAP,OAA6E+Q,QAAtE/Q,EAAAA,SAAU,QAAVA,EAAAA,EAAMA,YAANA,IAAAA,GAA4B,QAA5BA,EAAAA,EAAYV,OAAO,GAAGgB,cAAtBN,IAAAA,OAAAA,EAAAA,EAA8B0F,MAAMxD,GAAMA,EAAEX,OAAO++B,OAAO1oC,QAAYmZ,IAANnZ,aAAhEoI,IAAAA,GAAAA,CACT,CgDhEE,GArGWqjC,GAqGJxmC,aAAY,EAAGD,YACpB,MAAM,KAAE+C,GAAS/C,EAAM7D,WACjBW,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAAC6B,MAAAA,CAAIzB,UAAWN,EAAOgC,WACrB,kBAAC6mC,GAAY1lC,UAAS,CAACD,MAAOA,IAC7B+C,GAAQ,kBAACA,EAAK9C,UAAS,CAACD,MAAO+C,I,IhD2DjC,MAAM02B,GAAW,gBAEXjL,GAAoBnsB,GAC1Bo3B,GAAS1wB,KAAK1G,IAA2B,iBAAVA,GAAuBA,EAAM8b,WAAW,MAAS9b,EAAMq3B,SAAS,KAG7Fr3B,EAFE,IAAIA,KAKFqiC,GAAuB4C,I,IAAgBA,E,OAAAA,SAAQ,QAARA,EAAAA,EAAM,UAANA,IAAAA,OAAAA,EAAAA,EAAUC,gBAAgBD,aAAAA,EAAAA,EAAK1iC,MAAM,KAAM,EAAE,EAEpF6T,GAAgBjM,GACpB,CAACwL,EAAiBmH,KACvB3S,EAAM9F,aAAa,IAAI0Y,EAAAA,GAAiB,CAAEpH,UAASmH,YAAW,EAAK,EiDtMvE,GAX6B,K,IAGJqoB,EAAvB,MAAMC,EAAwCD,QAAvBA,EAAuBA,OAAvBA,EAAAA,8BAAuBA,IAAvBA,EAAAA,6BAAuBA,GAAvBA,EAAAA,EAAAA,kCAAAA,IAAAA,EAAAA,EAA+B,CAAC,EAEjDE,EAAYzM,aAAa6H,QAAQ3H,EAAAA,KAAsB,IACtDve,IAAezgB,EAAAA,EAAAA,WjDyBau/B,EiDzBiBgM,EjDyBG/L,EiDsBzD,SAA2B3iB,GACzB,MAAM2uB,EAASC,GAAqBC,UAAU7uB,GAC9C,GAAK2uB,EAAO15B,QAIZ,OAAO05B,EAAOvkC,KAAKmH,OACrB,CAtDiEu9B,CAAkBL,EAAeM,gBjD0BzF,IAAIlN,GAAiB,CAC1Ba,YACAC,eAAgBA,QAAAA,EAAkB,GAClC5E,WAAY,IAAIY,EAAAA,GAAe,CAAE/qB,KAAM,UAAWE,GAAI,YAJnD,IAA8B4uB,EAAoBC,EiDvBvD,OAAO,kBAACqM,GAAAA,CAAqBprB,YAAaA,G,EAKrC,SAASorB,IAAqB,YAAEprB,IACrC,MAAOqrB,EAAeC,GAAoBpK,IAAAA,UAAe,GAUzD,OARA1hC,EAAAA,EAAAA,YAAU,KACH6rC,IACHC,GAAiB,GAEjB95B,GAAqBO,GAAkBG,OAAQC,GAAoBD,OAAOwB,iBAC5E,GACC,CAACsM,EAAaqrB,IAEZA,EAKH,kBAACE,EAAAA,GAAsBA,CAAC37B,MAAOoQ,EAAawrB,iBAAiB,EAAMC,2BAA2B,GAC5F,kBAACzrB,EAAY3c,UAAS,CAACD,MAAO4c,KALzB,IAQX,CAEA,MAAM0rB,GAA4BC,EAAAA,GAAAA,OAAS,CACzC9kC,IAAK8kC,EAAAA,GAAAA,SACL/2B,SAAU+2B,EAAAA,GAAAA,SACVlmC,MAAOkmC,EAAAA,GAAAA,WAGHX,GAAuBW,EAAAA,GAAAA,OAAS,CACpCh+B,QAASg+B,EAAAA,GAAAA,MAAQD,K", "sources": ["webpack://grafana-exploretraces-app/../node_modules/moment/locale/ sync ^\\.\\/.*$", "webpack://grafana-exploretraces-app/./components/states/EmptyState/useMousePosition.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/GrotNotFound.tsx", "webpack://grafana-exploretraces-app/./components/states/EmptyState/EmptyState.tsx", "webpack://grafana-exploretraces-app/./utils/testIds.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/EmptyStateScene.tsx", "webpack://grafana-exploretraces-app/./components/states/LoadingState/LoadingStateScene.tsx", "webpack://grafana-exploretraces-app/./components/states/ErrorState/ErrorStateScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/Search.tsx", "webpack://grafana-exploretraces-app/./components/Explore/ByFrameRepeater.tsx", "webpack://grafana-exploretraces-app/./utils/frames.ts", "webpack://grafana-exploretraces-app/./components/Explore/panels/barsPanel.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/generateMetricsQuery.ts", "webpack://grafana-exploretraces-app/./utils/dates.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/StepQueryRunner.ts", "webpack://grafana-exploretraces-app/./components/Explore/StreamingIndicator.tsx", "webpack://grafana-exploretraces-app/./utils/analytics.ts", "webpack://grafana-exploretraces-app/./components/Explore/panels/histogram.ts", "webpack://grafana-exploretraces-app/./pages/Explore/primary-signals.ts", "webpack://grafana-exploretraces-app/./components/Explore/actions/AddToFiltersAction.tsx", "webpack://grafana-exploretraces-app/./utils/comparison.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/HighestDifferencePanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/layouts/allComparison.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/histogram.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/DurationComparisonControl.tsx", "webpack://grafana-exploretraces-app/./utils/utils.ts", "webpack://grafana-exploretraces-app/./utils/exemplars.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/REDPanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/actions/ShareExplorationAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpanListColumnsSelector.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpanListScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpansScene.tsx", "webpack://grafana-exploretraces-app/./utils/trace-merge/utils.ts", "webpack://grafana-exploretraces-app/./utils/trace-merge/tree-node.ts", "webpack://grafana-exploretraces-app/./utils/trace-merge/merge.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Structure/StructureScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/GroupBySelector.tsx", "webpack://grafana-exploretraces-app/./components/Explore/LayoutSwitcher.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/linesPanel.ts", "webpack://grafana-exploretraces-app/./components/Explore/actions/AddToInvestigationButton.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/PanelMenu.tsx", "webpack://grafana-exploretraces-app/./components/Explore/layouts/attributeBreakdown.ts", "webpack://grafana-exploretraces-app/./components/Explore/behaviors/syncYaxis.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/AttributesDescription.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/AttributesBreakdownScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/BreakdownScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/comparisonQuery.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/attributeComparison.ts", "webpack://grafana-exploretraces-app/./components/Explore/actions/InspectAttributeAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Comparison/AttributesComparisonScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Comparison/ComparisonScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/TabsBarScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/MiniREDPanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/TracesByServiceScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/TraceViewPanelScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/TraceDrawerScene.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/PrimarySignalVariable.tsx", "webpack://grafana-exploretraces-app/./utils/filters-renderer.ts", "webpack://grafana-exploretraces-app/./components/Explore/TraceQLIssueDetector.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/TraceExploration.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelRow.tsx", "webpack://grafana-exploretraces-app/./components/Home/ErroredServicesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/SlowestTracesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/SlowestServicesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelScene.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanel.tsx", "webpack://grafana-exploretraces-app/./utils/rockets.tsx", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/utils.ts", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/BookmarkItem.tsx", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/Bookmarks.tsx", "webpack://grafana-exploretraces-app/./components/Home/HeaderScene.tsx", "webpack://grafana-exploretraces-app/./pages/Home/utils.ts", "webpack://grafana-exploretraces-app/./pages/Home/Home.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/TraceExplorationPage.tsx"], "sourcesContent": ["var map = {\n\t\"./af\": 9805,\n\t\"./af.js\": 9805,\n\t\"./ar\": 4449,\n\t\"./ar-dz\": 4468,\n\t\"./ar-dz.js\": 4468,\n\t\"./ar-kw\": 3480,\n\t\"./ar-kw.js\": 3480,\n\t\"./ar-ly\": 4197,\n\t\"./ar-ly.js\": 4197,\n\t\"./ar-ma\": 2180,\n\t\"./ar-ma.js\": 2180,\n\t\"./ar-ps\": 9343,\n\t\"./ar-ps.js\": 9343,\n\t\"./ar-sa\": 230,\n\t\"./ar-sa.js\": 230,\n\t\"./ar-tn\": 2808,\n\t\"./ar-tn.js\": 2808,\n\t\"./ar.js\": 4449,\n\t\"./az\": 5865,\n\t\"./az.js\": 5865,\n\t\"./be\": 6627,\n\t\"./be.js\": 6627,\n\t\"./bg\": 901,\n\t\"./bg.js\": 901,\n\t\"./bm\": 3179,\n\t\"./bm.js\": 3179,\n\t\"./bn\": 1966,\n\t\"./bn-bd\": 969,\n\t\"./bn-bd.js\": 969,\n\t\"./bn.js\": 1966,\n\t\"./bo\": 6317,\n\t\"./bo.js\": 6317,\n\t\"./br\": 6474,\n\t\"./br.js\": 6474,\n\t\"./bs\": 5961,\n\t\"./bs.js\": 5961,\n\t\"./ca\": 7270,\n\t\"./ca.js\": 7270,\n\t\"./cs\": 1564,\n\t\"./cs.js\": 1564,\n\t\"./cv\": 3239,\n\t\"./cv.js\": 3239,\n\t\"./cy\": 2366,\n\t\"./cy.js\": 2366,\n\t\"./da\": 2453,\n\t\"./da.js\": 2453,\n\t\"./de\": 6601,\n\t\"./de-at\": 5027,\n\t\"./de-at.js\": 5027,\n\t\"./de-ch\": 8101,\n\t\"./de-ch.js\": 8101,\n\t\"./de.js\": 6601,\n\t\"./dv\": 6080,\n\t\"./dv.js\": 6080,\n\t\"./el\": 2655,\n\t\"./el.js\": 2655,\n\t\"./en-au\": 6836,\n\t\"./en-au.js\": 6836,\n\t\"./en-ca\": 2086,\n\t\"./en-ca.js\": 2086,\n\t\"./en-gb\": 2103,\n\t\"./en-gb.js\": 2103,\n\t\"./en-ie\": 5964,\n\t\"./en-ie.js\": 5964,\n\t\"./en-il\": 4379,\n\t\"./en-il.js\": 4379,\n\t\"./en-in\": 765,\n\t\"./en-in.js\": 765,\n\t\"./en-nz\": 1502,\n\t\"./en-nz.js\": 1502,\n\t\"./en-sg\": 1152,\n\t\"./en-sg.js\": 1152,\n\t\"./eo\": 50,\n\t\"./eo.js\": 50,\n\t\"./es\": 3350,\n\t\"./es-do\": 9338,\n\t\"./es-do.js\": 9338,\n\t\"./es-mx\": 1326,\n\t\"./es-mx.js\": 1326,\n\t\"./es-us\": 9947,\n\t\"./es-us.js\": 9947,\n\t\"./es.js\": 3350,\n\t\"./et\": 8231,\n\t\"./et.js\": 8231,\n\t\"./eu\": 8512,\n\t\"./eu.js\": 8512,\n\t\"./fa\": 9083,\n\t\"./fa.js\": 9083,\n\t\"./fi\": 5059,\n\t\"./fi.js\": 5059,\n\t\"./fil\": 2607,\n\t\"./fil.js\": 2607,\n\t\"./fo\": 3369,\n\t\"./fo.js\": 3369,\n\t\"./fr\": 7390,\n\t\"./fr-ca\": 6711,\n\t\"./fr-ca.js\": 6711,\n\t\"./fr-ch\": 6152,\n\t\"./fr-ch.js\": 6152,\n\t\"./fr.js\": 7390,\n\t\"./fy\": 2419,\n\t\"./fy.js\": 2419,\n\t\"./ga\": 3002,\n\t\"./ga.js\": 3002,\n\t\"./gd\": 4914,\n\t\"./gd.js\": 4914,\n\t\"./gl\": 6557,\n\t\"./gl.js\": 6557,\n\t\"./gom-deva\": 8944,\n\t\"./gom-deva.js\": 8944,\n\t\"./gom-latn\": 5387,\n\t\"./gom-latn.js\": 5387,\n\t\"./gu\": 7462,\n\t\"./gu.js\": 7462,\n\t\"./he\": 9237,\n\t\"./he.js\": 9237,\n\t\"./hi\": 9617,\n\t\"./hi.js\": 9617,\n\t\"./hr\": 6544,\n\t\"./hr.js\": 6544,\n\t\"./hu\": 341,\n\t\"./hu.js\": 341,\n\t\"./hy-am\": 1388,\n\t\"./hy-am.js\": 1388,\n\t\"./id\": 5251,\n\t\"./id.js\": 5251,\n\t\"./is\": 1146,\n\t\"./is.js\": 1146,\n\t\"./it\": 7891,\n\t\"./it-ch\": 7,\n\t\"./it-ch.js\": 7,\n\t\"./it.js\": 7891,\n\t\"./ja\": 3727,\n\t\"./ja.js\": 3727,\n\t\"./jv\": 5198,\n\t\"./jv.js\": 5198,\n\t\"./ka\": 8974,\n\t\"./ka.js\": 8974,\n\t\"./kk\": 7308,\n\t\"./kk.js\": 7308,\n\t\"./km\": 7786,\n\t\"./km.js\": 7786,\n\t\"./kn\": 4807,\n\t\"./kn.js\": 4807,\n\t\"./ko\": 1584,\n\t\"./ko.js\": 1584,\n\t\"./ku\": 1906,\n\t\"./ku-kmr\": 5305,\n\t\"./ku-kmr.js\": 5305,\n\t\"./ku.js\": 1906,\n\t\"./ky\": 9190,\n\t\"./ky.js\": 9190,\n\t\"./lb\": 7396,\n\t\"./lb.js\": 7396,\n\t\"./lo\": 8503,\n\t\"./lo.js\": 8503,\n\t\"./lt\": 3010,\n\t\"./lt.js\": 3010,\n\t\"./lv\": 5192,\n\t\"./lv.js\": 5192,\n\t\"./me\": 1944,\n\t\"./me.js\": 1944,\n\t\"./mi\": 6492,\n\t\"./mi.js\": 6492,\n\t\"./mk\": 2934,\n\t\"./mk.js\": 2934,\n\t\"./ml\": 1463,\n\t\"./ml.js\": 1463,\n\t\"./mn\": 8377,\n\t\"./mn.js\": 8377,\n\t\"./mr\": 8733,\n\t\"./mr.js\": 8733,\n\t\"./ms\": 8030,\n\t\"./ms-my\": 9445,\n\t\"./ms-my.js\": 9445,\n\t\"./ms.js\": 8030,\n\t\"./mt\": 5887,\n\t\"./mt.js\": 5887,\n\t\"./my\": 7228,\n\t\"./my.js\": 7228,\n\t\"./nb\": 8294,\n\t\"./nb.js\": 8294,\n\t\"./ne\": 9559,\n\t\"./ne.js\": 9559,\n\t\"./nl\": 600,\n\t\"./nl-be\": 8796,\n\t\"./nl-be.js\": 8796,\n\t\"./nl.js\": 600,\n\t\"./nn\": 9570,\n\t\"./nn.js\": 9570,\n\t\"./oc-lnc\": 5662,\n\t\"./oc-lnc.js\": 5662,\n\t\"./pa-in\": 7101,\n\t\"./pa-in.js\": 7101,\n\t\"./pl\": 6118,\n\t\"./pl.js\": 6118,\n\t\"./pt\": 9198,\n\t\"./pt-br\": 7203,\n\t\"./pt-br.js\": 7203,\n\t\"./pt.js\": 9198,\n\t\"./ro\": 5565,\n\t\"./ro.js\": 5565,\n\t\"./ru\": 3315,\n\t\"./ru.js\": 3315,\n\t\"./sd\": 8473,\n\t\"./sd.js\": 8473,\n\t\"./se\": 1258,\n\t\"./se.js\": 1258,\n\t\"./si\": 8798,\n\t\"./si.js\": 8798,\n\t\"./sk\": 6404,\n\t\"./sk.js\": 6404,\n\t\"./sl\": 7057,\n\t\"./sl.js\": 7057,\n\t\"./sq\": 5718,\n\t\"./sq.js\": 5718,\n\t\"./sr\": 5363,\n\t\"./sr-cyrl\": 478,\n\t\"./sr-cyrl.js\": 478,\n\t\"./sr.js\": 5363,\n\t\"./ss\": 7260,\n\t\"./ss.js\": 7260,\n\t\"./sv\": 2231,\n\t\"./sv.js\": 2231,\n\t\"./sw\": 7104,\n\t\"./sw.js\": 7104,\n\t\"./ta\": 7493,\n\t\"./ta.js\": 7493,\n\t\"./te\": 7705,\n\t\"./te.js\": 7705,\n\t\"./tet\": 4457,\n\t\"./tet.js\": 4457,\n\t\"./tg\": 2727,\n\t\"./tg.js\": 2727,\n\t\"./th\": 2206,\n\t\"./th.js\": 2206,\n\t\"./tk\": 3419,\n\t\"./tk.js\": 3419,\n\t\"./tl-ph\": 7243,\n\t\"./tl-ph.js\": 7243,\n\t\"./tlh\": 16,\n\t\"./tlh.js\": 16,\n\t\"./tr\": 7020,\n\t\"./tr.js\": 7020,\n\t\"./tzl\": 8026,\n\t\"./tzl.js\": 8026,\n\t\"./tzm\": 8537,\n\t\"./tzm-latn\": 7899,\n\t\"./tzm-latn.js\": 7899,\n\t\"./tzm.js\": 8537,\n\t\"./ug-cn\": 818,\n\t\"./ug-cn.js\": 818,\n\t\"./uk\": 8478,\n\t\"./uk.js\": 8478,\n\t\"./ur\": 7893,\n\t\"./ur.js\": 7893,\n\t\"./uz\": 9133,\n\t\"./uz-latn\": 311,\n\t\"./uz-latn.js\": 311,\n\t\"./uz.js\": 9133,\n\t\"./vi\": 2179,\n\t\"./vi.js\": 2179,\n\t\"./x-pseudo\": 2455,\n\t\"./x-pseudo.js\": 2455,\n\t\"./yo\": 3310,\n\t\"./yo.js\": 3310,\n\t\"./zh-cn\": 7244,\n\t\"./zh-cn.js\": 7244,\n\t\"./zh-hk\": 76,\n\t\"./zh-hk.js\": 76,\n\t\"./zh-mo\": 2305,\n\t\"./zh-mo.js\": 2305,\n\t\"./zh-tw\": 8588,\n\t\"./zh-tw.js\": 8588\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 1738;", "import { throttle } from 'lodash';\nimport { useState, useEffect } from 'react';\n\ninterface MousePosition {\n  x: number | null;\n  y: number | null;\n}\n\n// For performance reasons, we throttle the mouse position updates\nconst DEFAULT_THROTTLE_INTERVAL_MS = 50;\n\nconst useMousePosition = (throttleInterval = DEFAULT_THROTTLE_INTERVAL_MS) => {\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: null, y: null });\n\n  useEffect(() => {\n    const updateMousePosition = throttle((event: MouseEvent) => {\n      setMousePosition({ x: event.clientX, y: event.clientY });\n    }, throttleInterval);\n    window.addEventListener('mousemove', updateMousePosition);\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n    };\n  }, [throttleInterval]);\n\n  return mousePosition;\n};\n\nexport default useMousePosition;\n", "import { css } from '@emotion/css';\nimport React, { SVGProps } from 'react';\nimport SVG from 'react-inlinesvg';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\n\nimport dark404 from './img/grot-404-dark.svg';\nimport light404 from './img/grot-404-light.svg';\n\nimport useMousePosition from './useMousePosition';\n\nconst MIN_ARM_ROTATION = -20;\nconst MAX_ARM_ROTATION = 5;\nconst MIN_ARM_TRANSLATION = -5;\nconst MAX_ARM_TRANSLATION = 5;\n\nexport interface Props {\n  width?: SVGProps<SVGElement>['width'];\n  height?: SVGProps<SVGElement>['height'];\n  show404?: boolean;\n}\n\nexport const GrotNotFound = ({ width = 'auto', height, show404 = false }: Props) => {\n  const theme = useTheme2();\n  const { x, y } = useMousePosition();\n  const styles = useStyles2(getStyles, x, y, show404);\n  return <SVG src={theme.isDark ? dark404 : light404} className={styles.svg} height={height} width={width} />;\n};\n\nGrotNotFound.displayName = 'GrotNotFound';\n\nconst getStyles = (theme: GrafanaTheme2, xPos: number | null, yPos: number | null, show404: boolean) => {\n  const { innerWidth, innerHeight } = window;\n  const heightRatio = yPos && yPos / innerHeight;\n  const widthRatio = xPos && xPos / innerWidth;\n  const rotation = heightRatio !== null ? getIntermediateValue(heightRatio, MIN_ARM_ROTATION, MAX_ARM_ROTATION) : 0;\n  const translation =\n    widthRatio !== null ? getIntermediateValue(widthRatio, MIN_ARM_TRANSLATION, MAX_ARM_TRANSLATION) : 0;\n\n  return {\n    svg: css({\n      '#grot-404-arm, #grot-404-magnifier': {\n        transform: `rotate(${rotation}deg) translateX(${translation}%)`,\n        transformOrigin: 'center',\n        transition: 'transform 50ms linear',\n      },\n      '#grot-404-text': {\n        display: show404 ? 'block' : 'none',\n      },\n    }),\n  };\n};\n\n/**\n * Given a start value, end value, and a ratio, return the intermediate value\n * Works with negative and inverted start/end values\n */\nconst getIntermediateValue = (ratio: number, start: number, end: number) => {\n  const value = ratio * (end - start) + start;\n  return value;\n};\n", "import React from 'react';\n\nimport { Icon, Stack, Text, useStyles2 } from '@grafana/ui';\nimport { GrafanaTheme2 } from '@grafana/data';\n\nimport { GrotNotFound } from './GrotNotFound';\nimport { css } from '@emotion/css';\nimport { testIds } from 'utils/testIds';\n\nexport interface Props {\n  message?: string | React.ReactNode;\n  remedyMessage?: string;\n  imgWidth?: number;\n  padding?: string;\n}\n\nexport const EmptyState = ({ message, remedyMessage, imgWidth, padding }: Props) => {\n  const styles = useStyles2(getStyles, padding);\n\n  return (\n    <div className={styles.container} data-testid={testIds.emptyState}>\n      <Stack direction=\"column\" alignItems=\"center\" gap={3}>\n        <GrotNotFound width={imgWidth ?? 300} />\n        {typeof message === 'string' &&  <Text textAlignment={'center'} variant=\"h5\">{message}</Text>}\n        {typeof message !== 'string' &&  message}\n\n        {remedyMessage && (\n          <div className={styles.remedy}>\n            <Stack gap={0.5} alignItems={'center'}>\n              <Icon name=\"info-circle\" />\n              <Text textAlignment={'center'} variant=\"body\">\n                {remedyMessage}\n              </Text>\n            </Stack>\n          </div>\n        )}\n      </Stack>\n    </div>\n  );\n};\n\nEmptyState.displayName = 'EmptyState';\n\nfunction getStyles(theme: GrafanaTheme2, padding?: string) {\n  return {\n    container: css({\n      width: '100%',\n      display: 'flex',\n      justifyContent: 'space-evenly',\n      flexDirection: 'column',\n      padding: padding ? padding : 0,\n    }),\n    remedy: css({\n      marginBottom: theme.spacing(4),\n    })\n  };\n}\n", "export const testIds = {\n  emptyState: 'data-testid empty-state',\n  errorState: 'data-testid error-state',\n  loadingState: 'data-testid loading-state',\n};\n", "import { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport React from 'react';\nimport { EmptyState } from './EmptyState';\n\ninterface EmptyStateSceneState extends SceneObjectState {\n  message?: string;\n  remedyMessage?: string;\n  imgWidth?: number;\n  padding?: string;\n}\n\nexport class EmptyStateScene extends SceneObjectBase<EmptyStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<EmptyStateScene>) => {\n    const { message, remedyMessage, imgWidth, padding } = model.useState();\n    return <EmptyState message={message} remedyMessage={remedyMessage} imgWidth={imgWidth} padding={padding} />;\n  };\n}\n", "import { css, keyframes } from '@emotion/css';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\nimport React from 'react';\nimport { SkeletonTheme } from 'react-loading-skeleton';\nimport { testIds } from 'utils/testIds';\n\ninterface LoadingStateSceneState extends SceneObjectState {\n  component: () => React.JSX.Element;\n}\n\nexport class LoadingStateScene extends SceneObjectBase<LoadingStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<LoadingStateScene>) => {\n    const theme = useTheme2();\n    const styles = useStyles2(getStyles);\n    const { component } = model.useState();\n\n    return (\n      <div className={styles.container} data-testid={testIds.loadingState}>\n        <SkeletonTheme\n          baseColor={theme.colors.emphasize(theme.colors.background.secondary)}\n          highlightColor={theme.colors.emphasize(theme.colors.background.secondary, 0.1)}\n          borderRadius={theme.shape.radius.default}\n        >\n          {component()}\n        </SkeletonTheme>\n      </div>\n    );\n  };\n}\n\nconst fadeIn = keyframes({\n  '0%': {\n    opacity: 0,\n  },\n  '100%': {\n    opacity: 1,\n  },\n});\n\nfunction getStyles() {\n  return {\n    container: css({\n      label: 'loading-state-scene',\n      // animation prevents flickering when loading\n      animationName: fadeIn,\n      animationDelay: '100ms',\n      animationTimingFunction: 'ease-in',\n      animationDuration: '100ms',\n      animationFillMode: 'backwards',\n    }),\n  };\n}\n", "import { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport React from 'react';\nimport { Alert } from '@grafana/ui';\nimport { testIds } from 'utils/testIds';\n\ninterface ErrorStateSceneState extends SceneObjectState {\n  message: string;\n}\n\nexport class ErrorStateScene extends SceneObjectBase<ErrorStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<ErrorStateScene>) => {\n    const { message } = model.useState();\n    return (\n      <Alert title={'Query error'} severity={'error'} data-testid={testIds.errorState}>\n        {message}\n      </Alert>\n    );\n  };\n}\n", "import { Field, Input, Icon, useStyles2 } from \"@grafana/ui\"\nimport React from \"react\"\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { css } from \"@emotion/css\";\n\ntype Props = {\n  searchQuery: string;\n  onSearchQueryChange: (event: React.ChangeEvent<HTMLInputElement>) => void;\n}\n\nexport const Search = (props: Props) => {\n  const styles = useStyles2(getStyles);\n  const { searchQuery, onSearchQueryChange } = props;\n\n  return (\n    <Field className={styles.searchField}>\n      <Input\n        placeholder='Search'\n        prefix={<Icon name={'search'} />}\n        value={searchQuery}\n        onChange={onSearchQueryChange}\n        id='searchFieldInput'\n      />\n    </Field>\n  )\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    searchField: css({\n      marginBottom: theme.spacing(1),\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { DataFrame, FieldType, GrafanaTheme2, LoadingState, PanelData, sortDataFrame } from '@grafana/data';\nimport {\n  SceneComponentProps,\n  SceneCSSGridLayout,\n  SceneFlexItem,\n  sceneGraph,\n  SceneLayout,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport { useStyles2 } from '@grafana/ui';\nimport Skeleton from 'react-loading-skeleton';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { ErrorStateScene } from 'components/states/ErrorState/ErrorStateScene';\nimport { debounce } from 'lodash';\nimport { Search } from './Search';\nimport { getGroupByVariable } from 'utils/utils';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  EventTimeseriesDataReceived,\n  GRID_TEMPLATE_COLUMNS,\n} from '../../utils/shared';\nimport { cloneDataFrame } from '../../utils/frames';\n\ninterface ByFrameRepeaterState extends SceneObjectState {\n  body: SceneLayout;\n  groupBy?: boolean;\n\n  getLayoutChild(data: PanelData, frame: DataFrame, frameIndex: number): SceneFlexItem;\n\n  searchQuery?: string;\n}\n\nexport class ByFrameRepeater extends SceneObjectBase<ByFrameRepeaterState> {\n  public constructor(state: ByFrameRepeaterState) {\n    super(state);\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done || data.data?.state === LoadingState.Streaming) {\n            if (data.data.series.length === 0 && data.data?.state !== LoadingState.Streaming) {\n              this.state.body.setState({\n                children: [\n                  new SceneFlexItem({\n                    body: new EmptyStateScene({\n                      message: EMPTY_STATE_ERROR_MESSAGE,\n                      remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                      padding: '32px',\n                    }),\n                  }),\n                ],\n              });\n            } else if (data.data?.state === LoadingState.Done) {\n              const filtered = {\n                ...data.data,\n                series: data.data?.series.filter(doesQueryMatchDataFrameLabels(this.state.searchQuery)),\n              };\n              this.renderFilteredData(filtered as PanelData);\n              this.publishEvent(new EventTimeseriesDataReceived({ series: data.data.series }), true);\n            }\n          } else if (data.data?.state === LoadingState.Error) {\n            this.state.body.setState({\n              children: [\n                new SceneCSSGridLayout({\n                  children: [\n                    new ErrorStateScene({\n                      message: data.data.errors?.[0]?.message ?? 'An error occurred in the query',\n                    }),\n                  ],\n                }),\n              ],\n            });\n          } else {\n            this.state.body.setState({\n              children: [\n                new SceneCSSGridLayout({\n                  children: [\n                    new LoadingStateScene({\n                      component: () => SkeletonComponent(8),\n                    }),\n                  ],\n                }),\n              ],\n            });\n          }\n        })\n      );\n\n      this.subscribeToState((newState, prevState) => {\n        if (newState.searchQuery !== prevState.searchQuery) {\n          this.onSearchQueryChangeDebounced(newState.searchQuery ?? '');\n        }\n      });\n\n      if (data.state.data) {\n        this.performRepeat(data.state.data);\n      }\n    });\n  }\n\n  private onSearchQueryChange = (evt: React.SyntheticEvent<HTMLInputElement>) => {\n    this.setState({ searchQuery: evt.currentTarget.value });\n  };\n\n  private onSearchQueryChangeDebounced = debounce((searchQuery: string) => {\n    const data = sceneGraph.getData(this);\n    const filtered = {\n      ...data.state.data,\n      series: data.state.data?.series.filter(doesQueryMatchDataFrameLabels(searchQuery)),\n    };\n    this.renderFilteredData(filtered as PanelData);\n  }, 250);\n\n  private renderFilteredData(filtered: PanelData) {\n    if (filtered.series && filtered.series.length > 0) {\n      this.performRepeat(filtered as PanelData);\n    } else {\n      this.state.body.setState({\n        children: [\n          new SceneFlexItem({\n            body: new EmptyStateScene({\n              message: 'No data for search term',\n              padding: '32px',\n            }),\n          }),\n        ],\n      });\n    }\n  }\n\n  private groupSeriesBy(data: PanelData, groupBy: string) {\n    const groupedData = data.series.reduce(\n      (acc, series) => {\n        const key = series.fields.find((f) => f.type === FieldType.number)?.labels?.[groupBy];\n        if (!key) {\n          return acc;\n        }\n        if (!acc[key]) {\n          acc[key] = [];\n        }\n        acc[key].push(series);\n        return acc;\n      },\n      {} as Record<string, DataFrame[]>\n    );\n\n    const newSeries = [];\n    for (const key in groupedData) {\n      const frames = groupedData[key].sort((a, b) => a.name?.localeCompare(b.name!) || 0);\n      const mainFrame = cloneDataFrame(frames[0]);\n      frames.slice(1, frames.length).forEach((frame) => mainFrame.fields.push(frame.fields[1]));\n      newSeries.push(sortDataFrame(mainFrame, 0));\n    }\n    return newSeries;\n  }\n\n  private performRepeat(data: PanelData) {\n    const newChildren: SceneFlexItem[] = [];\n    let frames = data.series;\n\n    if (this.state.groupBy) {\n      frames = this.groupSeriesBy(data, getGroupByVariable(this).getValueText());\n    }\n\n    for (let frameIndex = 0; frameIndex < frames.length; frameIndex++) {\n      const currentFrame = frames[frameIndex];\n      // Skip frames with no data\n      const sum = currentFrame.fields\n        .filter((f) => f.type === FieldType.number)\n        .reduce((sum, f) => sum + f.values.reduce((vSum, v) => vSum + (v || 0), 0) || 0, 0);\n      if (sum === 0) {\n        continue;\n      }\n      // Build the layout child\n      const layoutChild = this.state.getLayoutChild(data, frames[frameIndex], frameIndex);\n      newChildren.push(layoutChild);\n    }\n\n    this.state.body.setState({ children: newChildren });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ByFrameRepeater>) => {\n    const { body, searchQuery } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <Search searchQuery={searchQuery ?? ''} onSearchQueryChange={model.onSearchQueryChange} />\n        <body.Component model={body} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles() {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n    }),\n  };\n}\n\nexport const SkeletonComponent = (repeat: number) => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      {[...Array(repeat)].map((_, i) => (\n        <div className={styles.itemContainer} key={i}>\n          <div className={styles.header}>\n            <div className={styles.title}>\n              <Skeleton count={1} />\n            </div>\n            <div className={styles.action}>\n              <Skeleton count={1} />\n            </div>\n          </div>\n          <div className={styles.yAxis}>\n            {[...Array(2)].map((_, i) => (\n              <div className={styles.yAxisItem} key={i}>\n                <Skeleton count={1} />\n              </div>\n            ))}\n          </div>\n          <div className={styles.xAxis}>\n            {[...Array(2)].map((_, i) => (\n              <div className={styles.xAxisItem} key={i}>\n                <Skeleton count={1} />\n              </div>\n            ))}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'grid',\n      gridTemplateColumns: GRID_TEMPLATE_COLUMNS,\n      gridAutoRows: '200px',\n      rowGap: theme.spacing(1),\n      columnGap: theme.spacing(1),\n    }),\n    itemContainer: css({\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.background.secondary}`,\n      padding: '5px',\n    }),\n    header: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    title: css({\n      width: '100px',\n    }),\n    action: css({\n      width: '60px',\n    }),\n    yAxis: css({\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'space-around',\n      marginTop: '35px',\n    }),\n    yAxisItem: css({\n      width: '60px',\n      height: '55px',\n    }),\n    xAxis: css({\n      display: 'flex',\n      justifyContent: 'space-evenly',\n    }),\n    xAxisItem: css({\n      width: '55px',\n    }),\n  };\n}\n\nexport const doesQueryMatchDataFrameLabels = (searchQuery?: string) => (dataFrame: DataFrame) => {\n  const pattern = searchQuery?.trim();\n  if (!pattern) {\n    return true;\n  }\n\n  const regex = new RegExp(pattern, 'i');\n\n  return dataFrame.fields.some((f) => (!f.labels ? false : Object.values(f.labels).find((label) => regex.test(label))));\n};\n", "import { DataQueryResponseData, Field } from '@grafana/data';\n\nexport function cloneDataFrame(frame: DataQueryResponseData): DataQueryResponseData {\n  return {\n    ...frame,\n    fields: frame.fields.map((field: Field) => ({\n      ...field,\n      values: field.values,\n    })),\n  };\n}\n", "import { PanelBuilders } from '@grafana/scenes';\nimport { DrawStyle, StackingMode, TooltipDisplayMode } from '@grafana/ui';\n\nexport const barsPanelConfig = () => {\n  return PanelBuilders.timeseries()\n    .setOption('legend', { showLegend: false })\n    .setCustomFieldConfig('drawStyle', DrawStyle.Bars)\n    .setCustomFieldConfig('stacking', { mode: StackingMode.Normal })\n    .setCustomFieldConfig('fillOpacity', 75)\n    .setCustomFieldConfig('lineWidth', 0)\n    .setCustomFieldConfig('pointSize', 0)\n    .setCustomFieldConfig('axisLabel', 'Rate')\n    .setOverrides((overrides) => {\n      overrides.matchFieldsWithNameByRegex('(^error$|.*status=\"error\".*)').overrideColor({\n        mode: 'fixed',\n        fixedColor: 'semi-dark-red',\n      });\n      overrides.matchFieldsWithNameByRegex('(^unset$|.*status=\"unset\".*)').overrideColor({\n        mode: 'fixed',\n        fixedColor: 'green',\n      });\n      overrides.matchFieldsWithNameByRegex('(^ok$|.*status=\"ok\".*)').overrideColor({\n        mode: 'fixed',\n        fixedColor: 'dark-green',\n      });\n    })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi });\n};\n", "import { ALL, MetricFunction, VAR_FILTERS_EXPR } from '../../../utils/shared';\n\ninterface QueryOptions {\n  metric: MetricFunction;\n  extraFilters?: string;\n  groupByKey?: string;\n  groupByStatus?: boolean;\n}\n\nexport function generateMetricsQuery({ metric, groupByKey, extraFilters, groupByStatus }: QueryOptions) {\n  // Generate span set filters\n  let filters = `${VAR_FILTERS_EXPR}`;\n\n  if (metric === 'rate') {\n    filters += ' && status!=error';\n  } else if (metric === 'errors') {\n    filters += ' && status=error';\n  }\n\n  if (extraFilters) {\n    filters += ` && ${extraFilters}`;\n  }\n\n  if (groupByKey && groupByKey !== ALL) {\n    filters += ` && ${groupByKey} != nil`;\n  }\n\n  // Generate metrics function\n  let metricFn = 'rate()';\n  switch (metric) {\n    case 'errors':\n      metricFn = 'rate()';\n      break;\n    case 'duration':\n      metricFn = 'quantile_over_time(duration, 0.9)';\n      break;\n  }\n\n  // Generate group by section\n  let groupByAttrs = [];\n  if (groupByKey && groupByKey !== ALL) {\n    groupByAttrs.push(groupByKey);\n  }\n\n  if (metric !== 'duration' && groupByStatus) {\n    groupByAttrs.push('status');\n  }\n\n  const groupBy = groupByAttrs.length ? `by(${groupByAttrs.join(', ')})` : '';\n\n  return `{${filters}} | ${metricFn} ${groupBy}`;\n}\n\nexport function metricByWithStatus(metric: MetricFunction, tagKey?: string) {\n  return {\n    refId: 'A',\n    query: generateMetricsQuery({ metric, groupByKey: tagKey, groupByStatus: true }),\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 100,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { dropWhile as _dropWhile, round as _round } from 'lodash';\nimport { sceneGraph, SceneObject } from '@grafana/scenes';\nimport { duration } from 'moment/moment';\n\nexport const ONE_MILLISECOND = 1000;\nexport const ONE_SECOND = 1000 * ONE_MILLISECOND;\nexport const ONE_MINUTE = 60 * ONE_SECOND;\nexport const ONE_HOUR = 60 * ONE_MINUTE;\nexport const ONE_DAY = 24 * ONE_HOUR;\nexport const DEFAULT_MS_PRECISION = Math.log10(ONE_MILLISECOND);\n\nconst UNIT_STEPS: Array<{ unit: string; microseconds: number; ofPrevious: number }> = [\n  { unit: 'd', microseconds: ONE_DAY, ofPrevious: 24 },\n  { unit: 'h', microseconds: ONE_HOUR, ofPrevious: 60 },\n  { unit: 'm', microseconds: ONE_MINUTE, ofPrevious: 60 },\n  { unit: 's', microseconds: ONE_SECOND, ofPrevious: 1000 },\n  { unit: 'ms', microseconds: ONE_MILLISECOND, ofPrevious: 1000 },\n  { unit: 'μs', microseconds: 1, ofPrevious: 1000 },\n];\n\n/**\n * Humanizes the duration for display.\n *\n * Example:\n * 5000ms => 5s\n * 1000μs => 1ms\n * 183840s => 2d 3h\n *\n * @param {number} duration (in microseconds)\n * @return {string} formatted duration\n */\nexport const formatDuration = (duration: number): string => {\n  // Drop all units that are too large except the last one\n  const [primaryUnit, secondaryUnit] = _dropWhile(\n    UNIT_STEPS,\n    ({ microseconds }, index) => index < UNIT_STEPS.length - 1 && microseconds > duration\n  );\n\n  if (primaryUnit.ofPrevious === 1000) {\n    // If the unit is decimal based, display as a decimal\n    return `${_round(duration / primaryUnit.microseconds, 2)}${primaryUnit.unit}`;\n  }\n\n  const primaryValue = Math.floor(duration / primaryUnit.microseconds);\n  const primaryUnitString = `${primaryValue}${primaryUnit.unit}`;\n  const secondaryValue = Math.round((duration / secondaryUnit.microseconds) % primaryUnit.ofPrevious);\n  const secondaryUnitString = `${secondaryValue}${secondaryUnit.unit}`;\n  return secondaryValue === 0 ? primaryUnitString : `${primaryUnitString} ${secondaryUnitString}`;\n}\n\nexport const getStepForTimeRange = (scene: SceneObject, dataPoints?: number) => {\n  const sceneTimeRange = sceneGraph.getTimeRange(scene);\n  const from = sceneTimeRange.state.value.from.unix();\n  const to = sceneTimeRange.state.value.to.unix();\n\n  const dur = duration(to - from, 's');\n  const finalDur = Math.floor(dur.asSeconds() / (dataPoints ?? 50)) || 1;\n  return `${finalDur}s`;\n}\n", "import { QueryRunnerState, sceneGraph, SceneQueryRunner } from '@grafana/scenes';\nimport { getStepForTimeRange } from '../../../utils/dates';\n\nexport class StepQueryRunner extends SceneQueryRunner {\n  constructor(state: QueryRunnerState) {\n    super(state);\n    this.addActivationHandler(this._onActivateStep.bind(this));\n  }\n\n  private _onActivateStep() {\n    const step = getStepForTimeRange(this, this.state.maxDataPoints);\n    this.setState({\n      queries: this.state.queries.map((query) => {\n        return {\n          ...query,\n          step,\n        };\n      }),\n    });\n\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    sceneTimeRange.subscribeToState((newState, prevState) => {\n      if (newState.value.from !== prevState.value.from || newState.value.to !== prevState.value.to) {\n        const newStep = getStepForTimeRange(this, this.state.maxDataPoints);\n        this.setState({\n          queries: this.state.queries.map((query) => {\n            return {\n              ...query,\n              step: newStep,\n            };\n          }),\n        });\n      }\n    });\n  }\n}\n", "import React from 'react';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Icon, Tooltip, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\n\ninterface StreamingIndicatorProps {\n  isStreaming: boolean;\n  iconSize?: number;\n}\n\nexport const StreamingIndicator = ({ \n  isStreaming, \n  iconSize = 14,\n}: StreamingIndicatorProps) => {\n  const styles = useStyles2(getStyles, iconSize);\n\n  if (!isStreaming) {\n    return null;\n  }\n\n  return (\n    <Tooltip content={'Streaming'}>\n      <Icon name={'circle-mono'} size=\"md\" className={styles.streamingIndicator} />\n    </Tooltip>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2, iconSize: number) => {\n  return {\n    streamingIndicator: css({\n      width: `${iconSize}px`,\n      height: `${iconSize}px`,\n      backgroundColor: theme.colors.success.text,\n      fill: theme.colors.success.text,\n      borderRadius: '50%',\n      display: 'inline-block',\n    }),\n  };\n}; \n", "import { reportInteraction } from '@grafana/runtime';\nimport pluginJson from '../plugin.json';\n\n// Helper function to create a unique interaction name for analytics\nconst createInteractionName = (page: UserEventPagesType, action: string) => {\n  return `${pluginJson.id.replace(/-/g, '_')}_${page}_${action}`;\n};\n\n// Runs reportInteraction with a standardized interaction name\nexport const reportAppInteraction = (\n  page: UserEventPagesType,\n  action: UserEventActionType,\n  properties?: Record<string, unknown>\n) => {\n  reportInteraction(createInteractionName(page, action), properties);\n};\n\nexport const USER_EVENTS_PAGES = {\n  analyse_traces: 'analyse_traces',\n  home: 'home',\n  common: 'common',\n} as const;\n\nexport type UserEventPagesType = keyof typeof USER_EVENTS_PAGES;\ntype UserEventActionType =\n  | keyof (typeof USER_EVENTS_ACTIONS)['analyse_traces']\n  | keyof (typeof USER_EVENTS_ACTIONS)['home']\n  | keyof (typeof USER_EVENTS_ACTIONS)['common'];\n\nexport const USER_EVENTS_ACTIONS = {\n  [USER_EVENTS_PAGES.analyse_traces]: {\n    action_view_changed: 'action_view_changed',\n    breakdown_group_by_changed: 'breakdown_group_by_changed',\n    breakdown_add_to_filters_clicked: 'breakdown_add_to_filters_clicked',\n    comparison_add_to_filters_clicked: 'comparison_add_to_filters_clicked',\n    select_attribute_in_comparison_clicked: 'select_attribute_in_comparison_clicked',\n    layout_type_changed: 'layout_type_changed',\n    start_investigation: 'start_investigation',\n    stop_investigation: 'stop_investigation',\n    open_trace: 'open_trace',\n    open_in_explore_clicked: 'open_in_explore_clicked',\n    add_to_investigation_clicked: 'add_to_investigation_clicked',\n    add_to_investigation_trace_view_clicked: 'add_to_investigation_trace_view_clicked',\n    span_list_columns_changed: 'span_list_columns_changed',\n    toggle_bookmark_clicked: 'toggle_bookmark_clicked',\n  },\n  [USER_EVENTS_PAGES.home]: {\n    homepage_initialized: 'homepage_initialized',\n    panel_row_clicked: 'panel_row_clicked',\n    explore_traces_clicked: 'explore_traces_clicked',\n    read_documentation_clicked: 'read_documentation_clicked',\n    filter_changed: 'filter_changed',\n    go_to_bookmark_clicked: 'go_to_bookmark_clicked',\n  },\n  [USER_EVENTS_PAGES.common]: {\n    metric_changed: 'metric_changed',\n    new_filter_added_manually: 'new_filter_added_manually',\n    app_initialized: 'app_initialized',\n    global_docs_link_clicked: 'global_docs_link_clicked',\n    metric_docs_link_clicked: 'metric_docs_link_clicked',\n    feedback_link_clicked: 'feedback_link_clicked',\n  },\n} as const;\n", "import { getTraceByServiceScene, shouldShowSelection } from '../../../utils/utils';\nimport { ComparisonSelection } from '../../../utils/shared';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { PanelBuilders, SceneFlexItem, SceneFlexLayout, SceneObject } from '@grafana/scenes';\n\nexport function getHistogramVizPanel(scene: SceneObject, yBuckets: number[]) {\n  const parent = getTraceByServiceScene(scene);\n  const panel = histogramPanelConfig()\n    .setHoverHeader(true)\n    // @ts-ignore\n    .setOption('selectionMode', 'xy')\n    .build();\n  panel.setState({\n    extendPanelContext: (vizPanel, context) => {\n      // TODO remove when we the Grafana version with #88107 is released\n      // @ts-ignore\n      context.onSelectRange = (args) => {\n        if (args.length === 0) {\n          parent.setState({ selection: undefined });\n          return;\n        }\n        const rawSelection = args[0];\n        // @ts-ignore\n        const newSelection: ComparisonSelection = { type: 'manual', raw: rawSelection };\n\n        newSelection.timeRange = {\n          from: Math.round((rawSelection.x?.from || 0) / 1000),\n          to: Math.round((rawSelection.x?.to || 0) / 1000),\n        };\n\n        // Ignore selection and return if the selection is invalid\n        if (newSelection.timeRange.from === newSelection.timeRange.to) {\n          return;\n        }\n\n        const yFrom = yBucketToDuration((args[0].y?.from || 0) - 1, yBuckets);\n        const yTo = yBucketToDuration(args[0].y?.to || 0, yBuckets);\n        newSelection.duration = { from: yFrom, to: yTo };\n\n        parent.onUserUpdateSelection(newSelection);\n        if (!shouldShowSelection(parent.state.actionView)) {\n          parent.setActionView('comparison');\n        }\n\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.start_investigation, {\n          selection: newSelection,\n          metric: 'duration',\n        });\n      };\n    },\n  });\n  return new SceneFlexLayout({\n    direction: 'row',\n    children: [\n      new SceneFlexItem({\n        body: panel,\n      }),\n    ],\n  });\n}\n\nexport const histogramPanelConfig = () => {\n  return PanelBuilders.heatmap()\n    .setOption('legend', { show: false })\n    .setOption('yAxis', {\n      unit: 's',\n      axisLabel: 'duration',\n    })\n    .setOption('color', {\n      scheme: 'Blues',\n      steps: 16,\n    })\n    .setOption('rowsFrame', { value: 'Spans' });\n};\n\nexport function yBucketToDuration(yValue: number, buckets?: number[], multiplier?: number) {\n  if (!buckets) {\n    return '';\n  }\n  if (yValue < 0) {\n    return '0';\n  }\n\n  const rawValue = buckets[Math.floor(yValue)] * (multiplier || 1);\n  if (!rawValue || isNaN(rawValue)) {\n    return '';\n  }\n  if (rawValue >= 1) {\n    return `${rawValue.toFixed(0)}s`;\n  }\n  return `${(rawValue * 1000).toFixed(0)}ms`;\n}\n", "import { SelectableValue } from '@grafana/data';\n\nexport const DATABASE_CALLS_KEY = 'span.db.name';\n\nexport const primarySignalOptions: Array<SelectableValue<string>> = [\n  {\n    label: 'Root spans',\n    value: 'nestedSetParent<0',\n    filter: { key: 'nestedSetParent', operator: '<', value: '0' },\n    description: 'Focus your analysis on the root span of each trace',\n  },\n  {\n    label: 'All spans',\n    value: 'true',\n    filter: { key: '', operator: '', value: true },\n    description: 'View and analyse raw span data. This option may result in long query times.',\n  },\n];\n\nexport const getSignalForKey = (key?: string) => {\n  return primarySignalOptions.find((option) => option.value === key);\n};\n", "import React from 'react';\n\nimport { DataFrame } from '@grafana/data';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, AdHocFiltersVariable } from '@grafana/scenes';\nimport { Button } from '@grafana/ui';\nimport { getFiltersVariable, getLabelValue } from '../../../utils/utils';\nimport { DATABASE_CALLS_KEY } from 'pages/Explore/primary-signals';\n\nexport interface AddToFiltersActionState extends SceneObjectState {\n  frame: DataFrame;\n  onClick: (payload: any) => void;\n  labelKey?: string;\n}\n\nexport class AddToFiltersAction extends SceneObjectBase<AddToFiltersActionState> {\n  public onClick = () => {\n    const variable = getFiltersVariable(this);\n\n    const labels = this.state.frame.fields.find((f) => f.labels)?.labels ?? {};\n    if (this.state.labelKey) {\n      if (!labels[this.state.labelKey]) {\n        return;\n      }\n    } else {\n      if (Object.keys(labels).length !== 1) {\n        return;\n      }\n    }\n\n    const labelName = this.state.labelKey ?? Object.keys(labels)[0];\n    const value = getLabelValue(this.state.frame, this.state.labelKey);\n\n    addToFilters(variable, labelName, value);\n\n    this.state.onClick({ labelName });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AddToFiltersAction>) => {\n    const key = model.state?.labelKey ?? '';\n    const field = model.state?.frame.fields.filter(x => x.type !== 'time');\n    const value = field?.[0]?.labels?.[key] ?? '';\n    const filterExists = filterExistsForKey(getFiltersVariable(model), key, value.replace(/\"/g, ''));\n\n    if (!filterExists) {\n      return (\n        <Button variant=\"primary\" size=\"sm\" fill=\"text\" onClick={model.onClick} icon={'search-plus'}>\n          Add to filters\n        </Button>\n      );\n    }\n    return <></>;\n  };\n}\n\nexport const addToFilters = (variable: AdHocFiltersVariable, label: string, value: string) => {\n  // ensure we set the new filter with latest value\n  // and remove any existing filter for the same key\n  // and also keep span.db.name as it is a primary filter\n  const filtersWithoutNew = variable.state.filters.filter((f) => f.key === DATABASE_CALLS_KEY || f.key !== label);\n\n  // TODO: Replace it with new API introduced in https://github.com/grafana/scenes/issues/1103\n  // At the moment AdHocFiltersVariable doesn't support pushing new history entry on change\n  history.pushState(null, '');\n\n  variable.setState({\n    filters: [\n      ...filtersWithoutNew,\n      {\n        key: label,\n        operator: '=',\n        value: value,\n      },\n    ],\n  });\n};\n\nexport const filterExistsForKey = (model: AdHocFiltersVariable, key: string, value: string) => {\n  const variable = getFiltersVariable(model);\n  return variable.state.filters.find((f) => f.key === key && f.value === value);\n}\n", "import { DataFrame } from '@grafana/data';\nimport { ComparisonSelection, MetricFunction } from './shared';\n\nexport const computeHighestDifference = (frame: DataFrame) => {\n  const baselineField = frame.fields.find((f) => f.name === 'Baseline');\n  const selectionField = frame.fields.find((f) => f.name === 'Selection');\n\n  let maxDifference = 0;\n  let maxDifferenceIndex = 0;\n\n  for (let i = 0; i < (baselineField?.values?.length || 0); i++) {\n    const diff = (selectionField?.values[i] || 0) - (baselineField?.values[i] || 0);\n    if (Math.abs(diff) > Math.abs(maxDifference || 0)) {\n      maxDifference = diff;\n      maxDifferenceIndex = i;\n    }\n  }\n\n  return { maxDifference, maxDifferenceIndex };\n};\n\nexport const getDefaultSelectionForMetric = (metric: MetricFunction): ComparisonSelection | undefined => {\n  if (metric === 'duration') {\n    return undefined;\n  }\n  return { query: 'status = error', type: 'auto' };\n};\n", "import { SceneComponentProps, SceneObjectBase, SceneObjectState, VizPanel } from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { But<PERSON>, Stack, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport React from 'react';\nimport { getFiltersVariable } from '../../../utils/utils';\nimport { addToFilters, filterExistsForKey } from '../actions/AddToFiltersAction';\nimport { computeHighestDifference } from '../../../utils/comparison';\n\nexport interface HighestDifferencePanelState extends SceneObjectState {\n  frame: DataFrame;\n  panel: VizPanel;\n  maxDifference?: number;\n  maxDifferenceIndex?: number;\n}\n\nexport class HighestDifferencePanel extends SceneObjectBase<HighestDifferencePanelState> {\n  constructor(state: HighestDifferencePanelState) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(() => this._onActivate());\n  }\n\n  private _onActivate() {\n    const { frame } = this.state;\n    this.setState({ ...computeHighestDifference(frame) });\n\n    this._subs.add(\n      this.subscribeToState((newState, prevState) => {\n        if (newState.frame !== prevState.frame) {\n          const { frame } = newState;\n          this.setState({ ...computeHighestDifference(frame) });\n        }\n      })\n    );\n  }\n\n  private getAttribute() {\n    return this.state.frame.name;\n  }\n\n  private getValue() {\n    const valueField = this.state.frame.fields.find((f) => f.name === 'Value');\n    return valueField?.values[this.state.maxDifferenceIndex || 0];\n  }\n\n  private onAddToFilters() {\n    const variable = getFiltersVariable(this);\n    const attribute = this.getAttribute();\n    if (attribute) {\n      addToFilters(variable, attribute, this.getValue());\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<HighestDifferencePanel>) => {\n    const { maxDifference, maxDifferenceIndex, panel } = model.useState();\n    const styles = useStyles2(getStyles);\n    const value = model.getValue();\n    const key = model.state.frame.name ?? '';\n    const filterExists = filterExistsForKey(getFiltersVariable(model), key, value.replace(/\"/g, ''));\n\n    return (\n      <div className={styles.container}>\n        {<panel.Component model={panel} />}\n        <div className={styles.differenceContainer}>\n          {maxDifference !== undefined && maxDifferenceIndex !== undefined && (\n            <>\n              <Stack gap={1} justifyContent={'space-between'} alignItems={'center'}>\n                <div className={styles.title}>Highest difference</div>\n                {!filterExists && (\n                  <Button\n                    size=\"sm\"\n                    variant=\"primary\"\n                    icon={'search-plus'}\n                    fill=\"text\"\n                    onClick={() => model.onAddToFilters()}\n                  >\n                    Add to filters\n                  </Button>\n                )}\n              </Stack>\n              <div className={styles.differenceValue}>\n                {(Math.abs(maxDifference) * 100).toFixed(maxDifference === 0 ? 0 : 2)}%\n              </div>\n              <div className={styles.value}>{value}</div>\n            </>\n          )}\n        </div>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n      height: '100%',\n    }),\n    differenceContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n      border: `1px solid ${theme.colors.secondary.border}`,\n      background: theme.colors.background.primary,\n      padding: '8px',\n      marginBottom: theme.spacing(2),\n      fontSize: '12px',\n      height: '116px',\n    }),\n    differenceValue: css({\n      fontSize: '36px',\n      fontWeight: 'bold',\n      textAlign: 'center',\n    }),\n    value: css({\n      textAlign: 'center',\n      color: theme.colors.secondary.text,\n      textWrap: 'nowrap',\n      whiteSpace: 'nowrap',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n    }),\n    title: css({\n      fontWeight: 500,\n    }),\n  };\n}\n", "import { PanelBuilders, SceneCSSGridItem, SceneCSSGridLayout, SceneDataNode, VizPanelState } from '@grafana/scenes';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { DataFrame, PanelData } from '@grafana/data';\nimport { AxisPlacement } from '@grafana/ui';\nimport { TooltipDisplayMode } from '@grafana/schema';\nimport { HighestDifferencePanel } from './HighestDifferencePanel';\nimport { GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\n\nexport const BaselineColor = '#5794F299';\nexport const SelectionColor = '#FF9930';\n\nexport function buildAllComparisonLayout(\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  return new ByFrameRepeater({\n    body: new SceneCSSGridLayout({\n      templateColumns: GRID_TEMPLATE_COLUMNS,\n      autoRows: '320px',\n      children: [],\n    }),\n    getLayoutChild: getLayoutChild(panels, getFrameName, actionsFn, metric),\n  });\n}\n\nconst getFrameName = (df: DataFrame) => {\n  return df.name || 'No name available';\n};\n\nfunction getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame) => string,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        series: [\n          {\n            ...frame,\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      const body = existingGridItem.state.body as HighestDifferencePanel;\n      body.setState({ frame });\n      body.state.panel.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const panel = getPanelConfig(metric).setTitle(getTitle(frame)).setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: new HighestDifferencePanel({ frame, panel: panel.build() }),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n\nexport function getPanelConfig(metric: MetricFunction) {\n  return PanelBuilders.barchart()\n    .setOption('legend', { showLegend: false })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi })\n    .setMax(1)\n    .setOverrides((overrides) => {\n      overrides.matchFieldsWithName('Value').overrideCustomFieldConfig('axisPlacement', AxisPlacement.Hidden);\n      overrides\n        .matchFieldsWithName('Baseline')\n        .overrideColor({\n          mode: 'fixed',\n          fixedColor: metric === 'duration' ? BaselineColor : 'semi-dark-green',\n        })\n        .overrideUnit('percentunit');\n      overrides\n        .matchFieldsWithName('Selection')\n        .overrideColor({\n          mode: 'fixed',\n          fixedColor: metric === 'duration' ? SelectionColor : 'semi-dark-red',\n        })\n        .overrideUnit('percentunit');\n    });\n}\n", "import { VAR_FILTERS_EXPR } from '../../../utils/shared';\n\nexport function buildHistogramQuery() {\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}} | histogram_over_time(duration)`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 1000,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import React from 'react';\n\nimport { SceneObjectBase, SceneComponentProps, SceneObjectState } from '@grafana/scenes';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Button, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { getMetricValue, getTraceByServiceScene, shouldShowSelection } from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { ComparisonSelection } from '../../../utils/shared';\n\nexport interface ComparisonControlState extends SceneObjectState {\n  selection?: ComparisonSelection;\n}\n\nexport class DurationComparisonControl extends SceneObjectBase<ComparisonControlState> {\n  public constructor({ selection }: ComparisonControlState) {\n    super({ selection });\n  }\n\n  public startInvestigation = () => {\n    const byServiceScene = getTraceByServiceScene(this);\n    byServiceScene.setState({ selection: this.state.selection });\n    if (!shouldShowSelection(byServiceScene.state.actionView)) {\n      byServiceScene.setActionView('comparison');\n    }\n\n    reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.start_investigation, {\n      selection: this.state.selection,\n      metric: getMetricValue(this),\n    });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<DurationComparisonControl>) => {\n    const { selection } = getTraceByServiceScene(model).useState();\n    const styles = useStyles2(getStyles);\n\n    const isDisabled = selection?.type === 'auto';\n    const tooltip = isDisabled\n      ? 'Slowest traces are selected, navigate to the Comparison or Slow Traces tab for more details.'\n      : undefined;\n\n    return (\n      <div className={styles.wrapper}>\n        <Button\n          variant=\"secondary\"\n          size=\"sm\"\n          fill=\"solid\"\n          disabled={isDisabled}\n          icon={'bolt'}\n          onClick={model.startInvestigation}\n          tooltip={tooltip}\n        >\n          {isDisabled ? 'Slowest traces selected' : 'Select slowest traces'}\n        </Button>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    wrapper: css({\n      display: 'flex',\n      gap: '16px',\n      alignItems: 'center',\n    }),\n    placeholder: css({\n      color: theme.colors.text.secondary,\n      fontSize: theme.typography.bodySmall.fontSize,\n      display: 'flex',\n      gap: theme.spacing.x0_5,\n    }),\n  };\n}\n", "import { AdHocVariableFilter, DataFrame, urlUtil } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  CustomVariable,\n  DataSourceVariable,\n  SceneDataQuery,\n  SceneDataState,\n  sceneGraph,\n  SceneObject,\n  SceneObjectUrlValues,\n  SceneTimeRange,\n  sceneUtils,\n} from '@grafana/scenes';\n\nimport { TraceExploration } from '../pages/Explore';\nimport {\n  EventTraceOpened,\n  EXPLORATIONS_ROUTE,\n  VAR_DATASOURCE,\n  VAR_DATASOURCE_EXPR,\n  VAR_FILTERS,\n  VAR_GROUPBY,\n  VAR_HOME_FILTER,\n  VAR_LATENCY_PARTIAL_THRESHOLD,\n  VAR_LATENCY_THRESHOLD,\n  VAR_METRIC,\n  VAR_PRIMARY_SIGNAL,\n  VAR_SPAN_LIST_COLUMNS,\n} from './shared';\nimport { TracesByServiceScene } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { ActionViewType } from '../components/Explore/TracesByService/Tabs/TabsBarScene';\nimport { Home } from 'pages/Home/Home';\nimport { PrimarySignalVariable } from 'pages/Explore/PrimarySignalVariable';\n\nexport function getTraceExplorationScene(model: SceneObject): TraceExploration {\n  return sceneGraph.getAncestor(model, TraceExploration);\n}\n\nexport function getHomeScene(model: SceneObject): Home {\n  return sceneGraph.getAncestor(model, Home);\n}\n\nexport function getTraceByServiceScene(model: SceneObject): TracesByServiceScene {\n  return sceneGraph.getAncestor(model, TracesByServiceScene);\n}\n\nexport function newTracesExploration(initialDS?: string, initialFilters?: AdHocVariableFilter[]): TraceExploration {\n  return new TraceExploration({\n    initialDS,\n    initialFilters: initialFilters ?? [],\n    $timeRange: new SceneTimeRange({ from: 'now-30m', to: 'now' }),\n  });\n}\n\nexport function newHome(initialFilters: AdHocVariableFilter[], initialDS?: string): Home {\n  return new Home({\n    initialDS,\n    initialFilters,\n    $timeRange: new SceneTimeRange({ from: 'now-30m', to: 'now' }),\n  });\n}\n\nexport function getErrorMessage(data: SceneDataState) {\n  return data?.data?.error?.message ?? 'There are no Tempo data sources';\n}\n\nexport function getNoDataMessage(context: string) {\n  return `No data for selected data source and filter. Select another to see ${context}.`;\n}\n\nexport function getUrlForExploration(exploration: TraceExploration) {\n  const params = sceneUtils.getUrlState(exploration);\n  return getUrlForValues(params);\n}\n\nexport function getUrlForValues(values: SceneObjectUrlValues) {\n  return urlUtil.renderUrl(EXPLORATIONS_ROUTE, values);\n}\n\nexport function getDataSource(exploration: TraceExploration) {\n  return sceneGraph.interpolate(exploration, VAR_DATASOURCE_EXPR);\n}\n\nexport const getFilterSignature = (filter: AdHocVariableFilter) => {\n  return `${filter.key}${filter.operator}${filter.value}`;\n};\n\nexport function getAttributesAsOptions(attributes: string[]) {\n  return attributes.map((attribute) => ({ label: attribute, value: attribute }));\n}\n\nexport function getLabelKey(frame: DataFrame) {\n  const labels = frame.fields.find((f) => f.type === 'number')?.labels;\n\n  if (!labels) {\n    return 'No labels';\n  }\n\n  const keys = Object.keys(labels);\n  if (keys.length === 0) {\n    return 'No labels';\n  }\n\n  return keys[0].replace(/\"/g, '');\n}\n\nexport function getLabelValue(frame: DataFrame, labelName?: string) {\n  const labels = frame.fields.find((f) => f.type === 'number')?.labels;\n\n  if (!labels) {\n    return 'No labels';\n  }\n\n  const keys = Object.keys(labels).filter((k) => k !== 'p'); // remove the percentile label\n  if (keys.length === 0) {\n    return 'No labels';\n  }\n\n  return labels[labelName || keys[0]].replace(/\"/g, '');\n}\n\nexport function getGroupByVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_GROUPBY, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Group by variable not found');\n  }\n  return variable;\n}\n\nexport function getSpanListColumnsVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_SPAN_LIST_COLUMNS, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Span list columns variable not found');\n  }\n  return variable;\n}\n\nexport function getLatencyThresholdVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_LATENCY_THRESHOLD, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Latency threshold variable not found');\n  }\n  return variable;\n}\n\nexport function getLatencyPartialThresholdVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_LATENCY_PARTIAL_THRESHOLD, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Partial latency threshold variable not found');\n  }\n  return variable;\n}\n\nexport function getMetricVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_METRIC, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Metric variable not found');\n  }\n  return variable;\n}\n\nexport function getFiltersVariable(scene: SceneObject): AdHocFiltersVariable {\n  const variable = sceneGraph.lookupVariable(VAR_FILTERS, scene);\n  if (!(variable instanceof AdHocFiltersVariable)) {\n    throw new Error('Filters variable not found');\n  }\n  return variable;\n}\n\nexport function getPrimarySignalVariable(scene: SceneObject): PrimarySignalVariable {\n  const variable = sceneGraph.lookupVariable(VAR_PRIMARY_SIGNAL, scene);\n  if (!(variable instanceof PrimarySignalVariable)) {\n    throw new Error('Primary signal variable not found');\n  }\n  return variable;\n}\n\nexport function getHomeFilterVariable(scene: SceneObject): AdHocFiltersVariable {\n  const variable = sceneGraph.lookupVariable(VAR_HOME_FILTER, scene);\n  if (!(variable instanceof AdHocFiltersVariable)) {\n    throw new Error('Home filter variable not found');\n  }\n  return variable;\n}\n\nexport function getDatasourceVariable(scene: SceneObject): DataSourceVariable {\n  const variable = sceneGraph.lookupVariable(VAR_DATASOURCE, scene);\n  if (!(variable instanceof DataSourceVariable)) {\n    throw new Error('Datasource variable not found');\n  }\n  return variable;\n}\n\nexport function getCurrentStep(scene: SceneObject): number | undefined {\n  const data = sceneGraph.getData(scene).state.data;\n  const targetQuery = data?.request?.targets[0];\n  return targetQuery ? (targetQuery as SceneDataQuery).step : undefined;\n}\n\nexport function shouldShowSelection(tab?: ActionViewType): boolean {\n  return tab === 'comparison' || tab === 'traceList';\n}\n\nexport function getMetricValue(scene: SceneObject) {\n  return getMetricVariable(scene).useState().value;\n}\n\nexport function fieldHasEmptyValues(data: SceneDataState) {\n  return data?.data?.series[0].fields?.some((v) => v.values.every((e) => e === undefined)) ?? false;\n}\n\nexport const isNumber = /^-?\\d+\\.?\\d*$/;\n\nexport const formatLabelValue = (value: string) => {\n  if (!isNumber.test(value) && typeof value === 'string' && !value.startsWith('\"') && !value.endsWith('\"')) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n\nexport const capitalizeFirstChar = (str: string) => str?.[0]?.toUpperCase() + str?.slice(1) || '';\n\nexport const getOpenTrace = (scene: SceneObject) => {\n  return (traceId: string, spanId?: string) => {\n    scene.publishEvent(new EventTraceOpened({ traceId, spanId }), true);\n  };\n};\n", "import { map, Observable } from 'rxjs';\nimport { DataFrame, DataTopic, Field } from '@grafana/data';\nimport { CustomTransformerDefinition } from '@grafana/scenes';\n\nexport const exemplarsTransformations = (\n  openTrace?: (traceId: string, spanId?: string) => void\n): CustomTransformerDefinition[] => [\n  {\n    topic: DataTopic.Annotations,\n    operator: () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.map((frame) => {\n            if (frame.name === 'exemplar') {\n              const traceIDField = frame.fields.find((field: Field) => field.name === 'traceId');\n              if (traceIDField) {\n                // The traceID will be interpolated in the url\n                // Then, onClick we retrieve the traceId from the url and navigate to the trace exploration scene by setting the state\n                traceIDField.config.links = [\n                  {\n                    title: 'View trace',\n                    url: '#${__value.raw}',\n                    onClick: (event) => {\n                      event.e.stopPropagation(); // Prevent the click event from propagating to the parent anchor\n                      const parentAnchorHref = event.e.target?.parentElement?.parentElement?.href;\n                      if (!parentAnchorHref || parentAnchorHref.indexOf('#') === -1) {\n                        return;\n                      }\n                      const traceId = parentAnchorHref.split('#')[1];\n                      if (!traceId || traceId === '') {\n                        return;\n                      }\n                      openTrace?.(traceId);\n                    },\n                  },\n                ];\n              }\n            }\n\n            return frame;\n          });\n        })\n      );\n    },\n  },\n];\n\nexport const removeExemplarsTransformation = (): CustomTransformerDefinition[] => [\n  {\n    topic: DataTopic.Annotations,\n    operator: () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.filter((frame) => frame.name !== 'exemplar');\n        })\n      );\n    },\n  },\n];\n\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { arrayToDataFrame, DataFrame, GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { ComparisonSelection, EMPTY_STATE_ERROR_MESSAGE, explorationDS, MetricFunction } from 'utils/shared';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { SkeletonComponent } from '../ByFrameRepeater';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { metricByWithStatus } from '../queries/generateMetricsQuery';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { css } from '@emotion/css';\nimport { RadioButtonList, useStyles2 } from '@grafana/ui';\nimport { StreamingIndicator } from '../StreamingIndicator';\nimport {\n  fieldHasEmptyValues,\n  getLatencyPartialThresholdVariable,\n  getLatencyThresholdVariable,\n  getMetricVariable,\n  getOpenTrace,\n  getTraceByServiceScene,\n  shouldShowSelection,\n} from '../../../utils/utils';\nimport { getHistogramVizPanel, yBucketToDuration } from '../panels/histogram';\nimport { TraceSceneState } from './TracesByServiceScene';\nimport { SelectionColor } from '../layouts/allComparison';\nimport { buildHistogramQuery } from '../queries/histogram';\nimport { isEqual } from 'lodash';\nimport { DurationComparisonControl } from './DurationComparisonControl';\nimport { exemplarsTransformations, removeExemplarsTransformation } from '../../../utils/exemplars';\n\nexport interface RateMetricsPanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  actions?: SceneObject[];\n  yBuckets?: number[];\n  isStreaming?: boolean;\n}\n\nexport class REDPanel extends SceneObjectBase<RateMetricsPanelState> {\n  constructor(state: RateMetricsPanelState) {\n    super({\n      yBuckets: [],\n      actions: [],\n      isStreaming: false,\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this._onActivate();\n      const data = sceneGraph.getData(this);\n      const parent = getTraceByServiceScene(this);\n      const timeRange = sceneGraph.getTimeRange(this);\n\n      this._subs.add(\n        data.subscribeToState((newData) => {\n          this.setState({ isStreaming: newData.data?.state === LoadingState.Streaming });\n\n          if (newData.data?.state === LoadingState.Done) {\n            if (\n              newData.data.series.length === 0 ||\n              newData.data.series[0].length === 0 ||\n              fieldHasEmptyValues(newData)\n            ) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new SceneFlexItem({\n                      body: new EmptyStateScene({\n                        message: EMPTY_STATE_ERROR_MESSAGE,\n                        imgWidth: 150,\n                      }),\n                    }),\n                  ],\n                }),\n              });\n            } else {\n              let yBuckets: number[] | undefined = [];\n              if (this.isDuration()) {\n                yBuckets = getYBuckets(data.state.data?.series || []);\n                if (parent.state.selection && newData.data?.state === LoadingState.Done) {\n                  // set selection annotation if it exists\n                  const annotations = this.buildSelectionAnnotation(parent.state);\n\n                  if (annotations && !data.state.data?.annotations?.length) {\n                    data.setState({\n                      data: {\n                        ...data.state.data!,\n                        annotations: annotations,\n                      },\n                    });\n                  }\n                }\n\n                if (yBuckets?.length) {\n                  const { minDuration, minBucket } = getMinimumsForDuration(yBuckets);\n                  const selection: ComparisonSelection = { type: 'auto' };\n\n                  getLatencyThresholdVariable(this).changeValueTo(minDuration);\n                  getLatencyPartialThresholdVariable(this).changeValueTo(\n                    yBucketToDuration(minBucket - 1, yBuckets, 0.3)\n                  );\n\n                  selection.duration = { from: minDuration, to: '' };\n                  selection.raw = {\n                    x: {\n                      from: timeRange.state.value.from.unix() * 1000,\n                      to: timeRange.state.value.to.unix() * 1000,\n                    },\n                    y: { from: minBucket - 0.5, to: yBuckets.length - 0.5 },\n                  };\n\n                  this.setState({\n                    actions: [\n                      new DurationComparisonControl({\n                        selection,\n                      }),\n                    ],\n                  });\n                  if (!parent.state.selection?.duration || parent.state.selection.type === 'auto') {\n                    parent.setState({ selection });\n                  }\n                }\n              }\n\n              // update panel\n              this.setState({\n                yBuckets,\n                panel: this.getVizPanel(),\n              });\n            }\n          } else if (newData.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(1),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n\n      this._subs.add(\n        parent.subscribeToState((newState, prevState) => {\n          if (data.state.data?.state === LoadingState.Done) {\n            if (!isEqual(newState.selection, prevState.selection) || newState.actionView !== prevState.actionView) {\n              if (this.isDuration()) {\n                const annotations = this.buildSelectionAnnotation(newState);\n                data.setState({\n                  data: {\n                    ...data.state.data!,\n                    annotations: annotations,\n                  },\n                });\n              }\n            }\n          }\n        })\n      );\n    });\n  }\n\n  private isDuration() {\n    return getMetricVariable(this).state.value === 'duration';\n  }\n\n  private _onActivate() {\n    const metric = getMetricVariable(this).state.value as MetricFunction;\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new StepQueryRunner({\n          maxDataPoints: this.isDuration() ? 24 : 64,\n          datasource: explorationDS,\n          queries: [this.isDuration() ? buildHistogramQuery() : metricByWithStatus(metric)],\n        }),\n        transformations: this.isDuration()\n          ? [...removeExemplarsTransformation()]\n          : [...exemplarsTransformations(getOpenTrace(this))],\n      }),\n      panel: this.getVizPanel(),\n    });\n  }\n\n  private getVizPanel() {\n    const metric = getMetricVariable(this).state.value as MetricFunction;\n    if (this.isDuration()) {\n      return getHistogramVizPanel(this, this.state.yBuckets ?? []);\n    }\n\n    return this.getRateOrErrorVizPanel(metric);\n  }\n\n  private getRateOrErrorVizPanel(type: MetricFunction) {\n    const panel = barsPanelConfig().setHoverHeader(true).setDisplayMode('transparent');\n    if (type === 'rate') {\n      panel.setCustomFieldConfig('axisLabel', 'span/s');\n    } else if (type === 'errors') {\n      panel.setCustomFieldConfig('axisLabel', 'error/s').setColor({\n        fixedColor: 'semi-dark-red',\n        mode: 'fixed',\n      });\n    }\n    return new SceneFlexLayout({\n      direction: 'row',\n      children: [\n        new SceneFlexItem({\n          body: panel.build(),\n        }),\n      ],\n    });\n  }\n\n  private buildSelectionAnnotation(state: TraceSceneState) {\n    if (!shouldShowSelection(state.actionView)) {\n      return undefined;\n    }\n\n    const xSel = state.selection?.raw?.x;\n    const ySel = state.selection?.raw?.y;\n\n    const frame = arrayToDataFrame([\n      {\n        time: xSel?.from || 0,\n        xMin: xSel?.from || 0,\n        xMax: xSel?.to || 0,\n        timeEnd: xSel?.to || 0,\n        yMin: ySel?.from,\n        yMax: ySel?.to,\n        isRegion: true,\n        fillOpacity: 0.15,\n        lineWidth: 1,\n        lineStyle: 'solid',\n        color: SelectionColor,\n        text: 'Comparison selection',\n      },\n    ]);\n    frame.name = 'xymark';\n\n    return [frame];\n  }\n\n  public static Component = ({ model }: SceneComponentProps<REDPanel>) => {\n    const { panel, actions, isStreaming } = model.useState();\n    const { value: metric } = getMetricVariable(model).useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    const getTitle = () => {\n      switch (metric) {\n        case 'errors':\n          return 'Errors rate';\n        case 'rate':\n          return 'Span rate';\n        case 'duration':\n          return 'Histogram by duration';\n        default:\n          return '';\n      }\n    };\n\n    const getSubtitle = () => {\n      switch (metric) {\n        case 'duration':\n          return 'Click and drag to compare selection with baseline.';\n        default:\n          return '';\n      }\n    };\n\n    const subtitle = getSubtitle();\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.headerContainer}>\n          <div className={styles.titleContainer}>\n            <div className={styles.titleRadioWrapper}>\n              <RadioButtonList\n                name={`metric-${metric}`}\n                options={[{ title: '', value: 'selected' }]}\n                value={'selected'}\n              />\n              <span>{getTitle()}</span>\n            </div>\n            {subtitle && <div className={styles.subtitle}>{subtitle}</div>}\n          </div>\n          <div className={styles.actions}>\n            {isStreaming && <StreamingIndicator isStreaming={true} iconSize={10} />}\n            {actions?.map((action) => <action.Component model={action} key={action.state.key} />)}\n          </div>\n        </div>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nexport const getYBuckets = (series: DataFrame[]) => {\n  return series.map((s) => parseFloat(s.fields[1].name)).sort((a, b) => a - b);\n};\n\nexport const getMinimumsForDuration = (yBuckets: number[]) => {\n  const slowestBuckets = Math.floor(yBuckets.length / 4);\n  let minBucket = yBuckets.length - slowestBuckets - 1;\n  if (minBucket < 0) {\n    minBucket = 0;\n  }\n\n  return {\n    minDuration: yBucketToDuration(minBucket - 1, yBuckets),\n    minBucket,\n  };\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: `1px solid ${theme.colors.border.weak}`,\n      borderRadius: '2px',\n      background: theme.colors.background.primary,\n\n      '.show-on-hover': {\n        display: 'none',\n      },\n      'section, section:hover': {\n        borderColor: 'transparent',\n      },\n      '& .u-select': {\n        border: '1px solid #ffffff75',\n      },\n    }),\n    headerContainer: css({\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'row',\n      padding: '8px',\n      gap: '8px',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start',\n      fontWeight: theme.typography.fontWeightBold,\n    }),\n    titleContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '4px',\n    }),\n    titleRadioWrapper: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    actions: css({\n      display: 'flex',\n      gap: '8px',\n      alignItems: 'center',\n    }),\n    subtitle: css({\n      display: 'flex',\n      color: theme.colors.text.secondary,\n      fontSize: '12px',\n      fontWeight: 400,\n\n      '& svg': {\n        margin: '0 2px',\n      },\n    }),\n  };\n}\n", "import React, { useState } from 'react';\nimport { useLocation } from 'react-use';\n\nimport { ToolbarButton } from '@grafana/ui';\n\nimport { TraceExploration } from '../../../pages/Explore';\nimport { getUrlForExploration } from '../../../utils/utils';\n\ninterface ShareExplorationActionState {\n  exploration: TraceExploration;\n}\n\nexport const ShareExplorationAction = ({ exploration }: ShareExplorationActionState) => {\n  const { origin } = useLocation();\n  const [tooltip, setTooltip] = useState('Copy url');\n\n  const onShare = () => {\n    if (navigator.clipboard) {\n      navigator.clipboard.writeText(origin + getUrlForExploration(exploration));\n      setTooltip('Copied!');\n      setTimeout(() => {\n        setTooltip('Copy url');\n      }, 2000);\n    }\n  };\n\n  return <ToolbarButton variant={'canvas'} icon={'share-alt'} tooltip={tooltip} onClick={onShare} />;\n};\n", "import React, { useMemo } from 'react';\n\nimport { SelectableValue } from '@grafana/data';\nimport { Icon, Select, Field, useStyles2 } from '@grafana/ui';\nimport { VariableValue } from '@grafana/scenes';\nimport { css } from '@emotion/css';\n\nconst RECOMMENDED_ATTRIBUTES = [\n  'span.http.method', \n  'span.http.request.method', \n  'span.http.route', \n  'span.http.path', \n  'span.http.status_code', \n  'span.http.response.status_code'\n]; \n\ntype Props = {\n  options: Array<SelectableValue<string>>;\n  onChange: (columns: string[]) => void;\n  value?: VariableValue;\n};\n\nconst labelOrder = ['Recommended', 'Resource', 'Span', 'Other'];\n\nexport function SpanListColumnsSelector({ options, value, onChange }: Props) {\n  const styles = useStyles2(getStyles);\n\n  const opt = useMemo(\n    () =>\n      Object.values(\n        options.reduce((acc, curr) => {\n          if (curr.label) {\n            const label = curr.label.slice(curr.label.indexOf('.') + 1);\n\n            // use text until first dot as key\n            if (RECOMMENDED_ATTRIBUTES.includes(curr.label)) {\n              const group = acc['recommended'] ?? { label: 'Recommended', options: [] };\n              group.options.push({ ...curr, label });\n              acc['recommended'] = group;\n            } else if (curr.label.startsWith('resource.')) {\n              const group = acc['resource'] ?? { label: 'Resource', options: [] };\n              group.options.push({ ...curr, label });\n              acc['resource'] = group;\n            } else {\n              if (curr.label.startsWith('span.')) {\n                const group = acc['span'] ?? { label: 'Span', options: [] };\n                group.options.push({ ...curr, label });\n                acc['span'] = group;\n              } else {\n                const group = acc['other'] ?? { label: 'Other', options: [] };\n                group.options.push(curr);\n                acc['other'] = group;\n              }\n            }\n          }\n          return acc;\n        }, {})\n      ).sort((a, b) => labelOrder.indexOf(a.label) - labelOrder.indexOf(b.label)),\n    [options]\n  );\n\n  return (\n    <div className={styles.container}>\n      <Field label=\"Add extra columns\">\n        <Select\n          value={value?.toString() !== '' ? (value?.toString()?.split(',') ?? '') : ''}\n          placeholder={'Select an attribute'}\n          options={opt}\n          onChange={(x) => onChange(x.map((x: SelectableValue) => x.value).join(','))}\n          isMulti={true}\n          isClearable\n          virtualized\n          prefix={<Icon name=\"columns\" />}\n        />\n      </Field>\n    </div>\n  );\n}\n\nconst getStyles = () => {\n  return {\n    container: css({\n      display: 'flex',\n      minWidth: '300px',\n\n      '& > div': {\n        width: '100%',\n      },\n    }),\n  };\n};\n", "import React from 'react';\n\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2, LoadingState, PanelData, toURLRange, urlUtil, toOption } from '@grafana/data';\nimport { config } from '@grafana/runtime';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { Icon, Link, TableCellDisplayMode, TableCustomCellOptions, useStyles2, useTheme2 } from '@grafana/ui';\nimport { map, Observable } from 'rxjs';\nimport {\n  getDataSource,\n  getSpanListColumnsVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from '../../../../../utils/utils';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  EventTraceOpened,\n} from '../../../../../utils/shared';\nimport { SpanListColumnsSelector } from './SpanListColumnsSelector';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\n\nexport interface SpanListSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  dataState: 'empty' | 'loading' | 'done';\n}\n\nexport class SpanListScene extends SceneObjectBase<SpanListSceneState> {\n  constructor(state: Partial<SpanListSceneState>) {\n    super({\n      dataState: 'empty',\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this.setState({\n        $data: new SceneDataTransformer({\n          transformations: this.setupTransformations(),\n        }),\n      });\n      const sceneData = sceneGraph.getData(this);\n\n      this.updatePanel(sceneData.state.data);\n      this._subs.add(\n        sceneData.subscribeToState((data) => {\n          this.updatePanel(data.data);\n        })\n      );\n    });\n  }\n\n  private setupTransformations() {\n    return [\n      () => (source: Observable<DataFrame[]>) => {\n        return source.pipe(\n          map((data: DataFrame[]) => {\n            return data.map((df) => {\n              const fields = df.fields;\n              const nameField = fields.find((f) => f.name === 'traceName');\n\n              const options: TableCustomCellOptions = {\n                type: TableCellDisplayMode.Custom,\n                cellComponent: (props) => {\n                  const data = props.frame;\n                  const traceIdField = data?.fields.find((f) => f.name === 'traceIdHidden');\n                  const spanIdField = data?.fields.find((f) => f.name === 'spanID');\n                  const traceId = traceIdField?.values[props.rowIndex];\n                  const spanId = spanIdField?.values[props.rowIndex];\n\n                  if (!traceId) {\n                    return props.value as string;\n                  }\n\n                  const name = props.value ? (props.value as string) : '<name not yet available>';\n                  return (\n                    <div className={'cell-link-wrapper'}>\n                      <div\n                        className={'cell-link'}\n                        title={name}\n                        onClick={() => {\n                          this.publishEvent(new EventTraceOpened({ traceId, spanId }), true);\n                        }}\n                      >\n                        {name}\n                      </div>\n                      <Link href={this.getLinkToExplore(traceId, spanId)} target={'_blank'} title={'Open in new tab'}>\n                        <Icon name={'external-link-alt'} size={'sm'} />\n                      </Link>\n                    </div>\n                  );\n                },\n              };\n              if (nameField?.config?.custom) {\n                nameField.config.custom.cellOptions = options;\n              }\n              return {\n                ...df,\n                fields,\n              };\n            });\n          })\n        );\n      },\n    ];\n  }\n\n  private getLinkToExplore = (traceId: string, spanId: string) => {\n    const traceExplorationScene = getTraceExplorationScene(this);\n    const datasource = getDataSource(traceExplorationScene);\n\n    const timeRange = sceneGraph.getTimeRange(this).state.value;\n    const exploreState = JSON.stringify({\n      ['explore-traces']: {\n        range: toURLRange(timeRange.raw),\n        queries: [{ refId: 'traceId', queryType: 'traceql', query: traceId, datasource }],\n        panelsState: {\n          trace: {\n            spanId,\n          },\n        },\n        datasource,\n      },\n    });\n    const subUrl = config.appSubUrl ?? '';\n    return urlUtil.renderUrl(`${subUrl}/explore`, { panes: exploreState, schemaVersion: 1 });\n  };\n\n  private updatePanel(data?: PanelData) {\n    if (\n      data?.state === LoadingState.Loading ||\n      data?.state === LoadingState.NotStarted ||\n      !data?.state ||\n      (data?.state === LoadingState.Streaming && !data.series?.[0]?.length)\n    ) {\n      if (this.state.dataState === 'loading') {\n        return;\n      }\n      this.setState({\n        dataState: 'loading',\n        panel: new SceneFlexLayout({\n          direction: 'row',\n          children: [\n            new LoadingStateScene({\n              component: SkeletonComponent,\n            }),\n          ],\n        }),\n      });\n      return;\n    }\n    if (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) {\n      if (data.series.length === 0 || data.series[0].length === 0) {\n        if (this.state.dataState === 'empty' && this.state.panel) {\n          return;\n        }\n        this.setState({\n          dataState: 'empty',\n          panel: new SceneFlexLayout({\n            children: [\n              new SceneFlexItem({\n                body: new EmptyStateScene({\n                  message: EMPTY_STATE_ERROR_MESSAGE,\n                  remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                  padding: '32px',\n                }),\n              }),\n            ],\n          }),\n        });\n      } else if (this.state.dataState !== 'done') {\n        this.setState({\n          dataState: 'done',\n          panel: new SceneFlexLayout({\n            direction: 'row',\n            children: [\n              new SceneFlexItem({\n                body: PanelBuilders.table()\n                  .setHoverHeader(true)\n                  .setOverrides((builder) => {\n                    return builder\n                      .matchFieldsWithName('spanID')\n                      .overrideCustomFieldConfig('hidden', true)\n                      .matchFieldsWithName('traceService')\n                      .overrideCustomFieldConfig('width', 350)\n                      .matchFieldsWithName('traceName')\n                      .overrideCustomFieldConfig('width', 350);\n                  })\n                  .build(),\n              }),\n            ],\n          }),\n        });\n      }\n    }\n  }\n\n  public onChange = (columns: string[]) => {\n    const variable = getSpanListColumnsVariable(this);\n    if (variable.getValue() !== columns) {\n      variable.changeValueTo(columns);\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.span_list_columns_changed,\n        {\n          columns,\n        }\n      );\n    }\n  };\n\n  public static Component = ({ model }: SceneComponentProps<SpanListScene>) => {\n    const { panel } = model.useState();\n    const styles = getStyles(useTheme2());\n    const variable = getSpanListColumnsVariable(model);\n    const { attributes } = getTraceByServiceScene(model).useState();\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.description}>View a list of spans for the current set of filters.</div>\n          <SpanListColumnsSelector\n            options={attributes?.map((x) => toOption(x)) ?? []}\n            value={variable.getValue()}\n            onChange={model.onChange}\n          />\n        </div>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'contents',\n\n      '[role=\"cell\"] > div': {\n        display: 'flex',\n        width: '100%',\n      },\n\n      '.cell-link-wrapper': {\n        display: 'flex',\n        gap: '4px',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        width: '100%',\n\n        a: {\n          padding: 4,\n          fontSize: 0,\n\n          ':hover': {\n            background: theme.colors.background.secondary,\n          },\n        },\n      },\n\n      '.cell-link': {\n        color: theme.colors.text.link,\n        cursor: 'pointer',\n        maxWidth: '300px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n\n        ':hover': {\n          textDecoration: 'underline',\n        },\n      },\n    }),\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0 ${theme.spacing(2)} 0`,\n    }),\n    header: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start',\n      gap: '10px',\n    }),\n  };\n};\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.title}>\n        <Skeleton count={1} width={80} />\n      </div>\n      {[...Array(3)].map((_, i) => (\n        <div className={styles.row} key={i}>\n          {[...Array(6)].map((_, j) => (\n            <span className={styles.rowItem} key={j}>\n              <Skeleton count={1} />\n            </span>\n          ))}\n        </div>\n      ))}\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      height: '100%',\n      width: '100%',\n      position: 'absolute',\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.border.weak}`,\n      padding: '5px',\n    }),\n    title: css({\n      marginBottom: '20px',\n    }),\n    row: css({\n      marginBottom: '5px',\n      display: 'flex',\n      justifyContent: 'space-around',\n    }),\n    rowItem: css({\n      width: '14%',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { SceneComponentProps, SceneFlexItem, SceneObject, SceneObjectBase, SceneObjectState } from '@grafana/scenes';\nimport { SpanListScene } from 'components/Explore/TracesByService/Tabs/Spans/SpanListScene';\nimport { getMetricVariable, getTraceByServiceScene } from 'utils/utils';\n\nexport interface SpansSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class SpansScene extends SceneObjectBase<SpansSceneState> {\n  constructor(state: Partial<SpansSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this._subs.add(\n      getTraceByServiceScene(this).state.$data?.subscribeToState(() => {\n        this.updateBody();\n      })\n    );\n\n    this._subs.add(\n      getTraceByServiceScene(this).subscribeToState((newState, prevState) => {\n        if (newState.$data?.state.key !== prevState.$data?.state.key) {\n          this.updateBody();\n        }\n      })\n    );\n\n    this._subs.add(\n      getMetricVariable(this).subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          this.updateBody();\n        }\n      })\n    );\n\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new SpanListScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<SpansScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildSpansScene() {\n  return new SceneFlexItem({\n    body: new SpansScene({}),\n  });\n}\n", "import { Span } from '../../types';\n\nexport function nestedSetLeft(span: Span): number {\n  if (span.attributes) {\n    for (const a of span.attributes) {\n      if (a.key === 'nestedSetLeft') {\n        return parseInt(a.value.intValue || a.value.Value?.int_value || '0', 10);\n      }\n    }\n  }\n\n  throw new Error('nestedSetLeft not found!');\n}\n\nexport function nestedSetRight(span: Span): number {\n  if (span.attributes) {\n    for (const a of span.attributes) {\n      if (a.key === 'nestedSetRight') {\n        return parseInt(a.value.intValue || a.value.Value?.int_value || '0', 10);\n      }\n    }\n  }\n\n  throw new Error('nestedSetRight not found!');\n}\n", "import { Span } from '../../types';\nimport { nestedSetLeft, nestedSetRight } from './utils';\n\nexport class TreeNode {\n  name: string;\n  serviceName: string;\n  operationName: string;\n  spans: Span[];\n  left: number;\n  right: number;\n  children: TreeNode[];\n  parent: TreeNode | null;\n  traceID: string;\n\n  constructor({\n    name,\n    serviceName,\n    operationName,\n    spans,\n    left,\n    right,\n    traceID,\n  }: {\n    name: string;\n    serviceName: string;\n    operationName: string;\n    spans: Span[];\n    left: number;\n    right: number;\n    traceID: string;\n  }) {\n    this.name = name;\n    this.serviceName = serviceName;\n    this.operationName = operationName;\n    this.spans = spans;\n    this.left = left;\n    this.right = right;\n    this.children = [];\n    this.parent = null;\n    this.traceID = traceID;\n  }\n\n  addSpan(span: Span) {\n    // expand our left/right based on this span\n    this.left = Math.min(nestedSetLeft(span), this.left);\n    this.right = Math.max(nestedSetRight(span), this.right);\n    this.spans.push(span);\n  }\n\n  addChild(node: TreeNode) {\n    node.parent = this;\n    this.children.push(node);\n  }\n\n  isChild(span: Span): boolean {\n    return nestedSetLeft(span) > this.left && nestedSetRight(span) < this.right;\n  }\n\n  findMatchingChild(span: Span): TreeNode | null {\n    const name = nodeName(span);\n\n    for (const child of this.children) {\n      if (child.name === name) {\n        return child;\n      }\n    }\n\n    return null;\n  }\n}\n\nexport function createNode(s: Span): TreeNode {\n  const serviceNameAttr = s.attributes?.find((a) => a.key === 'service.name');\n  return new TreeNode({\n    left: nestedSetLeft(s),\n    right: nestedSetRight(s),\n    name: nodeName(s),\n    serviceName: serviceNameAttr?.value.stringValue ?? serviceNameAttr?.value?.Value?.string_value ?? '',\n    operationName: s.name ?? '',\n    spans: [s],\n    traceID: s.traceId ?? '',\n  });\n}\n\nfunction nodeName(s: Span): string {\n  let svcName = '';\n  for (const a of s.attributes || []) {\n    if (a.key === 'service.name' && a.value.stringValue) {\n      svcName = a.value.stringValue;\n    }\n  }\n\n  return `${svcName}:${s.name}`;\n}\n", "import { TraceSearchMetadata } from '../../types';\nimport { createNode, TreeNode } from './tree-node';\nimport { nestedSetLeft } from './utils';\n\nexport function mergeTraces(traces: TraceSearchMetadata[]): TreeNode {\n  const tree = new TreeNode({\n    name: 'root',\n    serviceName: '',\n    operationName: '',\n    left: Number.MIN_SAFE_INTEGER,\n    right: Number.MAX_SAFE_INTEGER,\n    spans: [],\n    traceID: '',\n  });\n\n  if (traces && traces.length > 0) {\n    for (const trace of traces) {\n      if (trace.spanSets?.length !== 1) {\n        throw new Error('there should be only 1 spanset!');\n      }\n\n      const traceStartTime = parseInt(trace.startTimeUnixNano || '0', 10);\n\n      const ss = trace.spanSets[0];\n      // sort by nestedSetLeft\n      ss.spans.sort((s1, s2) => nestedSetLeft(s1) - nestedSetLeft(s2));\n\n      // reset curNode to root each loop to re-overlay the next trace\n      let curNode: TreeNode = tree;\n      // left/right is only valid w/i a trace, so reset it each loop\n      resetLeftRight(tree);\n      for (const span of ss.spans) {\n        // force traceID to be the same for all spans in a trace\n        span.traceId = trace.traceID;\n        span.startTimeUnixNano = `${parseInt(span.startTimeUnixNano, 10) - traceStartTime}`;\n\n        // walk up the tree until we find a node that is a parent of this span\n        while (curNode.parent !== null) {\n          if (curNode.isChild(span)) {\n            break;\n          }\n          curNode = curNode.parent;\n        }\n\n        // is there an already existing child that matches the span?\n        const child = curNode.findMatchingChild(span);\n        if (child) {\n          child.addSpan(span);\n          // to the next span!\n          curNode = child;\n          continue;\n        }\n\n        // if not, create a new child node and make it the cur node\n        const newNode = createNode(span);\n        newNode.traceID = trace.traceID;\n        curNode.addChild(newNode);\n        curNode = newNode;\n      }\n    }\n  }\n\n  return tree;\n}\n\nexport function dumpTree(t: TreeNode, depth: number): string {\n  let result = '';\n  const space = ' '.repeat(depth * 2);\n\n  result += `${space}${t.name} ${t.spans.length}\\n`;\n\n  for (const c of t.children) {\n    result += dumpTree(c, depth + 1);\n  }\n  return result;\n}\n\nfunction resetLeftRight(t: TreeNode) {\n  t.left = Number.MAX_SAFE_INTEGER;\n  t.right = Number.MIN_SAFE_INTEGER;\n\n  for (const c of t.children) {\n    resetLeftRight(c);\n  }\n}\n", "import React from 'react';\n\nimport {\n  Panel<PERSON><PERSON>ers,\n  SceneComponentProps,\n  SceneDataNode,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  explorationDS,\n  filterStreamingProgressTransformations,\n  MetricFunction,\n  VAR_FILTERS_EXPR,\n  VAR_LATENCY_PARTIAL_THRESHOLD_EXPR,\n  VAR_LATENCY_THRESHOLD_EXPR,\n} from '../../../../../utils/shared';\nimport { TraceSearchMetadata } from '../../../../../types';\nimport { mergeTraces } from '../../../../../utils/trace-merge/merge';\nimport { createDataFrame, Field, FieldType, GrafanaTheme2, LinkModel, LoadingState } from '@grafana/data';\nimport { TreeNode } from '../../../../../utils/trace-merge/tree-node';\nimport { Icon, LinkButton, Stack, Text, useTheme2 } from '@grafana/ui';\nimport Skeleton from 'react-loading-skeleton';\nimport { EmptyState } from '../../../../states/EmptyState/EmptyState';\nimport { css } from '@emotion/css';\nimport { getOpenTrace, getTraceExplorationScene } from 'utils/utils';\nimport { structureDisplayName } from '../TabsBarScene';\n\nexport interface ServicesTabSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  loading?: boolean;\n  tree?: TreeNode;\n  metric?: MetricFunction;\n}\n\nconst ROOT_SPAN_ID = '0000000000000000';\n\nexport class StructureTabScene extends SceneObjectBase<ServicesTabSceneState> {  \n  constructor(state: Partial<ServicesTabSceneState>) {\n    super({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(state.metric as MetricFunction)],\n        }),\n        transformations: filterStreamingProgressTransformations,\n      }),\n      loading: true,\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  public _onActivate() {\n    this._subs.add(\n      this.state.$data?.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Loading || state.data?.state === LoadingState.Streaming) {\n          this.setState({ loading: true });\n          return;\n        }\n        \n        if (state.data?.state === LoadingState.Done && state.data?.series.length) {\n          const frame = state.data?.series[0].fields[0].values[0];\n          if (frame) {\n            const response = JSON.parse(frame) as TraceSearchMetadata[];\n            const tree = mergeTraces(response);\n            tree.children.sort((a, b) => countSpans(b) - countSpans(a));\n                        \n            this.setState({\n              loading: false,\n              tree,\n              panel: new SceneFlexLayout({\n                height: '100%',\n                wrap: 'wrap',\n                children: this.getPanels(tree),\n              }),\n            });\n          }\n        }\n      })\n    );\n  }\n\n  private getPanels(tree: TreeNode) {\n    return tree.children.map((child) => {\n      return new SceneFlexItem({\n        height: 150,\n        width: '100%',\n        minHeight: '400px',\n        body: this.getPanel(child),\n      });\n    });\n  }\n\n  private getPanel(tree: TreeNode) {\n    const timeRange = sceneGraph.getTimeRange(this);\n    const from = timeRange.state.value.from;\n    const to = timeRange.state.value.to;\n\n    const openTrace = getOpenTrace(this);\n\n    return PanelBuilders.traces()\n      .setTitle(`Structure for ${tree.serviceName} [${countSpans(tree)} spans used]`)\n      .setOption('createFocusSpanLink' as any, (traceId: string, spanId: string): LinkModel<Field> => {\n        return {\n          title: 'Open trace',\n          href: '#',\n          onClick: () => openTrace(traceId, spanId),\n          origin: {} as Field,\n          target: '_self',\n        };\n      })\n      .setData(\n        new SceneDataNode({\n          data: {\n            state: LoadingState.Done,\n            timeRange: {\n              from,\n              to,\n              raw: { from, to },\n            },\n            series: [\n              {\n                ...this.buildData(tree),\n              },\n            ],\n          },\n        })\n      )\n      .build();\n  }\n\n  private buildData(tree: TreeNode) {\n    const trace = this.getTrace(tree, ROOT_SPAN_ID);\n    const traceName = trace[0].serviceName + ':' + trace[0].operationName;\n\n    return createDataFrame({\n      name: `Trace ${traceName}`,\n      refId: `trace_${traceName}`,\n      fields: [\n        {\n          name: 'references',\n          type: FieldType.other,\n          values: trace.map((x) => x.references),\n        },\n        {\n          name: 'traceID',\n          type: FieldType.string,\n          values: trace.map((x) => x.traceID),\n        },\n        {\n          name: 'spanID',\n          type: FieldType.string,\n          values: trace.map((x) => x.spanID),\n        },\n        {\n          name: 'parentSpanID',\n          type: FieldType.string,\n          values: trace.map((x) => x.parentSpanId),\n        },\n        {\n          name: 'serviceName',\n          type: FieldType.string,\n          values: trace.map((x) => x.serviceName),\n        },\n        {\n          name: 'operationName',\n          type: FieldType.string,\n          values: trace.map((x) => x.operationName),\n        },\n        {\n          name: 'duration',\n          type: FieldType.number,\n          values: trace.map((x) => x.duration),\n        },\n        {\n          name: 'startTime',\n          type: FieldType.number,\n          values: trace.map((x) => x.startTime),\n        },\n        {\n          name: 'statusCode',\n          type: FieldType.number,\n          values: trace.map((x) => x.statusCode),\n        },\n      ],\n    });\n  }\n\n  private getTrace(node: TreeNode, spanID: string) {\n    const erroredSpans = node.spans.reduce(\n      (acc, c) => (c.attributes?.find((a) => a.key === 'status')?.value.stringValue === 'error' ? acc + 1 : acc),\n      0\n    );\n\n    // start time needs to be different from zero otherwise for the root, otherwise the Trace View won't render it\n    let startTime = 0.0001;\n    if (spanID !== ROOT_SPAN_ID) {\n      startTime =\n        node.spans.reduce((acc, c) => acc + parseInt(c.startTimeUnixNano, 10), 0) / node.spans.length / 1000000;\n    }\n\n    const values = [\n      {\n        // Add last 5 spans of the list as external references\n        // refType = 'EXTERNAL' doesn't mean anything, it's just to be different from CHILD_OF and FOLLOW_FROM\n        references: node.spans.slice(-5).map((x) => ({\n          refType: 'EXTERNAL',\n          traceID: x.traceId,\n          spanID: x.spanID,\n        })),\n        traceID: node.traceID,\n        spanID: node.spans[0].spanID,\n        parentSpanId: spanID,\n        serviceName: node.serviceName,\n        operationName: node.operationName,\n        statusCode: erroredSpans > 0 ? 2 /*error*/ : 0 /*unset*/,\n        duration: node.spans.reduce((acc, c) => acc + parseInt(c.durationNanos, 10), 0) / node.spans.length / 1000000,\n        startTime,\n      },\n    ];\n\n    for (const child of node.children) {\n      values.push(...this.getTrace(child, node.spans[0].spanID));\n    }\n    return values;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<StructureTabScene>) => {\n    const { tree, loading, panel, $data } = model.useState();\n    const styles = getStyles(useTheme2());\n    const theme = useTheme2();\n\n    const exploration = getTraceExplorationScene(model);\n    const { value } = exploration.getMetricVariable().useState();\n\n    const metric = value as MetricFunction;\n\n    let isLoading = loading || !tree?.children.length;\n    if ($data?.state.data?.state === LoadingState.Done) {\n      isLoading = false;\n    }\n\n    let description;\n    let emptyMsg = '';\n    switch (metric) {\n      case 'rate':\n        description = (\n          <>\n            <div>Analyse the service structure of the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'server';\n        break;\n      case 'errors':\n        description = (\n          <>\n            <div>Analyse the errors structure of the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'error';\n        break;\n      case 'duration':\n        description = (\n          <>\n            <div>Analyse the structure of slow spans from the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'slow';\n        break;\n    }\n\n    const tabName = structureDisplayName(metric);\n\n    const noDataMessage = (\n      <>\n        <Text textAlignment={'center'} variant=\"h3\">\n          {EMPTY_STATE_ERROR_MESSAGE}\n        </Text>\n        <Text textAlignment={'center'} variant=\"body\">\n          <div className={styles.longText}>\n            The structure tab shows {emptyMsg} spans beneath what you are currently investigating. Currently, there are\n            no descendant {emptyMsg} spans beneath the spans you are investigating.\n          </div>\n        </Text>\n        <Stack gap={0.5} alignItems={'center'}>\n          <Icon name=\"info-circle\" />\n          <Text textAlignment={'center'} variant=\"body\">\n            The structure tab works best with full traces.\n          </Text>\n        </Stack>\n\n        <div className={styles.actionContainer}>\n          Read more about\n          <div className={styles.action}>\n            <LinkButton\n              icon=\"external-link-alt\"\n              fill=\"solid\"\n              size={'sm'}\n              target={'_blank'}\n              href={\n                'https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/concepts/#trace-structure'\n              }\n            >\n              {`${tabName.toLowerCase()}`}\n            </LinkButton>\n          </div>\n        </div>\n      </>\n    );\n\n    return (\n      <Stack direction={'column'} gap={1}>\n        <div className={styles.description}>{description}</div>\n        {isLoading && (\n          <Stack direction={'column'} gap={2}>\n            <Skeleton\n              count={4}\n              height={200}\n              baseColor={theme.colors.background.secondary}\n              highlightColor={theme.colors.background.primary}\n            />\n          </Stack>\n        )}\n\n        {!isLoading && tree && tree.children.length > 0 && (\n          <div className={styles.traceViewList}>{panel && <panel.Component model={panel} />}</div>\n        )}\n\n        {$data?.state.data?.state === LoadingState.Done && !tree?.children.length && (\n          <EmptyState message={noDataMessage} padding={'32px'} />\n        )}\n      </Stack>\n    );\n  };\n}\n\nfunction buildQuery(metric: MetricFunction) {\n  let metricQuery;\n  let selectionQuery = '';\n  switch (metric) {\n    case 'errors':\n      metricQuery = 'status = error';\n      selectionQuery = 'status = error';\n      break;\n    case 'duration':\n      metricQuery = `duration > ${VAR_LATENCY_PARTIAL_THRESHOLD_EXPR}`;\n      selectionQuery = `duration > ${VAR_LATENCY_THRESHOLD_EXPR}`;\n      break;\n    default:\n      metricQuery = 'kind = server';\n      break;\n  }\n\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR} ${\n      selectionQuery.length ? `&& ${selectionQuery}` : ''\n    }} &>> { ${metricQuery} } | select(status, resource.service.name, name, nestedSetParent, nestedSetLeft, nestedSetRight)`,\n    queryType: 'traceql',\n    tableType: 'raw',\n    limit: 200,\n    spss: 20,\n    filters: [],\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0`,\n    }),\n    traceViewList: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing.x1,\n      // Hide the minimap and header components\n      'div[class*=\"panel-content\"] > div': {\n        overflow: 'auto',\n        '> :not([class*=\"TraceTimelineViewer\"])': {\n          display: 'none',\n        },\n      },\n      // Hide the Span and Resource accordions from span details\n      'div[data-testid=\"span-detail-component\"] > :nth-child(4) > :nth-child(1)': {\n        display: 'none',\n      },\n\n      // Hide span details row\n      '.span-detail-row': {\n        display: 'none',\n      },\n\n      // Remove cursor pointer as span details is hidden\n      'div[data-testid=\"TimelineRowCell\"]': {\n        'button[role=\"switch\"]': {\n          cursor: 'text',\n        },\n      },\n      'div[data-testid=\"span-view\"]': {\n        cursor: 'text !important',\n      },\n    }),\n    longText: css({\n      maxWidth: '800px',\n      margin: '0 auto',\n    }),\n    action: css({\n      marginLeft: theme.spacing(1),\n    }),\n    actionContainer: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n    }),\n  };\n};\n\nfunction countSpans(tree: TreeNode) {\n  let count = tree.spans.length;\n  for (const child of tree.children) {\n    count += countSpans(child);\n  }\n  return count;\n}\n\nexport function buildStructureScene(metric: MetricFunction) {\n  return new SceneFlexItem({\n    body: new StructureTabScene({ metric }),\n  });\n}\n", "import { css } from '@emotion/css';\nimport { useResizeObserver } from '@react-aria/utils';\nimport React, { useEffect, useMemo, useRef, useState } from 'react';\n\nimport { GrafanaTheme2, SelectableValue } from '@grafana/data';\nimport { Select, RadioButtonGroup, useStyles2, useTheme2, measureText, Field, InputActionMeta } from '@grafana/ui';\nimport { ALL, ignoredAttributes, maxOptions, MetricFunction, RESOURCE_ATTR, SPAN_ATTR } from 'utils/shared';\nimport { AttributesBreakdownScene } from './TracesByService/Tabs/Breakdown/AttributesBreakdownScene';\nimport { AttributesComparisonScene } from './TracesByService/Tabs/Comparison/AttributesComparisonScene';\nimport { getFiltersVariable, getMetricVariable } from 'utils/utils';\n\ntype Props = {\n  options: Array<SelectableValue<string>>;\n  radioAttributes: string[];\n  value?: string;\n  onChange: (label: string, ignore?: boolean) => void;\n  showAll?: boolean;\n  model: AttributesBreakdownScene | AttributesComparisonScene;\n};\n\nconst additionalWidthPerItem = 40;\nconst widthOfOtherAttributes = 180;\n\nexport function GroupBySelector({ options, radioAttributes, value, onChange, showAll = false, model }: Props) {\n  const styles = useStyles2(getStyles);\n  const theme = useTheme2();\n  const { fontSize } = theme.typography;\n\n  const [selectQuery, setSelectQuery] = useState<string>('');\n\n  const [availableWidth, setAvailableWidth] = useState<number>(0);\n  const controlsContainer = useRef<HTMLDivElement>(null);\n\n  const { filters } = getFiltersVariable(model).useState();\n  const { value: metric } = getMetricVariable(model).useState();\n  const metricValue = metric as MetricFunction;\n\n  useResizeObserver({\n    ref: controlsContainer,\n    onResize: () => {\n      const element = controlsContainer.current;\n      if (element) {\n        setAvailableWidth(element.clientWidth);\n      }\n    },\n  });\n\n  const radioOptions = useMemo(() => {\n    let radioOptionsWidth = 0;\n    return radioAttributes\n      .filter((op) => {\n        // remove radio options that are in the dropdown\n        let checks = !!options.find((o) => o.value === op);\n\n        // remove radio options that are in the filters\n        if (filters.find((f) => f.key === op && (f.operator === '=' || f.operator === '!='))) {\n          return false;\n        }\n\n        // if filters (primary signal) has 'Full Traces' selected, then don't add rootName or rootServiceName to options\n        // as you would overwrite it in the query if it's selected\n        if (filters.find((f) => f.key === 'nestedSetParent')) {\n          checks = checks && op !== 'rootName' && op !== 'rootServiceName';\n        }\n\n        // if rate or error rate metric is selected, then don't add status to options\n        // as you would overwrite it in the query if it's selected\n        if (metricValue === 'rate' || metricValue === 'errors') {\n          checks = checks && op !== 'status';\n        }\n\n        return checks;\n      })\n      .map((attribute) => ({\n        label: attribute.replace(SPAN_ATTR, '').replace(RESOURCE_ATTR, ''),\n        text: attribute,\n        value: attribute,\n      }))\n      .filter((option) => {\n        const text = option.label || option.text || '';\n        const textWidth = measureText(text, fontSize).width;\n        if (radioOptionsWidth + textWidth + additionalWidthPerItem + widthOfOtherAttributes < availableWidth) {\n          radioOptionsWidth += textWidth + additionalWidthPerItem;\n          return true;\n        } else {\n          return false;\n        }\n      });\n  }, [radioAttributes, options, filters, metricValue, fontSize, availableWidth]);\n\n  const otherAttrOptions = useMemo(() => {\n    const ops = options.filter((op) => !radioOptions.find((ro) => ro.value === op.value?.toString()));\n    return filteredOptions(ops, selectQuery);\n  }, [selectQuery, options, radioOptions]);\n\n  const getModifiedSelectOptions = (options: Array<SelectableValue<string>>) => {\n    return options\n      .filter((op) => !ignoredAttributes.includes(op.value?.toString()!))\n      .map((op) => ({ label: op.label?.replace(SPAN_ATTR, '').replace(RESOURCE_ATTR, ''), value: op.value }));\n  };\n\n  // Set default value as first value in options\n  useEffect(() => {\n    const defaultValue = radioAttributes[0] ?? options[0]?.value;\n    if (defaultValue) {\n      if (!showAll && (!value || value === ALL)) {\n        onChange(defaultValue, true);\n      }\n    }\n  });\n\n  const showAllOption = showAll ? [{ label: ALL, value: ALL }] : [];\n  const defaultOnChangeValue = showAll ? ALL : '';\n\n  return (\n    <Field label=\"Group by\">\n      <div ref={controlsContainer} className={styles.container}>\n        {radioOptions.length > 0 && (\n          <RadioButtonGroup options={[...showAllOption, ...radioOptions]} value={value} onChange={onChange} />\n        )}\n        <Select\n          value={value && getModifiedSelectOptions(otherAttrOptions).some((x) => x.value === value) ? value : null} // remove value from select when radio button clicked\n          placeholder={'Other attributes'}\n          options={getModifiedSelectOptions(otherAttrOptions)}\n          onChange={(selected) => {\n            const newSelected = selected?.value ?? defaultOnChangeValue;\n            onChange(newSelected);\n          }}\n          className={styles.select}\n          isClearable\n          onInputChange={(value: string, { action }: InputActionMeta) => {\n            if (action === 'input-change') {\n              setSelectQuery(value);\n            }\n          }}\n          onCloseMenu={() => setSelectQuery('')}\n          virtualized\n        />\n      </div>\n    </Field>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    select: css({\n      maxWidth: theme.spacing(22),\n    }),\n    container: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n\nexport const filteredOptions = (options: Array<SelectableValue<string>>, query: string) => {\n  if (options.length === 0) {\n    return [];\n  }\n\n  if (query.length === 0) {\n    return options.slice(0, maxOptions);\n  }\n\n  const queryLowerCase = query.toLowerCase();\n  return options\n    .filter((tag) => {\n      if (tag.value && tag.value.length > 0) {\n        return tag.value.toLowerCase().includes(queryLowerCase);\n      }\n      return false;\n    })\n    .slice(0, maxOptions);\n};\n", "import React from 'react';\n\nimport { SelectableValue } from '@grafana/data';\nimport { SceneComponentProps, SceneObject, SceneObjectBase, SceneObjectState } from '@grafana/scenes';\nimport { Field, RadioButtonGroup } from '@grafana/ui';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../utils/analytics';\n\nexport interface LayoutSwitcherState extends SceneObjectState {\n  active: LayoutType;\n  layouts: SceneObject[];\n  options: Array<SelectableValue<LayoutType>>;\n}\n\nexport type LayoutType = 'single' | 'grid' | 'rows';\n\nexport class LayoutSwitcher extends SceneObjectBase<LayoutSwitcherState> {\n  public Selector({ model }: { model: LayoutSwitcher }) {\n    const { active, options } = model.useState();\n\n    return (\n      <Field label=\"View\">\n        <RadioButtonGroup options={options} value={active} onChange={model.onLayoutChange} />\n      </Field>\n    );\n  }\n\n  public onLayoutChange = (active: LayoutType) => {\n    this.setState({ active });\n    reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.layout_type_changed, {\n      layout: active,\n    });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<LayoutSwitcher>) => {\n    const { layouts, options, active } = model.useState();\n\n    const index = options.findIndex((o) => o.value === active);\n    if (index === -1) {\n      return null;\n    }\n\n    const layout = layouts[index];\n\n    return <layout.Component model={layout} />;\n  };\n}\n", "import { PanelBuilders } from '@grafana/scenes';\nimport { TooltipDisplayMode } from '@grafana/ui';\n\nexport const linesPanelConfig = () => {\n  return PanelBuilders.timeseries()\n    .setOption('legend', { showLegend: false })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi })\n    .setCustomFieldConfig('fillOpacity', 15);\n};\n", "import { TimeRange } from '@grafana/data';\nimport { sceneGraph, SceneObject, SceneObjectBase, SceneObjectState, SceneQueryRunner } from '@grafana/scenes';\nimport { DataQuery, DataSourceRef } from '@grafana/schema';\n\nimport Logo from '../../../../src/img/logo.svg';\n\nexport interface AddToInvestigationButtonState extends SceneObjectState {\n  dsUid?: string;\n  query?: string;\n  labelValue?: string;\n  type?: string;\n  context?: ExtensionContext;\n  queries: DataQuery[];\n}\n\ninterface ExtensionContext {\n  timeRange: TimeRange;\n  queries: DataQuery[];\n  datasource: DataSourceRef;\n  origin: string;\n  url: string;\n  type: string;\n  title: string;\n  id: string;\n  logoPath: string;\n}\n\nexport class AddToInvestigationButton extends SceneObjectBase<AddToInvestigationButtonState> {\n  constructor(state: Omit<AddToInvestigationButtonState, 'queries'>) {\n    super({ ...state, queries: [] });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate = () => {\n    this._subs.add(\n      this.subscribeToState(() => {\n        this.getQueries();\n        this.getContext();\n      })\n    );\n  };\n\n  private readonly getQueries = () => {\n    const data = sceneGraph.getData(this);\n    const queryRunner = sceneGraph.findObject(data, isQueryRunner);\n\n    if (isQueryRunner(queryRunner)) {\n      const queries = queryRunner.state.queries.map((q) => ({\n        ...q,\n        query: this.state.query,\n      }));\n\n      if (JSON.stringify(queries) !== JSON.stringify(this.state.queries)) {\n        this.setState({ queries });\n      }\n    }\n  };\n\n  private readonly getContext = () => {\n    const { queries, dsUid, labelValue, type = 'traceMetrics' } = this.state;\n    const timeRange = sceneGraph.getTimeRange(this);\n\n    if (!timeRange || !queries || !dsUid) {\n      return;\n    }\n    const ctx = {\n      origin: 'Explore Traces',\n      type,\n      queries,\n      timeRange: { ...timeRange.state.value },\n      datasource: { uid: dsUid },\n      url: window.location.href,\n      id: `${JSON.stringify(queries)}`,\n      title: `${labelValue}`,\n      logoPath: Logo,\n    };\n    if (JSON.stringify(ctx) !== JSON.stringify(this.state.context)) {\n      this.setState({ context: ctx });\n    }\n  };\n}\n\nfunction isQueryRunner(o: SceneObject<SceneObjectState> | null): o is SceneQueryRunner {\n  return o instanceof SceneQueryRunner;\n}\n", "import { PanelMenuItem, PluginExtensionLink, toURLRange, urlUtil } from '@grafana/data';\nimport {\n  SceneObjectBase,\n  VizPanelMenu,\n  SceneObject,\n  SceneComponentProps,\n  sceneGraph,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport React from 'react';\nimport { AddToInvestigationButton } from '../actions/AddToInvestigationButton';\n// Certain imports are not available in the dependant package, but can be if the plugin is running in a different Grafana version.\n// We need both imports to support Grafana v11 and v12.\n// @ts-expect-error\nimport { config, getPluginLinkExtensions, getObservablePluginLinks } from '@grafana/runtime';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\nimport { getCurrentStep, getDataSource, getTraceExplorationScene } from 'utils/utils';\nimport { firstValueFrom } from 'rxjs';\n\nexport const ADD_TO_INVESTIGATION_MENU_TEXT = 'Add to investigation';\nconst extensionPointId = 'grafana-exploretraces-app/investigation/v1';\nconst ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT = 'investigations_divider'; // Text won't be visible\nconst ADD_TO_INVESTIGATION_MENU_GROUP_TEXT = 'Investigations';\n\ninterface PanelMenuState extends SceneObjectState {\n  body?: VizPanelMenu;\n  query?: string;\n  labelValue?: string;\n  addToInvestigationButton?: AddToInvestigationButton;\n}\n\nexport class PanelMenu extends SceneObjectBase<PanelMenuState> implements VizPanelMenu, SceneObject {\n  constructor(state: Partial<PanelMenuState>) {\n    super(state);\n    this.addActivationHandler(() => {\n      const items: PanelMenuItem[] = [\n        {\n          text: 'Navigation',\n          type: 'group',\n        },\n        {\n          text: 'Explore',\n          iconClassName: 'compass',\n          href: getExploreHref(this),\n          onClick: () => onExploreClick(),\n        },\n      ];\n\n      this.setState({\n        body: new VizPanelMenu({\n          items,\n        }),\n      });\n\n      const traceExploration = getTraceExplorationScene(this);\n      const dsUid = getDataSource(traceExploration);\n\n      const addToInvestigationButton = new AddToInvestigationButton({\n        query: this.state.query,\n        dsUid,\n      });\n\n      addToInvestigationButton.activate();\n      this.setState({ addToInvestigationButton });\n      this._subs.add(\n        addToInvestigationButton?.subscribeToState(() => {\n          subscribeToAddToInvestigation(this);\n        })\n      );\n    \n      addToInvestigationButton.setState({\n        ...addToInvestigationButton.state,\n        labelValue: this.state.labelValue,\n      });\n    });\n  }\n\n  addItem(item: PanelMenuItem): void {\n    if (this.state.body) {\n      this.state.body.addItem(item);\n    }\n  }\n\n  setItems(items: PanelMenuItem[]): void {\n    if (this.state.body) {\n      this.state.body.setItems(items);\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<PanelMenu>) => {\n    const { body } = model.useState();\n\n    if (body) {\n      return <body.Component model={body} />;\n    }\n\n    return <></>;\n  };\n}\n\nconst getExploreHref = (model: SceneObject<PanelMenuState>) => {\n  const traceExploration = getTraceExplorationScene(model);\n  const datasource = getDataSource(traceExploration);\n  const timeRange = sceneGraph.getTimeRange(model).state.value;\n  const step = getCurrentStep(model);\n\n  const exploreState = JSON.stringify({\n    ['traces-explore']: {\n      range: toURLRange(timeRange.raw),\n      queries: [{ refId: 'A', datasource, query: model.state.query, step }],\n    },\n  });\n  const subUrl = config.appSubUrl ?? '';\n  const exploreUrl = urlUtil.renderUrl(`${subUrl}/explore`, { panes: exploreState, schemaVersion: 1 });\n  return exploreUrl;\n};\n\nconst onExploreClick = () => {\n  reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.open_in_explore_clicked);\n};\n\nexport const getInvestigationLink = async (addToInvestigations: AddToInvestigationButton) => {\n  const context = addToInvestigations.state.context;\n\n  // `getPluginLinkExtensions` is removed in Grafana v12\n  if (getPluginLinkExtensions !== undefined) {\n    const links = getPluginLinkExtensions({\n      extensionPointId,\n      context,\n    });\n\n    return links.extensions[0];\n  }\n\n  // `getObservablePluginLinks` is introduced in Grafana v12\n  if (getObservablePluginLinks !== undefined) {\n    const links: PluginExtensionLink[] = await firstValueFrom(\n      getObservablePluginLinks({\n        extensionPointId,\n        context,\n      })\n    );\n\n    return links[0];\n  }\n\n  return undefined;\n};\n\nasync function subscribeToAddToInvestigation(menu: PanelMenu) {\n  const addToInvestigationButton = menu.state.addToInvestigationButton;\n  if (addToInvestigationButton) {\n    const link = await getInvestigationLink(addToInvestigationButton);\n    const existingMenuItems = menu.state.body?.state.items ?? [];\n    const existingAddToInvestigationLink = existingMenuItems.find(\n      (item) => item.text === ADD_TO_INVESTIGATION_MENU_TEXT\n    );\n\n    if (link) {\n      if (!existingAddToInvestigationLink) {\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT,\n          type: 'divider',\n        });\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_GROUP_TEXT,\n          type: 'group',\n        });\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_TEXT,\n          iconClassName: 'plus-square',\n          onClick: (e) => {\n            if (link.onClick) {\n              link.onClick(e);\n            }\n\n            reportAppInteraction(\n              USER_EVENTS_PAGES.analyse_traces,\n              USER_EVENTS_ACTIONS.analyse_traces.add_to_investigation_clicked\n            );\n          },\n        });\n      } else {\n        if (existingAddToInvestigationLink) {\n          menu.state.body?.setItems(\n            existingMenuItems.filter(\n              (item) =>\n                [\n                  ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT,\n                  ADD_TO_INVESTIGATION_MENU_GROUP_TEXT,\n                  ADD_TO_INVESTIGATION_MENU_TEXT,\n                ].includes(item.text) === false\n            )\n          );\n        }\n      }\n    }\n  }\n}\n", "import {\n  CustomVariable,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  SceneDataNode,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  VizPanelState,\n} from '@grafana/scenes';\nimport { LayoutSwitcher } from '../LayoutSwitcher';\nimport { explorationDS, GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { formatLabelValue, getLabelValue, getOpenTrace, getTraceExplorationScene } from '../../../utils/utils';\nimport { map, Observable } from 'rxjs';\nimport { DataFrame, PanelData, reduceField, ReducerID } from '@grafana/data';\nimport { generateMetricsQuery, metricByWithStatus } from '../queries/generateMetricsQuery';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { linesPanelConfig } from '../panels/linesPanel';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { syncYAxis } from '../behaviors/syncYaxis';\nimport { exemplarsTransformations } from '../../../utils/exemplars';\nimport { PanelMenu } from '../panels/PanelMenu';\n\nexport function buildNormalLayout(\n  scene: SceneObject,\n  variable: CustomVariable,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions']\n) {\n  const traceExploration = getTraceExplorationScene(scene);\n  const metric = traceExploration.getMetricVariable().getValue() as MetricFunction;\n  const query = metricByWithStatus(metric, variable.getValueText());\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  return new LayoutSwitcher({\n    $behaviors: [syncYAxis()],\n    $data: new SceneDataTransformer({\n      $data: new StepQueryRunner({\n        maxDataPoints: 64,\n        datasource: explorationDS,\n        queries: [query],\n      }),\n      transformations: [\n        ...exemplarsTransformations(getOpenTrace(scene)),\n        () => (source: Observable<DataFrame[]>) => {\n          return source.pipe(\n            map((data: DataFrame[]) => {\n              data.forEach((a) => reduceField({ field: a.fields[1], reducers: [ReducerID.max] }));\n              return data.sort((a, b) => {\n                return (b.fields[1].state?.calcs?.max || 0) - (a.fields[1].state?.calcs?.max || 0);\n              });\n            })\n          );\n        },\n      ],\n    }),\n    options: [\n      { value: 'single', label: 'Single' },\n      { value: 'grid', label: 'Grid' },\n      { value: 'rows', label: 'Rows' },\n    ],\n    active: 'grid',\n    layouts: [\n      new SceneFlexLayout({\n        direction: 'column',\n        children: [\n          new SceneFlexItem({\n            minHeight: 300,\n            body: (metric === 'duration' ? linesPanelConfig().setUnit('s') : linesPanelConfig()).build(),\n          }),\n        ],\n      }),\n      new ByFrameRepeater({\n        body: new SceneCSSGridLayout({\n          templateColumns: GRID_TEMPLATE_COLUMNS,\n          autoRows: '200px',\n          isLazy: true,\n          children: [],\n        }),\n        groupBy: true,\n        getLayoutChild: getLayoutChild(panels, getLabelValue, variable, metric, actionsFn),\n      }),\n      new ByFrameRepeater({\n        body: new SceneCSSGridLayout({\n          templateColumns: '1fr',\n          autoRows: '200px',\n          isLazy: true,\n          children: [],\n        }),\n        groupBy: true,\n        getLayoutChild: getLayoutChild(panels, getLabelValue, variable, metric, actionsFn),\n      }),\n    ],\n  });\n}\n\nexport function getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame, labelName: string) => string,\n  variable: CustomVariable,\n  metric: MetricFunction,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions']\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        annotations: data.annotations?.filter((a) => a.refId === frame.refId),\n        series: [\n          {\n            ...frame,\n            fields: frame.fields.sort((a, b) => a.labels?.status?.localeCompare(b.labels?.status || '') || 0),\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      existingGridItem.state.body?.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const query = sceneGraph.interpolate(\n      variable,\n      generateMetricsQuery({\n        metric,\n        extraFilters: `${variable.getValueText()}=${formatLabelValue(getLabelValue(frame))}`,\n        groupByStatus: true,\n      })\n    );\n\n    const panel = (metric === 'duration' ? linesPanelConfig().setUnit('s') : barsPanelConfig())\n      .setTitle(getTitle(frame, variable.getValueText()))\n      .setMenu(new PanelMenu({ query, labelValue: getLabelValue(frame) }))\n      .setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: panel.build(),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n", "import { sceneGraph, SceneObject, SceneObjectState, VizPanel } from '@grafana/scenes';\nimport { cloneDeep, merge } from 'lodash';\nimport { EventTimeseriesDataReceived } from '../../../utils/shared';\n\nexport function syncYAxis() {\n  return (vizPanel: SceneObject<SceneObjectState>) => {\n    const maxima = new Map<string, number>();\n\n    const eventSub = vizPanel.subscribeToEvent(EventTimeseriesDataReceived, (event) => {\n      const series = event.payload.series;\n\n      series?.forEach((s) => {\n        s.fields.slice(1).forEach((f) => {\n          maxima.set(s.refId as string, Math.max(...f.values.filter((v) => v)));\n        })\n      });\n\n      updateTimeseriesAxis(vizPanel, Math.max(...maxima.values()));\n    });\n\n    return () => {\n      eventSub.unsubscribe();\n    };\n  };\n}\n\nfunction updateTimeseriesAxis(vizPanel: SceneObject, max: number) {\n  // findAllObjects searches down the full scene graph\n  const timeseries = sceneGraph.findAllObjects(vizPanel, (o) => o instanceof VizPanel) as VizPanel[];\n\n  for (const t of timeseries) {\n    t.clearFieldConfigCache(); // required\n\n    t.setState({\n      fieldConfig: merge(cloneDeep(t.state.fieldConfig), { defaults: { max } }),\n    });\n  }\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\ntype Tag = {\n  label: string;\n  color: string;\n};\n\ntype Props = {\n  description: string;\n  tags: Tag[];\n};\n\nexport function AttributesDescription({ description, tags }: Props) {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n\n  return (\n    <div className={styles.infoFlex}>\n      <div className={styles.tagsFlex}>{description}</div>\n      {tags.length > 0 &&\n        tags.map((tag) => (\n          <div className={styles.tagsFlex} key={tag.label}>\n            <div className={styles.tag} style={{ backgroundColor: tag.color }} />\n            <div>{tag.label}</div>\n          </div>\n        ))}\n    </div>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    infoFlex: css({\n      display: 'flex',\n      gap: theme.spacing(2),\n      alignItems: 'center',\n      padding: `${theme.spacing(1)} 0 ${theme.spacing(2)} 0`,\n    }),\n    tagsFlex: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n      alignItems: 'center',\n    }),\n    tag: css({\n      display: 'inline-block',\n      width: theme.spacing(2),\n      height: theme.spacing(0.5),\n      borderRadius: theme.spacing(0.5),\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport React, { useState } from 'react';\n\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport {\n  CustomVariable,\n  SceneComponentProps,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { Field, RadioButtonGroup, useStyles2 } from '@grafana/ui';\n\nimport { GroupBySelector } from '../../../GroupBySelector';\nimport {\n  MetricFunction,\n  RESOURCE,\n  RESOURCE_ATTR,\n  SPAN,\n  SPAN_ATTR,\n  VAR_FILTERS,\n  VAR_METRIC,\n  radioAttributesResource,\n  radioAttributesSpan,\n} from '../../../../../utils/shared';\n\nimport { LayoutSwitcher } from '../../../LayoutSwitcher';\nimport { AddToFiltersAction } from '../../../actions/AddToFiltersAction';\nimport { buildNormalLayout } from '../../../layouts/attributeBreakdown';\nimport {\n  getAttributesAsOptions,\n  getGroupByVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../../../utils/analytics';\nimport { AttributesDescription } from './AttributesDescription';\n\nexport interface AttributesBreakdownSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class AttributesBreakdownScene extends SceneObjectBase<AttributesBreakdownSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_FILTERS, VAR_METRIC],\n    onReferencedVariableValueChanged: this.onReferencedVariableValueChanged.bind(this),\n  });\n\n  constructor(state: Partial<AttributesBreakdownSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const variable = getGroupByVariable(this);\n\n    variable.subscribeToState(() => {\n      this.setBody(variable);\n    });\n\n    getTraceByServiceScene(this).subscribeToState(() => {\n      this.setBody(variable);\n    });\n\n    this.setBody(variable);\n  }\n\n  private onReferencedVariableValueChanged() {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(radioAttributesResource[0]);\n    this.setBody(variable);\n  }\n\n  private onAddToFiltersClick(payload: any) {\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.breakdown_add_to_filters_clicked,\n      payload\n    );\n  }\n\n  private setBody = (variable: CustomVariable) => {\n    this.setState({\n      body: buildNormalLayout(this, variable, (frame: DataFrame) => [\n        new AddToFiltersAction({ frame, labelKey: variable.getValueText(), onClick: this.onAddToFiltersClick }),\n      ]),\n    });\n  };\n\n  public onChange = (value: string, ignore?: boolean) => {\n    const variable = getGroupByVariable(this);\n    if (variable.getValueText() !== value) {\n      variable.changeValueTo(value, undefined, !ignore);\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.breakdown_group_by_changed,\n        {\n          groupBy: value,\n        }\n      );\n    }\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AttributesBreakdownScene>) => {\n    const groupBy = getGroupByVariable(model).getValueText();\n    const defaultScope = groupBy.includes(SPAN_ATTR) || radioAttributesSpan.includes(groupBy) ? SPAN : RESOURCE;\n    const [scope, setScope] = useState(defaultScope);\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    const { attributes } = getTraceByServiceScene(model).useState();\n    const filterType = scope === RESOURCE ? RESOURCE_ATTR : SPAN_ATTR;\n    let filteredAttributes = attributes?.filter((attr) => attr.includes(filterType));\n    if (scope === SPAN) {\n      filteredAttributes = filteredAttributes?.concat(radioAttributesSpan);\n    }\n\n    const exploration = getTraceExplorationScene(model);\n    const { value: metric } = exploration.getMetricVariable().useState();\n    const getDescription = (metric: MetricFunction) => {\n      switch (metric) {\n        case 'rate':\n          return 'Attributes are ordered by their rate of requests per second.';\n        case 'errors':\n          return 'Attributes are ordered by their rate of errors per second.';\n        case 'duration':\n          return 'Attributes are ordered by their average duration.';\n        default:\n          throw new Error('Metric not supported');\n      }\n    };\n    const description = getDescription(metric as MetricFunction);\n\n    return (\n      <div className={styles.container}>\n        <AttributesDescription\n          description={description}\n          tags={\n            metric === 'duration'\n              ? []\n              : [\n                  { label: 'Rate', color: 'green' },\n                  { label: 'Error', color: 'red' },\n                ]\n          }\n        />\n\n        <div className={styles.controls}>\n          {filteredAttributes?.length && (\n            <div className={styles.controlsLeft}>\n              <div className={styles.scope}>\n                <Field label=\"Scope\">\n                  <RadioButtonGroup\n                    options={getAttributesAsOptions([RESOURCE, SPAN])}\n                    value={scope}\n                    onChange={setScope}\n                  />\n                </Field>\n              </div>\n\n              <div className={styles.groupBy}>\n                <GroupBySelector\n                  options={getAttributesAsOptions(filteredAttributes!)}\n                  radioAttributes={scope === RESOURCE ? radioAttributesResource : radioAttributesSpan}\n                  value={groupBy}\n                  onChange={model.onChange}\n                  model={model}\n                />\n              </div>\n            </div>\n          )}\n          {body instanceof LayoutSwitcher && (\n            <div className={styles.controlsRight}>\n              <body.Selector model={body} />\n            </div>\n          )}\n        </div>\n        <div className={styles.content}>{body && <body.Component model={body} />}</div>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    content: css({\n      flexGrow: 1,\n      display: 'flex',\n      paddingTop: theme.spacing(0),\n    }),\n    controls: css({\n      flexGrow: 0,\n      display: 'flex',\n      alignItems: 'top',\n      gap: theme.spacing(2),\n    }),\n    controlsRight: css({\n      flexGrow: 0,\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    scope: css({\n      marginRight: theme.spacing(2),\n    }),\n    groupBy: css({\n      width: '100%',\n    }),\n    controlsLeft: css({\n      display: 'flex',\n      justifyContent: 'flex-left',\n      justifyItems: 'left',\n      width: '100%',\n      flexDirection: 'row',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexItem,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { AttributesBreakdownScene } from './AttributesBreakdownScene';\nimport { VAR_METRIC } from '../../../../../utils/shared';\n\ninterface BreakdownSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class BreakdownScene extends SceneObjectBase<BreakdownSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_METRIC],\n  });\n\n  constructor(state: Partial<BreakdownSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new AttributesBreakdownScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<BreakdownScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildBreakdownScene() {\n  return new SceneFlexItem({\n    body: new BreakdownScene({}),\n  });\n}\n", "import { ComparisonSelection } from '../../../utils/shared';\n\nexport function comparisonQuery(selection?: ComparisonSelection) {\n  let selector = '';\n\n  if (!selection) {\n    return '{}';\n  }\n\n  if (selection.query) {\n    selector += selection.query;\n  }\n\n  const duration = [];\n  if (selection.duration?.from.length) {\n    duration.push(`duration >= ${selection.duration.from}`);\n  }\n  if (selection.duration?.to.length) {\n    duration.push(`duration <= ${selection.duration.to}`);\n  }\n  if (duration.length) {\n    if (selector.length) {\n      selector += ' && ';\n    }\n    selector += duration.join(' && ');\n  }\n\n  const fromTimerange = selection.timeRange?.from;\n  const toTimerange = selection.timeRange?.to;\n  return `{${selector}}, 10${\n    fromTimerange && toTimerange ? `, ${fromTimerange * 1000000000}, ${toTimerange * 1000000000}` : ``\n  }`;\n}\n", "import {\n  CustomVariable,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  SceneDataNode,\n  SceneDataTransformer,\n  sceneGraph,\n  SceneObject,\n  VizPanelState,\n} from '@grafana/scenes';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { map, Observable } from 'rxjs';\nimport { DataFrame, FieldType, LoadingState, PanelData, reduceField, ReducerID } from '@grafana/data';\nimport { getPanelConfig } from './allComparison';\nimport { GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\n\nexport function buildAttributeComparison(\n  scene: SceneObject,\n  variable: CustomVariable,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  const timeRange = sceneGraph.getTimeRange(scene);\n  const data = sceneGraph.getData(scene);\n  const attribute = variable.getValueText();\n  const attributeSeries = data.state.data?.series.find((d) => d.name === attribute);\n  const splitFrames: DataFrame[] = [];\n  const nameField = attributeSeries?.fields.find((f) => f.name === 'Value');\n  const baselineField = attributeSeries?.fields.find((f) => f.name === 'Baseline');\n  const selectionField = attributeSeries?.fields.find((f) => f.name === 'Selection');\n\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  if (nameField && baselineField && selectionField) {\n    for (let i = 0; i < nameField.values.length; i++) {\n      if (!nameField.values[i] || (!baselineField.values[i] && !selectionField.values[i])) {\n        continue;\n      }\n\n      splitFrames.push({\n        name: nameField.values[i].replace(/\"/g, ''),\n        length: 1,\n        fields: [\n          {\n            name: 'Value',\n            type: FieldType.string,\n            values: ['Baseline', 'Comparison'],\n            config: {},\n          },\n          {\n            ...baselineField,\n            values: [baselineField.values[i]],\n            labels: {\n              [attribute]: nameField.values[i],\n            },\n            config: {\n              displayName: 'Baseline',\n            },\n          },\n          {\n            ...selectionField,\n            values: [selectionField.values[i]],\n          },\n        ],\n      });\n    }\n  }\n\n  return new ByFrameRepeater({\n    $data: new SceneDataTransformer({\n      $data: new SceneDataNode({\n        data: {\n          timeRange: timeRange.state.value,\n          state: LoadingState.Done,\n          series: splitFrames,\n        },\n      }),\n      transformations: [\n        () => (source: Observable<DataFrame[]>) => {\n          return source.pipe(\n            map((data: DataFrame[]) => {\n              data.forEach((a) => reduceField({ field: a.fields[2], reducers: [ReducerID.max] }));\n              return data.sort((a, b) => {\n                return (b.fields[2].state?.calcs?.max || 0) - (a.fields[2].state?.calcs?.max || 0);\n              });\n            })\n          );\n        },\n      ],\n    }),\n    body: new SceneCSSGridLayout({\n      templateColumns: GRID_TEMPLATE_COLUMNS,\n      autoRows: '200px',\n      isLazy: true,\n      children: [],\n    }),\n    getLayoutChild: getLayoutChild(panels, getLabel, actionsFn, metric),\n  });\n}\n\nconst getLabel = (df: DataFrame) => {\n  return df.name || 'No name available';\n};\n\nfunction getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame) => string,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        series: [\n          {\n            ...frame,\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      existingGridItem.state.body?.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const panel = getPanelConfig(metric).setTitle(getTitle(frame)).setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: panel.build(),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneObjectState,\n  SceneObjectBase,\n  SceneComponentProps,\n} from '@grafana/scenes';\nimport { Button } from '@grafana/ui';\n\ninterface InspectAttributeActionState extends SceneObjectState {\n  attribute?: string;\n  onClick: () => void;\n}\n\nexport class InspectAttributeAction extends SceneObjectBase<InspectAttributeActionState> {\n  public static Component = ({ model }: SceneComponentProps<InspectAttributeAction>) => {\n    if (!model.state.attribute) {\n      return null;\n    }\n\n    return (\n      <Button variant=\"secondary\" size=\"sm\" fill=\"solid\" onClick={() => model.state.onClick()}>\n        Inspect\n      </Button>\n    );\n  };\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { DataFrame, FieldType, GrafanaTheme2, Field } from '@grafana/data';\nimport {\n  CustomVariable,\n  SceneComponentProps,\n  SceneDataTransformer,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n  VariableDependencyConfig,\n  VariableValue,\n} from '@grafana/scenes';\nimport { getTheme, useStyles2 } from '@grafana/ui';\n\nimport { GroupBySelector } from '../../../GroupBySelector';\nimport { VAR_FILTERS, VAR_PRIMARY_SIGNAL, explorationDS, VAR_FILTERS_EXPR, ALL, radioAttributesSpan } from '../../../../../utils/shared';\n\nimport { LayoutSwitcher } from '../../../LayoutSwitcher';\nimport { AddToFiltersAction } from '../../../actions/AddToFiltersAction';\nimport { map, Observable } from 'rxjs';\nimport { BaselineColor, buildAllComparisonLayout, SelectionColor } from '../../../layouts/allComparison';\n// eslint-disable-next-line no-restricted-imports\nimport { duration } from 'moment';\nimport { comparisonQuery } from '../../../queries/comparisonQuery';\nimport { buildAttributeComparison } from '../../../layouts/attributeComparison';\nimport {\n  getAttributesAsOptions,\n  getGroupByVariable,\n  getPrimarySignalVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { InspectAttributeAction } from 'components/Explore/actions/InspectAttributeAction';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../../../utils/analytics';\nimport { computeHighestDifference } from '../../../../../utils/comparison';\nimport { AttributesDescription } from '../Breakdown/AttributesDescription';\nimport { isEqual } from 'lodash';\n\nexport interface AttributesComparisonSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class AttributesComparisonScene extends SceneObjectBase<AttributesComparisonSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_FILTERS, VAR_PRIMARY_SIGNAL],\n    onReferencedVariableValueChanged: this.onReferencedVariableValueChanged.bind(this),\n  });\n\n  constructor(state: Partial<AttributesComparisonSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const variable = getGroupByVariable(this);\n\n    variable.changeValueTo(ALL);\n\n    this.updateData();\n\n    variable.subscribeToState((newState, prevState) => {\n      if (newState.value !== prevState.value) {\n        this.setBody(variable);\n      }\n    });\n\n    getPrimarySignalVariable(this).subscribeToState(() => {\n      this.updateData();\n      this.setBody(variable);\n    });\n\n    getTraceByServiceScene(this).subscribeToState((newState, prevState) => {\n      if (!isEqual(newState.selection, prevState.selection)) {\n        this.updateData();\n        this.setBody(variable);\n      }\n    });\n\n    sceneGraph.getTimeRange(this).subscribeToState(() => {\n      this.updateData();\n    });\n\n    this.setBody(variable);\n  }\n\n  private getFilteredAttributes = (primarySignal: VariableValue): string[] => {\n    return primarySignal === 'nestedSetParent<0' ? ['rootName', 'rootServiceName'] : [];\n  };\n\n  private updateData() {\n    const byServiceScene = getTraceByServiceScene(this);\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    const from = sceneTimeRange.state.value.from.unix();\n    const to = sceneTimeRange.state.value.to.unix();\n    const primarySignal = getPrimarySignalVariable(this).state.value;\n    const filteredAttributes = this.getFilteredAttributes(primarySignal);\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(from, to, comparisonQuery(byServiceScene.state.selection))],\n        }),\n        transformations: [\n          () => (source: Observable<DataFrame[]>) => {\n            return source.pipe(\n              map((data: DataFrame[]) => {\n                const groupedFrames = groupFrameListByAttribute(data);\n                return Object.entries(groupedFrames)\n                  .filter(([attribute, _]) => !filteredAttributes.includes(attribute))\n                  .map(([attribute, frames]) => frameGroupToDataframe(attribute, frames))\n                  .sort((a, b) => {\n                    const aCompare = computeHighestDifference(a);\n                    const bCompare = computeHighestDifference(b);\n                    return Math.abs(bCompare.maxDifference) - Math.abs(aCompare.maxDifference);\n                  });\n              })\n            );\n          },\n        ],\n      }),\n    });\n  }\n\n  private onReferencedVariableValueChanged() {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(ALL);\n    this.setBody(variable);\n  }\n\n  private onAddToFiltersClick(payload: any) {\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.comparison_add_to_filters_clicked,\n      payload\n    );\n  }\n\n  private setBody = (variable: CustomVariable) => {\n    const traceExploration = getTraceExplorationScene(this);\n    this.setState({\n      body:\n        variable.hasAllValue() || variable.getValue() === ALL\n          ? buildAllComparisonLayout(\n              (frame) =>\n                new InspectAttributeAction({\n                  attribute: frame.name,\n                  onClick: () => this.onChange(frame.name || ''),\n                }),\n              traceExploration.getMetricFunction()\n            )\n          : buildAttributeComparison(\n              this,\n              variable,\n              (frame: DataFrame) => [\n                new AddToFiltersAction({\n                  frame,\n                  labelKey: variable.getValueText(),\n                  onClick: this.onAddToFiltersClick,\n                }),\n              ],\n              traceExploration.getMetricFunction()\n            ),\n    });\n  };\n\n  public onChange = (value: string, ignore?: boolean) => {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(value, undefined, !ignore);\n\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.select_attribute_in_comparison_clicked,\n      { value }\n    );\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AttributesComparisonScene>) => {\n    const { body } = model.useState();\n    const variable = getGroupByVariable(model);\n    const traceExploration = getTraceExplorationScene(model);\n    const { attributes } = getTraceByServiceScene(model).useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <AttributesDescription\n          description=\"Attributes are ordered by the difference between the baseline and selection values for each value.\"\n          tags={[\n            {\n              label: 'Baseline',\n              color:\n                traceExploration.getMetricFunction() === 'duration'\n                  ? BaselineColor\n                  : getTheme().visualization.getColorByName('semi-dark-green'),\n            },\n            {\n              label: 'Selection',\n              color:\n                traceExploration.getMetricFunction() === 'duration'\n                  ? SelectionColor\n                  : getTheme().visualization.getColorByName('semi-dark-red'),\n            },\n          ]}\n        />\n\n        <div className={styles.controls}>\n          {attributes?.length && (\n            <div className={styles.controlsLeft}>\n              <GroupBySelector\n                options={getAttributesAsOptions(attributes)}\n                radioAttributes={radioAttributesSpan}\n                value={variable.getValueText()}\n                onChange={model.onChange}\n                showAll={true}\n                model={model}\n              />\n            </div>\n          )}\n          {body instanceof LayoutSwitcher && (\n            <div className={styles.controlsRight}>\n              <body.Selector model={body} />\n            </div>\n          )}\n        </div>\n        <div className={styles.content}>{body && <body.Component model={body} />}</div>\n      </div>\n    );\n  };\n}\n\nexport function buildQuery(from: number, to: number, compareQuery: string) {\n  const dur = duration(to - from, 's');\n  const durString = `${dur.asSeconds()}s`;\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}} | compare(${compareQuery})`,\n    step: durString,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 100,\n    spss: 10,\n    filters: [],\n  };\n}\n\nconst groupFrameListByAttribute = (frames: DataFrame[]) => {\n  return frames.reduce((acc: Record<string, DataFrame[]>, series) => {\n    const numberField = series.fields.find((field) => field.type === 'number');\n    const nonInternalKey = Object.keys(numberField?.labels || {}).find((key) => !key.startsWith('__'));\n    if (nonInternalKey) {\n      acc[nonInternalKey] = [...(acc[nonInternalKey] || []), series];\n    }\n    return acc;\n  }, {});\n};\n\nconst frameGroupToDataframe = (attribute: string, frames: DataFrame[]): DataFrame => {\n  const newFrame: DataFrame = {\n    name: attribute,\n    refId: attribute,\n    fields: [],\n    length: 0,\n  };\n\n  const valueNameField: Field = {\n    name: 'Value',\n    type: FieldType.string,\n    values: [],\n    config: {},\n    labels: { [attribute]: attribute },\n  };\n  const baselineField: Field = {\n    name: 'Baseline',\n    type: FieldType.number,\n    values: [],\n    config: {},\n  };\n  const selectionField: Field = {\n    name: 'Selection',\n    type: FieldType.number,\n    values: [],\n    config: {},\n  };\n\n  const values = frames.reduce((acc: Record<string, Field[]>, frame) => {\n    const numberField = frame.fields.find((field) => field.type === 'number');\n    const val = numberField?.labels?.[attribute];\n    if (val) {\n      acc[val] = [...(acc[val] || []), numberField];\n    }\n    return acc;\n  }, {});\n\n  const baselineTotal = getTotalForMetaType(frames, 'baseline', values);\n  const selectionTotal = getTotalForMetaType(frames, 'selection', values);\n\n  newFrame.length = Object.keys(values).length;\n\n  Object.entries(values).forEach(([value, fields]) => {\n    valueNameField.values.push(value);\n    baselineField.values.push(\n      fields.find((field) => field.labels?.['__meta_type'] === '\"baseline\"')?.values[0] / baselineTotal\n    );\n    selectionField.values.push(\n      fields.find((field) => field.labels?.['__meta_type'] === '\"selection\"')?.values[0] / selectionTotal\n    );\n  });\n  newFrame.fields = [valueNameField, baselineField, selectionField];\n  return newFrame;\n};\n\nfunction getTotalForMetaType(frames: DataFrame[], metaType: string, values: Record<string, Field[]>) {\n  // calculate total from values so that we are properly normalizing the field values when dividing by the total\n  const calculatedTotal = Object.values(values).reduce((total, fields) => {\n    const field = fields.find((field) => field.labels?.['__meta_type'] === `\"${metaType}\"`);\n    return total + (field?.values[0] || 0);\n  }, 0);\n\n  let total = frames.reduce((currentValue, frame) => {\n    const field = frame.fields.find((f) => f.type === 'number');\n    if (field?.labels?.['__meta_type'] === `\"${metaType}_total\"`) {\n      return field.values[0];\n    }\n    return currentValue;\n  }, 1);\n\n  // if the baseline_total or selection_total field is found, but the total value is less than the calculated total\n  // we need to return the calculated total otherwise the values will be skewed\n  // e.g. calculatedTotal = 100, total = 80\n  // if we return the total, the field values will be normalized via 80/100 = 1.25 (incorrect)\n  // if we return the calculated total, the field values will be normalized via 100/100 = 1 (correct)\n  if (total < calculatedTotal) {\n    return calculatedTotal === 0 ? 1 : calculatedTotal; // fallback to 1 to avoid division by zero\n  }\n\n  // 1 if the baseline_total or selection_total field is not found\n  // 0 if the baseline_total or selection_total field is found, but the total value is 0\n  if (total === 1 || total === 0) {\n    return calculatedTotal === 0 ? 1 : calculatedTotal;\n  }\n\n  return total;\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    content: css({\n      flexGrow: 1,\n      display: 'flex',\n      paddingTop: theme.spacing(0),\n    }),\n    controls: css({\n      flexGrow: 0,\n      display: 'flex',\n      alignItems: 'top',\n      gap: theme.spacing(2),\n    }),\n    controlsRight: css({\n      flexGrow: 0,\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    controlsLeft: css({\n      display: 'flex',\n      justifyContent: 'flex-left',\n      justifyItems: 'left',\n      width: '100%',\n      flexDirection: 'column',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexItem,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { AttributesComparisonScene } from './AttributesComparisonScene';\nimport { MetricFunction, VAR_METRIC } from '../../../../../utils/shared';\nimport { getMetricVariable, getTraceByServiceScene } from '../../../../../utils/utils';\nimport { getDefaultSelectionForMetric } from '../../../../../utils/comparison';\n\ninterface ComparisonSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class ComparisonScene extends SceneObjectBase<ComparisonSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_METRIC],\n  });\n\n  constructor(state: Partial<ComparisonSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const metricVar = getMetricVariable(this);\n    const metric = metricVar.getValue() as MetricFunction;\n\n    const tracesByService = getTraceByServiceScene(this);\n    if (!tracesByService.state.selection) {\n      const selection = getDefaultSelectionForMetric(metric);\n      if (selection) {\n        tracesByService.setState({ selection });\n      }\n    }\n\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new AttributesComparisonScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ComparisonScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildComparisonScene() {\n  return new SceneFlexItem({\n    body: new ComparisonScene({}),\n  });\n}\n", "import { css } from '@emotion/css';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, SceneObject, sceneGraph } from '@grafana/scenes';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2, Box, Stack, TabsBar, Tab } from '@grafana/ui';\nimport React from 'react';\nimport { getTraceExplorationScene, getTraceByServiceScene } from 'utils/utils';\nimport { ShareExplorationAction } from '../../actions/ShareExplorationAction';\nimport { buildSpansScene } from './Spans/SpansScene';\nimport { buildStructureScene } from './Structure/StructureScene';\nimport { buildBreakdownScene } from './Breakdown/BreakdownScene';\nimport { MetricFunction } from 'utils/shared';\nimport { buildComparisonScene } from './Comparison/ComparisonScene';\n\ninterface ActionViewDefinition {\n  displayName: (metric: MetricFunction) => string;\n  value: ActionViewType;\n  getScene: (metric: MetricFunction) => SceneObject;\n}\n\nexport type ActionViewType = 'traceList' | 'breakdown' | 'structure' | 'comparison';\nexport const actionViewsDefinitions: ActionViewDefinition[] = [\n  { displayName: breakdownDisplayName, value: 'breakdown', getScene: buildBreakdownScene },\n  { displayName: structureDisplayName, value: 'structure', getScene: buildStructureScene },\n  { displayName: comparisonDisplayName, value: 'comparison', getScene: buildComparisonScene },\n  {\n    displayName: tracesDisplayName,\n    value: 'traceList',\n    getScene: buildSpansScene,\n  },\n];\n\nexport interface TabsBarSceneState extends SceneObjectState {}\n\nexport class TabsBarScene extends SceneObjectBase<TabsBarSceneState> {\n  public static Component = ({ model }: SceneComponentProps<TabsBarScene>) => {\n    const metricScene = getTraceByServiceScene(model);\n    const styles = useStyles2(getStyles);\n    const exploration = getTraceExplorationScene(model);\n    const { actionView } = metricScene.useState();\n    const { value: metric } = exploration.getMetricVariable().useState();\n    const dataState = sceneGraph.getData(model).useState();\n    const tracesCount = dataState.data?.series?.[0]?.length;\n\n    return (\n      <Box>\n        <div className={styles.actions}>\n          <Stack gap={1}>\n            <ShareExplorationAction exploration={exploration} />\n          </Stack>\n        </div>\n\n        <TabsBar>\n          {actionViewsDefinitions.map((tab, index) => {\n            return (\n              <Tab\n                key={index}\n                label={tab.displayName(metric as MetricFunction)}\n                active={actionView === tab.value}\n                onChangeTab={() => metricScene.setActionView(tab.value)}\n                counter={tab.value === 'traceList' ? tracesCount : undefined}\n              />\n            );\n          })}\n        </TabsBar>\n      </Box>\n    );\n  };\n}\n\nfunction breakdownDisplayName(_: MetricFunction) {\n  return 'Breakdown';\n}\n\nfunction comparisonDisplayName(_: MetricFunction) {\n  return 'Comparison';\n}\n\nexport function structureDisplayName(metric: MetricFunction) {\n  switch (metric) {\n    case 'rate':\n      return 'Service structure';\n    case 'errors':\n      return 'Root cause errors';\n    case 'duration':\n      return 'Root cause latency';\n  }\n}\n\nfunction tracesDisplayName(metric: MetricFunction) {\n  return metric === 'errors' ? 'Errored traces' : metric === 'duration' ? 'Slow traces' : 'Traces';\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    actions: css({\n      [theme.breakpoints.up(theme.breakpoints.values.md)]: {\n        position: 'absolute',\n        right: 0,\n        top: 5,\n        zIndex: 2,\n      },\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { explorationDS, MetricFunction } from 'utils/shared';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { SkeletonComponent } from '../ByFrameRepeater';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { metricByWithStatus } from '../queries/generateMetricsQuery';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { RadioButtonList, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { fieldHasEmptyValues, getOpenTrace, getTraceExplorationScene } from '../../../utils/utils';\nimport { MINI_PANEL_HEIGHT } from './TracesByServiceScene';\nimport { buildHistogramQuery } from '../queries/histogram';\nimport { histogramPanelConfig } from '../panels/histogram';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { exemplarsTransformations, removeExemplarsTransformation } from '../../../utils/exemplars';\nimport { StreamingIndicator } from '../StreamingIndicator';\n\nexport interface MiniREDPanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  metric: MetricFunction;\n  isStreaming?: boolean;\n}\n\nexport class MiniREDPanel extends SceneObjectBase<MiniREDPanelState> {\n  constructor(state: MiniREDPanelState) {\n    super({\n      isStreaming: false,\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this._onActivate();\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          this.setState({ isStreaming: data.data?.state === LoadingState.Streaming });\n\n          if (data.data?.state === LoadingState.Done) {\n            if (data.data.series.length === 0 || data.data.series[0].length === 0 || fieldHasEmptyValues(data)) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new SceneFlexItem({\n                      body: new EmptyStateScene({\n                        imgWidth: 110,\n                      }),\n                    }),\n                  ],\n                }),\n              });\n            } else {\n              this.setState({\n                panel: this.getVizPanel(this.state.metric),\n              });\n            }\n          } else if (data.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(1),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  private _onActivate() {\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new StepQueryRunner({\n          maxDataPoints: this.state.metric === 'duration' ? 24 : 64,\n          datasource: explorationDS,\n          queries: [this.state.metric === 'duration' ? buildHistogramQuery() : metricByWithStatus(this.state.metric)],\n        }),\n        transformations:\n          this.state.metric === 'duration'\n            ? [...removeExemplarsTransformation()]\n            : [...exemplarsTransformations(getOpenTrace(this))],\n      }),\n      panel: this.getVizPanel(this.state.metric),\n    });\n  }\n\n  private getVizPanel(metric: MetricFunction) {\n    return new SceneFlexLayout({\n      direction: 'row',\n      children: [\n        new SceneFlexItem({\n          body: metric === 'duration' ? this.getDurationVizPanel() : this.getRateOrErrorPanel(metric),\n        }),\n      ],\n    });\n  }\n\n  private getRateOrErrorPanel(metric: MetricFunction) {\n    const panel = barsPanelConfig().setHoverHeader(true).setDisplayMode('transparent');\n    if (metric === 'rate') {\n      panel.setCustomFieldConfig('axisLabel', 'span/s');\n    } else if (metric === 'errors') {\n      panel.setTitle('Errors rate').setCustomFieldConfig('axisLabel', 'error/s').setColor({\n        fixedColor: 'semi-dark-red',\n        mode: 'fixed',\n      });\n    }\n\n    return panel.build();\n  }\n\n  private getDurationVizPanel() {\n    return histogramPanelConfig()\n      .setTitle('Histogram by duration')\n      .setHoverHeader(true)\n      .setDisplayMode('transparent')\n      .build();\n  }\n\n  public static Component = ({ model }: SceneComponentProps<MiniREDPanel>) => {\n    const { panel, isStreaming } = model.useState();\n    const styles = useStyles2(getStyles);\n    const traceExploration = getTraceExplorationScene(model);\n\n    const selectMetric = () => {\n      reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.metric_changed, {\n        metric: model.state.metric,\n        location: 'panel',\n      });\n      traceExploration.onChangeMetricFunction(model.state.metric);\n    };\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={css([styles.container, styles.clickable])} onClick={selectMetric}>\n        <div className={styles.headerWrapper}>\n          <RadioButtonList\n            className={styles.radioButton}\n            name={`metric-${model.state.metric}`}\n            options={[{ title: '', value: 'selected' }]}\n            onChange={() => selectMetric()}\n            value={'not-selected'}\n          />\n        </div>\n        {isStreaming && (\n          <div className={styles.indicatorWrapper}>\n            <StreamingIndicator isStreaming={true} iconSize={10} />\n          </div>\n        )}\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flex: 1,\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: `1px solid ${theme.colors.border.weak}`,\n      borderRadius: '2px',\n      background: theme.colors.background.primary,\n      paddingTop: '8px',\n\n      'section, section:hover': {\n        borderColor: 'transparent',\n      },\n\n      '& .show-on-hover': {\n        display: 'none',\n      },\n    }),\n    headerWrapper: css({\n      display: 'flex',\n      alignItems: 'center',\n      position: 'absolute',\n      top: '4px',\n      left: '8px',\n      zIndex: 2,\n    }),\n    clickable: css({\n      cursor: 'pointer',\n      maxHeight: MINI_PANEL_HEIGHT,\n\n      ['[class*=\"loading-state-scene\"]']: {\n        height: MINI_PANEL_HEIGHT,\n        overflow: 'hidden',\n      },\n\n      ':hover': {\n        background: theme.colors.background.secondary,\n        input: {\n          backgroundColor: '#ffffff',\n          border: '5px solid #3D71D9',\n          cursor: 'pointer',\n        },\n      },\n    }),\n    radioButton: css({\n      display: 'block',\n    }),\n    indicatorWrapper: css({\n      position: 'absolute',\n      top: '4px',\n      right: '8px',\n      zIndex: 2,\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  Dashboard<PERSON>ursorSync,\n  GrafanaTheme2,\n  MetricFindValue,\n  dateTime,\n  DataFrame,\n  GetTagResponse,\n} from '@grafana/data';\nimport {\n  behaviors,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneObjectUrlSyncConfig,\n  SceneObjectUrlValues,\n  SceneQueryRunner,\n  SceneTimeRange,\n} from '@grafana/scenes';\n\nimport { REDPanel } from './REDPanel';\nimport {\n  MakeOptional,\n  explorationDS,\n  VAR_FILTERS_EXPR,\n  VAR_DATASOURCE_EXPR,\n  MetricFunction,\n  ComparisonSelection,\n  ALL,\n  VAR_LATENCY_THRESHOLD_EXPR,\n  filterStreamingProgressTransformations,\n} from '../../../utils/shared';\nimport { getDataSourceSrv } from '@grafana/runtime';\nimport { ActionViewType, TabsBarScene, actionViewsDefinitions } from './Tabs/TabsBarScene';\nimport { isEqual } from 'lodash';\nimport {\n  getDatasourceVariable,\n  getGroupByVariable,\n  getSpanListColumnsVariable,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { MiniREDPanel } from './MiniREDPanel';\nimport { Icon, LinkButton, Stack, Tooltip, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { getDefaultSelectionForMetric } from '../../../utils/comparison';\nimport { map, Observable } from 'rxjs';\n\nexport interface TraceSceneState extends SceneObjectState {\n  body: SceneFlexLayout;\n  actionView?: ActionViewType;\n\n  attributes?: string[];\n  selection?: ComparisonSelection;\n}\n\nexport class TracesByServiceScene extends SceneObjectBase<TraceSceneState> {\n  protected _urlSync = new SceneObjectUrlSyncConfig(this, { keys: ['actionView', 'selection'] });\n\n  public constructor(state: MakeOptional<TraceSceneState, 'body'>) {\n    super({\n      body: state.body ?? new SceneFlexLayout({ children: [] }),\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    // Get the initial actionView from URL if it exists i.e. coming from a bookmark\n    const params = new URLSearchParams(window.location.search);\n    const urlActionView = params.get('actionView');\n    if (urlActionView && actionViewsDefinitions.find((v) => v.value === urlActionView)) {\n      this.setState({ actionView: urlActionView as ActionViewType });\n    }\n\n    this.updateBody();\n\n    const exploration = getTraceExplorationScene(this);\n    const metricVariable = exploration.getMetricVariable();\n    this._subs.add(\n      metricVariable.subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          const selection = getDefaultSelectionForMetric(newState.value as MetricFunction);\n          if (selection) {\n            this.setState({ selection });\n          }\n          this.updateQueryRunner(newState.value as MetricFunction);\n          this.updateBody();\n        }\n      })\n    );\n\n    this._subs.add(\n      this.subscribeToState((newState, prevState) => {\n        const timeRange = sceneGraph.getTimeRange(this);\n        const selectionFrom = newState.selection?.timeRange?.from;\n        // clear selection if it's out of time range\n        if (selectionFrom && selectionFrom < timeRange.state.value.from.unix()) {\n          this.setState({ selection: undefined });\n        }\n\n        // Set group by to All when starting a comparison\n        if (!isEqual(newState.selection, prevState.selection)) {\n          const groupByVar = getGroupByVariable(this);\n          groupByVar.changeValueTo(ALL);\n          this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n        }\n      })\n    );\n\n    this._subs.add(\n      getDatasourceVariable(this).subscribeToState(() => {\n        this.updateAttributes();\n      })\n    );\n\n    this._subs.add(\n      getSpanListColumnsVariable(this).subscribeToState(() => {\n        this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n      })\n    );\n\n    this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n    this.updateAttributes();\n  }\n\n  updateBody() {\n    const traceExploration = getTraceExplorationScene(this);\n    const metric = traceExploration.getMetricVariable().getValue();\n    const actionViewDef = actionViewsDefinitions.find((v) => v.value === this.state.actionView);\n\n    this.setState({\n      body: buildGraphScene(\n        metric as MetricFunction,\n        actionViewDef ? [actionViewDef?.getScene(metric as MetricFunction)] : undefined\n      ),\n    });\n\n    if (this.state.actionView === undefined) {\n      this.setActionView('breakdown');\n    }\n  }\n\n  private async updateAttributes() {\n    const ds = await getDataSourceSrv().get(VAR_DATASOURCE_EXPR, { __sceneObject: { value: this } });\n\n    if (!ds) {\n      return;\n    }\n\n    ds.getTagKeys?.().then((tagKeys: GetTagResponse | MetricFindValue[]) => {\n      let keys: MetricFindValue[] = [];\n      if ('data' in tagKeys) {\n        keys = (tagKeys as GetTagResponse).data;\n      } else {\n        keys = tagKeys;\n      }\n      const attributes = keys.map((l) => l.text);\n      if (attributes !== this.state.attributes) {\n        this.setState({ attributes });\n      }\n    });\n  }\n\n  getUrlState() {\n    return {\n      actionView: this.state.actionView,\n      selection: this.state.selection ? JSON.stringify(this.state.selection) : undefined,\n    };\n  }\n\n  updateFromUrl(values: SceneObjectUrlValues) {\n    if (typeof values.actionView === 'string') {\n      if (this.state.actionView !== values.actionView) {\n        const actionViewDef = actionViewsDefinitions.find((v) => v.value === values.actionView);\n        if (actionViewDef) {\n          this.setActionView(actionViewDef.value);\n        }\n      }\n    } else if (values.actionView === null) {\n      this.setActionView('breakdown');\n    }\n\n    if (typeof values.selection === 'string') {\n      const newSelection = JSON.parse(values.selection);\n      if (!isEqual(newSelection, this.state.selection)) {\n        this.setState({ selection: newSelection });\n      }\n    }\n  }\n\n  onUserUpdateSelection(newSelection: ComparisonSelection) {\n    this._urlSync.performBrowserHistoryAction(() => {\n      this.setState({ selection: newSelection });\n    });\n  }\n\n  public setActionView(actionView?: ActionViewType) {\n    const { body } = this.state;\n    const actionViewDef = actionViewsDefinitions.find((v) => v.value === actionView);\n    const traceExploration = getTraceExplorationScene(this);\n    const metric = traceExploration.getMetricVariable().getValue();\n\n    if (body.state.children.length > 1) {\n      if (actionViewDef) {\n        body.setState({\n          children: [...body.state.children.slice(0, 2), actionViewDef.getScene(metric as MetricFunction)],\n        });\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.action_view_changed, {\n          oldAction: this.state.actionView,\n          newAction: actionView,\n        });\n        this.setState({ actionView: actionViewDef.value });\n      }\n    }\n  }\n\n  private updateQueryRunner(metric: MetricFunction) {\n    const selection = this.state.selection;\n    const columns = getSpanListColumnsVariable(this).getValue()?.toString() ?? '';\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(metric, columns, selection)],\n          $timeRange: timeRangeFromSelection(selection),\n        }),\n        transformations: [...filterStreamingProgressTransformations, ...spanListTransformations],\n      }),\n    });\n  }\n\n  static Component = ({ model }: SceneComponentProps<TracesByServiceScene>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <>\n        <div className={styles.title}>\n          <Tooltip content={<MetricTypeTooltip />} placement={'right-start'} interactive>\n            <span className={styles.hand}>\n              Select metric type <Icon name={'info-circle'} />\n            </span>\n          </Tooltip>\n        </div>\n        <body.Component model={body} />\n      </>\n    );\n  };\n}\n\nconst MetricTypeTooltip = () => {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <Stack direction={'column'} gap={1}>\n      <div className={styles.tooltip.title}>RED metrics for traces</div>\n      <span className={styles.tooltip.subtitle}>\n        Explore rate, errors, and duration (RED) metrics generated from traces by Tempo.\n      </span>\n      <div className={styles.tooltip.text}>\n        <div>\n          <span className={styles.tooltip.emphasize}>Rate</span> - Spans per second that match your filter, useful to\n          find unusual spikes in activity\n        </div>\n        <div>\n          <span className={styles.tooltip.emphasize}>Errors</span> -Spans that are failing, overall issues in tracing\n          ecosystem\n        </div>\n        <div>\n          <span className={styles.tooltip.emphasize}>Duration</span> - Amount of time those spans take, represented as a\n          heat map (responds time, latency)\n        </div>\n      </div>\n\n      <div className={styles.tooltip.button}>\n        <LinkButton\n          icon=\"external-link-alt\"\n          fill=\"solid\"\n          size={'sm'}\n          target={'_blank'}\n          href={\n            'https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces/concepts/#rate-error-and-duration-metrics'\n          }\n          onClick={() =>\n            reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.metric_docs_link_clicked)\n          }\n        >\n          Read documentation\n        </LinkButton>\n      </div>\n    </Stack>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    title: css({\n      label: 'title',\n      display: 'flex',\n      gap: theme.spacing.x0_5,\n      fontSize: theme.typography.bodySmall.fontSize,\n      paddingBottom: theme.spacing.x0_5,\n      alignItems: 'center',\n    }),\n    hand: css({\n      label: 'hand',\n      cursor: 'pointer',\n    }),\n    tooltip: {\n      label: 'tooltip',\n      title: css({\n        fontSize: '14px',\n        fontWeight: 500,\n      }),\n      subtitle: css({\n        marginBottom: theme.spacing.x1,\n      }),\n      text: css({\n        label: 'text',\n        color: theme.colors.text.secondary,\n\n        div: {\n          marginBottom: theme.spacing.x0_5,\n        },\n      }),\n      emphasize: css({\n        label: 'emphasize',\n        color: theme.colors.text.primary,\n      }),\n      button: css({\n        marginBottom: theme.spacing.x0_5,\n      }),\n    },\n  };\n}\n\nconst MAIN_PANEL_HEIGHT = 240;\nexport const MINI_PANEL_HEIGHT = (MAIN_PANEL_HEIGHT - 8) / 2;\n\nexport function buildQuery(type: MetricFunction, columns: string, selection?: ComparisonSelection) {\n  const selectQuery = columns !== '' ? ` | select(${columns})` : '';\n  let typeQuery = '';\n  switch (type) {\n    case 'errors':\n      typeQuery = ' && status = error';\n      break;\n    case 'duration':\n      if (selection) {\n        const duration = [];\n        if (selection.duration?.from.length) {\n          duration.push(`duration >= ${selection.duration.from}`);\n        }\n        if (selection.duration?.to.length) {\n          duration.push(`duration <= ${selection.duration.to}`);\n        }\n        if (duration.length) {\n          typeQuery += '&& ' + duration.join(' && ');\n        }\n      }\n      if (!typeQuery.length) {\n        typeQuery = `&& duration > ${VAR_LATENCY_THRESHOLD_EXPR}`;\n      }\n      break;\n  }\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}${typeQuery}}${selectQuery}`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 200,\n    spss: 10,\n    filters: [],\n  };\n}\n\nfunction timeRangeFromSelection(selection?: ComparisonSelection) {\n  const fromTimerange = (selection?.timeRange?.from || 0) * 1000;\n  const toTimerange = (selection?.timeRange?.to || 0) * 1000;\n  return fromTimerange && toTimerange\n    ? new SceneTimeRange({\n        from: fromTimerange.toFixed(0),\n        to: toTimerange.toFixed(0),\n        value: {\n          from: dateTime(fromTimerange),\n          to: dateTime(toTimerange),\n          raw: { from: dateTime(fromTimerange), to: dateTime(toTimerange) },\n        },\n      })\n    : undefined;\n}\n\nfunction buildGraphScene(metric: MetricFunction, children?: SceneObject[]) {\n  const secondaryPanel =\n    metric === 'rate'\n      ? new MiniREDPanel({ metric: 'errors' })\n      : new MiniREDPanel({\n          metric: 'rate',\n        });\n\n  const tertiaryPanel =\n    metric === 'duration'\n      ? new MiniREDPanel({\n          metric: 'errors',\n        })\n      : new MiniREDPanel({ metric: 'duration' });\n\n  return new SceneFlexLayout({\n    direction: 'column',\n    $behaviors: [\n      new behaviors.CursorSync({\n        key: 'metricCrosshairSync',\n        sync: DashboardCursorSync.Crosshair,\n      }),\n    ],\n    children: [\n      new SceneFlexLayout({\n        direction: 'row',\n        ySizing: 'content',\n        children: [\n          new SceneFlexItem({\n            minHeight: MAIN_PANEL_HEIGHT,\n            maxHeight: MAIN_PANEL_HEIGHT,\n            width: '60%',\n            body: new REDPanel({}),\n          }),\n          new SceneFlexLayout({\n            direction: 'column',\n            minHeight: MAIN_PANEL_HEIGHT,\n            maxHeight: MAIN_PANEL_HEIGHT,\n            children: [\n              new SceneFlexItem({\n                minHeight: MINI_PANEL_HEIGHT,\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n\n                body: secondaryPanel,\n              }),\n              new SceneFlexItem({\n                minHeight: MINI_PANEL_HEIGHT,\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n\n                ySizing: 'fill',\n\n                body: tertiaryPanel,\n              }),\n            ],\n          }),\n        ],\n      }),\n      new SceneFlexItem({\n        ySizing: 'content',\n        body: new TabsBarScene({}),\n      }),\n      ...(children || []),\n    ],\n  });\n}\n\nconst spanListTransformations = [\n  () => (source: Observable<DataFrame[]>) => {\n    return source.pipe(\n      map((data: DataFrame[]) => {\n        return data.map((df) => ({\n          ...df,\n          fields: df.fields.filter((f) => !f.name.startsWith('nestedSet')),\n        }));\n      })\n    );\n  },\n  {\n    id: 'sortBy',\n    options: {\n      fields: {},\n      sort: [\n        {\n          field: 'Duration',\n          desc: true,\n        },\n      ],\n    },\n  },\n  {\n    id: 'organize',\n    options: {\n      indexByName: {\n        'Start time': 0,\n        status: 1,\n        'Trace Service': 2,\n        'Trace Name': 3,\n        Duration: 4,\n        'Span ID': 5,\n        'span.http.method': 6,\n        'span.http.request.method': 7,\n        'span.http.path': 8,\n        'span.http.route': 9,\n        'span.http.status_code': 10,\n        'span.http.response.status_code': 11,\n      },\n    },\n  },\n];\n", "import React from 'react';\n\nimport {\n  SceneObjectState,\n  SceneObjectBase,\n  SceneComponentProps,\n  PanelBuilders,\n  SceneQueryRunner,\n  sceneGraph,\n  SceneObject,\n} from '@grafana/scenes';\nimport { LoadingState, GrafanaTheme2 } from '@grafana/data';\nimport { explorationDS } from 'utils/shared';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { useStyles2 } from '@grafana/ui';\n\nexport interface TracePanelState extends SceneObjectState {\n  panel?: SceneObject;\n  traceId: string;\n  spanId?: string;\n}\n\nexport class TraceViewPanelScene extends SceneObjectBase<TracePanelState> {\n  constructor(state: TracePanelState) {\n    super({\n      $data: new SceneQueryRunner({\n        datasource: explorationDS,\n        queries: [{ refId: 'A', query: state.traceId, queryType: 'traceql' }],\n      }),\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done) {\n            this.setState({\n              panel: this.getVizPanel().build(),\n            });\n          } else if (data.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new LoadingStateScene({\n                component: SkeletonComponent,\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  private getVizPanel() {\n    const panel = PanelBuilders.traces().setHoverHeader(true);\n    if (this.state.spanId) {\n      panel.setOption('focusedSpanId' as any, this.state.spanId as any);\n    }\n    return panel;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<TraceViewPanelScene>) => {\n    const { panel } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.panelContainer}>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.header}>\n        <Skeleton count={1} width={60} />\n        <Skeleton count={1} width={60} />\n      </div>\n      <Skeleton count={2} width={'80%'} />\n      <div className={styles.map}>\n        <Skeleton count={1} />\n        <Skeleton count={1} height={70} />\n      </div>\n\n      <div className={styles.span}>\n        <span className={styles.service1}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar1}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service2}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar2}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service3}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar3}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service4}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar4}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service5}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar5}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service6}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar6}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    panelContainer: css({\n      display: 'flex',\n      height: '100%',\n\n      '& .show-on-hover': {\n        display: 'none',\n      },\n    }),\n    container: css({\n      height: 'calc(100% - 32px)',\n      width: 'calc(100% - 32px)',\n      position: 'absolute',\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.border.weak}`,\n      padding: '5px',\n    }),\n    header: css({\n      marginBottom: '20px',\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    map: css({\n      marginTop: '20px',\n      marginBottom: '20px',\n    }),\n    span: css({\n      display: 'flex',\n    }),\n    service1: css({\n      width: '25%',\n    }),\n    bar1: css({\n      marginLeft: '5%',\n      width: '70%',\n    }),\n    service2: css({\n      width: '25%',\n    }),\n    bar2: css({\n      marginLeft: '10%',\n      width: '15%',\n    }),\n    service3: css({\n      width: '20%',\n      marginLeft: '5%',\n    }),\n    bar3: css({\n      marginLeft: '10%',\n      width: '65%',\n    }),\n    service4: css({\n      width: '20%',\n      marginLeft: '5%',\n    }),\n    bar4: css({\n      marginLeft: '15%',\n      width: '60%',\n    }),\n    service5: css({\n      width: '15%',\n      marginLeft: '10%',\n    }),\n    bar5: css({\n      marginLeft: '20%',\n      width: '35%',\n    }),\n    service6: css({\n      width: '15%',\n      marginLeft: '10%',\n    }),\n    bar6: css({\n      marginLeft: '30%',\n      width: '15%',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, SceneObject } from '@grafana/scenes';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { TraceViewPanelScene } from '../panels/TraceViewPanelScene';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { getTraceExplorationScene } from '../../../utils/utils';\n\nexport interface DetailsSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class TraceDrawerScene extends SceneObjectBase<DetailsSceneState> {\n  constructor(state: Partial<DetailsSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this.updateBody();\n\n    const traceExploration = getTraceExplorationScene(this);\n\n    traceExploration.subscribeToState((newState, prevState) => {\n      if (newState.traceId !== prevState.traceId || newState.spanId !== prevState.spanId) {\n        this.updateBody();\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.open_trace, {\n          traceId: newState.traceId,\n          spanId: newState.spanId,\n        });\n      }\n    });\n  }\n\n  private updateBody() {\n    const traceExploration = getTraceExplorationScene(this);\n\n    if (traceExploration.state.traceId) {\n      this.setState({\n        body: new TraceViewPanelScene({\n          traceId: traceExploration.state.traceId,\n          spanId: traceExploration.state.spanId,\n        }),\n      });\n    } else {\n      this.setState({\n        body: new EmptyStateScene({\n          message: 'No trace selected',\n        }),\n      });\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<TraceDrawerScene>) => {\n    const { body } = model.useState();   \n    return body && <body.Component model={body} />;\n  };\n}\n", "import React from 'react';\nimport { CustomVariable, MultiValueVariable, MultiValueVariableState, SceneComponentProps } from '@grafana/scenes';\nimport { primarySignalOptions } from './primary-signals';\nimport { RadioButtonGroup } from '@grafana/ui';\nimport { useMount } from 'react-use';\n\nexport class PrimarySignalVariable extends CustomVariable {\n  static Component = ({ model }: SceneComponentProps<MultiValueVariable<MultiValueVariableState>>) => {\n    const { value } = model.useState();\n\n    // ensure the variable is set to the default value\n    useMount(() => {\n      if (!value) {\n        model.changeValueTo(primarySignalOptions[0].value!);\n      }\n    });\n\n    return (\n      <RadioButtonGroup\n        options={primarySignalOptions}\n        value={value as string}\n        onChange={(v: string) => model.changeValueTo(v!, undefined, true)}\n      />\n    );\n  };\n}\n", "import { AdHocVariableFilter } from '@grafana/data';\n\nexport function renderTraceQLLabelFilters(filters: AdHocVariableFilter[]) {\n  const expr = filters\n    .filter((f) => f.key && f.operator && f.value)\n    .map((filter) => renderFilter(filter))\n    .join('&&');\n  // Return 'true' if there are no filters to help with cases where we want to concatenate additional filters in the expression\n  // and avoid invalid queries like '{ && key=value }'\n  return expr.length ? expr : 'true';\n}\n\nfunction renderFilter(filter: AdHocVariableFilter) {\n  let val = filter.value;\n  if (['span.messaging.destination.partition.id', 'span.network.protocol.version'].includes(filter.key) || \n    (!isNumber(val) && !['status', 'kind', 'span:status', 'span:kind', 'duration', 'span:duration', 'trace:duration', 'event:timeSinceStart'].includes(filter.key))\n  ) {\n    // Add quotes if it's coming from the filter input and it's not already quoted.\n    // Adding a filter from a time series graph already has quotes. This should be handled better.\n    if (typeof val === 'string' && !val.startsWith('\"') && !val.endsWith('\"')) {\n      val = `\"${val}\"`;\n    }\n  }\n\n  return `${filter.key}${filter.operator}${val}`;\n}\n\nfunction isNumber(value?: string | number): boolean {\n  return value != null && value !== '' && !isNaN(Number(value.toString().trim()));\n}\n", "import { LoadingState, dateTime } from '@grafana/data';\nimport {\n  SceneObjectBase,\n  SceneObjectState,\n  SceneTimeRange,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { getDatasourceVariable } from '../../utils/utils';\nimport { Alert, LinkButton } from '@grafana/ui';\nimport React from 'react';\n\nexport interface TraceQLIssueDetectorState extends SceneObjectState {\n  hasIssue: boolean;\n}\n\nexport class TraceQLIssueDetector extends SceneObjectBase<TraceQLIssueDetectorState> {\n  constructor() {\n    super({\n      hasIssue: false,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {    \n    this.runIssueDetectionQuery();\n\n    const datasourceVar = getDatasourceVariable(this);\n    this._subs.add(\n      datasourceVar.subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          this.resetIssues();\n          this.runIssueDetectionQuery();\n        }\n      })\n    );\n  }\n\n  private runIssueDetectionQuery() {\n    const datasourceVar = getDatasourceVariable(this);\n    \n    // Create a minimal time range to reduce resource usage\n    const now = dateTime();\n    const from = dateTime(now).subtract(1, 'minute');\n    const minimalTimeRange = new SceneTimeRange({\n      from: from.toISOString(),\n      to: now.toISOString(),\n    });\n    \n    const issueDetector = new SceneQueryRunner({\n      maxDataPoints: 1,\n      datasource: { uid: String(datasourceVar.state.value) },\n      $timeRange: minimalTimeRange,\n      queries: [{\n        refId: 'issueDetectorQuery',\n        query: '{} | rate()',\n        queryType: 'traceql',\n        tableType: 'spans',\n        limit: 1,\n        spss: 1,\n        filters: [],\n      }],\n    });\n    \n    this._subs.add(\n      issueDetector.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Error) {\n          const message = state.data?.errors?.[0]?.message || '';\n          // This is the error message when the datasource is not configured for TraceQL metrics\n          // https://grafana.com/docs/tempo/latest/operations/traceql-metrics/#activate-and-configure-the-local-blocks-processor\n          if (message.includes('localblocks processor not found')) {\n            this.setState({ hasIssue: true });\n          }\n        }\n      })\n    );\n    \n    issueDetector.activate();\n  }\n\n  public resetIssues() {\n    this.setState({\n      hasIssue: false,\n    });\n  }\n} \n\nconst TraceQLWarningTitle = 'TraceQL metrics not configured';\nconst TraceQLWarningMessage = 'We found an error running a TraceQL metrics query: \"localblocks processor not found\". This typically means the \"local-blocks\" processor is not configured in Tempo, which is required for Grafana Traces Drilldown to work.';\n\nexport const TraceQLConfigWarning: React.FC<{ detector: TraceQLIssueDetector }> = ({ detector }) => {\n  const { hasIssue } = detector.useState();\n\n  if (!hasIssue) {\n    return null;\n  }\n\n  return (\n    <Alert\n      severity=\"warning\"\n      title={TraceQLWarningTitle}\n    >\n      <p>\n        {TraceQLWarningMessage}\n        <LinkButton\n          icon=\"external-link-alt\"\n          fill=\"text\"\n          size=\"sm\"\n          target=\"_blank\"\n          href=\"https://grafana.com/docs/tempo/latest/operations/traceql-metrics\"\n        >\n          Read documentation\n        </LinkButton>\n      </p>\n    </Alert>\n  );\n};\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { AdHocVariableFilter, GrafanaTheme2, LoadingState, PluginExtensionLink } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  CustomVariable,\n  DataSourceVariable,\n  SceneComponentProps,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneObjectUrlSyncConfig,\n  SceneObjectUrlValues,\n  SceneQueryRunner,\n  SceneRefreshPicker,\n  SceneTimePicker,\n  SceneTimeRange,\n  SceneVariableSet,\n} from '@grafana/scenes';\nimport { config } from '@grafana/runtime';\nimport { Button, Drawer, Dropdown, Icon, IconButton, Menu, Stack, useStyles2 } from '@grafana/ui';\n\nimport { TracesByServiceScene } from '../../components/Explore/TracesByService/TracesByServiceScene';\nimport {\n  DATASOURCE_LS_KEY,\n  EventTraceOpened,\n  explorationDS,\n  MetricFunction,\n  VAR_DATASOURCE,\n  VAR_FILTERS,\n  VAR_GROUPBY,\n  VAR_LATENCY_PARTIAL_THRESHOLD,\n  VAR_LATENCY_THRESHOLD,\n  VAR_METRIC,\n  VAR_PRIMARY_SIGNAL,\n  VAR_SPAN_LIST_COLUMNS,\n} from '../../utils/shared';\nimport { getTraceExplorationScene, getFiltersVariable, getPrimarySignalVariable, getDataSource } from '../../utils/utils';\nimport { TraceDrawerScene } from '../../components/Explore/TracesByService/TraceDrawerScene';\nimport { primarySignalOptions } from './primary-signals';\nimport { VariableHide } from '@grafana/schema';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { PrimarySignalVariable } from './PrimarySignalVariable';\nimport { renderTraceQLLabelFilters } from 'utils/filters-renderer';\nimport { TraceQLIssueDetector, TraceQLConfigWarning } from '../../components/Explore/TraceQLIssueDetector';\nimport { AddToInvestigationButton } from 'components/Explore/actions/AddToInvestigationButton';\nimport { ADD_TO_INVESTIGATION_MENU_TEXT, getInvestigationLink } from 'components/Explore/panels/PanelMenu';\n\nexport interface TraceExplorationState extends SceneObjectState {\n  topScene?: SceneObject;\n  controls: SceneObject[];\n\n  body: SceneObject;\n\n  drawerScene?: TraceDrawerScene;\n\n  // details scene\n  traceId?: string;\n  spanId?: string;\n\n  // just for the starting data source\n  initialDS?: string;\n  initialFilters?: AdHocVariableFilter[];\n\n  issueDetector?: TraceQLIssueDetector;\n\n  investigationLink?: PluginExtensionLink;\n  addToInvestigationButton?: AddToInvestigationButton;\n}\n\nconst version = process.env.VERSION;\nconst buildTime = process.env.BUILD_TIME;\nconst commitSha = process.env.COMMIT_SHA;\nconst compositeVersion = `${buildTime?.split('T')[0]} (${commitSha})`;\n\nexport class TraceExploration extends SceneObjectBase<TraceExplorationState> {\n  protected _urlSync = new SceneObjectUrlSyncConfig(this, { keys: ['primarySignal', 'traceId', 'spanId', 'metric'] });\n\n  public constructor(state: Partial<TraceExplorationState>) {\n    super({\n      $timeRange: state.$timeRange ?? new SceneTimeRange({}),\n      $variables: state.$variables ?? getVariableSet(state.initialDS, state.initialFilters),\n      controls: state.controls ?? [new SceneTimePicker({}), new SceneRefreshPicker({})],\n      body: new TraceExplorationScene({}),\n      drawerScene: new TraceDrawerScene({}),\n      issueDetector: new TraceQLIssueDetector(),\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  public _onActivate() {\n    if (!this.state.topScene) {\n      this.setState({ topScene: getTopScene() });\n    }\n\n    this._subs.add(\n      this.subscribeToEvent(EventTraceOpened, (event) => {\n        this.setupInvestigationButton(event.payload.traceId);\n        this.setState({ traceId: event.payload.traceId, spanId: event.payload.spanId });\n      })\n    );\n\n    if (this.state.traceId) {\n      this.setupInvestigationButton(this.state.traceId);\n    }\n\n    const datasourceVar = sceneGraph.lookupVariable(VAR_DATASOURCE, this) as DataSourceVariable;\n    datasourceVar.subscribeToState((newState) => {\n      if (newState.value) {\n        localStorage.setItem(DATASOURCE_LS_KEY, newState.value.toString());\n      }\n    });\n\n    if (this.state.issueDetector) {\n      if (!this.state.issueDetector.isActive) {\n        this.state.issueDetector.activate();\n      }\n    }\n  }\n\n  getUrlState() {\n    return { traceId: this.state.traceId, spanId: this.state.spanId };\n  }\n\n  updateFromUrl(values: SceneObjectUrlValues) {\n    const stateUpdate: Partial<TraceExplorationState> = {};\n\n    if (values.traceId || values.spanId) {\n      stateUpdate.traceId = values.traceId ? (values.traceId as string) : undefined;\n      stateUpdate.spanId = values.spanId ? (values.spanId as string) : undefined;\n    }\n\n    this.setState(stateUpdate);\n  }\n\n  public getMetricVariable() {\n    const variable = sceneGraph.lookupVariable(VAR_METRIC, this);\n    if (!(variable instanceof CustomVariable)) {\n      throw new Error('Metric variable not found');\n    }\n\n    if (!variable.getValue()) {\n      variable.changeValueTo('rate');\n    }\n\n    return variable;\n  }\n\n  public onChangeMetricFunction = (metric: string) => {\n    const variable = this.getMetricVariable();\n    if (!metric || variable.getValue() === metric) {\n      return;\n    }\n\n    variable.changeValueTo(metric, undefined, true);\n  };\n\n  public getMetricFunction() {\n    return this.getMetricVariable().getValue() as MetricFunction;\n  }\n\n  public closeDrawer() {\n    this.setState({ traceId: undefined, spanId: undefined });\n  }\n\n  private setupInvestigationButton(traceId: string) {\n    const traceExploration = getTraceExplorationScene(this);\n    const dsUid = getDataSource(traceExploration);\n\n    const queryRunner = new SceneQueryRunner({\n      datasource: { uid: dsUid },\n      queries: [{ \n        refId: 'A', \n        query: traceId, \n        queryType: 'traceql',\n      }],\n    });\n\n    const addToInvestigationButton = new AddToInvestigationButton({\n      query: traceId,\n      type: 'trace',\n      dsUid,\n      $data: queryRunner,\n    });\n    \n    addToInvestigationButton.activate();\n    this.setState({ addToInvestigationButton });\n    this._subs.add(\n      addToInvestigationButton.subscribeToState(() => {\n        this.updateInvestigationLink();\n      })\n    );\n        \n    queryRunner.activate();\n    \n    this._subs.add(\n      queryRunner.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Done && state.data?.series?.length > 0) {\n          const serviceNameField = state.data.series[0]?.fields?.find((f) => f.name === 'serviceName');\n          \n          if (serviceNameField && serviceNameField.values[0]) {\n            addToInvestigationButton.setState({\n              ...addToInvestigationButton.state,\n              labelValue: `${serviceNameField.values[0]}`,\n            });\n          }\n        }\n      })\n    );\n    \n    addToInvestigationButton.setState({\n      ...addToInvestigationButton.state,\n      labelValue: traceId,\n    });\n  }\n\n  private async updateInvestigationLink() {\n    const { addToInvestigationButton } = this.state;\n    if (!addToInvestigationButton) { \n      return;\n    }\n\n    const link = await getInvestigationLink(addToInvestigationButton);\n    if (link) {\n      this.setState({ investigationLink: link });\n    }\n  }\n\n  static Component = ({ model }: SceneComponentProps<TraceExploration>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return <div className={styles.bodyContainer}> {body && <body.Component model={body} />} </div>;\n  };\n}\n\nexport class TraceExplorationScene extends SceneObjectBase {\n  static Component = ({ model }: SceneComponentProps<TraceExplorationScene>) => {\n    const traceExploration = getTraceExplorationScene(model);\n    const { controls, topScene, drawerScene, traceId, issueDetector, investigationLink, addToInvestigationButton } = traceExploration.useState();\n    const { hasIssue } = issueDetector?.useState() || {\n      hasIssue: false,\n    };\n    const styles = useStyles2(getStyles);\n    const [menuVisible, setMenuVisible] = React.useState(false);\n\n    const dsVariable = sceneGraph.lookupVariable(VAR_DATASOURCE, traceExploration);\n    const filtersVariable = getFiltersVariable(traceExploration);\n    const primarySignalVariable = getPrimarySignalVariable(traceExploration);\n\n    function VersionHeader() {\n      const styles = useStyles2(getStyles);\n    \n      return (\n        <div className={styles.menuHeader}>\n          <h5>Grafana Traces Drilldown v{version}</h5>\n          <div className={styles.menuHeaderSubtitle}>Last update: {compositeVersion}</div>\n        </div>\n      );\n    }\n\n    const menu = (\n      <Menu header={<VersionHeader />}>\n        <div className={styles.menu}>\n          {config.feedbackLinksEnabled && (\n            <Menu.Item\n              label=\"Give feedback\"\n              ariaLabel=\"Give feedback\"\n              icon={'comment-alt-message'}\n              url=\"https://grafana.qualtrics.com/jfe/form/SV_9LUZ21zl3x4vUcS\"\n              target=\"_blank\"\n              onClick={() =>\n                reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.global_docs_link_clicked)\n              }\n            />\n          )}\n          <Menu.Item\n            label=\"Documentation\"\n            ariaLabel=\"Documentation\"\n            icon={'external-link-alt'}\n            url=\"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/\"\n            target=\"_blank\"\n            onClick={() =>\n              reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.feedback_link_clicked)\n            }\n          />\n        </div>\n      </Menu>\n    );\n\n    const addToInvestigationClicked = (e: React.MouseEvent) => {\n      if (investigationLink?.onClick) {\n        investigationLink.onClick(e);\n      }\n      \n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.add_to_investigation_trace_view_clicked\n      );\n      \n      setTimeout(() => traceExploration.closeDrawer(), 100);\n    };\n\n    return (\n      <>\n        <div className={styles.container}>\n          <div className={styles.headerContainer}>\n            {hasIssue && issueDetector && <TraceQLConfigWarning detector={issueDetector} />}\n            <Stack gap={1} justifyContent={'space-between'} wrap={'wrap'}>\n              <Stack gap={1} alignItems={'center'} wrap={'wrap'}>\n                {dsVariable && (\n                  <Stack gap={0} alignItems={'center'}>\n                    <div className={styles.datasourceLabel}>Data source</div>\n                    <dsVariable.Component model={dsVariable} />\n                  </Stack>\n                )}\n              </Stack>\n\n              <div className={styles.controls}>\n                <Dropdown overlay={menu} onVisibleChange={() => setMenuVisible(!menuVisible)}>\n                  <Button variant=\"secondary\" icon=\"info-circle\">\n                    Need help\n                    <Icon className={styles.helpIcon} name={menuVisible ? 'angle-up' : 'angle-down'} size=\"lg\" />\n                  </Button>\n                </Dropdown>\n                {controls.map((control) => (\n                  <control.Component key={control.state.key} model={control} />\n                ))}\n              </div>\n            </Stack>\n            <Stack gap={1} alignItems={'center'} wrap={'wrap'}>\n              <Stack gap={0} alignItems={'center'}>\n                <div className={styles.datasourceLabel}>Filters</div>\n                {primarySignalVariable && <primarySignalVariable.Component model={primarySignalVariable} />}\n              </Stack>\n              {filtersVariable && (\n                <div>\n                  <filtersVariable.Component model={filtersVariable} />\n                </div>\n              )}\n            </Stack>\n          </div>\n          <div className={styles.body}>{topScene && <topScene.Component model={topScene} />}</div>\n        </div>\n        {drawerScene && traceId && (\n          <Drawer size={'lg'} onClose={() => traceExploration.closeDrawer()}>\n            <div className={styles.drawerHeader}>\n              <h3>View trace {traceId}</h3>\n                <div className={styles.drawerHeaderButtons}>\n                  {addToInvestigationButton && investigationLink && (\n                    <Button\n                      variant='secondary'\n                      size='sm'\n                      icon='plus-square'\n                      onClick={addToInvestigationClicked}\n                    >\n                      {ADD_TO_INVESTIGATION_MENU_TEXT}\n                    </Button>\n                  )}\n                  <IconButton \n                    name='times' \n                    onClick={() => traceExploration.closeDrawer()} \n                    tooltip='Close drawer'\n                    size='lg'\n                  />\n                </div>\n            </div>\n            <drawerScene.Component model={drawerScene} />\n          </Drawer>\n        )}\n      </>\n    );\n  };\n}\n\nfunction getTopScene() {\n  return new TracesByServiceScene({});\n}\n\nfunction getVariableSet(initialDS?: string, initialFilters?: AdHocVariableFilter[]) {\n  return new SceneVariableSet({\n    variables: [\n      new DataSourceVariable({\n        name: VAR_DATASOURCE,\n        label: 'Data source',\n        value: initialDS,\n        pluginId: 'tempo',\n      }),\n      new PrimarySignalVariable({\n        name: VAR_PRIMARY_SIGNAL,\n        value: primarySignalOptions[0].value,\n      }),\n      new AdHocFiltersVariable({\n        addFilterButtonText: 'Add filter',\n        hide: VariableHide.hideLabel,\n        name: VAR_FILTERS,\n        datasource: explorationDS,\n        layout: 'combobox',\n        filters: initialFilters ?? [],\n        allowCustomValue: true,\n        expressionBuilder: renderTraceQLLabelFilters,\n      }),\n      new CustomVariable({\n        name: VAR_METRIC,\n        hide: VariableHide.hideVariable,\n      }),\n      new CustomVariable({\n        name: VAR_GROUPBY,\n        defaultToAll: false,\n      }),\n      new CustomVariable({\n        name: VAR_SPAN_LIST_COLUMNS,\n        defaultToAll: false,\n      }),\n      new CustomVariable({\n        name: VAR_LATENCY_THRESHOLD,\n        defaultToAll: false,\n        hide: VariableHide.hideVariable,\n      }),\n      new CustomVariable({\n        name: VAR_LATENCY_PARTIAL_THRESHOLD,\n        defaultToAll: false,\n        hide: VariableHide.hideVariable,\n      }),\n    ],\n  });\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    bodyContainer: css({\n      label: 'bodyContainer',\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    container: css({\n      label: 'container',\n      flexGrow: 1,\n      display: 'flex',\n      gap: theme.spacing(1),\n      minHeight: '100%',\n      flexDirection: 'column',\n      padding: `0 ${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(2)}`,\n      overflow: 'auto', /* Needed for sticky positioning */\n      maxHeight: '100%' /* Needed for sticky positioning */\n    }),\n    drawerHeader: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      borderBottom: `1px solid ${theme.colors.border.weak}`,\n      paddingBottom: theme.spacing(2),\n      marginBottom: theme.spacing(2),\n\n      'h3': {\n        margin: 0,\n      },\n    }),\n    drawerHeaderButtons: css({\n      display: 'flex',\n      justifyContent: 'flex-end',\n      gap: theme.spacing(1.5),\n    }),\n    body: css({\n      label: 'body',\n      flexGrow: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing(1),\n    }),\n    headerContainer: css({\n      label: 'headerContainer',\n      backgroundColor: theme.colors.background.canvas,\n      display: 'flex',\n      flexDirection: 'column',\n      position: 'sticky',\n      top: 0,\n      zIndex: 3,\n      padding: `${theme.spacing(1.5)} 0`,\n      gap: theme.spacing(1),\n    }),\n    datasourceLabel: css({\n      label: 'datasourceLabel',\n      fontSize: '12px',\n      padding: `0 ${theme.spacing(1)}`,\n      height: '32px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'flex-start',\n      fontWeight: theme.typography.fontWeightMedium,\n      position: 'relative',\n      right: -1,\n      width: '90px',\n    }),\n    controls: css({\n      label: 'controls',\n      display: 'flex',\n      gap: theme.spacing(1),\n      zIndex: 3,\n      flexWrap: 'wrap',\n    }),\n    menu: css({\n      label: 'menu',\n      'svg, span': {\n        color: theme.colors.text.link,\n      },\n    }),\n    menuHeader: css`\n      padding: ${theme.spacing(0.5, 1)};\n      white-space: nowrap;\n    `,\n    menuHeaderSubtitle: css`\n      color: ${theme.colors.text.secondary};\n      font-size: ${theme.typography.bodySmall.fontSize};\n    `,\n    tooltip: css({\n      label: 'tooltip',\n      fontSize: '14px',\n      lineHeight: '22px',\n      width: '180px',\n      textAlign: 'center',\n    }),\n    helpIcon: css({\n      label: 'helpIcon',\n      marginLeft: theme.spacing(1),\n    }),\n    filters: css({\n      label: 'filters',\n      marginTop: theme.spacing(1),\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { locationService } from '@grafana/runtime';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { HomepagePanelType } from './AttributePanel';\n\ntype Props = {\n  index: number;\n  type: HomepagePanelType;\n  label: string;\n  labelTitle: string;\n  value: string;\n  valueTitle: string;\n  url: string;\n};\n\nexport const AttributePanelRow = (props: Props) => {\n  const { index, type, label, labelTitle, value, valueTitle, url } = props;\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div key={index}>\n      {index === 0 && (\n        <div className={styles.rowHeader}>\n          <span>{labelTitle}</span>\n          <span className={styles.valueTitle}>{valueTitle}</span>\n        </div>\n      )}\n\n      <div\n        className={styles.row}\n        key={index}\n        onClick={() => {\n          reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.panel_row_clicked, {\n            type,\n            index,\n            value,\n          });\n          locationService.push(url);\n        }}\n      >\n        <div className={'rowLabel'}>{label}</div>\n\n        <div className={styles.action}>\n          <span className={styles.actionText}>{value}</span>\n          <Icon className={styles.actionIcon} name=\"arrow-right\" size=\"xl\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    rowHeader: css({\n      color: theme.colors.text.secondary,\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: `0 ${theme.spacing(2)} ${theme.spacing(1)} ${theme.spacing(2)}`,\n    }),\n    valueTitle: css({\n      margin: '0 45px 0 0',\n    }),\n    row: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      gap: theme.spacing(2),\n      padding: `${theme.spacing(0.75)} ${theme.spacing(2)}`,\n\n      '&:hover': {\n        backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n        cursor: 'pointer',\n        '.rowLabel': {\n          textDecoration: 'underline',\n        },\n      },\n    }),\n    action: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    actionText: css({\n      color: '#d5983c',\n      padding: `0 ${theme.spacing(1)}`,\n      width: 'max-content',\n    }),\n    actionIcon: css({\n      cursor: 'pointer',\n      margin: `0 ${theme.spacing(0.5)} 0 ${theme.spacing(1)}`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const ErroredServicesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const getLabel = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return valuesField?.labels?.['resource.service.name'].replace(/\"/g, '') ?? 'Service name not found';\n  };\n\n  const getUrl = (df: DataFrame) => {\n    const serviceName = getLabel(df);\n    const params = {\n      'var-filters': `resource.service.name|=|${serviceName}`,\n      'var-metric': 'errors',\n    };\n    return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n  };\n\n  const getTotalErrs = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return (\n      valuesField?.values?.reduce((x, acc) => {\n        if (typeof x === 'number' && !isNaN(x)) {\n          return x + acc;\n        }\n        return acc;\n      }, 0) ?? 1\n    );\n  };\n\n  return (\n    <div className={styles.container}>\n      {series\n        .sort((a, b) => getTotalErrs(b) - getTotalErrs(a))\n        .slice(0, 10)\n        ?.map((df, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(df)}\n              labelTitle=\"Service\"\n              value={getTotalErrs(df)}\n              valueTitle=\"Total errors\"\n              url={getUrl(df)}\n            />\n          </span>\n        ))}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, Field, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE, ROUTES } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\nimport { formatDuration } from '../../utils/dates';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const SlowestTracesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const durField = series[0].fields.find((f) => f.name === 'duration');\n  if (durField && durField.values) {\n    const sortedByDuration = durField?.values\n      .map((_, i) => i)\n      ?.sort((a, b) => durField?.values[b] - durField?.values[a]);\n    const sortedFields = series[0].fields.map((f) => {\n      return {\n        ...f,\n        values: sortedByDuration?.map((i) => f.values[i]),\n      };\n    });\n\n    const getLabel = (traceServiceField: Field | undefined, traceNameField: Field | undefined, index: number) => {\n      let label = '';\n      if (traceServiceField?.values[index]) {\n        label = traceServiceField.values[index];\n      }\n      if (traceNameField?.values[index]) {\n        label = label.length === 0 ? traceNameField.values[index] : `${label}: ${traceNameField.values[index]}`;\n      }\n      return label.length === 0 ? 'Trace service & name not found' : label;\n    };\n\n    const getUrl = (\n      traceId: string,\n      spanIdField: Field | undefined,\n      traceServiceField: Field | undefined,\n      index: number\n    ) => {\n      if (!spanIdField || !spanIdField.values[index] || !traceServiceField || !traceServiceField.values[index]) {\n        console.error('SpanId or traceService not found');\n        return ROUTES.Explore;\n      }\n\n      const params = {\n        traceId,\n        spanId: spanIdField.values[index],\n        'var-filters': `resource.service.name|=|${traceServiceField.values[index]}`,\n        'var-metric': 'duration',\n      };\n\n      return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n    };\n\n    const getDuration = (durationField: Field | undefined, index: number) => {\n      if (!durationField || !durationField.values) {\n        return 'Duration not found';\n      }\n\n      return formatDuration(durationField.values[index] / 1000);\n    };\n\n    const traceIdField = sortedFields.find((f) => f.name === 'traceIdHidden');\n    const spanIdField = sortedFields.find((f) => f.name === 'spanID');\n    const traceNameField = sortedFields.find((f) => f.name === 'traceName');\n    const traceServiceField = sortedFields.find((f) => f.name === 'traceService');\n    const durationField = sortedFields.find((f) => f.name === 'duration');\n\n    return (\n      <div className={styles.container}>\n        {traceIdField?.values?.map((traceId, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(traceServiceField, traceNameField, index)}\n              labelTitle=\"Trace\"\n              value={getDuration(durationField, index)}\n              valueTitle=\"Duration\"\n              url={getUrl(traceId, spanIdField, traceServiceField, index)}\n            />\n          </span>\n        ))}\n      </div>\n    );\n  }\n  return null;\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\nimport { formatDuration } from '../../utils/dates';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const SlowestServicesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const getLabel = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return valuesField?.labels?.['resource.service.name'].replace(/\"/g, '') ?? 'Service name not found';\n  };\n\n  const getUrl = (df: DataFrame) => {\n    const serviceName = getLabel(df);\n    const params = {\n      'var-filters': `resource.service.name|=|${serviceName}`,\n      'var-metric': 'duration',\n    };\n    return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n  };\n\n  const getDuration = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return (\n      valuesField?.values?.reduce((x, acc) => {\n        if (typeof x === 'number' && !isNaN(x)) {\n          return x + acc;\n        }\n        return acc;\n      }, 0) ?? 1\n    );\n  };\n\n  return (\n    <div className={styles.container}>\n      {series\n        .sort((a, b) => getDuration(b) - getDuration(a))\n        .slice(0, 10)\n        ?.map((df, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(df)}\n              labelTitle=\"Service\"\n              value={formatDuration(getDuration(df) * 1000000 /*s to μs*/)}\n              valueTitle=\"p90\"\n              url={getUrl(df)}\n            />\n          </span>\n        ))}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { HomepagePanelType } from './AttributePanel';\nimport { ErroredServicesRows } from './ErroredServicesRows';\nimport { SlowestTracesRows } from './SlowestTracesRows';\nimport { SlowestServicesRows } from './SlowestServicesRows';\n\ntype Props = {\n  series?: DataFrame[];\n  type: HomepagePanelType;\n  message?: string;\n};\n\nexport const AttributePanelRows = (props: Props) => {\n  const { series, type, message } = props;\n  const styles = useStyles2(getStyles);\n\n  if (message) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.message}>\n          <Icon className={styles.icon} name=\"exclamation-circle\" size=\"xl\" />\n          {message}\n        </div>\n      </div>\n    );\n  }\n\n  if (series && series.length > 0) {\n    switch (type) {\n      case 'slowest-traces':\n        return <SlowestTracesRows series={series} type={type} />;\n      case 'errored-services':\n        return <ErroredServicesRows series={series} type={type} />;\n      case 'slowest-services':\n        return <SlowestServicesRows series={series} type={type} />;\n    }\n  }\n  return <div className={styles.container}>No series data</div>;\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n    icon: css({\n      margin: `0 ${theme.spacing(0.5)} 0 ${theme.spacing(1)}`,\n    }),\n    message: css({\n      display: 'flex',\n      gap: theme.spacing(1.5),\n      margin: `${theme.spacing(2)} auto`,\n      width: '60%',\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { AttributePanelRows } from './AttributePanelRows';\nimport { HomepagePanelType } from './AttributePanel';\n\ninterface AttributePanelSceneState extends SceneObjectState {\n  series?: DataFrame[];\n  title: string;\n  type: HomepagePanelType;\n  message?: string;\n}\n\nexport class AttributePanelScene extends SceneObjectBase<AttributePanelSceneState> {\n  public static Component = ({ model }: SceneComponentProps<AttributePanelScene>) => {\n    const { series, title, type, message } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.title}>\n          <Icon name={getIcon(type)} size=\"lg\" />\n          <span className={styles.titleText}>{title}</span>\n        </div>\n        <AttributePanelRows series={series} type={type} message={message} />\n      </div>\n    );\n  };\n}\n\nfunction getIcon(type: HomepagePanelType) {\n  switch (type) {\n    case 'slowest-services':\n      return 'clock-nine';\n    case 'slowest-traces':\n      return 'crosshair';\n    case 'errored-services':\n      return 'exclamation-triangle';\n    default:\n      return 'exclamation-triangle';\n  }\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      border: `1px solid ${theme.isDark ? theme.colors.border.medium : theme.colors.border.weak}`,\n      borderRadius: theme.spacing(0.5),\n      marginBottom: theme.spacing(4),\n      width: '100%',\n    }),\n    title: css({\n      color: theme.isDark ? theme.colors.text.secondary : theme.colors.text.primary,\n      backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      borderTopLeftRadius: theme.spacing(0.5),\n      borderTopRightRadius: theme.spacing(0.5),\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      fontSize: '1.3rem',\n      padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,\n    }),\n    titleText: css({\n      marginLeft: theme.spacing(1),\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { explorationDS } from 'utils/shared';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { MINI_PANEL_HEIGHT } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { AttributePanelScene } from './AttributePanelScene';\nimport Skeleton from 'react-loading-skeleton';\nimport { getErrorMessage, getNoDataMessage } from 'utils/utils';\nimport { getMinimumsForDuration, getYBuckets } from 'components/Explore/TracesByService/REDPanel';\n\nexport type HomepagePanelType = 'errored-services' | 'slowest-services' | 'slowest-traces';\n\nexport interface AttributePanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  query: {\n    query: string;\n    step?: string;\n  };\n  title: string;\n  type: HomepagePanelType;\n  renderDurationPanel?: boolean;\n  filter?: string;\n}\n\nexport class AttributePanel extends SceneObjectBase<AttributePanelState> {\n  constructor(state: AttributePanelState) {\n    super({\n      $data: new SceneQueryRunner({\n        datasource: explorationDS,\n        queries: [{ refId: 'A', queryType: 'traceql', tableType: 'spans', limit: 10, ...state.query, exemplars: 0 }],\n      }),\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done || data.data?.state === LoadingState.Streaming) {\n            if (\n              data.data?.state === LoadingState.Done &&\n              (data.data.series.length === 0 || data.data.series[0].length === 0)\n            ) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new AttributePanelScene({\n                      message: getNoDataMessage(state.title.toLowerCase()),\n                      title: state.title,\n                      type: state.type,\n                    }),\n                  ],\n                }),\n              });\n            } else if (data.data.series.length > 0) {\n              if (state.type !== 'slowest-traces' || state.renderDurationPanel) {\n                this.setState({\n                  panel: new SceneFlexLayout({\n                    children: [\n                      new AttributePanelScene({\n                        series: data.data.series,\n                        title: state.title,\n                        type: state.type,\n                      }),\n                    ],\n                  }),\n                });\n              } else if (data.data?.state === LoadingState.Done) {\n                let yBuckets = getYBuckets(data.data?.series ?? []);\n                if (yBuckets?.length) {\n                  const { minDuration } = getMinimumsForDuration(yBuckets);\n\n                  this.setState({\n                    panel: new SceneFlexLayout({\n                      children: [\n                        new AttributePanel({\n                          query: {\n                            query: `{nestedSetParent<0 && duration > ${minDuration} ${state.filter ?? ''}}`,\n                          },\n                          title: state.title,\n                          type: state.type,\n                          renderDurationPanel: true,\n                        }),\n                      ],\n                    }),\n                  });\n                }\n              }\n            }\n          } else if (data.data?.state === LoadingState.Error) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                children: [\n                  new AttributePanelScene({\n                    message: getErrorMessage(data),\n                    title: state.title,\n                    type: state.type,\n                  }),\n                ],\n              }),\n            });\n          } else {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<AttributePanel>) => {\n    const { panel } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.container}>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles() {\n  return {\n    container: css({\n      minWidth: '350px',\n      width: '-webkit-fill-available',\n    }),\n  };\n}\n\nexport const SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.title}>\n        <Skeleton count={1} width={200} />\n      </div>\n      <div className={styles.tracesContainer}>\n        {[...Array(11)].map((_, i) => (\n          <div className={styles.row} key={i}>\n            <div className={styles.rowLeft}>\n              <Skeleton count={1} />\n            </div>\n            <div className={styles.rowRight}>\n              <Skeleton count={1} />\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      border: `1px solid ${theme.isDark ? theme.colors.border.medium : theme.colors.border.weak}`,\n      borderRadius: theme.spacing(0.5),\n      marginBottom: theme.spacing(4),\n      width: '100%',\n    }),\n    title: css({\n      color: theme.colors.text.secondary,\n      backgroundColor: theme.colors.background.secondary,\n      fontSize: '1.3rem',\n      padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,\n      textAlign: 'center',\n    }),\n    tracesContainer: css({\n      padding: `13px ${theme.spacing(2)}`,\n    }),\n    row: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    rowLeft: css({\n      margin: '7px 0',\n      width: '150px',\n    }),\n    rowRight: css({\n      width: '50px',\n    }),\n  };\n}\n", "import React from \"react\";\n\nexport const LightModeRocket = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"73\" height=\"72\" viewBox=\"0 0 73 72\" fill=\"none\">\n    <path\n      d=\"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z\"\n      fill=\"#24292E\"\n      fillOpacity=\"0.75\"\n    />\n  </svg>\n);\n\nexport const DarkModeRocket = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"73\" height=\"72\" viewBox=\"0 0 73 72\" fill=\"none\">\n    <path\n      d=\"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z\"\n      fill=\"#CCCCDC\"\n      fillOpacity=\"0.65\"\n    />\n  </svg>\n);\n", "import { ACTION_VIEW, PRIMARY_SIGNAL, VAR_FILTERS, FILTER_SEPARATOR, BOOKMARKS_LS_KEY, EXPLORATIONS_ROUTE, VAR_LATENCY_PARTIAL_THRESHOLD, VAR_LATENCY_THRESHOLD, SELECTION, VAR_METRIC } from \"utils/shared\";\nimport { Bookmark } from \"./Bookmarks\";\nimport { urlUtil } from \"@grafana/data\";\nimport { locationService, usePluginUserStorage } from '@grafana/runtime';\nimport { USER_EVENTS_ACTIONS, USER_EVENTS_PAGES, reportAppInteraction } from \"utils/analytics\";\n\ntype PluginStorage = ReturnType<typeof usePluginUserStorage>;\n\nconst cleanupParams = (params: URLSearchParams) => {\n  // Remove selection, latency threshold, and latency partial threshold because\n  // selection keeps changing as time moves on, so it's not a good match for bookmarking\n  params.delete(SELECTION);\n  params.delete(`var-${VAR_LATENCY_THRESHOLD}`);\n  params.delete(`var-${VAR_LATENCY_PARTIAL_THRESHOLD}`);\n  return params;\n}\n\nexport const useBookmarksStorage = () => {\n  const storage = usePluginUserStorage();\n  \n  return {\n    getBookmarks: () => getBookmarks(storage),\n    removeBookmark: (bookmark: Bookmark) => removeBookmark(storage, bookmark),\n    bookmarkExists: (bookmark: Bookmark) => bookmarkExists(storage, bookmark),\n    toggleBookmark: () => toggleBookmark(storage),\n  };\n};\n\nexport const getBookmarkParams = (bookmark: Bookmark) => {\n  if (!bookmark || !bookmark.params) {\n    return { actionView: '', primarySignal: '', filters: '', metric: '' };\n  }\n  \n  const params = new URLSearchParams(bookmark.params);\n  const actionView = params.get(ACTION_VIEW) ?? '';\n  const primarySignal = params.get(PRIMARY_SIGNAL) ?? '';\n  const filters = params.getAll(`var-${VAR_FILTERS}`).join(FILTER_SEPARATOR);\n  const metric = params.get(`var-${VAR_METRIC}`) ?? '';\n  return { actionView, primarySignal, filters, metric };\n}\n\nexport const getBookmarkFromURL = (): Bookmark => {\n  const params = cleanupParams(new URLSearchParams(window.location.search));\n  return { params: params.toString() };\n}\n\nexport const getBookmarkForUrl = (bookmark: Bookmark): string => {\n  if (!bookmark || !bookmark.params) {\n    return EXPLORATIONS_ROUTE;\n  }\n  \n  const params = new URLSearchParams(bookmark.params);\n  const urlQueryMap = Object.fromEntries(params.entries());\n  \n  const filters = params.getAll(`var-${VAR_FILTERS}`); \n  \n  const url = urlUtil.renderUrl(EXPLORATIONS_ROUTE, {\n    ...urlQueryMap,\n    [`var-${VAR_FILTERS}`]: filters // Filters need to be added as separate params in the url as there are multiple filters with the same key\n  });\n  \n  return url;\n}\n\nconst setBookmarks = async (storage: PluginStorage, bookmarks: Bookmark[]): Promise<void> => {\n  try {\n    await storage.setItem(BOOKMARKS_LS_KEY, JSON.stringify(bookmarks));\n  } catch (e) {\n    console.error(\"Failed to save bookmarks to storage:\", e);\n  }\n};\n\nexport const getBookmarks = async (storage: PluginStorage): Promise<Bookmark[]> => {\n  try {\n    const value = await storage.getItem(BOOKMARKS_LS_KEY);\n    if (value) {\n      return JSON.parse(value);\n    }\n    return [];\n  } catch (e) {\n    console.error(\"Failed to get bookmarks from storage:\", e);\n    return [];\n  }\n};\n\nexport const toggleBookmark = async (storage: PluginStorage): Promise<boolean> => {\n  const bookmark = getBookmarkFromURL();\n  const exists = await bookmarkExists(storage, bookmark);\n  \n  if (exists) {\n    await removeBookmark(storage, bookmark);\n    return false;\n  } else {\n    await addBookmark(storage, bookmark);\n    return true;\n  }\n};\n\nconst addBookmark = async (storage: PluginStorage, bookmark: Bookmark): Promise<void> => {\n  const bookmarks = await getBookmarks(storage);\n  bookmarks.push(bookmark);\n  await setBookmarks(storage, bookmarks);\n};\n\nexport const removeBookmark = async (storage: PluginStorage, bookmark: Bookmark): Promise<void> => {\n  const storedBookmarks = await getBookmarks(storage);\n  const filteredBookmarks = storedBookmarks.filter((storedBookmark) => !areBookmarksEqual(bookmark, storedBookmark));\n  await setBookmarks(storage, filteredBookmarks);\n};\n\nexport const bookmarkExists = async (storage: PluginStorage, bookmark: Bookmark): Promise<boolean> => {\n  const bookmarks = await getBookmarks(storage);\n  return bookmarks.some((b) => areBookmarksEqual(bookmark, b));\n};\n\nexport const areBookmarksEqual = (bookmark: Bookmark, storedBookmark: Bookmark) => {\n  const bookmarkParams = cleanupParams(new URLSearchParams(bookmark.params));\n  const storedBookmarkParams = cleanupParams(new URLSearchParams(storedBookmark.params));\n\n  const filterKey = `var-${VAR_FILTERS}`;\n  const bookmarkKeys = Array.from(bookmarkParams.keys()).filter(k => k !== filterKey);\n  const storedKeys = Array.from(storedBookmarkParams.keys()).filter(k => k !== filterKey);\n\n  // If they have different number of keys (excluding filters), they can't be equal\n  if (bookmarkKeys.length !== storedKeys.length) {\n    return false;\n  }\n  \n  // Check if every key in bookmarkParams exists in storedBookmarkParams with the same value\n  const allKeysMatch = bookmarkKeys.every(key => \n    storedBookmarkParams.has(key) && bookmarkParams.get(key) === storedBookmarkParams.get(key)\n  );  \n  if (!allKeysMatch) {\n    return false;\n  }\n  \n  // Compare filters (which can have multiple values with the same key)\n  const bookmarkFilters = bookmarkParams.getAll(filterKey);\n  const storedFilters = storedBookmarkParams.getAll(filterKey);  \n  if (bookmarkFilters.length !== storedFilters.length) {\n    return false;\n  }\n  \n  // Check if every filter in bookmarkFilters exists in storedFilters\n  // This handles cases where order might be different\n  return bookmarkFilters.every(filter => storedFilters.includes(filter));\n}\n\nexport const goToBookmark = (bookmark: Bookmark) => {\n  reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.go_to_bookmark_clicked);\n  const url = getBookmarkForUrl(bookmark);\n  locationService.push(url);\n}\n", "import { EVENT_ATTR, FILTER_SEPARATOR, RESOURCE_ATTR, SPAN_ATTR } from \"utils/shared\";\nimport React from \"react\";\nimport { capitalizeFirstChar } from \"utils/utils\";\nimport { css } from \"@emotion/css\";\nimport { useStyles2 } from \"@grafana/ui\";\nimport { Bookmark } from \"./Bookmarks\";\nimport { getBookmarkParams } from \"./utils\";\nimport { getSignalForKey } from \"pages/Explore/primary-signals\";\n\nexport const BookmarkItem = ({ bookmark }: { bookmark: Bookmark }) => {\n  let { actionView, primarySignal, metric, filters } = getBookmarkParams(bookmark);\n  const styles = useStyles2(getStyles);\n\n  const getPrimarySignalFilter = (primarySignal: string): string => {\n    const signalData = getSignalForKey(primarySignal);\n    if (!signalData || !signalData.filter) {\n      return '';\n    }\n    const filter = signalData.filter;\n\n    if (filter.key && filter.operator && filter.value !== undefined) {\n      return `${filter.key}|${filter.operator}|${filter.value}`;\n    }\n    return '';\n  }\n  \n  // Don't render the primary signal filter as the primary signal already represents this information\n  const getFiltersWithoutPrimarySignal = (filters: string, primarySignal: string): string => {\n    const primarySignalFilter = getPrimarySignalFilter(primarySignal);\n    let filtersArray = filters.split(FILTER_SEPARATOR);\n    filtersArray = filtersArray.filter(f => f !== primarySignalFilter);\n    return filtersArray.join(FILTER_SEPARATOR);\n  }\n\n  filters = getFiltersWithoutPrimarySignal(filters, primarySignal);\n  filters = filters.replace(/\\|=\\|/g, ' = ');\n  filters = filters.replace(RESOURCE_ATTR, '').replace(SPAN_ATTR, '').replace(EVENT_ATTR, '');\n\n  return (\n    <div title={filters}>\n      <div>\n        <b>{capitalizeFirstChar(metric)}</b> of <b>{primarySignal.replace('_', ' ')}</b> ({actionView})\n      </div>\n      <div className={styles.filters}>\n        {filters}\n      </div>\n    </div>\n  );\n}\n\nfunction getStyles() {\n  return {\n    filters: css({\n      textOverflow: 'ellipsis', \n      overflow: 'hidden',\n      WebkitLineClamp: 2, \n      display: '-webkit-box', \n      WebkitBoxOrient: 'vertical'\n    }),\n  }\n}\n", "import { css } from \"@emotion/css\";\nimport { GrafanaTheme2 } from \"@grafana/data\";\nimport { Button, useStyles2, LoadingPlaceholder } from \"@grafana/ui\";\nimport React, { useEffect, useState } from \"react\";\nimport { BookmarkItem } from \"./BookmarkItem\";\nimport { useBookmarksStorage, goToBookmark } from \"./utils\";\n\nexport type Bookmark = {\n  params: string;\n}\n\nexport const Bookmarks = () => {\n  const styles = useStyles2(getStyles);\n  const { getBookmarks, removeBookmark } = useBookmarksStorage();\n  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [isRemoving, setIsRemoving] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchBookmarks = async () => {\n      setIsLoading(true);\n      try {\n        const loadedBookmarks = await getBookmarks();\n        setBookmarks(loadedBookmarks);\n      } catch (error) {\n        console.error('Error loading bookmarks:', error);\n        setBookmarks([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    fetchBookmarks();\n  }, []);\n\n  const removeBookmarkClicked = async (bookmark: Bookmark, event: React.MouseEvent) => {\n    event.stopPropagation();\n    setIsRemoving(true);\n    \n    try {\n      await removeBookmark(bookmark);\n      const updatedBookmarks = await getBookmarks();\n      setBookmarks(updatedBookmarks);\n    } catch (error) {\n      console.error('Error removing bookmark:', error);\n    } finally {\n      setIsRemoving(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div>\n        <div className={styles.header}>\n          <h4>Or view bookmarks</h4>\n        </div>\n        <div className={styles.loading}>\n          <LoadingPlaceholder text=\"Loading bookmarks...\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className={styles.header}>\n        <h4>Or view bookmarks</h4>\n      </div>\n      {bookmarks.length === 0 ? (\n        <p className={styles.noBookmarks}>Bookmark your favorite queries to view them here.</p>\n      ) : (\n        <div className={styles.bookmarks}>\n          {bookmarks.map((bookmark: Bookmark, i: number) => (\n            <div \n              className={styles.bookmark} \n              key={i} \n              onClick={() => goToBookmark(bookmark)}\n            >\n              <div className={styles.bookmarkItem}>\n                <BookmarkItem bookmark={bookmark} />\n              </div>\n              <div className={styles.remove}>\n                <Button \n                  variant='secondary' \n                  fill='text' \n                  icon='trash-alt'\n                  disabled={isRemoving}\n                  onClick={(e) => removeBookmarkClicked(bookmark, e)}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    header: css({\n      textAlign: 'center',\n      'h4': {\n        margin: 0,\n      }\n    }),\n    bookmarks: css({\n      display: 'flex',\n      flexWrap: 'wrap',\n      gap: theme.spacing(2),\n      margin: `${theme.spacing(4)} 0 ${theme.spacing(2)} 0`,\n      justifyContent: 'center',\n    }),\n    bookmark: css({\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'space-between',\n      cursor: 'pointer',\n      width: '318px',\n      border: `1px solid ${theme.colors.border.medium}`,\n      borderRadius: theme.shape.radius.default,\n\n      '&:hover': {\n        backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      }\n    }),\n    bookmarkItem: css({\n      padding: `${theme.spacing(1.5)} ${theme.spacing(1.5)} 0 ${theme.spacing(1.5)}`,\n      overflow: 'hidden'\n    }),\n    filters: css({\n      textOverflow: 'ellipsis', \n      overflow: 'hidden',\n      WebkitLineClamp: 2, \n      display: '-webkit-box', \n      WebkitBoxOrient: 'vertical'\n    }),\n    remove: css({\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    noBookmarks: css({\n      margin: `${theme.spacing(4)} 0 ${theme.spacing(2)} 0`,\n      textAlign: 'center',\n    }),\n    loading: css({\n      display: 'flex',\n      justifyContent: 'center',\n      margin: `${theme.spacing(4)} 0`,\n    }),\n  }\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport {\n  SceneComponentProps,\n  SceneObjectBase,\n} from '@grafana/scenes';\nimport { Button, Icon, LinkButton, Stack, useStyles2, useTheme2 } from '@grafana/ui';\n\nimport {\n  EXPLORATIONS_ROUTE,\n} from '../../utils/shared';\nimport { getDatasourceVariable, getHomeFilterVariable, getHomeScene } from '../../utils/utils';\nimport { DarkModeRocket, LightModeRocket } from '../../utils/rockets';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { Home } from 'pages/Home/Home';\nimport { useNavigate } from 'react-router-dom';\nimport { Bookmarks } from 'pages/Home/bookmarks/Bookmarks';\n\nexport class HeaderScene extends SceneObjectBase {\n  public static Component = ({ model }: SceneComponentProps<Home>) => {\n    const home = getHomeScene(model);\n    const navigate = useNavigate();\n    const { controls } = home.useState();\n    const styles = useStyles2(getStyles);\n    const theme = useTheme2();\n\n    const dsVariable = getDatasourceVariable(home);\n    const filterVariable = getHomeFilterVariable(home);\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.headerTitleContainer}>\n            {theme.isDark ? <DarkModeRocket /> : <LightModeRocket />}\n            <h2 className={styles.title}>Start your traces exploration!</h2>\n          </div>\n          <div>\n            <p>Drilldown and visualize your trace data without writing a query.</p>\n            <div className={styles.headerActions}>\n              <Button variant='primary' onClick={() => {\n                  reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.explore_traces_clicked);\n                  navigate(EXPLORATIONS_ROUTE);\n                }}>\n                Let’s start\n                <Icon name='arrow-right' size='lg' />\n              </Button>\n              <LinkButton\n                icon=\"external-link-alt\"\n                fill=\"text\"\n                size={'md'}\n                target={'_blank'}\n                href={\n                  'https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces'\n                }\n                className={styles.documentationLink}\n                onClick={() => reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.read_documentation_clicked)}\n              >\n                Read documentation\n              </LinkButton>\n            </div>\n          </div>\n        </div>\n\n        <Bookmarks />\n\n        <div className={styles.subHeader}>\n          <h4>Or quick-start into your tracing data</h4>\n        </div>\n\n        <Stack gap={2}>\n          <div className={styles.variablesAndControls}>\n            <div className={styles.variables}>\n              {dsVariable && (\n                <Stack gap={1} alignItems={'center'}>\n                  <div className={styles.label}>Data source</div>\n                  <dsVariable.Component model={dsVariable} />\n                </Stack>\n              )}\n              {filterVariable && (\n                <Stack gap={1} alignItems={'center'}>\n                  <div className={styles.label}>Filter</div>\n                  <filterVariable.Component model={filterVariable} />\n                </Stack>\n              )}\n            </div>\n\n            <div className={styles.controls}>\n              {controls?.map((control) => (\n                <control.Component key={control.state.key} model={control} />\n              ))}\n            </div>\n          </div>\n        </Stack>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'flex',\n      gap: theme.spacing(7),\n      flexDirection: 'column',\n      margin: `0 0 ${theme.spacing(4)} 0`,\n      justifyContent: 'center',\n    }),\n    header: css({\n      display: 'flex',\n      alignItems: 'center',\n      backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      borderRadius: theme.spacing(0.5),\n      flexWrap: 'wrap',\n      justifyContent: 'center',\n      padding: theme.spacing(3),\n      gap: theme.spacing(4),\n    }),\n    headerTitleContainer: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    title: css({\n      margin: `0 0 0 ${theme.spacing(2)}`,\n    }),\n\n    headerActions: css({\n      alignItems: 'center',\n      justifyContent: 'flex-start',\n      display: 'flex',\n      gap: theme.spacing(2),\n    }),\n    documentationLink: css({\n      textDecoration: 'underline',\n      '&:hover': {\n        textDecoration: 'underline',\n      },\n    }),\n\n    subHeader: css({\n      textAlign: 'center',\n      'h4': {\n        margin: `0 0 -${theme.spacing(2)} 0`,\n      }\n    }),\n\n    label: css({\n      fontSize: '12px',\n    }),\n    variablesAndControls: css({\n      alignItems: 'center',\n      gap: theme.spacing(2),\n      display: 'flex',\n      justifyContent: 'space-between',\n      width: '100%',\n    }),\n    variables: css({\n      display: 'flex',\n      gap: theme.spacing(2),\n    }),\n    controls: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n", "import { AdHocVariableFilter, MetricFindValue } from \"@grafana/data\";\nimport { getDataSourceSrv, DataSourceWithBackend } from \"@grafana/runtime\";\nimport { AdHocFiltersVariable, sceneGraph } from \"@grafana/scenes\";\nimport { EVENT_ATTR, EVENT_INTRINSIC, FILTER_SEPARATOR, ignoredAttributes, ignoredAttributesHomeFilter, RESOURCE_ATTR, SPAN_ATTR, VAR_DATASOURCE_EXPR } from \"utils/shared\";\nimport { isNumber } from \"utils/utils\";\n\nexport async function getTagKeysProvider(variable: AdHocFiltersVariable): Promise<{replace?: boolean, values: MetricFindValue[]}> {\n  const dsVar = sceneGraph.interpolate(variable, VAR_DATASOURCE_EXPR);\n  const datasource_ = await getDataSourceSrv().get(dsVar);\n  if (!(datasource_ instanceof DataSourceWithBackend)) {\n    console.error(new Error('getTagKeysProvider: invalid datasource!'));\n    throw new Error('getTagKeysProvider: invalid datasource!');\n  }\n  \n  const datasource = datasource_ as DataSourceWithBackend;\n  if (datasource && datasource.getTagKeys) {\n    const tagKeys = await datasource.getTagKeys();\n\n    if (Array.isArray(tagKeys)) {\n      const filteredKeys = filterKeys(tagKeys);\n      return { replace: true, values: filteredKeys };\n    } else {\n      console.error(new Error('getTagKeysProvider: invalid tagKeys!'));\n      return { values: [] };\n    }\n  } else {\n    console.error(new Error('getTagKeysProvider: missing or invalid datasource!'));\n    return { values: [] };\n  }\n}\n\nexport function filterKeys(keys: MetricFindValue[]): MetricFindValue[] {\n  const resourceAttributes = keys.filter((k) => k.text?.includes(RESOURCE_ATTR));\n  const spanAttributes = keys.filter((k) => k.text?.includes(SPAN_ATTR));\n  const otherAttributes = keys.filter((k) => {\n    return !k.text?.includes(RESOURCE_ATTR) && !k.text?.includes(SPAN_ATTR)\n      && !k.text?.includes(EVENT_ATTR) && !k.text?.includes(EVENT_INTRINSIC)\n      && ignoredAttributes.concat(ignoredAttributesHomeFilter).indexOf(k.text!) === -1;\n  })\n  return [...resourceAttributes, ...spanAttributes, ...otherAttributes];\n}\n\nexport function renderTraceQLLabelFilters(filters: AdHocVariableFilter[]) {\n  const expr = filters\n    .filter((f) => f.key && f.operator && f.value)\n    .map((filter) => renderFilter(filter))\n    .join(FILTER_SEPARATOR);\n  return expr.length ? `&& ${expr}` : '';\n}\n\nconst renderFilter = (filter: AdHocVariableFilter) => {\n  if (!filter) {\n    return '';\n  } \n  \n  let val = filter.value;\n  if (val === undefined || val === null || val === '') {\n    return '';\n  }\n\n  if (!isNumber.test(val) && !['kind'].includes(filter.key)) {\n    if (typeof val === 'string' && !val.startsWith('\"') && !val.endsWith('\"')) {\n      val = `\"${val}\"`;\n    }\n  }\n\n  return `${filter.key}${filter.operator}${val}`;\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n// eslint-disable-next-line no-restricted-imports\nimport { duration } from 'moment';\n\nimport { AdHocVariableFilter, GrafanaTheme2 } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  DataSourceVariable,\n  SceneComponentProps,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneRefreshPicker,\n  SceneTimePicker,\n  SceneTimeRange,\n  SceneTimeRangeLike,\n  SceneVariableSet,\n} from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport {\n  DATASOURCE_LS_KEY,\n  explorationDS,\n  HOMEPAGE_FILTERS_LS_KEY,\n  VAR_DATASOURCE,\n  VAR_HOME_FILTER,\n} from '../../utils/shared';\nimport { AttributePanel } from 'components/Home/AttributePanel';\nimport { HeaderScene } from 'components/Home/HeaderScene';\nimport { getDatasourceVariable, getHomeFilterVariable } from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\nimport { getTagKeysProvider, renderTraceQLLabelFilters } from './utils';\n\nexport interface HomeState extends SceneObjectState {\n  controls?: SceneObject[];\n  initialDS?: string;\n  initialFilters: AdHocVariableFilter[];\n  body?: SceneCSSGridLayout;\n}\n\nexport class Home extends SceneObjectBase<HomeState> {\n  public constructor(state: HomeState) {\n    super({\n      $timeRange: state.$timeRange ?? new SceneTimeRange({}),\n      $variables: state.$variables ?? getVariableSet(state.initialFilters, state.initialDS),\n      controls: state.controls ?? [new SceneTimePicker({}), new SceneRefreshPicker({})],\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    const filterVariable = getHomeFilterVariable(this);\n    filterVariable.setState({\n      getTagKeysProvider: getTagKeysProvider,\n    });\n\n    getDatasourceVariable(this).subscribeToState((newState) => {\n      if (newState.value) {\n        localStorage.setItem(DATASOURCE_LS_KEY, newState.value.toString());\n      }\n    });\n\n    getHomeFilterVariable(this).subscribeToState((newState, prevState) => {\n      if (newState.filters !== prevState.filters) {\n        this.buildPanels(sceneTimeRange, newState.filters);\n\n        // save the filters to local storage\n        localStorage.setItem(HOMEPAGE_FILTERS_LS_KEY, JSON.stringify(newState.filters));\n\n        const newFilters = newState.filters.filter((f) => !prevState.filters.find((pf) => pf.key === f.key));\n        if (newFilters.length > 0) {\n          reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.filter_changed, {\n            key: newFilters[0].key,\n          });\n        }\n      }\n    });\n\n    sceneTimeRange.subscribeToState((newState, prevState) => {\n      if (newState.value.from !== prevState.value.from || newState.value.to !== prevState.value.to) {\n        this.buildPanels(sceneTimeRange, filterVariable.state.filters);\n      }\n    });\n    this.buildPanels(sceneTimeRange, filterVariable.state.filters);\n  }\n\n  buildPanels(sceneTimeRange: SceneTimeRangeLike, filters: AdHocVariableFilter[]) {\n    const from = sceneTimeRange.state.value.from.unix();\n    const to = sceneTimeRange.state.value.to.unix();\n    const dur = duration(to - from, 's');\n    const durString = `${dur.asSeconds()}s`;\n    const renderedFilters = renderTraceQLLabelFilters(filters);\n\n    this.setState({\n      body: new SceneCSSGridLayout({\n        children: [\n          new SceneCSSGridLayout({\n            autoRows: 'min-content',\n            columnGap: 2,\n            rowGap: 2,\n            children: [\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent < 0 && status = error ${renderedFilters}} | count_over_time() by (resource.service.name)`,\n                    step: durString,\n                  },\n                  title: 'Errored services',\n                  type: 'errored-services',\n                }),\n              }),\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent < 0 ${renderedFilters}} | quantile_over_time(duration, 0.9) by (resource.service.name)`,\n                    step: durString,\n                  },\n                  title: 'Slow services',\n                  type: 'slowest-services',\n                }),\n              }),\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent<0 ${renderedFilters}} | histogram_over_time(duration)`,\n                  },\n                  title: 'Slow traces',\n                  type: 'slowest-traces',\n                  filter: renderedFilters,\n                }),\n              }),\n            ],\n          }),\n        ],\n      }),\n    });\n  }\n\n  static Component = ({ model }: SceneComponentProps<Home>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <HeaderScene.Component model={model} />\n        {body && <body.Component model={body} />}\n      </div>\n    );\n  };\n}\n\nfunction getVariableSet(initialFilters: AdHocVariableFilter[], initialDS?: string) {\n  return new SceneVariableSet({\n    variables: [\n      new DataSourceVariable({\n        name: VAR_DATASOURCE,\n        label: 'Data source',\n        value: initialDS,\n        pluginId: 'tempo',\n      }),\n      new AdHocFiltersVariable({\n        name: VAR_HOME_FILTER,\n        datasource: explorationDS,\n        layout: 'combobox',\n        filters: initialFilters,\n        allowCustomValue: true,\n      }),\n    ],\n  });\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      margin: `${theme.spacing(4)} auto`,\n      width: '75%',\n\n      '@media (max-width: 900px)': {\n        width: '95%',\n      },\n    }),\n  };\n}\n", "import React, { useEffect, useState } from 'react';\nimport z from 'zod';\n\nimport { newTracesExploration } from '../../utils/utils';\nimport { TraceExploration } from './TraceExploration';\nimport { DATASOURCE_LS_KEY } from '../../utils/shared';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../utils/analytics';\nimport { UrlSyncContextProvider } from '@grafana/scenes';\nimport { AdHocVariableFilter } from '@grafana/data';\n\nimport {\n  // @ts-ignore new API that is not yet in stable release\n  useSidecar_EXPERIMENTAL,\n} from '@grafana/runtime';\n\nconst TraceExplorationPage = () => {\n  // We are calling this conditionally, but it will depend on grafana version and should not change in runtime so we\n  // can ignore the hook rule here\n  const sidecarContext = useSidecar_EXPERIMENTAL?.() ?? {};\n\n  const initialDs = localStorage.getItem(DATASOURCE_LS_KEY) || '';\n  const [exploration] = useState(newTracesExploration(initialDs, getInitialFilters(sidecarContext.initialContext)));\n\n  return <TraceExplorationView exploration={exploration} />;\n};\n\nexport default TraceExplorationPage;\n\nexport function TraceExplorationView({ exploration }: { exploration: TraceExploration }) {\n  const [isInitialized, setIsInitialized] = React.useState(false);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      setIsInitialized(true);\n\n      reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.app_initialized);\n    }\n  }, [exploration, isInitialized]);\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return (\n    <UrlSyncContextProvider scene={exploration} updateUrlOnInit={true} createBrowserHistorySteps={true}>\n      <exploration.Component model={exploration} />\n    </UrlSyncContextProvider>\n  );\n}\n\nconst AdHocVariableFilterSchema = z.object({\n  key: z.string(),\n  operator: z.string(),\n  value: z.string(),\n});\n\nconst InitialFiltersSchema = z.object({\n  filters: z.array(AdHocVariableFilterSchema),\n});\n\n/** Because the context comes from a different app plugin we cannot really count on it being the correct type even if\n * it was typed, so it is safer to do runtime parsing here. It also can come from different app extensions and at this\n * point we don't know which, but we also have implemented only one so far it's a fair guess.\n *\n * At this point there is no smartness. What ever we got from the other app we use as is. Ideally there should be some\n * normalization of the filters or smart guesses when there are differences.\n * @param context\n */\nfunction getInitialFilters(context: unknown): AdHocVariableFilter[] | undefined {\n  const result = InitialFiltersSchema.safeParse(context);\n  if (!result.success) {\n    return undefined;\n  }\n\n  return result.data.filters;\n}\n"], "names": ["map", "webpackContext", "req", "id", "webpackContextResolve", "__webpack_require__", "o", "e", "Error", "code", "keys", "Object", "resolve", "module", "exports", "GrotNotFound", "width", "height", "show404", "theme", "useTheme2", "x", "y", "throttleInterval", "mousePosition", "setMousePosition", "useState", "useEffect", "updateMousePosition", "throttle", "event", "clientX", "clientY", "window", "addEventListener", "removeEventListener", "useMousePosition", "styles", "useStyles2", "getStyles", "SVG", "src", "isDark", "className", "svg", "displayName", "xPos", "yPos", "innerWidth", "innerHeight", "heightRatio", "widthRatio", "rotation", "getIntermediateValue", "translation", "css", "transform", "transform<PERSON><PERSON>in", "transition", "display", "ratio", "start", "end", "EmptyState", "message", "remedyMessage", "imgWidth", "padding", "div", "container", "data-testid", "<PERSON><PERSON>", "direction", "alignItems", "gap", "Text", "textAlignment", "variant", "remedy", "Icon", "name", "justifyContent", "flexDirection", "marginBottom", "spacing", "EmptyStateScene", "SceneObjectBase", "model", "Component", "LoadingStateScene", "component", "SkeletonTheme", "baseColor", "colors", "emphasize", "background", "secondary", "highlightColor", "borderRadius", "shape", "radius", "default", "fadeIn", "keyframes", "opacity", "label", "animationName", "animationDelay", "animationTimingFunction", "animationDuration", "animationFillMode", "ErrorStateScene", "<PERSON><PERSON>", "title", "severity", "Search", "props", "searchQuery", "onSearchQueryChange", "Field", "searchField", "Input", "placeholder", "prefix", "value", "onChange", "By<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderFilteredData", "filtered", "series", "length", "this", "performRepeat", "state", "body", "setState", "children", "SceneFlexItem", "groupSeriesBy", "data", "groupBy", "groupedData", "reduce", "acc", "key", "fields", "find", "f", "type", "FieldType", "number", "labels", "push", "newSeries", "frames", "sort", "a", "b", "localeCompare", "mainFrame", "frame", "field", "values", "slice", "for<PERSON>ach", "sortDataFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getGroupByVariable", "getValueText", "frameIndex", "filter", "sum", "vSum", "v", "<PERSON><PERSON><PERSON><PERSON>", "getLayoutChild", "super", "evt", "currentTarget", "onSearchQueryChangeDebounced", "debounce", "sceneGraph", "getData", "doesQueryMatchDataFrameLabels", "addActivationHandler", "_subs", "add", "subscribeToState", "LoadingState", "Done", "Streaming", "EMPTY_STATE_ERROR_MESSAGE", "EMPTY_STATE_ERROR_REMEDY_MESSAGE", "publishEvent", "EventTimeseriesDataReceived", "SceneCSSGridLayout", "errors", "SkeletonComponent", "newState", "prevState", "flexGrow", "repeat", "getSkeletonStyles", "Array", "_", "i", "itemContainer", "header", "Skeleton", "count", "action", "yAxis", "yAxisItem", "xAxis", "xAxisItem", "gridTemplateColumns", "GRID_TEMPLATE_COLUMNS", "gridAutoRows", "rowGap", "columnGap", "backgroundColor", "primary", "border", "marginTop", "dataFrame", "pattern", "trim", "regex", "RegExp", "some", "test", "barsPanelConfig", "PanelBuilders", "timeseries", "setOption", "showLegend", "setCustomFieldConfig", "DrawStyle", "Bars", "mode", "StackingMode", "Normal", "setOverrides", "overrides", "matchFieldsWithNameByRegex", "overrideColor", "fixedColor", "TooltipDisplayMode", "Multi", "generateMetricsQuery", "metric", "groupByKey", "extraFilters", "groupByStatus", "filters", "VAR_FILTERS_EXPR", "ALL", "metricFn", "groupByAttrs", "join", "metricByWithStatus", "<PERSON><PERSON><PERSON>", "refId", "query", "queryType", "tableType", "limit", "spss", "UNIT_STEPS", "Math", "log10", "unit", "microseconds", "ofPrevious", "formatDuration", "duration", "primaryUnit", "secondaryUnit", "_dropWhile", "index", "_round", "primaryUnitString", "floor", "secondaryValue", "round", "secondaryUnitString", "getStepForTimeRange", "scene", "dataPoints", "sceneTimeRange", "getTimeRange", "from", "unix", "to", "dur", "asSeconds", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onActivateStep", "step", "maxDataPoints", "queries", "newStep", "constructor", "bind", "StreamingIndicator", "isStreaming", "iconSize", "<PERSON><PERSON><PERSON>", "content", "size", "streamingIndicator", "success", "text", "fill", "reportAppInteraction", "page", "properties", "reportInteraction", "pluginJson", "replace", "createInteractionName", "USER_EVENTS_PAGES", "analyse_traces", "home", "common", "USER_EVENTS_ACTIONS", "action_view_changed", "breakdown_group_by_changed", "breakdown_add_to_filters_clicked", "comparison_add_to_filters_clicked", "select_attribute_in_comparison_clicked", "layout_type_changed", "start_investigation", "stop_investigation", "open_trace", "open_in_explore_clicked", "add_to_investigation_clicked", "add_to_investigation_trace_view_clicked", "span_list_columns_changed", "toggle_bookmark_clicked", "homepage_initialized", "panel_row_clicked", "explore_traces_clicked", "read_documentation_clicked", "filter_changed", "go_to_bookmark_clicked", "metric_changed", "new_filter_added_manually", "app_initialized", "global_docs_link_clicked", "metric_docs_link_clicked", "feedback_link_clicked", "histogramPanelConfig", "heatmap", "show", "axisLabel", "scheme", "steps", "yBucketToDuration", "yValue", "buckets", "multiplier", "rawValue", "isNaN", "toFixed", "primarySignalOptions", "operator", "description", "AddToFiltersAction", "onClick", "variable", "getFiltersVariable", "labelKey", "labelName", "getLabelValue", "addToFilters", "filterExistsForKey", "<PERSON><PERSON>", "icon", "filtersWithoutNew", "history", "pushState", "computeHighestDifference", "baselineField", "selection<PERSON><PERSON>", "maxDifference", "maxDifferenceIndex", "diff", "abs", "getDefaultSelectionForMetric", "HighestDifferencePanel", "_onActivate", "getAttribute", "getValue", "valueField", "onAddToFilters", "attribute", "differenceContainer", "fontSize", "differenceValue", "fontWeight", "textAlign", "color", "textWrap", "whiteSpace", "overflow", "textOverflow", "panel", "filterExists", "undefined", "BaselineColor", "SelectionColor", "getFrameName", "df", "panels", "getTitle", "actionsFn", "existingGridItem", "dataNode", "SceneDataNode", "$data", "getPanelConfig", "setTitle", "setData", "actions", "setHeaderActions", "gridItem", "SceneCSSGridItem", "build", "barchart", "setMax", "matchFieldsWithName", "overrideCustomFieldConfig", "AxisPlacement", "Hidden", "overrideUnit", "buildHistogramQuery", "DurationComparisonControl", "selection", "startInvestigation", "byServiceScene", "getTraceByServiceScene", "shouldShowSelection", "actionView", "setActionView", "getMetricVariable", "wrapper", "typography", "bodySmall", "x0_5", "isDisabled", "tooltip", "disabled", "exemplarsTransformations", "openTrace", "topic", "DataTopic", "Annotations", "source", "pipe", "traceIDField", "config", "links", "url", "stopPropagation", "parentAnchorHref", "target", "parentElement", "href", "indexOf", "traceId", "split", "removeExemplarsTransformation", "REDPanel", "isDuration", "SceneDataTransformer", "datasource", "explorationDS", "transformations", "getOpenTrace", "getVizPanel", "yBuckets", "parent", "setHoverHeader", "extendPanelContext", "vizPanel", "context", "onSelectRange", "args", "rawSelection", "newSelection", "raw", "timeRange", "yFrom", "yTo", "onUserUpdateSelection", "SceneFlexLayout", "getHistogramVizPanel", "getRateOrErrorVizPanel", "setDisplayMode", "setColor", "buildSelectionAnnotation", "xSel", "ySel", "arrayToDataFrame", "time", "xMin", "xMax", "timeEnd", "yMin", "yMax", "isRegion", "fillOpacity", "lineWidth", "lineStyle", "newData", "fieldHasEmptyValues", "getYBuckets", "annotations", "minDuration", "minBucket", "getMinimumsForDuration", "lookupVariable", "VAR_LATENCY_THRESHOLD", "CustomVariable", "getLatencyThresholdVariable", "changeValueTo", "VAR_LATENCY_PARTIAL_THRESHOLD", "getLatencyPartialThresholdVariable", "Loading", "isEqual", "subtitle", "headerContainer", "<PERSON><PERSON><PERSON><PERSON>", "titleRadioWrapper", "RadioButtonList", "options", "span", "s", "parseFloat", "slowestBuckets", "weak", "borderColor", "fontWeightBold", "margin", "ShareExplorationAction", "exploration", "origin", "useLocation", "setTooltip", "<PERSON><PERSON>barButton", "navigator", "clipboard", "writeText", "sceneUtils", "getUrlState", "url<PERSON><PERSON>", "renderUrl", "EXPLORATIONS_ROUTE", "getUrlForExploration", "setTimeout", "RECOMMENDED_ATTRIBUTES", "labelOrder", "SpanListColumnsSelector", "opt", "useMemo", "curr", "includes", "group", "startsWith", "Select", "toString", "is<PERSON><PERSON><PERSON>", "isClearable", "virtualized", "min<PERSON><PERSON><PERSON>", "SpanListScene", "setupTransformations", "nameField", "TableCellDisplayMode", "Custom", "cellComponent", "traceIdField", "spanIdField", "rowIndex", "spanId", "EventTraceOpened", "Link", "getLinkToExplore", "custom", "cellOptions", "updatePanel", "NotStarted", "dataState", "table", "builder", "getDataSource", "getTraceExplorationScene", "exploreState", "JSON", "stringify", "range", "toURLRange", "panelsState", "trace", "subUrl", "appSubUrl", "panes", "schemaVersion", "columns", "getSpanListColumnsVariable", "sceneData", "attributes", "toOption", "link", "cursor", "max<PERSON><PERSON><PERSON>", "textDecoration", "h6", "row", "j", "rowItem", "position", "SpansScene", "updateBody", "nestedSetLeft", "parseInt", "intValue", "Value", "int_value", "nestedSetRight", "TreeNode", "addSpan", "left", "min", "right", "max", "spans", "<PERSON><PERSON><PERSON><PERSON>", "node", "<PERSON><PERSON><PERSON><PERSON>", "findMatchingChild", "nodeName", "child", "serviceName", "operationName", "traceID", "createNode", "serviceNameAttr", "stringValue", "string_value", "svcName", "resetLeftRight", "t", "Number", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "c", "ROOT_SPAN_ID", "StructureTabScene", "tree", "traces", "spanSets", "traceStartTime", "startTimeUnixNano", "ss", "s1", "s2", "curNode", "newNode", "mergeTraces", "parse", "countSpans", "loading", "wrap", "getPanels", "minHeight", "getPanel", "buildData", "getTrace", "traceName", "createDataFrame", "other", "references", "string", "spanID", "parentSpanId", "startTime", "statusCode", "erroredSpans", "refType", "durationNanos", "buildQuery", "filterStreamingProgressTransformations", "metricQuery", "<PERSON><PERSON><PERSON><PERSON>", "VAR_LATENCY_PARTIAL_THRESHOLD_EXPR", "VAR_LATENCY_THRESHOLD_EXPR", "isLoading", "emptyMsg", "tabName", "structureDisplayName", "noDataMessage", "longText", "actionContainer", "LinkButton", "toLowerCase", "traceViewList", "x1", "marginLeft", "GroupBySelector", "radioAttributes", "showAll", "select<PERSON><PERSON><PERSON>", "setSelectQuery", "availableWidth", "setAvailableWidth", "controlsContainer", "useRef", "metricValue", "useResizeObserver", "ref", "onResize", "element", "current", "clientWidth", "radioOptions", "radioOptionsWidth", "op", "checks", "SPAN_ATTR", "RESOURCE_ATTR", "option", "textWidth", "measureText", "otherAttrOptions", "ops", "ro", "filteredOptions", "getModifiedSelectOptions", "ignoredAttributes", "defaultValue", "showAllOption", "defaultOnChangeValue", "RadioButtonGroup", "selected", "newSelected", "select", "onInputChange", "onCloseMenu", "maxOptions", "queryLowerCase", "tag", "LayoutSwitcher", "Selector", "active", "onLayoutChange", "layout", "layouts", "findIndex", "linesPanelConfig", "AddToInvestigationButton", "getQueries", "getContext", "queryRunner", "findObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "q", "dsUid", "labelValue", "ctx", "uid", "location", "logoPath", "ADD_TO_INVESTIGATION_MENU_TEXT", "extensionPointId", "ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT", "ADD_TO_INVESTIGATION_MENU_GROUP_TEXT", "PanelMenu", "addItem", "item", "setItems", "items", "iconClassName", "getExploreHref", "onExploreClick", "VizPanelMenu", "addToInvestigationButton", "activate", "menu", "subscribeToAddToInvestigation", "targetQuery", "request", "targets", "getCurrentStep", "getInvestigationLink", "addToInvestigations", "getPluginLinkExtensions", "extensions", "getObservablePluginLinks", "firstValueFrom", "existingMenuItems", "existingAddToInvestigationLink", "buildNormalLayout", "$behaviors", "maxima", "Map", "eventSub", "subscribeToEvent", "payload", "set", "findAllObjects", "VizPanel", "clearFieldConfigCache", "fieldConfig", "merge", "cloneDeep", "defaults", "updateTimeseriesAxis", "unsubscribe", "reduceField", "reducers", "ReducerID", "calcs", "setUnit", "templateColumns", "autoRows", "isLazy", "status", "interpolate", "formatLabelValue", "setMenu", "AttributesDescription", "tags", "infoFlex", "tagsFlex", "style", "AttributesBreakdownScene", "setBody", "onReferencedVariableValueChanged", "radioAttributesResource", "onAddToFiltersClick", "_variableDependency", "VariableDependencyConfig", "variableNames", "VAR_FILTERS", "VAR_METRIC", "ignore", "paddingTop", "controls", "controlsRight", "scope", "marginRight", "controlsLeft", "justifyItems", "defaultScope", "radioAttributesSpan", "SPAN", "RESOURCE", "setScope", "filterType", "filteredAttributes", "attr", "concat", "getDescription", "getAttributesAsOptions", "BreakdownScene", "comparisonQuery", "selector", "fromTimerange", "toTimerange", "buildAttributeComparison", "attributeSeries", "d", "splitFrames", "get<PERSON><PERSON><PERSON>", "InspectAttributeAction", "AttributesComparisonScene", "updateData", "getPrimarySignalVariable", "primarySignal", "getFilteredAttributes", "grouped<PERSON>rames", "groupFrameListByAttribute", "entries", "frameGroupToDataframe", "aCompare", "b<PERSON>om<PERSON>e", "VAR_PRIMARY_SIGNAL", "traceExploration", "hasAllValue", "getMetricFunction", "compare<PERSON><PERSON>y", "durString", "getTheme", "visualization", "getColorByName", "numberField", "nonInternalKey", "newFrame", "valueNameField", "val", "baselineTotal", "getTotalForMetaType", "selectionTotal", "metaType", "calculatedTotal", "total", "currentValue", "ComparisonScene", "tracesByService", "actionViewsDefinitions", "getScene", "TabsBarScene", "breakpoints", "up", "md", "top", "zIndex", "metricScene", "tracesCount", "Box", "TabsBar", "tab", "Tab", "onChangeTab", "counter", "MiniREDPanel", "getDurationVizPanel", "getRateOrErrorPanel", "maxHeight", "MINI_PANEL_HEIGHT", "flex", "headerWrapper", "clickable", "input", "radioButton", "indicatorWrapper", "selectMetric", "onChangeMetricFunction", "TracesByServiceScene", "urlActionView", "URLSearchParams", "search", "get", "metricVariable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectionFrom", "getDatasourceVariable", "updateAttributes", "actionViewDef", "buildGraphScene", "ds", "getDataSourceSrv", "VAR_DATASOURCE_EXPR", "__sceneObject", "getTagKeys", "then", "tagKeys", "l", "updateFromUrl", "_urlSync", "performBrowserHistoryAction", "oldAction", "newAction", "$timeRange", "timeRangeFromSelection", "spanListTransformations", "SceneObjectUrlSyncConfig", "MetricTypeTooltip", "placement", "interactive", "hand", "button", "paddingBottom", "MAIN_PANEL_HEIGHT", "typeQuery", "SceneTimeRange", "dateTime", "secondaryPanel", "tertiaryPanel", "behaviors", "sync", "DashboardCursorSync", "<PERSON><PERSON><PERSON>", "ySizing", "desc", "indexByName", "Duration", "TraceViewPanelScene", "panelContainer", "service1", "bar1", "service2", "bar2", "service3", "bar3", "service4", "bar4", "service5", "bar5", "service6", "bar6", "TraceDrawerScene", "PrimarySignalVariable", "renderTraceQLLabelFilters", "expr", "isNumber", "endsWith", "renderFilter", "useMount", "TraceQLIssueDetector", "runIssueDetectionQuery", "datasourceVar", "resetIssues", "now", "subtract", "minimalTimeRange", "toISOString", "issueDetector", "String", "hasIssue", "TraceQLConfigWarning", "detector", "p", "compositeVersion", "process", "TraceExploration", "topScene", "setupInvestigationButton", "VAR_DATASOURCE", "localStorage", "setItem", "DATASOURCE_LS_KEY", "isActive", "stateUpdate", "closeDrawer", "updateInvestigationLink", "serviceNameField", "investigationLink", "initialDS", "initialFilters", "$variables", "SceneVariableSet", "variables", "DataSourceVariable", "pluginId", "AdHocFiltersVariable", "addFilterButtonText", "hide", "VariableHide", "<PERSON><PERSON><PERSON><PERSON>", "allowCustomValue", "expressionBuilder", "hideVariable", "VAR_GROUPBY", "defaultToAll", "VAR_SPAN_LIST_COLUMNS", "SceneTimePicker", "SceneRefreshPicker", "TraceExplorationScene", "drawerScene", "bodyContainer", "<PERSON><PERSON><PERSON>er", "borderBottom", "drawerHeaderButtons", "canvas", "datasourceLabel", "fontWeightMedium", "flexWrap", "menuHeader", "menuHeaderSubtitle", "lineHeight", "helpIcon", "menuVisible", "setMenuVisible", "React", "dsVariable", "filtersVariable", "primarySignalVariable", "VersionHeader", "h5", "<PERSON><PERSON>", "feedbackLinksEnabled", "<PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "Dropdown", "overlay", "onVisibleChange", "control", "Drawer", "onClose", "h3", "IconButton", "AttributePanelRow", "labelTitle", "valueTitle", "<PERSON><PERSON><PERSON><PERSON>", "locationService", "actionText", "actionIcon", "ErroredServicesRows", "valuesField", "getUrl", "params", "getTotalErrs", "SlowestTracesRows", "<PERSON>r<PERSON><PERSON>", "sortedByDuration", "sortedFields", "traceServiceField", "traceNameField", "console", "error", "ROUTES", "Explore", "getDuration", "durationField", "SlowestServicesRows", "AttributePanelRows", "AttributePanelScene", "getIcon", "medium", "borderTopLeftRadius", "borderTopRightRadius", "titleText", "AttributePanel", "exemplars", "renderDurationPanel", "getErrorMessage", "tracesContainer", "rowLeft", "rowRight", "LightModeRocket", "xmlns", "viewBox", "path", "DarkModeRocket", "cleanupParams", "delete", "SELECTION", "useBookmarksStorage", "storage", "usePluginUserStorage", "getBookmarks", "removeBookmark", "bookmark", "bookmarkExists", "toggleBookmark", "getBookmarkForUrl", "urlQueryMap", "fromEntries", "getAll", "setBookmarks", "bookmarks", "BOOKMARKS_LS_KEY", "getItem", "addBookmark", "filteredBookmarks", "storedBookmark", "areBookmarksEqual", "bookmarkParams", "storedBookmarkParams", "<PERSON><PERSON><PERSON>", "bookmarkKeys", "k", "storedKeys", "allKeysMatch", "every", "has", "bookmarkFilters", "storedFilters", "BookmarkItem", "ACTION_VIEW", "PRIMARY_SIGNAL", "FILTER_SEPARATOR", "getBookmarkParams", "primarySignalFilter", "signalData", "getSignalForKey", "getPrimarySignalFilter", "filtersArray", "getFiltersWithoutPrimarySignal", "EVENT_ATTR", "capitalizeFirstChar", "WebkitLineClamp", "WebkitBoxOrient", "Bookmarks", "setIsLoading", "isRemoving", "setIsRemoving", "fetchBookmarks", "loadedBookmarks", "removeBookmarkClicked", "updatedBookmarks", "h4", "LoadingPlaceholder", "noBookmarks", "goToBookmark", "bookmarkItem", "remove", "HeaderScene", "headerTitleContainer", "headerActions", "documentationLink", "subHeader", "variablesAndControls", "getTagKeysProvider", "dsVar", "datasource_", "DataSourceWithBackend", "isArray", "EVENT_INTRINSIC", "ignoredAttributesHomeFilter", "getAncestor", "Home", "getHomeScene", "navigate", "useNavigate", "filterVariable", "getHomeFilterVariable", "h2", "buildPanels", "HOMEPAGE_FILTERS_LS_KEY", "newFilters", "pf", "renderedFilters", "VAR_HOME_FILTER", "str", "toUpperCase", "useSidecar_EXPERIMENTAL", "sidecarContext", "initialDs", "result", "InitialFiltersSchema", "safeParse", "getInitialFilters", "initialContext", "TraceExplorationView", "isInitialized", "setIsInitialized", "UrlSyncContextProvider", "updateUrlOnInit", "createBrowserHistorySteps", "AdHocVariableFilterSchema", "z"], "sourceRoot": ""}