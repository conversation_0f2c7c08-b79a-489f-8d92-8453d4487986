"use strict";(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[142],{3142:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var l=a(5959),n=a.n(l),r=a(7781),o=a(1159),c=a(1454);const u=(0,l.lazy)((()=>Promise.all([a.e(746),a.e(389)]).then(a.bind(a,9677)))),p=()=>n().createElement(o.Routes,null,n().createElement(o.Route,{path:c.bw.Explore,element:n().createElement(u,null)}),n().createElement(o.Route,{path:"/",element:n().createElement(o.Navigate,{replace:!0,to:c.bw.Explore})}));var s=a(8531);const m=n().createContext(null);class E extends n().PureComponent{render(){return n().createElement(m.Provider,{value:this.props},n().createElement(s.PluginPage,{layout:r.PageLayoutType.Custom},n().createElement(p,null)))}}const h=E}}]);
//# sourceMappingURL=142.js.map