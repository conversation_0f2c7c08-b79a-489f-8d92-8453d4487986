{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "ctfd_active_users", "refId": "A"}], "title": "Active Users", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "rate(ctfd_requests_total[5m]) * 60", "legendFormat": "Requests per minute", "refId": "A"}], "title": "Request Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(ctfd_failed_logins)", "refId": "A"}], "title": "Failed <PERSON><PERSON>", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 8, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "tooltip": {"mode": "single"}}, "targets": [{"expr": "topk(10, ctfd_requests_by_endpoint)", "legendFormat": "{{endpoint}}", "refId": "A"}], "title": "Top Endpoints", "type": "piechart"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 10, "options": {"showLabels": false, "showCommonLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false, "showAbsoluteTime": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "maxLines": 200}, "targets": [{"expr": "{job=\"nginx\"} |= \"limiting requests\"", "refId": "A"}], "title": "Rate Limit Violations", "type": "logs"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineWidth": 1}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 12, "options": {"barWidth": 0.97, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "targets": [{"expr": "ctfd_response_time_seconds * 1000", "legendFormat": "Response Time", "refId": "A"}], "title": "Average Response Time", "type": "barchart"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 14, "options": {"showLabels": false, "showCommonLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false, "showAbsoluteTime": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "maxLines": 200}, "targets": [{"expr": "{job=\"ctfd\"} |= \"failed_login\"", "refId": "A"}], "title": "Failed Login Attempts", "type": "logs"}], "schemaVersion": 27, "style": "dark", "tags": ["ctfd", "security"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "CTFd Platform Metrics Dashboard", "uid": "ctfd-security", "version": 0}