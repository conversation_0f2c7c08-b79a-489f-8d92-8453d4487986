"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[513,601],{9617:(e,t,r)=>{r.r(t),r.d(t,{MetricsContext:()=>O,default:()=>S});var n=r(6089),a=r(8531),i=r(2007),o=r(5959),s=r.n(o),l=r(6530),c=r(4169),u=r(1159),d=r(6503);function p({error:e}){const t=(0,i.useStyles2)(h),r=(0,u.useNavigate)(),{pathname:n,search:a}=(0,u.useLocation)(),l=(0,o.useCallback)((()=>{const e=new URLSearchParams(a),t=new URLSearchParams;["from","to","timezone"].filter((t=>e.has(t))).forEach((r=>t.set(r,e.get(r)))),r({pathname:n,search:t.toString()}),window.location.reload()}),[r,n,a]);return s().createElement("div",{className:t.container},s().createElement(d._,{severity:"error",title:"Fatal error!",message:s().createElement(s().Fragment,null,"Please"," ",s().createElement(i.TextLink,{href:"#",onClick:l},"try reloading the page")," ","or, if the problem persists, contact your organization admin. Sorry for the inconvenience."),error:e,errorContext:{handheldBy:"React error boundary"}}))}function h(e){return{container:(0,n.css)({margin:e.spacing(2)})}}var m=r(4137);const f=(0,o.lazy)((()=>r.e(78).then(r.bind(r,9078)))),g=()=>{const e=(0,u.useLocation)();return s().createElement(u.Navigate,{to:`${m.bw.Drilldown}${e.search}`,replace:!0})},b=()=>{const{trail:e}=(0,o.useContext)(O);return s().createElement(u.Routes,null,s().createElement(u.Route,{path:m.bw.Drilldown,element:s().createElement(f,{trail:e})}),s().createElement(u.Route,{path:m.bw.Trail,element:s().createElement(g,null)}),s().createElement(u.Route,{path:"*",element:s().createElement(u.Navigate,{to:m.bw.Drilldown,replace:!0})}))};function y(e,t){return e instanceof Error?e:"string"==typeof e?new Error(e):"string"==typeof e.message?new Error(e.message):new Error(t)}const v=(0,o.createContext)(null);var w=r(5521);(0,l.Js)();const O=(0,o.createContext)({trail:(0,c.ef)(void 0),goToUrlForTrail:()=>{}});const S=function(e){const[t]=function(){const[e,t]=(0,o.useState)();return(0,o.useEffect)((()=>{const e=e=>{t(y(e.error,"Uncaught exception!"))},r=e=>{"cancelled"!==e.reason.type?t(y(e.reason,"Unhandled rejection!")):t(void 0)};return window.addEventListener("error",e),window.addEventListener("unhandledrejection",r),()=>{window.removeEventListener("unhandledrejection",r),window.removeEventListener("error",e)}}),[]),[e,t]}(),[r,n]=(0,o.useState)((0,c.ef)(void 0)),l=(0,i.useStyles2)(k),u=e=>{a.locationService.push((0,c.xi)(e)),n(e)};return(0,o.useEffect)((()=>{const e=w.C.subscribe((e=>{u(e)}));return()=>e()}),[]),t?s().createElement("div",{className:l.appContainer,"data-testid":"metrics-drilldown-app"},s().createElement(p,{error:t})):s().createElement("div",{className:l.appContainer,"data-testid":"metrics-drilldown-app"},s().createElement(v.Provider,{value:e},s().createElement(O.Provider,{value:{trail:r,goToUrlForTrail:u}},s().createElement(b,null))))};function k(e){return{appContainer:(0,n.css)({display:"flex",flexDirection:"column",height:"100%",backgroundColor:e.colors.background.primary})}}},6503:(e,t,r)=>{r.d(t,{_:()=>c});var n=r(2007),a=r(5959),i=r.n(a),o=r(2445);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}function c({severity:e,title:t,message:r,error:a,errorContext:c,children:u}){let d;return a&&(d="string"==typeof a?new Error(a):a,o.v.error(d,l(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){s(e,t,r[t])}))}return e}({},d.cause||{},c),{bannerTitle:t}))),i().createElement(n.Alert,{title:t,severity:e},d&&i().createElement(i().Fragment,null,d.message||d.toString(),i().createElement("br",null)),r,u)}},7570:(e,t,r)=>{r.d(t,{b8:()=>Vr});var n,a,i,o=r(6089),s=r(7781),l=r(2872),c=r(8531),u=r(6680),d=r(2007),p=r(5959),h=r.n(p),m=r(2127);class f extends u.mI{onActivate(){this.setState({skipUrlSync:!1}),this.subscribeToState(((e,t)=>{e.value&&e.value!==t.value&&localStorage.setItem(f.LOCAL_STORAGE_KEY,e.value)}))}static getCurrentDataSource(){const e=Object.values(c.config.datasources).filter((e=>"prometheus"===e.type)),t=new URL(window.location.href).searchParams.get(`var-${m.EY}`),r=localStorage.getItem(f.LOCAL_STORAGE_KEY),n=e.find((e=>e.uid===t))||e.find((e=>e.uid===r))||e.find((e=>e.isDefault))||e[0];return n?n.uid:(console.warn("Cannot find any Prometheus data source!"),"no-data-source-configured")}constructor({initialDS:e}){super({key:m.EY,name:m.EY,pluginId:"prometheus",label:"Data source",description:"Only prometheus data sources are supported",skipUrlSync:!e,value:e||f.getCurrentDataSource()}),this.addActivationHandler(this.onActivate.bind(this))}}i="metricsDrilldownDataSource",(a="LOCAL_STORAGE_KEY")in(n=f)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i;const g=(0,p.memo)((function({size:e}){const t=(0,d.useStyles2)(b);return h().createElement("img",{className:(0,o.cx)(t.logo,e),src:"public/plugins/grafana-metricsdrilldown-app/img/logo.svg"})})),b=()=>({logo:o.css`
    &.small {
      width: 24px;
      height: 24px;
      margin-right: 4px;
      position: relative;
      top: -2px;
    }

    &.large {
      width: 40px;
      height: 40px;
    }
  `});const y=r(5176).t,v=`https://github.com/grafana/metrics-drilldown/commit/${y}`,{buildInfo:w}=c.config;function O(){const e=(0,d.useStyles2)(E),{meta:{info:{version:t,updated:r}}}=(0,s.usePluginContext)()||{meta:{info:{version:"?.?.?",updated:"?"}}};return h().createElement("div",{className:e.menuHeader},h().createElement("h5",null,h().createElement(g,{size:"small"}),"Grafana Metrics Drilldown v",t),h().createElement("div",{className:e.subTitle},"Last update: ",r))}function S(){const e="dev"===y,t=e?y:y.slice(0,8);return h().createElement(d.Menu,{header:h().createElement(O,null)},h().createElement(d.Menu.Item,{label:`Commit SHA: ${t}`,icon:"github",onClick:()=>window.open(v),disabled:e}),h().createElement(d.Menu.Item,{label:"Changelog",icon:"list-ul",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/CHANGELOG.md")}),h().createElement(d.Menu.Item,{label:"Contribute",icon:"external-link-alt",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/docs/contributing.md")}),h().createElement(d.Menu.Item,{label:"Documentation",icon:"document-info",onClick:()=>window.open("https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics")}),h().createElement(d.Menu.Item,{label:"Report an issue",icon:"bug",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/issues/new?template=bug_report.md")}),h().createElement(d.Menu.Divider,null),h().createElement(d.Menu.Item,{label:`Grafana ${w.edition} v${w.version} (${w.env})`,icon:"grafana",onClick:()=>window.open(`https://github.com/grafana/grafana/commit/${w.commit}`)}))}function k(){return h().createElement(d.Dropdown,{overlay:()=>h().createElement(S,null),placement:"bottom-end"},h().createElement(d.Button,{icon:"info-circle",variant:"secondary",tooltip:"Plugin info",tooltipPlacement:"top",title:"Plugin info","data-testid":"plugin-info-button"}))}const E=e=>({button:o.css`
    position: relative;
    display: flex;
    align-items: center;
    width: 32px;
    height: 32px;
    line-height: 30px;
    border: 1px solid ${e.colors.border.weak};
    border-radius: 2px;
    border-left: 0;
    color: ${e.colors.text.primary};
    background: ${e.colors.background.secondary};

    &:hover {
      border-color: ${e.colors.border.medium};
      background-color: ${e.colors.background.canvas};
    }
  `,menuHeader:o.css`
    padding: ${e.spacing(.5,1)};
    white-space: nowrap;
  `,subTitle:o.css`
    color: ${e.colors.text.secondary};
    font-size: ${e.typography.bodySmall.fontSize};
  `});var x=r(4351),j=r(3616),_=r(3241),P=r(3347),C=r(4169),L=r(6503),B=r(5749),N=r(384);const D=new Intl.Collator("en",{sensitivity:"base"}).compare;function T(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function A(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){T(i,n,a,o,s,"next",e)}function s(e){T(i,n,a,o,s,"throw",e)}o(void 0)}))}}const R="(none)";class $ extends u.UU{query(e){return A((function*(){return{state:s.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:s.FieldType.other,values:[],config:{}}],length:0}]}}))()}metricFindQuery(e,t){var r=this;return A((function*(){var n,a;const i=null===(a=t.scopedVars)||void 0===a||null===(n=a.__sceneObject)||void 0===n?void 0:n.valueOf(),o=yield $.getPrometheusDataSource(i);if(!o)return[];var s;const[,l]=null!==(s=e.match(/valuesOf\((.+)\)/))&&void 0!==s?s:[];if(l){return(yield $.fetchLabelValues(l,i)).map((e=>({value:e,text:e})))}let c=[];try{c=yield r.fetchLabels(o,i,e)}catch(e){(0,j.HA)(["Error while fetching labels! Defaulting to an empty array.",e.toString()])}return[{value:R,text:"(none)"},...c]}))()}static getPrometheusDataSource(e){return A((function*(){try{const r=u.jh.findByKey(e,m.EY);var t;const n=null!==(t=null==r?void 0:r.state.value)&&void 0!==t?t:"";return yield(0,c.getDataSourceSrv)().get({uid:n})}catch(e){return void(0,j.jx)(e,["Error while getting the Prometheus data source!"])}}))()}fetchLabels(e,t,r){var n=this;return A((function*(){if(!$.getLabelsMatchAPISupport(e)){const r=$.getFiltersFromVariable(t),a=yield e.getTagKeys(r);return n.processLabelOptions(a.map((({text:e})=>({value:e,text:e}))))}const a=B.q.datasourceUsesTimeRangeInLanguageProviderMethods(e)?[u.jh.getTimeRange(t).state.value,r]:[r],i=yield e.languageProvider.fetchLabelsWithMatch(...a);return n.processLabelOptions(Object.entries(i).map((([e,t])=>({value:e,text:Array.isArray(t)?t[0]:t||e}))))}))()}static getLabelsMatchAPISupport(e){try{return e.hasLabelsMatchAPISupport()}catch(e){return(0,j.HA)(["Error while checking if the current data source supports the labels match API! Defaulting to false.",e.toString()]),!1}}static getFiltersFromVariable(e){const t=u.jh.lookupVariable(m.Ao,e);return(0,N.BE)(t)?{filters:t.state.filters}:{filters:[]}}processLabelOptions(e){return e.filter((({value:e})=>!e.startsWith("__"))).sort(((e,t)=>D(e.value,t.value)))}static fetchLabelValues(e,t){return A((function*(){const r=yield $.getPrometheusDataSource(t);if(!r)return[];const n=B.q.datasourceUsesTimeRangeInLanguageProviderMethods(r)?[u.jh.getTimeRange(t).state.value,e]:[e];try{return yield r.languageProvider.fetchLabelValues(...n)}catch(t){return(0,j.HA)([`Error while retrieving label "${e}" values! Defaulting to an empty array.`,t.toString()]),[]}}))()}testDatasource(){return A((function*(){return{status:"success",message:"OK"}}))()}constructor(){super($.uid,$.uid)}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}($,"uid","grafana-prometheus-labels-datasource");const M="wingmanLabelValues";class V extends u.fS{constructor({labelName:e}){super({name:M,datasource:{uid:$.uid},query:`valuesOf(${e})`,isMulti:!1,allowCustomValue:!1,refresh:s.VariableRefresh.onTimeRangeChanged,hide:s.VariableHide.hideVariable,value:"$__all",includeAll:!0})}}function F(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(V,"Component",(()=>h().createElement(h().Fragment,null)));class H extends u.Bs{performRepeat(){var e,t;if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({loadingLayout:null===(e=(t=this.state).getLayoutLoading)||void 0===e?void 0:e.call(t),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const r=u.jh.lookupVariable(this.state.variableName,this);if(!(r instanceof u.n8))return void console.error("SceneByVariableRepeater: variable is not a MultiValueVariable!",r);var n,a;if(r.state.error)return void this.setState({errorLayout:null===(n=(a=this.state).getLayoutError)||void 0===n?void 0:n.call(a,r.state.error),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const i=I(r);var o,s;if(!i.length)return void this.setState({emptyLayout:null===(o=(s=this.state).getLayoutEmpty)||void 0===o?void 0:o.call(s),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize});const l=i.slice(0,this.state.initialPageSize).map(((e,t)=>this.state.getLayoutChild(e,t,i))).filter(Boolean);this.state.body.setState({children:l})}increaseBatchSize(){const e=I(u.jh.lookupVariable(this.state.variableName,this)),t=this.state.currentBatchSize+this.state.pageSizeIncrement,r=e.slice(this.state.currentBatchSize,t).map(((t,r)=>this.state.getLayoutChild(t,this.state.currentBatchSize+r,e))).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...r]}),this.setState({currentBatchSize:t})}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState();return{increment:t,current:e,total:u.jh.lookupVariable(this.state.variableName,this).state.options.length}}constructor({variableName:e,body:t,getLayoutChild:r,getLayoutLoading:n,getLayoutError:a,getLayoutEmpty:i,initialPageSize:o,pageSizeIncrement:s}){super({variableName:e,body:t,getLayoutChild:r,getLayoutLoading:n,getLayoutError:a,getLayoutEmpty:i,currentBatchSize:0,initialPageSize:o||6,pageSizeIncrement:s||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0}),F(this,"_variableDependency",new u.Sh(this,{variableNames:[this.state.variableName],onVariableUpdateCompleted:()=>this.performRepeat()})),this.addActivationHandler((()=>this.performRepeat()))}}function I(e){const{value:t,text:r,options:n}=e.state;return e.hasAllValue()?n:Array.isArray(t)&&Array.isArray(r)?t.map(((e,t)=>({value:e,label:r[t]}))):[{value:t,label:r}]}F(H,"Component",(({model:e})=>{const{body:t,loadingLayout:r,errorLayout:n,emptyLayout:a}=e.useState();return r?h().createElement(r.Component,{model:r}):n?h().createElement(n.Component,{model:n}):a?h().createElement(a.Component,{model:a}):h().createElement(t.Component,{model:t})}));class z extends s.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(z,"type","sort-by-changed");var U=r(9851),G=r(7348),q=r(2445),W=r(7476);function K(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function Q(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){K(i,n,a,o,s,"next",e)}function s(e){K(i,n,a,o,s,"throw",e)}o(void 0)}))}}class Y{getUsageMetrics(e){return this._usageState[e].metrics&&Object.keys(this._usageState[e].metrics).length>0?Promise.resolve(this._usageState[e].metrics):(this._usageState[e].metricsPromise||(this._usageState[e].metricsPromise=this._usageState[e].fetcher().then((t=>(this._usageState[e].metrics=t,this._usageState[e].metricsPromise=void 0,t)))),this._usageState[e].metricsPromise)}getUsageForMetric(e,t){return this.getUsageMetrics(t).then((t=>{var r;return null!==(r=t[e])&&void 0!==r?r:0}))}constructor(){!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"_usageState",{"dashboard-usage":{metrics:{},metricsPromise:void 0,fetcher:ee},"alerting-usage":{metrics:{},metricsPromise:void 0,fetcher:re}})}}const J={showSuccessAlert:!1,showErrorAlert:!1},X=new Map,Z=(0,G.g)(function(){var e=Q((function*(e,t){let r=X.get(e);return r||(r=(0,c.getBackendSrv)().get(`/api/dashboards/uid/${e}`,void 0,`grafana-metricsdrilldown-app-dashboard-metric-usage-${e}`,J).catch((r=>(t<=5&&q.v.error(r,{dashboardUid:e}),t++,Promise.resolve(null)))).finally((()=>{X.delete(e)})),X.set(e,r)),r}));return function(t,r){return e.apply(this,arguments)}}(),{concurrency:50});function ee(){return te.apply(this,arguments)}function te(){return(te=Q((function*(){try{const e=yield(0,c.getBackendSrv)().get("/api/search",{type:"dash-db",limit:500},"grafana-metricsdrilldown-app-dashboard-search",J);let t=0;return yield Promise.all(e.map((({uid:e})=>Z(e,t)))).then((e=>{const t={},r=e.filter((e=>null!==e));for(const{dashboard:e}of r){var n;if(null===(n=e.panels)||void 0===n?void 0:n.length)for(const r of e.panels){var a;const{datasource:e}=r;if((0,W.a)(e)&&"targets"in r&&(null===(a=r.targets)||void 0===a?void 0:a.length))for(const e of r.targets){const r=ae("string"==typeof e.expr?e.expr:"");for(const e of r)e&&(t[e]=(t[e]||0)+1)}}}return t}))}catch(e){const t="string"==typeof e?new Error(e):e;return q.v.error(t,{message:"Failed to fetch dashboard metrics"}),{}}}))).apply(this,arguments)}function re(){return ne.apply(this,arguments)}function ne(){return(ne=Q((function*(){try{const t=yield(0,c.getBackendSrv)().get("/api/v1/provisioning/alert-rules",void 0,"grafana-metricsdrilldown-app-alert-rule-metric-usage",J),r={};for(const n of t){var e;if(null===(e=n.data)||void 0===e?void 0:e.length)for(const e of n.data){if(!e.model||"__expr__"===e.datasourceUid)continue;const t=e.model.expr;if(t&&"string"==typeof t)try{const e=ae(t);for(const t of e)t&&(r[t]=(r[t]||0)+1)}catch(e){q.v.warn(e,{message:`Failed to parse PromQL expression in alert rule ${n.title}`})}}}return r}catch(e){const t="string"==typeof e?new Error(e):e;return q.v.error(t,{message:"Failed to fetch alerting rules"}),{}}}))).apply(this,arguments)}function ae(e){const t=U.K3.parse(e),r=new Set,n=t.cursor();do{if(n.type.is("VectorSelector")&&n.firstChild()){do{if(n.type.is("Identifier")){const t=e.slice(n.from,n.to);r.add(t)}}while(n.nextSibling());n.parent()}}while(n.next());return Array.from(r)}function ie(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){ie(e,t,r[t])}))}return e}function se(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const le="metrics-drilldown-recent-metrics/v1";function ce(){try{const e=localStorage.getItem(le);if(!e)return[];const t=JSON.parse(e),r=Date.now()-2592e6,n=t.filter((e=>e.timestamp>r));return n.length!==t.length&&localStorage.setItem(le,JSON.stringify(n)),n}catch(e){return console.error("Failed to get recent metrics:",e),[]}}const ue=[{label:"Default",value:"default"},{label:"Dashboard Usage",value:"dashboard-usage"},{label:"Alerting Usage",value:"alerting-usage"}],de="metrics-reducer-sort-by";class pe extends u.Bs{activationHandler(){const e=u.jh.getVariables(this).getByName(de);this.supportedSortByOptions.has(e.getValue())||e.changeValueTo("default"),this._subs.add(e.subscribeToState(((e,t)=>{e.value!==t.value&&this.publishEvent(new z({sortBy:e.value}),!0)})))}getUsageForMetric(e,t){return this.usageFetcher.getUsageForMetric(e,t)}getUsageMetrics(e){return this.usageFetcher.getUsageMetrics(e)}constructor(e){super(se(oe({},e),{key:"metrics-sorter",$variables:new u.Pj({variables:[new u.yP({name:de,label:"Sort by",value:"default",query:ue.map((e=>`${e.label} : ${e.value}`)).join(","),description:"Sort metrics by default (alphabetically, with recently-selected metrics first), by prevalence in dashboard panel queries, or by prevalence in alerting rules"})]}),inputControls:new u.K8({layout:"horizontal"})})),ie(this,"initialized",!1),ie(this,"supportedSortByOptions",new Set(["default","dashboard-usage","alerting-usage"])),ie(this,"usageFetcher",new Y),this.addActivationHandler((()=>this.activationHandler()))}}function he(e){const t=ce().map((e=>e.name)),r=new Set(t),[n,a]=e.reduce((([e,t],n)=>(r.has(n)?e.push(n):t.push(n),[e,t])),[[],[]]),i=function(e){return[...e].sort(((e,t)=>D(e,t)))}(a);return[...t.filter((e=>n.includes(e))),...i]}function me(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}ie(pe,"Component",(({model:e})=>{const{inputControls:t}=e.useState();return h().createElement(t.Component,{model:t,"data-testid":"sort-by-select"})}));const ge="metrics-wingman";class be extends u.fS{constructor(e){super(fe(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){me(e,t,r[t])}))}return e}({key:ge,name:ge,label:"Metrics"},e),{datasource:m.GH,query:`label_values({$${m.Ao}}, __name__)`,includeAll:!0,value:"$__all",skipUrlSync:!0,refresh:s.VariableRefresh.onTimeRangeChanged,sort:s.VariableSort.alphabeticalAsc,hide:s.VariableHide.hideVariable}))}}class ye extends s.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(ye,"type","metrics-variable-activated");class ve extends s.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(ve,"type","metrics-variable-deactivated");class we extends s.BusEventWithPayload{}function Oe(e){const t=e.state.key;if(!t)throw new TypeError(`Variable "${e.state.name}" has no key. Please provide a key in order to publish its lifecycle events.`);return e.addActivationHandler((()=>(e.publishEvent(new ye({key:t}),!0),e.subscribeToState(((r,n)=>{!r.loading&&n.loading&&e.publishEvent(new we({key:t,options:r.options}),!0)})),()=>{e.publishEvent(new ve({key:t}),!0)}))),e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(we,"type","metrics-variable-loaded");const Se="filtered-metrics-wingman";class ke extends be{updateGroupByQuery(e){const t=`label_values({${e&&e!==R?`${e}!="",$${m.Ao}`:`$${m.Ao}`}}, __name__)`;t!==this.state.query&&(this.setState({query:t}),this.refreshOptions())}constructor(){return super({key:Se,name:Se,label:"Filtered Metrics"}),Oe(this)}}var Ee=r(3552),xe=r(7437);function je(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class _e extends u.Bs{constructor({metricName:e,variant:t,fill:r}){super({key:`select-action-${e}`,metricName:e,variant:t||"primary",fill:r||"outline"}),je(this,"onClick",(()=>{this.publishEvent(new m.OO(this.state.metricName),!0)}))}}je(_e,"Component",(({model:e})=>{const{variant:t,fill:r}=e.useState();return h().createElement(d.Button,{variant:t,fill:r,size:"sm",onClick:e.onClick,"data-testid":`select-action-${e.state.metricName}`},"Select")}));class Pe extends u.Bs{}function Ce(e){return{badge:(0,o.css)({borderRadius:e.shape.radius.pill,border:`1px solid ${e.colors.info.text}`,background:e.colors.info.transparent,cursor:"auto",width:"112px",padding:"0rem 0.25rem 0 0.35rem"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Pe,"Component",(()=>{const e=(0,d.useStyles2)(Ce);return h().createElement(d.Badge,{className:e.badge,color:"blue",text:"Native Histogram"})}));var Le=r(5938);var Be=r(6145),Ne=r(1625);function De(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Te(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){De(e,t,r[t])}))}return e}function Ae(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const Re="240px",$e="200px",Me="160px",Ve=new Set(["count","total","sum","bucket"]);class Fe extends u.Bs{onActivate(){const{body:e,prometheusFunction:t}=this.state;this._subs.add(e.state.$data.subscribeToState((r=>{var n;if((null===(n=r.data)||void 0===n?void 0:n.state)!==s.LoadingState.Done)return;const{series:a}=r.data;(null==a?void 0:a.length)&&e.setState({fieldConfig:{defaults:e.state.fieldConfig.defaults,overrides:[{matcher:{id:s.FieldMatcherID.byFrameRefID,options:a[0].refId},properties:[{id:"displayName",value:t}]}]}})})))}static buildVizPanel({metricName:e,title:t,highlight:r,color:n,hideLegend:a,prometheusFunction:i,matchers:o,headerActions:s,isNativeHistogram:l=!1}){const c=r?`${t} (current)`:t,d=(0,xe.l_)(e),p=e.endsWith("_bucket")||l;return"up"===e||e.endsWith("_up")?function({panelTitle:e,headerActions:t,queryRunner:r}){return r.setState({maxDataPoints:100}),u.d0.statushistory().setTitle(e).setHeaderActions(t.map((e=>e.clone()))).setData(r).setColor({mode:"thresholds"}).setMappings([{type:Be.dM.ValueToText,options:{0:{color:"red",text:"down"},1:{color:"green",text:"up"}}}]).setThresholds({mode:Be.Ol.Absolute,steps:[{value:0,color:"red"},{value:1,color:"green"}]}).setOption("legend",{showLegend:!1}).setOption("showValue",Ne.yL.Never)}({panelTitle:c,headerActions:s,queryRunner:Fe.buildQueryRunner({metricName:e,matchers:o,prometheusFunction:"min",isHistogram:p})}).setUnit(d).build():p?function({panelTitle:e,queryRunner:t,color:r,headerActions:n,hideLegend:a}){return u.d0.heatmap().setTitle(e).setData(t).setOption("calculate",!1).setOption("color",{mode:Le.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1}).setHeaderActions(n.map((e=>e.clone()))).setOption("legend",{show:!a})}({panelTitle:c,color:n,headerActions:s,hideLegend:a,queryRunner:Fe.buildQueryRunner({metricName:e,matchers:o,prometheusFunction:"rate",isHistogram:p,queryOptions:{format:"heatmap"}})}).setUnit(d).build():function({panelTitle:e,queryRunner:t,color:r,headerActions:n,hideLegend:a}){return u.d0.timeseries().setTitle(e).setData(t).setColor({mode:"fixed",fixedColor:r}).setCustomFieldConfig("fillOpacity",9).setHeaderActions(n.map((e=>e.clone()))).setOption("legend",{showLegend:!a})}({panelTitle:c,headerActions:s,color:n,hideLegend:a,queryRunner:Fe.buildQueryRunner({metricName:e,matchers:o,prometheusFunction:i,isHistogram:p})}).setUnit(d).build()}static buildQueryRunner({metricName:e,matchers:t,isHistogram:r,prometheusFunction:n,queryOptions:a={}}){const i=t.map((e=>{const[t,r]=e.split("=");return{key:t,value:r.replace(/['"]/g,""),operator:"="}})),{isRateQuery:o,groupings:s}=Fe.determineQueryProperties(e,r),l=(0,Ee.$5)(Te({metric:e,filters:i,isRateQuery:o,useOtelJoin:!1,ignoreUsage:!0,groupings:s},n?{nonRateQueryFunction:n}:{}));return new u.dt({datasource:m.GH,maxDataPoints:Fe.MAX_DATA_POINTS,queries:[Ae(Te({},a),{refId:e,expr:l,fromExploreMetrics:!0})]})}static determineQueryProperties(e,t){const r=e.split("_").at(-1);let n;return t&&(n=["le"]),{isRateQuery:Ve.has(r||""),groupings:n}}constructor(e){const{isRateQuery:t}=Fe.determineQueryProperties(e.metricName,e.isNativeHistogram);var r;const n=Ae(Te({},e),{prometheusFunction:null!==(r=e.prometheusFunction)&&void 0!==r?r:(0,Ee.UL)(t),isNativeHistogram:e.isNativeHistogram,matchers:e.matchers||[],title:e.title||e.metricName,height:e.height||$e,hideLegend:Boolean(e.hideLegend),highlight:Boolean(e.highlight),headerActions:[...e.isNativeHistogram?[new Pe({})]:[],...e.headerActions||[new _e({metricName:e.metricName})]]});super(Ae(Te({key:`metric-viz-panel-${n.metricName}`},n),{body:Fe.buildVizPanel(Te({},n))})),this.addActivationHandler(this.onActivate.bind(this))}}function He(e,t){return{container:(0,o.css)({height:t}),highlight:(0,o.css)({border:`2px solid ${e.colors.primary.main}`})}}function Ie(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ze(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}De(Fe,"MAX_DATA_POINTS",250),De(Fe,"Component",(({model:e})=>{const{body:t,height:r,highlight:n}=e.useState(),a=(0,d.useStyles2)(He,r);return h().createElement("div",{className:(0,o.cx)(a.container,n&&a.highlight)},t&&h().createElement(t.Component,{model:t}))}));class Ue extends u.Bs{_onActivate(){if(!u.jh.getAncestor(this,wr).state.enginesMap.get(Se))return;const e=u.jh.findByKeyAndType(this,"metrics-sorter",pe),t=u.jh.getVariables(e).getByName(de);(0,N.UG)(t)&&(this.updateSortBy(e,t.getValue()),this._subs.add(t.subscribeToState((({value:t})=>{this.updateSortBy(e,t)}))))}updateSortBy(e,t){this.setState({sortBy:t});const r=u.jh.getAncestor(this,u.gF),n=null==r?void 0:r.state.autoRows;switch(t){case"dashboard-usage":case"alerting-usage":e.getUsageForMetric(this.state.metric,t).then((e=>{this.setState({[t]:e})})),n!==Re&&r.setState({autoRows:Re});break;default:n!==$e&&r.setState({autoRows:$e})}}constructor(e){super(ze(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Ie(e,t,r[t])}))}return e}({},e),{sortBy:"default","alerting-usage":0,"dashboard-usage":0})),this.addActivationHandler(this._onActivate.bind(this))}}function Ge({usageType:e,usageCount:t,singularUsageType:r,pluralUsageType:n,icon:a}){const i=(0,d.useStyles2)(qe);return h().createElement("div",{className:i.usageContainer,"data-testid":"usage-data-panel"},h().createElement(d.Tooltip,{content:`Metric is used in ${t} ${1===t?r:n}`,placement:"top"},h().createElement("span",{className:i.usageItem,"data-testid":e},h().createElement(d.Icon,{name:a})," ",t)))}function qe(e){return{panelContainer:(0,o.css)({}),usageContainer:(0,o.css)({display:"flex",flexDirection:"row",justifyContent:"flex-start",gap:"17px",padding:"8px 12px",border:`1px solid ${e.colors.border.weak}`,borderTopWidth:0,backgroundColor:e.colors.background.primary,alignItems:"center"}),usageItem:(0,o.css)({display:"flex",alignItems:"center",gap:"4px",color:e.colors.text.secondary,opacity:"65%"})}}Ie(Ue,"Component",(({model:e})=>{const{vizPanelInGridItem:t,sortBy:r,"alerting-usage":n,"dashboard-usage":a}=e.useState();if(!t)return;const i=(0,d.useStyles2)(qe);if("default"===r)return h().createElement("div",{className:i.panelContainer,"data-testid":"with-usage-data-preview-panel"},h().createElement(t.Component,{model:t}));const o={"dashboard-usage":{usageCount:a,singularUsageType:"dashboard panel query",pluralUsageType:"dashboard panel queries",icon:"apps"},"alerting-usage":{usageCount:n,singularUsageType:"alert rule",pluralUsageType:"alert rules",icon:"bell"}};return h().createElement("div",{className:i.panelContainer,"data-testid":"with-usage-data-preview-panel"},h().createElement(t.Component,{model:t}),h().createElement(Ge,{usageType:r,usageCount:o[r].usageCount,singularUsageType:o[r].singularUsageType,pluralUsageType:o[r].pluralUsageType,icon:o[r].icon}))}));const We="labelsWingman";class Ke extends u.fS{onActivate(){const e=u.jh.lookupVariable(Se,this);e.updateGroupByQuery(this.state.value),this._subs.add(this.subscribeToState(((t,r)=>{t.value!==r.value&&e.updateGroupByQuery(this.state.value)}))),this._subs.add(this.subscribeToState(((e,t)=>{e.query!==t.query&&(t.query&&this.setState({value:R}),this.refreshOptions())}))),this._subs.add(u.jh.findByKey(this,m.EY).subscribeToState(((e,t)=>{e.value!==t.value&&(this.setState({value:R}),this.refreshOptions())})));const t=u.jh.interpolate(this,m.ui,{});this.setState({query:`{__name__=~".+",${t}}`})}constructor(){super({name:We,label:"Group by label",placeholder:"Group by label...",datasource:{uid:$.uid},query:"",includeAll:!1,isMulti:!1,allowCustomValue:!1,refresh:s.VariableRefresh.onTimeRangeChanged,hide:s.VariableHide.hideVariable}),this.addActivationHandler(this.onActivate.bind(this))}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Ke,"Component",(({model:e})=>{const t=(0,d.useStyles2)(Qe),{label:r}=e.useState();return h().createElement("div",{className:t.container},h().createElement(d.Label,{className:t.label},r),h().createElement(u.fS.Component,{model:e}))}));const Qe=e=>({container:o.css`
    display: flex;
    align-items: center;
    gap: 0;

    [class*='input-wrapper'] {
      width: 240px;
    }
  `,label:o.css`
    height: 32px;
    white-space: nowrap;
    margin: 0;
    background-color: ${e.colors.background.primary};
    padding: ${e.spacing(1)};
    border-radius: ${e.shape.radius.default};
    border: 1px solid ${e.colors.border.weak};
    border-right: none;
  `});function Ye(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Je=function(e){return e.GRID="grid",e.ROWS="rows",e}({});class Xe extends u.Bs{getUrlState(){return{layout:this.state.layout}}updateFromUrl(e){const t={};"string"==typeof e.layout&&e.layout!==this.state.layout&&(t.layout=Object.values(Je).includes(e.layout)?e.layout:Xe.DEFAULT_LAYOUT),this.setState(t)}constructor(){super({key:"layout-switcher",layout:Xe.DEFAULT_LAYOUT}),Ye(this,"_urlSync",new u.So(this,{keys:["layout"]})),Ye(this,"onChange",(e=>{this.setState({layout:e})}))}}Ye(Xe,"OPTIONS",[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]),Ye(Xe,"DEFAULT_LAYOUT","grid"),Ye(Xe,"Component",(({model:e})=>{const{layout:t}=e.useState();return h().createElement(d.RadioButtonGroup,{"aria-label":"Layout switcher",options:Xe.OPTIONS,value:t,onChange:e.onChange,fullWidth:!1})}));const Ze="repeat(auto-fit, minmax(400px, 1fr))";class et extends u.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=u.jh.findByKeyAndType(this,"layout-switcher",Xe),t=this.state.body.state.body,r=(e,r)=>{e.layout!==(null==r?void 0:r.layout)&&t.setState({templateColumns:e.layout===Je.ROWS?"1fr":Ze})};r(e.state),this._subs.add(e.subscribeToState(r))}constructor(){super({key:"simple-metrics-list",body:new H({variableName:Se,initialPageSize:120,pageSizeIncrement:9,body:new u.gF({children:[],isLazy:!0,templateColumns:Ze,autoRows:Re,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:Be.yV.Crosshair})]}),getLayoutLoading:()=>new u.dM({reactNode:h().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:h().createElement(L._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new u.dM({reactNode:h().createElement(L._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,t)=>{const r=(0,C.kj)(this).isNativeHistogram(e.value),n=u.jh.lookupVariable(m.Ao,this),a=(0,N.BE)(n)?n.state.filters.map((e=>`${e.key}${e.operator}${e.value}`)):[];return new u.xK({body:new Ue({vizPanelInGridItem:new Fe({metricName:e.value,color:(0,C.Vy)(t),isNativeHistogram:r,matchers:a}),metric:e.value})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function tt(e){return{container:(0,o.css)({}),footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}function rt(){return h().createElement("svg",{stroke:"currentColor",width:"17",height:"16",viewBox:"0 0 17 16",fill:"none"},h().createElement("circle",{cx:"8.92688",cy:"3.63132",r:"2.375",strokeWidth:"1.5"}),h().createElement("path",{d:"M13.6469 4.37965C14.6813 4.76699 15.3235 7.03139 14.9362 8.06582",strokeWidth:"1.5"}),h().createElement("path",{d:"M4.35309 4.37965C3.31866 4.76699 2.67651 7.03139 3.06384 8.06582",strokeWidth:"1.5"}),h().createElement("path",{d:"M10.3408 14.2531C9.75237 14.8415 8.11813 14.7799 7.50903 14.1708",strokeWidth:"1.5"}),h().createElement("circle",{cx:"4.00195",cy:"12.251",r:"2.375",strokeWidth:"1.5"}),h().createElement("circle",{cx:"13.8478",cy:"12.251",r:"2.375",strokeWidth:"1.5"}))}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(et,"Component",(({model:e})=>{const{body:t}=e.useState(),r=(0,d.useStyles2)(tt),n=u.jh.lookupVariable(Se,e),{loading:a,error:i}=n.useState(),o=t.useSizes(),s=!a&&!i&&o.total>0&&o.current<o.total;return h().createElement("div",{"data-testid":"metrics-list"},h().createElement("div",{className:r.container},h().createElement(t.Component,{model:t})),s&&h().createElement("div",{className:r.footer},h().createElement(d.Button,{variant:"secondary",fill:"outline",onClick:()=>{t.increaseBatchSize()}},"Show ",o.increment," more metrics (",o.current,"/",o.total,")")))}));function nt(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function at(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){nt(i,n,a,o,s,"next",e)}function s(e){nt(i,n,a,o,s,"throw",e)}o(void 0)}))}}class it extends u.UU{query(e){return at((function*(){return{state:s.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:s.FieldType.other,values:[],config:{}}],length:0}]}}))()}metricFindQuery(e,t){return at((function*(){var r,n;const a=null===(n=t.scopedVars)||void 0===n||null===(r=n.__sceneObject)||void 0===r?void 0:r.valueOf(),i=yield it.getPrometheusDataSource(a);if(!i)return[];const o=u.jh.getTimeRange(a).state.value;let s=[];const l=e.startsWith("removeRules"),c=l?e.replace("removeRules",""):e;return s=B.q.datasourceUsesTimeRangeInLanguageProviderMethods(i)?yield i.languageProvider.fetchSeriesValuesWithMatch(o,"__name__",c):yield i.languageProvider.fetchSeriesValuesWithMatch("__name__",c),l&&(s=s.filter((e=>!(e=>"ALERTS"===e||"ALERTS_FOR_STATE"===e||e.includes(":"))(e)))),s.map((e=>({value:e,text:e})))}))()}static getPrometheusDataSource(e){return at((function*(){try{const r=u.jh.findByKey(e,m.EY);var t;const n=null!==(t=null==r?void 0:r.state.value)&&void 0!==t?t:"";return yield(0,c.getDataSourceSrv)().get({uid:n})}catch(e){return console.error("Error getting Prometheus data source!"),void console.error(e)}}))()}testDatasource(){return at((function*(){return{status:"success",message:"OK"}}))()}constructor(){super(it.uid,it.uid)}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(it,"uid","grafana-prometheus-metrics-with-label-values-datasource");const ot="metrics-with-label-value";class st extends u.fS{onActivate(e,t,r){const n=u.jh.lookupVariable(m.Ao,this);(null==n?void 0:n.state.hide)!==s.VariableHide.hideVariable&&this.setState({query:st.buildQuery(e,t,r)})}static buildQuery(e,t,r){return r?`removeRules{${e}="${t}",${m.ui}}`:`{${e}="${t}",${m.ui}}`}constructor({labelName:e,labelValue:t,removeRules:r}){return super({key:`${ot}-${e}-${t}`,name:ot,datasource:{uid:it.uid},query:st.buildQuery(e,t,r),isMulti:!1,allowCustomValue:!1,refresh:s.VariableRefresh.onTimeRangeChanged,hide:s.VariableHide.hideVariable,skipUrlSync:!0,value:"$__all",includeAll:!0}),this.addActivationHandler(this.onActivate.bind(this,e,t,r)),Oe(this)}}class lt extends u.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=u.jh.findByKeyAndType(this,"layout-switcher",Xe),t=this.state.body.state.body,r=(e,r)=>{e.layout!==(null==r?void 0:r.layout)&&t.setState({templateColumns:e.layout===Je.ROWS?"1fr":Ze})};r(e.state),this._subs.add(e.subscribeToState(r))}constructor({index:e,labelName:t,labelValue:r,labelCardinality:n}){super({index:e,labelName:t,labelValue:r,labelCardinality:n,key:`${t||""}-${r||""}`,$variables:new u.Pj({variables:[new st({labelName:t,labelValue:r})]}),body:new H({variableName:ot,initialPageSize:3,body:new u.gF({children:[],isLazy:!0,templateColumns:Ze,autoRows:Re,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})]}),getLayoutLoading:()=>new u.dM({reactNode:h().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:h().createElement(L._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new u.dM({reactNode:h().createElement(L._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,n)=>{const a=(0,C.kj)(this).isNativeHistogram(e.value);return new u.xK({body:new Ue({vizPanelInGridItem:new Fe({metricName:e.value,color:(0,C.Vy)(n),matchers:[`${t}="${r}"`],headerActions:[new _e({metricName:e.value})],isNativeHistogram:a}),metric:e.value})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function ct(e){return{container:(0,o.css)({background:e.colors.background.canvas,margin:e.spacing(1,0,0,0),"& div:focus-within":{boxShadow:"none !important"}}),containerHeader:(0,o.css)({display:"flex",alignItems:"center",gap:"8px",marginBottom:"-36px",paddingBottom:e.spacing(1.5),borderBottom:`1px solid ${e.colors.border.medium}`}),headerButtons:(0,o.css)({position:"relative",top:"3px",marginLeft:"auto",marginRight:"30px",zIndex:100}),selectButton:(0,o.css)({height:"28px"}),collapsableSectionBody:(0,o.css)({display:"flex",flexDirection:"column",gap:"24px",padding:e.spacing(1)}),groupName:(0,o.css)({display:"flex",alignItems:"center",fontSize:"1.3rem",lineHeight:"1.3rem"}),labelValue:(0,o.css)({fontSize:"16px",marginLeft:"8px"}),index:(0,o.css)({fontSize:"12px",color:e.colors.text.secondary,marginLeft:"8px"}),footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(1),"& button":{height:"40px"}}),variable:(0,o.css)({display:"none"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(lt,"Component",(({model:e})=>{const[t,r]=(0,p.useState)(!1),n=(0,d.useStyles2)(ct),{index:a,labelName:i,labelValue:o,labelCardinality:s,$variables:l,body:c}=e.useState(),f=l.state.variables[0],{loading:g,error:b}=f.useState(),y=c.useSizes(),v=!g&&!b&&y.total>0&&y.current<y.total;return h().createElement("div",{className:n.container},h().createElement("div",{className:n.containerHeader},h().createElement("div",{className:n.headerButtons},h().createElement(d.Button,{className:n.selectButton,variant:"secondary",onClick:()=>{var t;const r=u.jh.lookupVariable(m.Ao,e);r.setState({filters:[...r.state.filters,{key:i,operator:"=",value:o}]}),null===(t=u.jh.lookupVariable(We,e))||void 0===t||t.changeValueTo(R)}},"Select"))),h().createElement(d.CollapsableSection,{isOpen:!t,onToggle:()=>r(!t),label:h().createElement("div",{className:n.groupName},h().createElement(rt,null),h().createElement("div",{className:n.labelValue},o),s>1&&h().createElement("div",{className:n.index},"(",a+1,"/",s,")"))},h().createElement("div",{className:n.collapsableSectionBody},h().createElement(c.Component,{model:c})),v&&h().createElement("div",{className:n.footer},h().createElement(d.Button,{variant:"secondary",fill:"outline",onClick:()=>{c.increaseBatchSize()},tooltip:`Show more metrics for ${i}="${o}"`,tooltipPlacement:"top"},"Show ",y.increment," more metrics (",y.current,"/",y.total,")"))),h().createElement("div",{className:n.variable},h().createElement(f.Component,{key:f.state.name,model:f})))}));class ut extends u.Bs{constructor({labelName:e}){super({key:"metrics-group-list",labelName:e,$variables:new u.Pj({variables:[new V({labelName:e})]}),body:new H({variableName:M,initialPageSize:20,pageSizeIncrement:10,body:new u.gF({children:[],isLazy:!0,templateColumns:"1fr",autoRows:"auto",rowGap:1}),getLayoutLoading:()=>new u.dM({reactNode:h().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:h().createElement(L._,{title:"",severity:"info"},'No label values found for label "',e,'".')}),getLayoutError:t=>new u.dM({reactNode:h().createElement(L._,{severity:"error",title:`Error while loading label "${e}" values!`,error:t})}),getLayoutChild:(t,r,n)=>new u.xK({body:new lt({index:r,labelName:e,labelValue:t.value,labelCardinality:n.length})})})})}}function dt(e){return{footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",margin:e.spacing(3,0,1,0),"& button":{height:"40px"}}),variable:(0,o.css)({display:"none"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(ut,"Component",(({model:e})=>{const t=(0,d.useStyles2)(dt),{body:r,$variables:n,labelName:a}=e.useState(),i=n.state.variables[0],{loading:o,error:s}=i.useState(),l=r.useSizes(),c=!o&&!s&&l.total>0&&l.current<l.total;return h().createElement("div",{"data-testid":"metrics-groupby-list"},h().createElement(r.Component,{model:r}),c&&h().createElement("div",{className:t.footer},h().createElement(d.Button,{variant:"secondary",fill:"outline",onClick:()=>{r.increaseBatchSize()}},"Show ",l.increment,' more "',a,'" values (',l.current,"/",l.total,")")),h().createElement("div",{className:t.variable},h().createElement(i.Component,{key:i.state.name,model:i})))}));class pt extends s.BusEventWithPayload{}function ht(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){ht(e,t,r[t])}))}return e}function ft(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(pt,"type","quick-search-changed");class gt extends u.Bs{getUrlState(){return{[gt.URL_SEARCH_PARAM_NAME]:this.state.value}}updateFromUrl(e){const t=e[gt.URL_SEARCH_PARAM_NAME]||"";t!==this.state.value&&this.setState({value:t})}onActivate(){this.setState({counts:{current:u.jh.lookupVariable(Se,this).state.options.length,total:u.jh.lookupVariable(ge,this).state.options.length}}),this.updateDisableRatioDisplay()}updateDisableRatioDisplay(){const e=u.jh.lookupVariable(We,this).state.value;this.setState({disableRatioDisplay:Boolean(e&&e!==R)})}updateValue(e){this.setState({value:e}),this.notifyValueChange(e)}getHumanFriendlyCountsMessage(){const{counts:e,disableRatioDisplay:t}=this.state;return t||e.current===e.total?{tagName:`${e.current}`,tooltipContent:1!==e.current?`${e.current} metrics in total`:"1 metric in total"}:{tagName:`${e.current}/${e.total}`,tooltipContent:1!==e.current?`${e.current} out of ${e.total} metrics in total`:`1 out of ${e.total} metrics in total`}}constructor(){super({key:"quick-search",value:"",counts:{current:0,total:0},disableRatioDisplay:!1}),ht(this,"_variableDependency",new u.Sh(this,{variableNames:[m.EY,ge,Se,We],onAnyVariableChanged:e=>{if([ge,Se].includes(e.state.name)){const{counts:t}=this.state,r=e.state.name===ge?"total":"current",n=u.jh.lookupVariable(e.state.name,this).state.options.length;n!==t[r]&&this.setState({counts:ft(mt({},t),{[r]:n})})}else if(e.state.name!==We)this.setState({disableRatioDisplay:!1}),e.state.name===m.EY&&this.setState({value:""});else{const t=e.state.value;this.setState({disableRatioDisplay:Boolean(t&&t!==R)})}}})),ht(this,"_urlSync",new u.So(this,{keys:[gt.URL_SEARCH_PARAM_NAME]})),ht(this,"notifyValueChange",(0,_.debounce)((e=>{this.publishEvent(new pt({searchText:e}),!0)}),250)),ht(this,"onChange",(e=>{this.updateValue(e.currentTarget.value)})),ht(this,"clear",(()=>{this.updateValue("")})),ht(this,"onKeyDown",(e=>{"Escape"===e.key&&(e.preventDefault(),this.clear())})),this.addActivationHandler(this.onActivate.bind(this))}}ht(gt,"URL_SEARCH_PARAM_NAME","search_txt"),ht(gt,"Component",(({model:e})=>{const t=(0,d.useStyles2)(bt),{value:r}=e.useState(),{tagName:n,tooltipContent:a}=e.getHumanFriendlyCountsMessage();return h().createElement(d.Input,{value:r,onChange:e.onChange,onKeyDown:e.onKeyDown,placeholder:"Quick search metrics...",prefix:h().createElement("i",{className:"fa fa-search"}),suffix:h().createElement(h().Fragment,null,h().createElement(d.Tooltip,{content:a,placement:"top"},h().createElement(d.Tag,{className:t.counts,name:n,colorIndex:9})),h().createElement(d.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:e.clear,disabled:!r}))})}));const bt=e=>({counts:o.css`
    margin-right: ${e.spacing(1)};
    border-radius: 11px;
    padding: 2px ${e.spacing(1)};
    color: ${e.colors.text.primary};
    background-color: ${e.colors.background.secondary};
  `});function yt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}class wt extends u.P1{constructor(e){super(vt(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){yt(e,t,r[t])}))}return e}({},e),{key:"list-controls",body:new u.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new u.vA({body:new gt}),new u.vA({width:"auto",body:new pe({})}),new u.vA({width:"auto",body:new Xe})]})}))}}function Ot(){return{headerWrapper:(0,o.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}function St(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}yt(wt,"Component",(({model:e})=>{const t=(0,d.useStyles2)(Ot),{body:r}=e.useState();return h().createElement("div",{className:t.headerWrapper},h().createElement(r.Component,{model:r}))}));class kt{setInitOptions(e){this.initOptions=(0,_.cloneDeep)(e)}getFilters(){return this.filters}static getFilteredOptions(e,t){let r=e;return t.categories.length>0&&(r=kt.applyCategoryFilters(r,t.categories)),t.prefixes.length>0&&(r=kt.applyPrefixFilters(r,t.prefixes)),t.suffixes.length>0&&(r=kt.applySuffixFilters(r,t.suffixes)),t.names.length>0&&(r=kt.applyNameFilters(r,t.names)),r}applyFilters(e=this.filters,t={forceUpdate:!1,notify:!0}){const r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){St(e,t,r[t])}))}return e}({},this.filters,e);if(!t.forceUpdate&&(0,_.isEqual)(this.filters,r))return;if(!(r.categories.length||r.prefixes.length||r.suffixes.length||r.names.length))return this.filters=r,this.variable.setState({options:this.initOptions}),void(t.notify&&this.notifyUpdate());this.filters=r;const n=kt.getFilteredOptions(this.initOptions,this.filters);this.variable.setState({options:n}),t.notify&&this.notifyUpdate()}static applyCategoryFilters(e,t){let r=[];for(const n of t){const t=kt.buildRegex(n,"i");r=r.concat(e.filter((e=>t.test(e.value))))}return r}static applyPrefixFilters(e,t){const r=t.map((e=>e.includes("|")?`${e.split("|").map((e=>`^${e}([^a-z0-9]|$)`)).join("|")}`:`^${e}([^a-z0-9]|$)`)).join("|"),n=kt.buildRegex(`(${r})`);return e.filter((e=>n.test(e.value)))}static applySuffixFilters(e,t){const r=t.map((e=>e.includes("|")?`${e.split("|").map((e=>`(^|[^a-z0-9])${e}$`)).join("|")}`:`(^|[^a-z0-9])${e}$`)).join("|"),n=kt.buildRegex(`(${r})`);return e.filter((e=>n.test(e.value)))}static applyNameFilters(e,t){const[r]=t,n=r.split(",").map((e=>e.trim())).filter(Boolean).map((e=>{try{return new RegExp(e)}catch(e){return null}})).filter(Boolean);return e.filter((e=>n.some((t=>t.test(e.value)))))}static buildRegex(e,t){try{return new RegExp(e,t)}catch(e){return new RegExp(".*")}}notifyUpdate(){this.variable.publishEvent(new u.oh(this.variable),!0)}constructor(e){St(this,"variable",void 0),St(this,"initOptions",[]),St(this,"filters",{categories:[],prefixes:[],suffixes:[],names:[]}),this.variable=e}}function Et(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function xt(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Et(i,n,a,o,s,"next",e)}function s(e){Et(i,n,a,o,s,"throw",e)}o(void 0)}))}}function jt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class _t{sort(e=this.sortBy){var t=this;return xt((function*(){const r=t.variable.state.options.map((e=>e.value));if(e===t.sortBy&&(n=r,a=t.lastMetrics,n.length===a.length&&(0,_.isEqual)(n,a)))return;var n,a;let i;switch(e){case"dashboard-usage":case"alerting-usage":i=yield t.sortByUsage(r,e);break;default:i=he(r)}t.sortBy=e,t.lastMetrics=i,t.variable.setState({options:i.map((e=>({label:e,value:e})))}),t.notifyUpdate()}))()}sortByUsage(e,t){var r=this;return xt((function*(){try{const n=u.jh.findByKeyAndType(r.variable,"metrics-sorter",pe);if(!n)return q.v.warn("Metrics sorter not found. Returning unsorted metrics.",{usageType:t}),e;const a=yield n.getUsageMetrics(t);return function(e,t){return[...e].sort(((e,r)=>{const n=t[e]||0,a=t[r]||0;return a!==n?a-n:D(e,r)}))}(e,a)}catch(r){const n="string"==typeof r?new Error(r):r;return q.v.error(n,{usageType:t}),e}}))()}notifyUpdate(){this.variable.publishEvent(new u.oh(this.variable),!0)}constructor(e){jt(this,"variable",void 0),jt(this,"lastMetrics",void 0),jt(this,"sortBy",void 0),this.variable=e,this.sortBy=void 0,this.lastMetrics=[]}}class Pt extends s.BusEventWithPayload{}function Ct(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Pt,"type","apply-function");class Lt extends u.Bs{constructor({metricName:e,prometheusFunction:t,disabled:r}){super({key:`apply-action-${e}`,metricName:e,prometheusFunction:t,disabled:Boolean(r)}),Ct(this,"onClick",(e=>{const{metricName:t,prometheusFunction:r}=this.state;e.preventDefault(),this.publishEvent(new Pt({metricName:t,prometheusFunction:r}),!0)}))}}Ct(Lt,"Component",(({model:e})=>{const t=(0,d.useStyles2)(Bt),{disabled:r}=e.useState();return h().createElement(d.Button,{variant:"primary",fill:"outline",size:"sm",className:t.selectButton,onClick:e.onClick,disabled:r},"Apply")}));const Bt=()=>({selectButton:o.css``});class Nt extends s.BusEventWithPayload{}function Dt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Nt,"type","configure-function");class Tt extends u.Bs{constructor({metricName:e}){super({key:`configure-action-${e}`,metricName:e}),Dt(this,"onClick",(()=>{this.publishEvent(new Nt({metricName:this.state.metricName}),!0)}))}}Dt(Tt,"PROMETHEUS_FN_OPTIONS",[{label:"Average",value:"avg"},{label:"Sum",value:"sum"},{label:"Minimum",value:"min"},{label:"Maximum",value:"max"},{label:"Rate",value:"rate"}]),Dt(Tt,"Component",(({model:e})=>{const t=(0,d.useStyles2)(At);return h().createElement(d.Button,{className:t.selectButton,"aria-label":"Configure",variant:"secondary",size:"sm",fill:"text",onClick:e.onClick,icon:"cog",tooltip:"Configure the Prometheus function",tooltipPlacement:"top"})}));const At=()=>({selectButton:o.css`
    margin: 0;
    padding: 0;
  `});function Rt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Rt(e,t,r[t])}))}return e}function Mt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}class Vt extends u.Bs{constructor(e){super($t({key:"drawer",isOpen:!1},e)),Rt(this,"open",(({title:e,subTitle:t,body:r})=>{this.setState(Mt($t({},this.state),{isOpen:!0,title:e,subTitle:t,body:r}))})),Rt(this,"close",(()=>{this.setState({isOpen:!1})}))}}Rt(Vt,"Component",(({model:e})=>{const{isOpen:t,title:r,subTitle:n,body:a}=e.useState();return h().createElement(h().Fragment,null,a&&t&&h().createElement(d.Drawer,{size:"lg",title:r,subtitle:n,closeOnMaskClick:!0,onClose:e.close},h().createElement(a.Component,{model:a})))}));class Ft extends s.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Ft,"type","filters-changed");class Ht extends s.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Ht,"type","filters-changed");const It="Non-rules metrics",zt="Recording rules";class Ut extends s.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Ut,"type","section-value-changed");var Gt=r(1053);const qt=({label:e,count:t,checked:r,onChange:n})=>{const a=(0,d.useStyles2)(Wt);return h().createElement("div",{className:a.checkboxWrapper,title:e},h().createElement(d.Checkbox,{label:e,value:r,onChange:n}),h().createElement("span",{className:a.count},"(",t,")"))};function Wt(e){return{checkboxWrapper:(0,o.css)({display:"flex",alignItems:"center",width:"100%","& label *":{fontSize:"14px !important",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),count:(0,o.css)({color:e.colors.text.secondary,marginLeft:e.spacing(.5),display:"inline-block"})}}function Kt({groups:e,selectedGroups:t,onSelectionChange:r}){const n=(0,d.useStyles2)(Qt);return h().createElement(h().Fragment,null,h().createElement("div",{className:n.checkboxListHeader},h().createElement("div",null,t.length," selected"),h().createElement(d.Button,{variant:"secondary",fill:"text",onClick:()=>r([]),disabled:!t.length},"clear")),!e.length&&h().createElement("div",{className:n.noResults},"No results."),e.length>0&&h().createElement("ul",{className:n.checkboxList,"data-testid":"checkbox-filters-list"},e.map((e=>h().createElement("li",{key:e.value,className:n.checkboxItem},h().createElement(qt,{label:e.label,count:e.count,checked:t.some((t=>t.value===e.value)),onChange:n=>{const a=n.currentTarget.checked?[...t,{label:e.label,value:e.value}]:t.filter((t=>t.value!==e.value));r(a)}}))))))}function Qt(e){return{checkboxListHeader:(0,o.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),checkboxList:(0,o.css)({height:"100%",margin:0,padding:e.spacing(0,1,1,1),overflowY:"auto","& .css-1n4u71h-Label":{fontSize:"14px !important"},"&::-webkit-scrollbar":{"-webkit-appearance":"none",width:"7px"},"&::-webkit-scrollbar-thumb":{borderRadius:"4px",backgroundColor:e.colors.secondary.main,"-webkit-box-shadow":`0 0 1px ${e.colors.secondary.shade}`}}),checkboxItem:(0,o.css)({display:"flex",alignItems:"center",width:"100%",padding:e.spacing(.5,0)}),noResults:(0,o.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function Yt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Yt(e,t,r[t])}))}return e}function Xt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}class Zt extends u.Bs{getUrlState(){return{[this.state.key]:this.state.selectedGroups.map((e=>e.value)).join(",")}}updateFromUrl(e){const t={};"string"==typeof e[this.state.key]&&e[this.state.key]!==this.state.selectedGroups.map((e=>e.value)).join(",")&&(t.selectedGroups=e[this.state.key].split(",").map((e=>({label:e,value:e})))),this.setState(t)}onActivate(){const e=u.jh.lookupVariable(ge,this),t=u.jh.lookupVariable(Se,this);this.updateLists(e.state.options),this.updateCounts();const{selectedGroups:r}=this.state;this.setState({loading:t.state.loading,active:r.length>0})}updateLists(e){this.setState({groups:this.state.computeGroups(e),loading:!1})}updateCounts(){var e;const{groups:t,computeGroups:r,type:n}=this.state,a=u.jh.lookupVariable(ge,this).state.options,i=null===(e=u.jh.getAncestor(this,wr).state.enginesMap.get(Se))||void 0===e?void 0:e.filterEngine;if(!i)return void q.v.warn("MetricsFilterSection: No filter engine found");const o=Xt(Jt({},i.getFilters()),{[n]:[]}),s=kt.getFilteredOptions(a,o),l=new Map(r(s).map((e=>[e.label,e.count]))),c=t.map((e=>{var t;return Xt(Jt({},e),{count:null!==(t=l.get(e.label))&&void 0!==t?t:0})}));this.setState({groups:c,loading:!1})}constructor({key:e,type:t,title:r,description:n,icon:a,computeGroups:i,showHideEmpty:o,showSearch:s,disabled:l,active:c}){super({key:e,type:t,title:r,description:n,icon:a,groups:[],computeGroups:i,selectedGroups:[],loading:!0,showHideEmpty:null==o||o,showSearch:null==s||s,disabled:null!=l&&l,active:null!=c&&c}),Yt(this,"_variableDependency",new u.Sh(this,{variableNames:[ge,Se],onReferencedVariableValueChanged:e=>{const{name:t,options:r}=e.state;t!==ge?t===Se&&this.updateCounts():this.updateLists(r)}})),Yt(this,"_urlSync",new u.So(this,{keys:[this.state.key]})),Yt(this,"onSelectionChange",(e=>{this.setState({selectedGroups:e,active:e.length>0}),this.publishEvent(new Ht({type:this.state.type,filters:e.map((e=>e.value))}),!0),this.publishEvent(new Ut({key:this.state.key,values:e.map((e=>e.label))}),!0),"prefixes"===this.state.type&&(0,P.z)("sidebar_prefix_filter_applied",{filter_count:e.length}),"suffixes"===this.state.type&&(0,P.z)("sidebar_suffix_filter_applied",{filter_count:e.length}),"filters-rule"===this.state.key&&e.length>0&&e.forEach((e=>{let t;switch(e.label){case It:t="non_rules_metrics";break;case zt:t="recording_rules";break;default:return}(0,P.z)("sidebar_rules_filter_selected",{filter_type:t})}))})),this.addActivationHandler(this.onActivate.bind(this))}}function er(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),switchContainer:(0,o.css)({display:"flex",alignItems:"center",justifyContent:"flex-end",gap:e.spacing(1)}),switchLabel:(0,o.css)({fontSize:"12px",color:e.colors.text.primary}),searchInput:(0,o.css)({flexBasis:"32px",flexShrink:0,marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}Yt(Zt,"Component",(({model:e})=>{const t=(0,d.useStyles2)(er),{groups:r,selectedGroups:n,loading:a,title:i,description:o,showHideEmpty:s,showSearch:l}=e.useState(),[c,u]=(0,p.useState)(!1),[m,f]=(0,p.useState)(""),g=(0,p.useMemo)((()=>{const e=[];return c&&e.push((e=>e.count>0)),e.push((e=>e.label.toLowerCase().includes(m.toLowerCase()))),r.filter((t=>e.every((e=>e(t)))))}),[c,r,m]);return h().createElement("div",{className:t.container},h().createElement(Gt._,{title:i,description:o}),s&&h().createElement("div",{className:t.switchContainer},h().createElement("span",{className:t.switchLabel},"Hide empty"),h().createElement(d.Switch,{value:c,onChange:e=>u(e.currentTarget.checked)})),l&&h().createElement(d.Input,{className:t.searchInput,prefix:h().createElement(d.Icon,{name:"search"}),placeholder:"Search...",value:m,onChange:e=>f(e.currentTarget.value),onKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),f(""))},suffix:h().createElement(d.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:e=>f("")})}),a&&h().createElement(d.Spinner,{inline:!0}),!a&&h().createElement(Kt,{groups:g,selectedGroups:n,onSelectionChange:e.onSelectionChange}))}));function tr(e){const t=new Map;for(const n of e){const e=n.value.split(/[^a-z0-9]/i),a=e.length<=1?n.value:e[0];var r;const i=null!==(r=t.get(a))&&void 0!==r?r:[];i.push(n.value),t.set(a||"<none>",i)}const n=new Map;for(const[e,r]of t)n.set(e,r.length);return Array.from(n.entries()).sort(((e,t)=>e[1]!==t[1]?t[1]-e[1]:D(e[0],t[0]))).map((([e,t])=>({value:e,count:t,label:e})))}function rr(e){const t=new Map;for(const n of e){const e=n.value.split(/[^a-z0-9]/i),a=e.length<=1?n.value:e[e.length-1];var r;const i=null!==(r=t.get(a))&&void 0!==r?r:[];i.push(n.value),t.set(a||"<none>",i)}const n=new Map;for(const[e,r]of t)n.set(e,r.length);return Array.from(n.entries()).sort(((e,t)=>e[1]!==t[1]?t[1]-e[1]:D(e[0],t[0]))).map((([e,t])=>({value:e,count:t,label:e})))}function nr(e){const t=new Map([["metrics",[]],["rules",[]]]);for(const n of e){const{value:e}=n,a=/:/i.test(e)?"rules":"metrics";var r;const i=null!==(r=t.get(a))&&void 0!==r?r:[];i.push(e),t.set(a,i)}return[{value:"^(?!.*:.*)",label:It,count:t.get("metrics").length},{value:":",label:zt,count:t.get("rules").length}]}var ar=r(5521);function ir({labels:e,selectedLabel:t,onClickLabel:r,onClickClearSelection:n}){const a=(0,d.useStyles2)(or);return h().createElement(h().Fragment,null,h().createElement("div",{className:a.listHeader},h().createElement("div",{className:a.selected},t===R?"No selection":`Selected: "${t}"`),h().createElement(d.Button,{variant:"secondary",fill:"text",onClick:n,disabled:t===R},"clear")),!e.length&&h().createElement("div",{className:a.noResults},"No results."),e.length>0&&h().createElement("div",{className:a.list,"data-testid":"labels-list"},h().createElement(d.RadioButtonList,{name:"labels-list",options:e,onChange:r,value:t})))}function or(e){return{listHeader:(0,o.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),selected:(0,o.css)({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),list:(0,o.css)({display:"flex",flex:1,flexDirection:"column",gap:0,overflowY:"auto",'& [role="radiogroup"]':{gap:0},"& label":{cursor:"pointer",padding:e.spacing(.5,1),"&:hover":{background:e.colors.background.secondary}},"& label div":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),noResults:(0,o.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function sr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class lr extends u.Bs{onActivate(){const e=u.jh.lookupVariable(this.state.variableName,this).state.value;this.setState({active:Boolean(e&&e!==R)})}selectLabel(e){u.jh.lookupVariable(this.state.variableName,this).changeValueTo(e);const t=Boolean(e&&e!==R);this.setState({active:t}),this.publishEvent(new Ut({key:this.state.key,values:t?[e]:[]}),!0)}constructor({key:e,variableName:t,title:r,description:n,icon:a,disabled:i,active:o}){super({key:e,variableName:t,title:r,description:n,icon:a,disabled:null!=i&&i,active:null!=o&&o}),sr(this,"onClickLabel",(e=>{this.selectLabel(e),(0,P.z)("sidebar_group_by_label_filter_applied",{label:e})})),sr(this,"onClickClearSelection",(()=>{this.selectLabel(R)})),sr(this,"useLabelsBrowser",(()=>{const{variableName:e,title:t,description:r}=this.useState(),n=u.jh.lookupVariable(e,this),{loading:a,options:i,value:o}=n.useState(),[s,l]=(0,p.useState)("");return{title:t,description:r,loading:a,selectedLabel:o,labelsList:(0,p.useMemo)((()=>{const e=[e=>e!==R,e=>e.toLowerCase().includes(s.toLowerCase())];return i.filter((t=>e.every((e=>e(t.value)))))}),[i,s]),searchValue:s,onInputChange:e=>{l(e.currentTarget.value)},onInputKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),l(""))},onInputClear:()=>{l("")}}})),this.addActivationHandler(this.onActivate.bind(this))}}function cr(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),search:(0,o.css)({marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}sr(lr,"Component",(({model:e})=>{const t=(0,d.useStyles2)(cr),{title:r,description:n,loading:a,labelsList:i,selectedLabel:o,searchValue:s,onInputChange:l,onInputKeyDown:c,onInputClear:u}=e.useLabelsBrowser();return h().createElement("div",{className:t.container,"data-testid":"labels-browser"},h().createElement(Gt._,{title:r,description:n}),h().createElement(d.Input,{className:t.search,prefix:h().createElement(d.Icon,{name:"search"}),placeholder:"Search...",value:s,onChange:l,onKeyDown:c,suffix:h().createElement(d.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:u})}),a&&h().createElement(d.Spinner,{inline:!0}),!a&&h().createElement(ir,{labels:i,selectedLabel:o,onClickLabel:e.onClickLabel,onClickClearSelection:e.onClickClearSelection}))}));class ur extends u.Bs{onActivate(){}constructor({key:e,title:t,description:r,icon:n,disabled:a}){super({key:e,title:t,description:r,icon:n,disabled:null!=a&&a,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}function dr(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(ur,"Component",(({model:e})=>{const t=(0,d.useStyles2)(dr),{title:r,description:n}=e.useState();return h().createElement("div",{className:t.container},h().createElement(Gt._,{title:r,description:n}))}));const pr=new Map([["rules",function(){return h().createElement("svg",{stroke:"currentColor",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},h().createElement("rect",{x:"1.25",y:"1.625",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}),h().createElement("circle",{cx:"12.25",cy:"4.25",r:"2.75",strokeWidth:"1.5"}),h().createElement("circle",{cx:"3.75",cy:"11.75",r:"2.75",strokeWidth:"1.5"}),h().createElement("rect",{x:"9.5",y:"9.125",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}))}],["groups",rt]]);function hr({ariaLabel:e,disabled:t,visible:r,active:n,tooltip:a,iconOrText:i,onClick:l}){const c=(0,d.useStyles2)(mr);let u,p;return i in s.availableIconsIndex?u=i:p=pr.has(i)?pr.get(i):function(){return h().createElement(h().Fragment,null,i)},h().createElement(d.Button,{className:(0,o.cx)(c.button,t&&"disabled",r&&"visible",n&&"active"),size:"md",variant:"secondary",fill:"text",icon:u,"aria-label":e,tooltip:a,tooltipPlacement:"right",onClick:l,disabled:t},p&&h().createElement(p,null))}function mr(e){return{button:(0,o.css)({margin:0,color:e.colors.text.secondary,"&:hover":{color:e.colors.text.maxContrast,background:"transparent"},"&.disabled:hover":{color:e.colors.text.secondary},"&.visible":{color:e.colors.text.maxContrast},"&.active":{color:e.colors.text.maxContrast}})}}function fr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const gr=["filters-rule","filters-prefix","filters-suffix"];class br extends u.Bs{onActivate(){const e=this.initOtherMetricsVar();return this._subs.add(this.subscribeToEvent(Ut,(e=>{const{key:t,values:r}=e.payload,{sectionValues:n}=this.state,a=new Map(n).set(t,r);this.setOtherMetricFilters(a),this.setState({sectionValues:a})}))),()=>{e()}}setOtherMetricFilters(e){const t=u.jh.lookupVariable(m.hc,this);if(!(0,N.BE)(t))return;const r={"filters-rule":"rule group","filters-prefix":"prefix","filters-suffix":"suffix"},n=Array.from(e.entries()).reduce(((e,[t,n])=>(n.length&&gr.includes(t)&&e.push({key:t,operator:"=",value:n.join(", "),keyLabel:r[t]}),e)),[]);t.setState({filters:n,hide:n.length?s.VariableHide.hideLabel:s.VariableHide.hideVariable})}initOtherMetricsVar(){const e=(0,C.kj)(this).state.$variables;if(!e)return()=>{};const t=new u.H9({name:m.hc,readOnly:!0,skipUrlSync:!0,datasource:null,hide:s.VariableHide.hideVariable,layout:"combobox",applyMode:"manual",allowCustomValue:!0});return e.setState({variables:[...e.state.variables,t]}),this.setOtherMetricFilters(this.state.sectionValues),()=>{e.setState({variables:[...e.state.variables.filter((e=>e!==t))]})}}static getSectionValuesFromUrl(){const e=new URLSearchParams(window.location.search),t=new Map;for(const r of gr){const n=e.get(r);t.set(r,n?n.split(",").map((e=>e.trim())):[])}const r=e.get(`var-${We}`);return Boolean(r&&r!==R)&&t.set("groupby-labels",[r]),t}setActiveSection(e){const{visibleSection:t,sections:r}=this.state;if(!e||e===(null==t?void 0:t.state.key))return(0,P.z)("metrics_sidebar_toggled",{action:"closed",section:null==t?void 0:t.state.key}),void this.setState({visibleSection:null});var n;(0,P.z)("metrics_sidebar_toggled",{action:"opened",section:e}),this.setState({visibleSection:null!==(n=r.find((t=>t.state.key===e)))&&void 0!==n?n:null}),"filters-prefix"===e&&(0,P.z)("sidebar_prefix_filter_section_clicked",{}),"filters-suffix"===e&&(0,P.z)("sidebar_suffix_filter_section_clicked",{})}constructor(e){var t,r,n;const a=br.getSectionValuesFromUrl();super(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){fr(e,t,r[t])}))}return e}({key:"sidebar",visibleSection:null,sections:[new Zt({key:"filters-rule",type:"categories",title:"Rules filters",description:"Filter metrics and recording rules",icon:"rules",computeGroups:nr,showHideEmpty:!1,showSearch:!1,active:Boolean(null===(t=a.get("filters-rule"))||void 0===t?void 0:t.length)}),new Zt({key:"filters-prefix",type:"prefixes",title:"Prefix filters",description:"Filter metrics based on their name prefix (Prometheus namespace)",icon:"A_",computeGroups:tr,active:Boolean(null===(r=a.get("filters-prefix"))||void 0===r?void 0:r.length)}),new Zt({key:"filters-suffix",type:"suffixes",title:"Suffix filters",description:"Filter metrics based on their name suffix",icon:"_Z",computeGroups:rr,active:Boolean(null===(n=a.get("filters-suffix"))||void 0===n?void 0:n.length)}),new lr({key:"groupby-labels",variableName:We,title:"Group by labels",description:"Group metrics by their label values",icon:"groups",active:a.has("groupby-labels")}),new ar.O({key:"bookmarks",title:"Bookmarks",description:"Access your saved metrics for quick reference",icon:"star"}),new ur({key:"settings",title:"Settings",description:"Settings",icon:"cog",disabled:!0})],sectionValues:a},e)),a.set("filters-rule",[]),this.addActivationHandler(this.onActivate.bind(this))}}function yr(e){return{container:(0,o.css)({position:"relative",display:"flex",flexDirection:"row",height:"100%",overflow:"hidden"}),buttonsBar:(0,o.css)({display:"flex",flexDirection:"column",alignItems:"center",gap:0,width:"42px",padding:0,margin:0,boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.primary,borderTopLeftRadius:0,borderBottomLeftRadius:0,position:"relative"}),buttonContainer:(0,o.css)({marginTop:e.spacing(1),"&::before":{transition:"0.5s ease",content:'""',position:"absolute",left:0,height:"32px",borderLeft:`2px solid ${e.colors.action.selectedBorder}`,boxSizing:"border-box",opacity:0,visibility:"hidden"},"&:hover::before":{opacity:1,visibility:"visible"},"&.visible::before":{opacity:1,visibility:"visible"},"&.disabled::before":{opacity:0,visibility:"hidden"},"&.active::after":{content:'""',position:"absolute",right:0,width:"8px",height:"8px",backgroundColor:e.colors.action.selectedBorder,borderRadius:"50%",margin:"2px 4px 0 0"}}),content:(0,o.css)({width:"calc(300px - 42px)",boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderLeft:"none",borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.canvas,padding:e.spacing(1.5)}),closeButton:(0,o.css)({position:"absolute",top:e.spacing(1.5),right:e.spacing(1),margin:0})}}function vr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}fr(br,"Component",(({model:e})=>{const t=(0,d.useStyles2)(yr),{sections:r,visibleSection:n,sectionValues:a}=e.useState();return h().createElement("div",{className:t.container},h().createElement("div",{className:t.buttonsBar,"data-testid":"sidebar-buttons"},r.map((r=>{var i,s;const{key:l,title:c,icon:u,disabled:d,active:p}=r.state,m=(null==n?void 0:n.state.key)===l,f=(null===(i=a.get(l))||void 0===i?void 0:i.length)?`${c}: ${null===(s=a.get(l))||void 0===s?void 0:s.join(", ")}`:c;return h().createElement("div",{key:l,className:(0,o.cx)(t.buttonContainer,m&&"visible",p&&"active",d&&"disabled")},h().createElement(hr,{ariaLabel:c,disabled:d,visible:m,active:p,tooltip:f,onClick:()=>e.setActiveSection(l),iconOrText:u}))}))),n&&h().createElement("div",{className:t.content,"data-testid":"sidebar-content"},h().createElement(d.IconButton,{className:t.closeButton,name:"times","aria-label":"Close",tooltip:"Close",tooltipPlacement:"top",onClick:()=>e.setActiveSection("")}),n instanceof Zt&&h().createElement(n.Component,{model:n}),n instanceof lr&&h().createElement(n.Component,{model:n}),n instanceof ar.O&&h().createElement(n.Component,{model:n}),n instanceof ur&&h().createElement(n.Component,{model:n})))}));class wr extends u.Bs{onActivate(){this.updateBodyBasedOnGroupBy(u.jh.lookupVariable(We,this).state.value),this.subscribeToEvents()}subscribeToEvents(){this.initVariablesFilteringAndSorting(),this._subs.add(this.subscribeToEvent(Nt,(e=>{this.openDrawer(e.payload.metricName)}))),this._subs.add(this.subscribeToEvent(Pt,(()=>{this.state.drawer.close()}))),this._subs.add(this.subscribeToEvent(m.OO,(e=>{void 0!==e.payload&&function(e){try{const t=ce(),r=Date.now(),n=t.filter((t=>t.name!==e));n.unshift({name:e,timestamp:r});const a=n.slice(0,6);localStorage.setItem(le,JSON.stringify(a))}catch(t){const r=t instanceof Error?t:new Error(String(t));q.v.error(r,se(oe({},r.cause||{}),{metricName:e}))}}(e.payload)})))}initVariablesFilteringAndSorting(){this._subs.add(this.subscribeToEvent(ye,(e=>{const{key:t}=e.payload,r=u.jh.findByKey(this,t);this.state.enginesMap.set(t,{filterEngine:new kt(r),sortEngine:new _t(r)})}))),this._subs.add(this.subscribeToEvent(ve,(e=>{this.state.enginesMap.delete(e.payload.key)})));const e=u.jh.findByKeyAndType(this,"quick-search",gt),t=u.jh.findAllObjects(this,(e=>e instanceof Zt)),r=u.jh.findByKeyAndType(this,"metrics-sorter",pe).state.$variables.getByName(de);this._subs.add(this.subscribeToEvent(we,(n=>{const{key:a,options:i}=n.payload,{filterEngine:o,sortEngine:s}=this.state.enginesMap.get(a);o.setInitOptions(i);const l={names:e.state.value?[e.state.value]:[]};for(const e of t)l[e.state.type]=e.state.selectedGroups.map((e=>e.value));o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort(r.state.value)}))),this._subs.add(this.subscribeToEvent(pt,(e=>{const{searchText:t}=e.payload;for(const[,{filterEngine:e,sortEngine:n}]of this.state.enginesMap)e.applyFilters({names:t?[t]:[]}),n.sort(r.state.value);this._debounceReportQuickSearchChange(t)}))),this._subs.add(this.subscribeToEvent(Ft,(e=>{const{type:t,filters:n}=e.payload;for(const[,{filterEngine:e,sortEngine:a}]of this.state.enginesMap)e.applyFilters({[t]:n}),a.sort(r.state.value)}))),this._subs.add(this.subscribeToEvent(z,(e=>{const{sortBy:t}=e.payload;for(const[,{sortEngine:e}]of this.state.enginesMap)e.sort(t);(0,P.z)("sorting_changed",{from:"metrics-reducer",sortBy:t})})))}updateBodyBasedOnGroupBy(e){this.setState({body:e&&e!==R?new ut({labelName:e}):new et})}openDrawer(e){const t=(0,C.kj)(this);this.state.drawer.open({title:"Choose a new Prometheus function",subTitle:e,body:new u.gF({templateColumns:Ze,autoRows:Me,isLazy:!0,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],children:Tt.PROMETHEUS_FN_OPTIONS.map(((r,n)=>new u.xK({body:new Fe({title:r.label,metricName:e,color:(0,C.Vy)(n),prometheusFunction:r.value,height:Me,hideLegend:!0,highlight:1===n,isNativeHistogram:t.isNativeHistogram(e),headerActions:[new Lt({metricName:e,prometheusFunction:r.value,disabled:1===n})]})})))})})}constructor(){super({$variables:new u.Pj({variables:[new be,new ke,new Ke]}),listControls:new wt({}),sidebar:new br({}),body:new et,drawer:new Vt({}),enginesMap:new Map}),vr(this,"_variableDependency",new u.Sh(this,{variableNames:[We],onReferencedVariableValueChanged:e=>{this.updateBodyBasedOnGroupBy(e.state.value)}})),vr(this,"_debounceReportQuickSearchChange",(0,_.debounce)((e=>{e&&(0,P.z)("quick_search_used",{})}),1e3)),function(e){try{for(const t of e)(0,u.pY)({dataSource:t})}catch(e){const{message:t}=e;/A runtime data source with uid (.+) has already been registered/.test(t)||(0,j.jx)(e,["Fail to register all the runtime data sources!","The application cannot work as expected, please try reloading the page or if the problem persists, contact your organization admin."])}}([new $,new it]),this.addActivationHandler(this.onActivate.bind(this))}}vr(wr,"Component",(({model:e})=>{var t;const r=null!==(t=(0,c.useChromeHeaderHeight)())&&void 0!==t?t:0,n=(0,d.useStyles2)(Sr,r),{$variables:a,body:i,listControls:o,drawer:s,sidebar:l}=e.useState();return h().createElement(h().Fragment,null,h().createElement("div",{className:n.listControls,"data-testid":"list-controls"},h().createElement(o.Component,{model:o})),h().createElement("div",{className:n.body},h().createElement("div",{className:n.sidebar,"data-testid":"sidebar"},h().createElement(l.Component,{model:l})),h().createElement("div",{className:n.list},h().createElement(i.Component,{model:i}))),h().createElement("div",{className:n.variables},null==a?void 0:a.state.variables.map((e=>h().createElement(e.Component,{key:e.state.name,model:e})))),h().createElement(s.Component,{model:s}))}));const Or=144;function Sr(e,t){return{listControls:(0,o.css)({marginBottom:e.spacing(1.5)}),body:(0,o.css)({display:"flex",flexDirection:"row",gap:e.spacing(1),height:`calc(100vh - ${t+Or}px)`}),list:(0,o.css)({width:"100%",overflowY:"auto"}),sidebar:(0,o.css)({flex:"0 0 auto",overflowY:"auto"}),variables:(0,o.css)({display:"none"})}}var kr=r(4137),Er=r(203),xr=r(1385);function jr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class _r extends u.Bs{constructor(e){var t,r;super({stickyMainGraph:null===(t=e.stickyMainGraph)||void 0===t||t,isOpen:null!==(r=e.isOpen)&&void 0!==r&&r}),jr(this,"onToggleStickyMainGraph",(()=>{const e=!this.state.stickyMainGraph;(0,P.z)("settings_changed",{stickyMainGraph:e}),this.setState({stickyMainGraph:e})})),jr(this,"onToggleOpen",(e=>{this.setState({isOpen:e})}))}}function Pr(e){return{popover:(0,o.css)({display:"flex",padding:e.spacing(2),flexDirection:"column",background:e.colors.background.primary,boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default,border:`1px solid ${e.colors.border.weak}`,zIndex:1,marginRight:e.spacing(2)}),heading:(0,o.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,o.css)({display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1),columnGap:e.spacing(2)})}}jr(_r,"Component",(({model:e})=>{const{stickyMainGraph:t,isOpen:r}=e.useState(),n=(0,d.useStyles2)(Pr),a=(0,C.kj)(e),{topScene:i}=a.useState();if(!(i instanceof Er.t||i instanceof xr.Rd))return null;return h().createElement(d.Dropdown,{overlay:()=>h().createElement("div",{className:n.popover,onClick:e=>e.stopPropagation()},h().createElement("div",{className:n.heading},"Settings"),i instanceof xr.Rd&&h().createElement("div",{className:n.options},h().createElement("div",null,"Always keep selected metric graph in-view"),h().createElement(d.Switch,{value:t,onChange:e.onToggleStickyMainGraph}))),placement:"bottom",onVisibleChange:e.onToggleOpen},h().createElement(d.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:r,"data-testid":"settings-button"}))}));var Cr=r(8371),Lr=r(6099),Br=r(2425),Nr=r(9646),Dr=r(2188);function Tr(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function Ar(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Tr(i,n,a,o,s,"next",e)}function s(e){Tr(i,n,a,o,s,"throw",e)}o(void 0)}))}}function Rr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Rr(e,t,r[t])}))}return e}function Mr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}class Vr extends u.Bs{_onActivate(){this.setState({trailActivated:!0}),this.state.topScene||this.setState({topScene:Hr(this.state.metric)}),this.subscribeToEvent(m.OO,this._handleMetricSelectedEvent.bind(this));const e=u.jh.lookupVariable(m.Ao,this);(0,N.BE)(e)&&this._subs.add(null==e?void 0:e.subscribeToState(((e,t)=>{this._addingFilterWithoutReportingInteraction||(0,P.h)(e.filters,t.filters)})));"gdev-prometheus"===u.jh.interpolate(this,m.gR)&&this.checkDataSourceForOTelResources();const t=u.jh.lookupVariable(m.mW,this),r=u.jh.lookupVariable(m.DU,this),n=u.jh.lookupVariable(m.Xt,this);(0,N.BE)(t)&&(0,N.BE)(r)&&(0,N.BE)(e)&&(0,N.DJ)(n)&&this._subs.add(null==t?void 0:t.subscribeToState(((t,a)=>{if(this.state.useOtelExperience&&this.state.initialOtelCheckComplete&&!this.state.addingLabelFromBreakdown){var i;const o=null!==(i=this.state.nonPromotedOtelResources)&&void 0!==i?i:[];(0,Lr.mc)(t.filters,a.filters,o,r,e);const s=(0,Lr.Dz)(this);n.setState({value:(0,Lr.L4)(s)})}})));const a=()=>{const e=u.jh.lookupVariable(m.Ao,this),t=(0,N.BE)(e)&&e.state.filters.length>0;(this.state.metric||t)&&(0,Br._r)().setRecentTrail(this)};return window.addEventListener("unload",a),()=>{this.state.embedded||a(),window.removeEventListener("unload",a)}}addFilterWithoutReportingInteraction(e){const t=u.jh.lookupVariable("filters",this),r=u.jh.lookupVariable(m.mW,this);(0,N.BE)(t)&&(0,N.BE)(r)&&(this._addingFilterWithoutReportingInteraction=!0,this.state.useOtelExperience?r.setState({filters:[...r.state.filters,e]}):t.setState({filters:[...t.state.filters,e]}),this._addingFilterWithoutReportingInteraction=!1)}getMetricMetadata(e){return this.datasourceHelper.getMetricMetadata(e)}isNativeHistogram(e){return this.datasourceHelper.isNativeHistogram(e)}initializeHistograms(){var e=this;return Ar((function*(){if(!e.state.histogramsLoaded){try{yield e.datasourceHelper.initializeHistograms()}catch(e){(0,j.HA)(["Error while initializing histograms!"])}e.setState({nativeHistograms:e.listNativeHistograms(),histogramsLoaded:!0})}}))()}listNativeHistograms(){var e;return null!==(e=this.datasourceHelper.listNativeHistograms())&&void 0!==e?e:[]}resetNativeHistograms(){this.setState({histogramsLoaded:!1,nativeHistograms:[]})}getCurrentMetricMetadata(){return this.getMetricMetadata(this.state.metric)}_handleMetricSelectedEvent(e){var t=this;return Ar((function*(){var r;const n=null!==(r=e.payload)&&void 0!==r?r:"";t.state.useOtelExperience&&(yield(0,Lr.KO)(t,n));let a=!1;t.isNativeHistogram(n)&&(a=!0),t._urlSync.performBrowserHistoryAction((()=>{t.setState(t.getSceneUpdatesForNewMetricValue(n,a))}));const i=u.jh.lookupVariable(m.Ao,t);(0,N.BE)(i)&&i.setState({baseFilters:zr(e.payload)})}))()}getSceneUpdatesForNewMetricValue(e,t){const r={};return r.metric=e,r.nativeHistogramMetric=t?"1":"",r.topScene=Hr(e,t),r}getUrlState(){const{metric:e,metricSearch:t,nativeHistogramMetric:r}=this.state;return{metric:e,metricSearch:t,nativeHistogramMetric:r}}updateFromUrl(e){const t={};if("string"==typeof e.metric){if(this.state.metric!==e.metric){let r=!1;"1"===e.nativeHistogramMetric&&(r=!0),Object.assign(t,this.getSceneUpdatesForNewMetricValue(e.metric,r))}}else null==e.metric&&(t.metric=void 0,t.topScene=Fr());"string"==typeof e.metricSearch?t.metricSearch=e.metricSearch:null==e.metric&&(t.metricSearch=void 0),this.setState(t)}checkDataSourceForOTelResources(){var e=this;return Ar((function*(){var t;const r=(0,C.kj)(e),n=null===(t=r.state.$timeRange)||void 0===t?void 0:t.state;if(!n)return;const a=u.jh.interpolate(r,m.gR),i=yield e.fetchOtelResources(a,n),o=(0,x.gO)();e.shouldEnableOtelExperience(i,o)?(0,Lr.qJ)(e,a,n,i.deploymentEnvironments,i.hasOtelResources,i.nonPromotedOtelResources):e.resetOtelExperience(i.hasOtelResources,i.nonPromotedOtelResources)}))()}fetchOtelResources(e,t){return Ar((function*(){const r=yield(0,Cr.a8)(e,t);if(0===r.jobs.length&&0===r.instances.length)return{hasOtelResources:!1,nonPromotedOtelResources:[],previouslyUsedOtelResources:!1};return{otelTargets:r,deploymentEnvironments:yield(0,Cr.wI)(e,t,(0,Dr.f_)()),hasOtelResources:r.jobs.length>0&&r.instances.length>0,previouslyUsedOtelResources:!1,nonPromotedOtelResources:yield(0,Cr.hm)(e,t)}}))()}shouldEnableOtelExperience(e,t){return e.hasOtelResources&&!this.state.startButtonClicked&&(e.previouslyUsedOtelResources||this.state.resettingOtel||!1)&&t}resetOtelExperience(e,t){const r=u.jh.lookupVariable(m.DU,this),n=u.jh.lookupVariable(m.Ao,this),a=u.jh.lookupVariable(m.mW,this),i=u.jh.lookupVariable(m.Xt,this);(0,N.BE)(r)&&(0,N.BE)(n)&&(0,N.BE)(a)&&(0,N.DJ)(i)&&(n.setState({addFilterButtonText:"Add label",label:"Select label",hide:s.VariableHide.hideLabel}),a.setState({filters:[],hide:s.VariableHide.hideVariable}),r.setState({filters:[],defaultKeys:[],hide:s.VariableHide.hideVariable}),i.setState({value:""}),e&&t?this.setState({hasOtelResources:e,isStandardOtel:t.length>0,useOtelExperience:!1,initialOtelCheckComplete:!0,afterFirstOtelCheck:!0}):this.setState({useOtelExperience:!1,initialOtelCheckComplete:!0,afterFirstOtelCheck:!0}))}getQueries(){return u.jh.findAllObjects(this,(e=>(0,Nr.xT)(e))).reduce(((e,t)=>(e.push(...t.state.queries.map((e=>Mr($r({},e),{expr:u.jh.interpolate(t,e.expr)})))),e)),[])}constructor(e){var t,r,n,a,i,o,c,d,p,h,g,b,y,v;super($r({$timeRange:null!==(r=e.$timeRange)&&void 0!==r?r:new u.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:(b=e.initialDS,y=e.metric,v=e.initialFilters,new u.Pj({variables:[new f({initialDS:b}),new u.H9({name:m.DU,label:"Select resource attributes",addFilterButtonText:"Select resource attributes",datasource:m.GH,hide:s.VariableHide.hideVariable,layout:"combobox",defaultKeys:[],applyMode:"manual",allowCustomValue:!0}),new u.H9({key:m.Ao,name:m.Ao,addFilterButtonText:"Add label",datasource:m.GH,hide:s.VariableHide.hideLabel,layout:"combobox",filters:null!=v?v:[],baseFilters:zr(y),applyMode:"manual",allowCustomValue:!0,expressionBuilder:e=>{const t=e.filter((e=>"__name__"!==e.key));return[...zr(y),...t].map((e=>`${(0,l.Nc)(e.key)}${e.operator}"${e.value}"`)).join(",")}}),...(0,m.Mi)(),new u.x0({name:m.lr,value:void 0,hide:s.VariableHide.hideVariable}),new u.x0({name:m.jl,hide:s.VariableHide.hideVariable,value:!1}),new u.H9({name:m.mW,addFilterButtonText:"Filter",datasource:m.GH,hide:s.VariableHide.hideVariable,layout:"combobox",filters:null!=v?v:[],baseFilters:zr(y),applyMode:"manual",allowCustomValue:!0}),new u.yP({name:m.p4,label:"Deployment environment",hide:s.VariableHide.hideVariable,value:void 0,placeholder:"Select",isMulti:!0})]})),controls:null!==(a=e.controls)&&void 0!==a?a:[new u.K8({layout:"vertical"}),new u.N0,new u.KE({}),new u.WM({})],settings:null!==(i=e.settings)&&void 0!==i?i:new _r({}),pluginInfo:new u.dM({component:k}),createdAt:null!==(o=e.createdAt)&&void 0!==o?o:(new Date).getTime(),dashboardMetrics:{},alertingMetrics:{},useOtelExperience:null!==(c=e.useOtelExperience)&&void 0!==c&&c,nativeHistograms:null!==(d=e.nativeHistograms)&&void 0!==d?d:[],histogramsLoaded:null!==(p=e.histogramsLoaded)&&void 0!==p&&p,nativeHistogramMetric:null!==(h=e.nativeHistogramMetric)&&void 0!==h?h:"",trailActivated:null!==(g=e.trailActivated)&&void 0!==g&&g},e)),t=this,Rr(this,"_urlSync",new u.So(this,{keys:["metric","metricSearch","nativeHistogramMetric"]})),Rr(this,"_variableDependency",new u.Sh(this,{variableNames:[m.EY,m.DU,m.Xt,m.mW],onReferencedVariableValueChanged:function(){var e=Ar((function*(e){const{name:r}=e.state;r===m.EY&&(t.datasourceHelper.reset(),t.resetNativeHistograms(),t.state.afterFirstOtelCheck&&(t.setState({initialOtelCheckComplete:!1}),t.resetOtelExperience()),t.checkDataSourceForOTelResources())}));return function(t){return e.apply(this,arguments)}}()})),Rr(this,"_addingFilterWithoutReportingInteraction",!1),Rr(this,"datasourceHelper",new B.q(this)),this.addActivationHandler(this._onActivate.bind(this))}}function Fr(){return(0,C.y)(kr.bw.Drilldown)?new wr:new Er.t({})}function Hr(e,t){return e?new xr.Rd({metric:e,nativeHistogram:null!=t&&t}):Fr()}function Ir(e,t){return{container:(0,o.css)({flexGrow:1,display:"flex",gap:e.spacing(1),flexDirection:"column",background:e.isLight?e.colors.background.primary:e.colors.background.canvas,padding:e.spacing(1,2)}),body:(0,o.css)({flexGrow:1,display:"flex",flexDirection:"column"}),controls:(0,o.css)({display:"flex",gap:e.spacing(1),padding:e.spacing(1,0),alignItems:"flex-end",flexWrap:"wrap",position:"sticky",background:e.isDark?e.colors.background.canvas:e.colors.background.primary,zIndex:e.zIndex.navbarFixed,top:t}),settingsInfo:(0,o.css)({display:"flex",gap:e.spacing(.5)})}}function zr(e){return e?[{key:"__name__",operator:"=",value:e}]:[]}Rr(Vr,"Component",(({model:e})=>{const{controls:t,topScene:r,settings:n,pluginInfo:a,useOtelExperience:i,embedded:o}=e.useState(),s=(0,c.useChromeHeaderHeight)(),l=(0,d.useStyles2)(Ir,o?0:null!=s?s:0);e.initializeHistograms(),(0,p.useEffect)((()=>{const t=u.jh.lookupVariable(m.Ao,e),r=u.jh.lookupVariable(m.mW,e),n=i?r:t,a=e.datasourceHelper;(0,C.FG)(e,n,a)}),[e,i]);const f=(0,p.useRef)(!1);return i&&!f.current&&((0,P.z)("otel_experience_used",{}),f.current=!0),h().createElement("div",{className:l.container},t&&h().createElement("div",{className:l.controls,"data-testid":"app-controls"},t.map((e=>h().createElement(e.Component,{key:e.state.key,model:e}))),h().createElement("div",{className:l.settingsInfo},h().createElement(n.Component,{model:n}),h().createElement(a.Component,{model:a}))),r&&h().createElement(u.$L,{scene:r,createBrowserHistorySteps:!0,updateUrlOnInit:!0},h().createElement("div",{className:l.body},r&&h().createElement(r.Component,{model:r}))))}))},7265:(e,t,r)=>{r.d(t,{G:()=>P});var n=r(8531),a=r(6680),i=r(5959),o=r.n(i),s=r(1269),l=r(2007);const c=r.p+"ac01ecbc64128d2f3e68.svg";var u=r(4169),d=r(2533),p=r(2127),h=r(9646);function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){m(e,t,r[t])}))}return e}function g(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const b=`${d.id}/investigation/v1`;class y extends a.Bs{constructor(e){super(g(f({},e),{queries:[]})),m(this,"_onActivate",(()=>{this._subs.add(this.subscribeToState((()=>{this.getQueries(),this.getContext()})));const e=a.jh.interpolate(this,p.gR);this.setState({dsUid:e})})),m(this,"getQueries",(()=>{const e=a.jh.getData(this),t=a.jh.findObject(e,h.xT);if((0,h.xT)(t)){const e=this.state.frame?v(this.state.frame):null,r=t.state.queries.map((r=>g(f({},r),{expr:a.jh.interpolate(t,r.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:a.jh.interpolate(t,r.legendFormat)})));JSON.stringify(r)!==JSON.stringify(this.state.queries)&&this.setState({queries:r})}})),m(this,"getFieldConfig",(()=>{var e;const t=(0,u.UX)(this,(e=>e instanceof a.Eb),a.Eb),r=a.jh.getData(this),n=null==r||null===(e=r.state.data)||void 0===e?void 0:e.series;let i=null==t?void 0:t.state.fieldConfig;if(i&&(null==n?void 0:n.length))for(const e of n)for(const t of e.fields){const e=Object.keys(t.config).map((e=>({id:e,value:t.config[e]}))),r=i.overrides.find((e=>{var r,n;return e.matcher.options===(null!==(n=null!==(r=t.config.displayNameFromDS)&&void 0!==r?r:t.config.displayName)&&void 0!==n?n:t.name)&&"byName"===e.matcher.id}));var o,s;if(!r)i.overrides.unshift({matcher:{id:"byName",options:null!==(s=null!==(o=t.config.displayNameFromDS)&&void 0!==o?o:t.config.displayName)&&void 0!==s?s:t.name},properties:e});r&&JSON.stringify(r.properties)!==JSON.stringify(e)&&(r.properties=e)}return i})),m(this,"getContext",(()=>{const e=this.getFieldConfig(),{queries:t,dsUid:r,labelName:n,fieldName:i}=this.state,o=a.jh.getTimeRange(this);if(!o||!t||!r)return;const s={origin:"Metrics Drilldown",type:"timeseries",queries:t,timeRange:f({},o.state.value),datasource:{uid:r},url:window.location.href,id:`${JSON.stringify(t)}${n}${i}`,title:`${n}${i?` > ${i}`:""}`,logoPath:c,drillDownLabel:i,fieldConfig:e};JSON.stringify(s)!==JSON.stringify(this.state.context)&&this.setState({context:s})})),this.addActivationHandler(this._onActivate.bind(this))}}m(y,"Component",(({model:e})=>{const{context:t}=e.useState(),{links:r}=(0,n.usePluginLinks)({extensionPointId:b,context:t,limitPerPlugin:1}),a=r.find((e=>"grafana-investigations-app"===e.pluginId));return a?o().createElement(l.IconButton,{tooltip:a.description,"aria-label":"add panel to exploration",key:a.id,name:null!==(i=a.icon)&&void 0!==i?i:"panel-add",onClick:e=>{a.onClick&&a.onClick(e)}}):null;var i}));const v=e=>{var t,r;const n=null!==(r=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==r?r:{},a=Object.keys(n);if(1!==a.length)return;const i=a[0];return{name:i,value:n[i]}};function w(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function O(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){w(i,n,a,o,s,"next",e)}function s(e){w(i,n,a,o,s,"throw",e)}o(void 0)}))}}function S(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){S(e,t,r[t])}))}return e}function E(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const x="Add to investigation",j="investigations_divider",_="Investigations";class P extends a.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t;super(E(k({},e),{addExplorationsLink:null===(t=e.addExplorationsLink)||void 0===t||t})),this.addActivationHandler((()=>{let e;try{const r=a.jh.getAncestor(this,a.Eb),n=a.jh.getData(r).state.data;if(!n)throw new Error("Cannot get link to explore, no panel data found");const i=(0,h.un)(r);var t;(null!==(t=null==i?void 0:i.state.queries)&&void 0!==t?t:[]).forEach((e=>{delete e.legendFormat})),e=(0,a.pN)(n,this,n.timeRange,(e=>"expr"in e&&"string"==typeof e.expr&&e.expr.includes("__ignore_usage__")?E(k({},e),{expr:e.expr.replace(/,?__ignore_usage__=""/,"")}):e))}catch(e){}const r=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",onClick:()=>null==e?void 0:e.then((e=>e&&window.open(e,"_blank"))),shortcut:"p x"}];this.setState({body:new a.Lw({items:r})});const n=new y({labelName:this.state.labelName,fieldName:this.state.fieldName,frame:this.state.frame});var i,o=this;(this._subs.add(null==n?void 0:n.subscribeToState(O((function*(){yield function(e){return L.apply(this,arguments)}(o)})))),this.setState({explorationsButton:n}),this.state.addExplorationsLink)&&(null===(i=this.state.explorationsButton)||void 0===i||i.activate())}))}}S(P,"Component",(({model:e})=>{const{body:t}=e.useState();return t?o().createElement(t.Component,{model:t}):o().createElement(o().Fragment,null)}));const C=function(){var e=O((function*(e){const t=e.state.context;if(n.config.buildInfo.version.startsWith("11."))try{const{getPluginLinkExtensions:e}=yield Promise.resolve().then(r.t.bind(r,8531,23));if(void 0!==e){return e({extensionPointId:b,context:t}).extensions[0]}}catch(e){console.error("Error importing getPluginLinkExtensions",e)}if(void 0!==n.getObservablePluginLinks){return(yield(0,s.firstValueFrom)((0,n.getObservablePluginLinks)({extensionPointId:b,context:t})))[0]}}));return function(t){return e.apply(this,arguments)}}();function L(){return(L=O((function*(e){const t=e.state.explorationsButton;if(t){var r;const l=yield C(t);var n;const c=null!==(n=null===(r=e.state.body)||void 0===r?void 0:r.state.items)&&void 0!==n?n:[],u=c.find((e=>e.text===x));var a,i,o,s;if(l)if(u){if(u)null===(a=e.state.body)||void 0===a||a.setItems(c.filter((e=>!1===[j,_,x].includes(e.text))))}else null===(i=e.state.body)||void 0===i||i.addItem({text:j,type:"divider"}),null===(o=e.state.body)||void 0===o||o.addItem({text:_,type:"group"}),null===(s=e.state.body)||void 0===s||s.addItem({text:x,iconClassName:"plus-square",onClick:e=>l.onClick&&l.onClick(e)})}}))).apply(this,arguments)}},1385:(e,t,r)=>{r.d(t,{k3:()=>Rt,Rd:()=>Tt});var n=r(6089),a=r(8531),i=r(6680),o=r(2007),s=r(5959),l=r.n(s),c=r(203);var u,d,p,h=r(5749),m=r(7265),f=r(2127),g=r(4169);class b extends i.Bs{_onActivate(){const{autoQuery:e}=(0,g.aM)(this).state;0!==e.variants.length&&this.setState({options:e.variants.map((e=>({label:e.variant,value:e.variant})))})}constructor(e){super(e),this.addActivationHandler(this._onActivate.bind(this))}}function y(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function v(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}p=({model:e})=>{const{options:t,onChangeQuery:r,queryDef:n}=e.useState();return t?l().createElement(o.RadioButtonGroup,{size:"sm",options:t,value:n.variant,onChange:r}):null},(d="Component")in(u=b)?Object.defineProperty(u,d,{value:p,enumerable:!0,configurable:!0,writable:!0}):u[d]=p;class w extends i.Bs{onActivate(){if(!this.state.panel){const{autoQuery:e,metric:t}=(0,g.aM)(this).state;this.getVizPanelFor(e.main,t).then((e=>this.setState({panel:e,metric:t})))}}getVizPanelFor(e,t){var r,n=this;return(r=function*(){const r=(0,g.kj)(n),a=yield r.getMetricMetadata(t),o=(0,h.R)(a);return e.vizBuilder().setData(new i.dt({datasource:f.GH,maxDataPoints:f.dc,queries:e.queries})).setDescription(o).setHeaderActions([new b({queryDef:e,onChangeQuery:n.onChangeQuery})]).setShowMenuAlways(!0).setMenu(new m.G({labelName:null!=t?t:n.state.metric})).build()},function(){var e=this,t=arguments;return new Promise((function(n,a){var i=r.apply(e,t);function o(e){y(i,n,a,o,s,"next",e)}function s(e){y(i,n,a,o,s,"throw",e)}o(void 0)}))})()}constructor(e){super(e),v(this,"onChangeQuery",(e=>{const t=(0,g.aM)(this),r=t.state.autoQuery.variants.find((t=>t.variant===e));this.getVizPanelFor(r).then((e=>this.setState({panel:e}))),t.setState({queryDef:r})})),this.addActivationHandler(this.onActivate.bind(this))}}v(w,"Component",(({model:e})=>{const{panel:t}=e.useState();if(t)return l().createElement(t.Component,{model:t})}));var O=r(2872),S=r(7437);const k=`${f.Rp}{${f.ui}}`,E=`rate(${k}[$__rate_interval])`,x=`{"${f.Rp}", ${f.ui}}`,j=`rate(${x}[$__rate_interval])`;function _({isRateQuery:e=!1,groupings:t=[],isUtf8Metric:r=!1}){const n=r?e?j:x:e?E:k;return t.length>0?`sum by(${t.join(", ")}) (${n} ${f.tM})`:`${n} ${f.tM}`}var P=r(8222);function C(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){C(e,t,r[t])}))}return e}function B(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}function N({description:e,mainQueryExpr:t,breakdownQueryExpr:r,unit:n}){const a={title:f.Rp,unit:n},i={refId:"A",expr:t,legendFormat:e,fromExploreMetrics:!0},o=B(L({},a),{title:e,queries:[i],variant:"main",vizBuilder:()=>(0,P.sj)(L({},o))}),s=B(L({},a),{queries:[B(L({},i),{legendFormat:e})],vizBuilder:()=>(0,P.sj)(s),variant:"preview"}),l=B(L({},a),{queries:[{refId:"A",expr:r,legendFormat:`{{${f.aZ}}}`,fromExploreMetrics:!0}],vizBuilder:()=>(0,P.sj)(l),variant:"breakdown"});return{preview:s,main:o,breakdown:l,variants:[]}}const D=new Set(["count","total"]),T={count:"sum",total:"sum"},A={avg:"average",sum:"overall"};function R(e){const{metricParts:t,suffix:r,isUtf8Metric:n}=e,a="total"===r?t.at(-2):r,i=D.has(r),o=T[r]||"avg",s=i?(0,S.MM)(a):(0,S.l_)(a),l=_({isRateQuery:i,isUtf8Metric:n}),c=`${u=o,A[u]||u}${i?" per-second rate":""}`;var u;return N({description:`${f.Rp} (${c})`,mainQueryExpr:`${o}(${l})`,breakdownQueryExpr:`${o}(${l})by(${f.aZ})`,unit:s})}function $(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){$(e,t,r[t])}))}return e}function V(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}function F(e,t,r=[]){const n=t/100;let a=`${t}th Percentile`;r[0]&&(a=`{{${r[0]}}}`);return{refId:`Percentile${t}`,expr:`histogram_quantile(${n}, ${_({isRateQuery:!0,isUtf8Metric:e.isUtf8Metric,groupings:e.nativeHistogram?[...r]:["le",...r]})})`,legendFormat:a,fromExploreMetrics:!0}}function H(e,t){return`${e.replace(f.Rp,`${t}_sum`)}/${e.replace(f.Rp,`${t}_count`)}`}function I(e,t){const r=!(0,O.Rq)(e),n=e.split("_"),a=n.at(-1);if(null==a)throw new Error(`This function does not support a metric suffix of "${a}"`);const i=n.at(-2),o={metricParts:n,isUtf8Metric:r,suffix:a,unitSuffix:i,unit:(0,S.l_)(i),nativeHistogram:t};return"sum"===a?function(e){const{metricParts:t,isUtf8Metric:r,unit:n}=e,a=t.slice(0,-1).join("_"),i=`${a} (average)`,o=_({isRateQuery:!0,isUtf8Metric:r});return N({description:i,mainQueryExpr:H(`sum(${o})`,a),breakdownQueryExpr:H(`sum(${o})by(${f.aZ})`,a),unit:n})}(o):"bucket"===a||t?function(e){const{unit:t,nativeHistogram:r}=e,n={title:f.Rp,unit:t},a=V(M({},n),{variant:"p50",queries:[F(e,50)],vizBuilder:()=>(0,P.sj)(a)}),i=V(M({},n),{variant:"p50",queries:[F(e,50,[f.aZ])],vizBuilder:()=>(0,P.sj)(i)}),o=V(M({},n),{variant:"percentiles",queries:[99,90,50].map((t=>F(e,t))),vizBuilder:()=>(0,P.s1)(o)}),s=V(M({},n),{variant:"heatmap",queries:[{refId:"Heatmap",expr:_({isRateQuery:!0,isUtf8Metric:e.isUtf8Metric,groupings:r?[]:["le"]}),fromExploreMetrics:!0,format:"heatmap"}],vizBuilder:()=>(0,P.sp)(s)});return{preview:s,main:s,variants:[o,s],breakdown:i}}(o):R(o)}var z=r(6944),U=r(7781),G=r(1625),q=r(3241);function W({options:e,value:t,onChange:r}){const n=(0,o.useStyles2)(K);return l().createElement("div",{className:n.select},l().createElement(o.Combobox,{options:e.map((e=>({label:e.label||"",value:e.value||""}))),value:t||"",onChange:e=>r(null==e?void 0:e.value),width:16}))}function K(e){return{select:(0,n.css)({maxWidth:e.spacing(16)})}}var Q=r(3347),Y=r(384);function J(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class X extends i.Bs{constructor(...e){super(...e),J(this,"onClick",(()=>{var e;const t=i.jh.lookupVariable("filters",this);if(!(0,Y.BE)(t))return;var r;const n=null!==(r=null===(e=this.state.frame.fields[1])||void 0===e?void 0:e.labels)&&void 0!==r?r:{};if(1!==Object.keys(n).length)return;const a=Object.keys(n)[0];(0,Q.z)("label_filter_changed",{label:a,action:"added",cause:"breakdown"});const o=(0,g.kj)(this),s=i.jh.lookupVariable(f.lr,o),l=null==s?void 0:s.getValue(),c={key:a,operator:"=",value:n[a]};if(l&&"string"==typeof l&&(null==l?void 0:l.split(",").includes(a))){o.setState({addingLabelFromBreakdown:!0});const e=i.jh.lookupVariable(f.DU,o),t=i.jh.lookupVariable(f.mW,o);if(!(0,Y.BE)(e)||!(0,Y.BE)(t))return;e.setState({filters:[...e.state.filters,c]}),t.setState({filters:[...t.state.filters,c]}),o.setState({addingLabelFromBreakdown:!1})}else o.addFilterWithoutReportingInteraction(c)}))}}function Z(e,t){function r(e){return e instanceof t}return i.jh.findAllObjects(e,r).filter(r)}J(X,"Component",(({model:e})=>{var t;const r=(null===(t=e.useState().frame.fields[1])||void 0===t?void 0:t.labels)||{};return 0!==Object.keys(r).length?l().createElement(o.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Add to filters"):null}));var ee=r(5570),te=r(7993);const re=new te.A({intraMode:1,intraIns:1,intraSub:1,intraTrn:1,intraDel:1});function ne(e,t,r){const[n,a,i]=re.search(e,t,0,1e5);let o=[],s=new Set;if(n&&i){const t=(e,t)=>{t&&s.add(e)};for(let r=0;r<i.length;r++){let n=i[r];te.A.highlight(e[a.idx[n]],a.ranges[n],t),o.push(e[a.idx[n]])}r([o,[...s]])}else t||r([])}(0,q.debounce)(ne,300);var ae=r(2601);function ie(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){ie(e,t,r[t])}))}return e}function se(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}function le(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}class ce extends i.Bs{performRepeat(e){const t=[],r=(0,ae.sortSeries)(e.series,this.sortBy);for(let n=0;n<r.length;n++){const a=this.state.getLayoutChild(e,r[n],n);t.push(a)}this.sortedSeries=r,this.unfilteredChildren=t,this.getFilter()?(this.state.body.setState({children:[]}),this.filterByString(this.getFilter())):this.state.body.setState({children:t})}constructor(e){var{sortBy:t,getFilter:r}=e;super(le(e,["sortBy","getFilter"])),ie(this,"unfilteredChildren",[]),ie(this,"sortBy",void 0),ie(this,"sortedSeries",[]),ie(this,"getFilter",void 0),ie(this,"sort",(e=>{const t=i.jh.getData(this);this.sortBy=e,t.state.data&&this.performRepeat(t.state.data)})),ie(this,"filterByString",(e=>{let t=[];this.iterateFrames(((e,r)=>{const n=de(e[r]);t.push(n)})),ne(t,e,(e=>{e&&e[0]?this.filterFrames((t=>{const r=de(t);return e[0].includes(r)})):this.filterFrames((()=>!0))}))})),ie(this,"iterateFrames",(e=>{if(i.jh.getData(this).state.data)for(let t=0;t<this.sortedSeries.length;t++)e(this.sortedSeries,t)})),ie(this,"filterFrames",(e=>{const t=[];var r,n;this.iterateFrames(((r,n)=>{e(r[n])&&t.push(this.unfilteredChildren[n])})),0===t.length?this.state.body.setState({children:[(r=this.getFilter(),n=this.clearFilter,new i.G1({direction:"row",children:[new i.vA({body:new i.dM({reactNode:l().createElement("div",{className:ue.alertContainer},l().createElement(o.Alert,{title:"",severity:"info",className:ue.noResultsAlert},"No values found matching: ",r,l().createElement(o.Button,{className:ue.clearButton,onClick:n},"Clear filter")))})})]}))]}):this.state.body.setState({children:t})})),ie(this,"clearFilter",(()=>{this.publishEvent(new be,!0)})),this.sortBy=t,this.getFilter=r,this.addActivationHandler((()=>{const e=i.jh.getData(this);this._subs.add(e.subscribeToState(((e,t)=>{var r,n;if(void 0===e.data)return;const a=e.data;(null===(r=e.data)||void 0===r?void 0:r.state)!==(null===(n=t.data)||void 0===n?void 0:n.state)&&Z(this,i.Zv).forEach((e=>{e.setState({data:se(oe({},e.state.data),{state:a.state})})})),a.state===U.LoadingState.Done&&this.performRepeat(a)}))),e.state.data&&this.performRepeat(e.state.data)}))}}ie(ce,"Component",(({model:e})=>{const{body:t}=e.useState();return l().createElement(t.Component,{model:t})}));const ue={alertContainer:(0,n.css)({flexGrow:1,display:"flex",justifyContent:"center",alignItems:"center"}),noResultsAlert:(0,n.css)({minWidth:"30vw",flexGrow:0}),clearButton:(0,n.css)({marginLeft:"1.5rem"})};function de(e){var t;return null!==(t=(0,ee.H)(e))&&void 0!==t?t:"No labels"}function pe(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function he(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}const me=e=>{var{value:t,onChange:r,placeholder:n,onClear:a}=e,i=he(e,["value","onChange","placeholder","onClear"]);return l().createElement(o.Input,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){pe(e,t,r[t])}))}return e}({value:t,onChange:r,suffix:t?l().createElement(o.Icon,{onClick:a,title:"Clear search",name:"times",className:fe.clearIcon}):void 0,prefix:l().createElement(o.Icon,{name:"search"}),placeholder:n},i))},fe={clearIcon:(0,n.css)({cursor:"pointer"})};function ge(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class be extends U.BusEventBase{}ge(be,"type","breakdown-search-reset");const ye={};class ve extends i.Bs{filterValues(e){if(this.parent instanceof Ve){ye[this.cacheKey]=e;const t=this.parent.state.body;null==t||t.forEachChild((t=>{t instanceof ce&&t.state.body.isActive&&t.filterByString(e)}))}}constructor(e){var t;super({filter:null!==(t=ye[e])&&void 0!==t?t:""}),ge(this,"cacheKey",void 0),ge(this,"onValueFilterChange",(e=>{this.setState({filter:e.target.value}),this.filterValues(e.target.value)})),ge(this,"clearValueFilter",(()=>{this.setState({filter:""}),this.filterValues("")})),ge(this,"reset",(()=>{this.setState({filter:""}),ye[this.cacheKey]=""})),this.cacheKey=e}}ge(ve,"Component",(({model:e})=>{const{filter:t}=e.useState();return l().createElement(me,{value:t,onChange:e.onValueFilterChange,onClear:e.clearValueFilter,placeholder:"Search for value"})}));var we=r(4351);const Oe=["single","grid","rows"];function Se(e){return Oe.includes(e)}function ke(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class Ee extends i.Bs{getUrlState(){return{breakdownLayout:this.state.activeBreakdownLayout}}updateFromUrl(e){const t=e.breakdownLayout;"string"==typeof t&&Se(t)&&this.state.activeBreakdownLayout!==t&&this.setState({activeBreakdownLayout:t})}Selector({model:e}){const{activeBreakdownLayout:t,breakdownLayoutOptions:r}=e.useState();return l().createElement(o.RadioButtonGroup,{options:r,value:t,onChange:e.onLayoutChange})}constructor(e){const t=(0,we.Hy)();super(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){ke(e,t,r[t])}))}return e}({activeBreakdownLayout:Se(t)?t:"grid"},e)),ke(this,"_urlSync",new i.So(this,{keys:["breakdownLayout"]})),ke(this,"onLayoutChange",(e=>{this.state.activeBreakdownLayout!==e&&((0,Q.z)("breakdown_layout_changed",{layout:e}),(0,we.HU)(e),this.setState({activeBreakdownLayout:e}),this.state.onBreakdownLayoutChange(e))}))}}function xe(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}ke(Ee,"Component",(({model:e})=>{const{breakdownLayouts:t,breakdownLayoutOptions:r,activeBreakdownLayout:n}=e.useState(),a=r.findIndex((e=>e.value===n));if(-1===a)return null;const i=t[a];return l().createElement(i.Component,{model:i})}));class je extends U.BusEventBase{constructor(e,t){super(),xe(this,"target",void 0),xe(this,"sortBy",void 0),this.target=e,this.sortBy=t}}xe(je,"type","sort-criteria-changed");const _e=[{value:"outliers",label:"Outlying series",description:"Prioritizes values that show distinct behavior from others within the same label"},{value:"alphabetical",label:"Name [A-Z]",description:"Alphabetical order"},{value:"alphabetical-reversed",label:"Name [Z-A]",description:"Reversed alphabetical order"}];class Pe extends i.Bs{constructor(e){const{sortBy:t}=(0,we.vs)(e.target,"outliers");super({target:e.target,sortBy:t}),xe(this,"onCriteriaChange",(e=>{(null==e?void 0:e.value)&&(this.setState({sortBy:e.value}),(0,we.fq)(this.state.target,e.value),this.publishEvent(new je(this.state.target,e.value),!0))}))}}function Ce(e){return{sortByTooltip:(0,n.css)({display:"flex",gap:e.spacing(1)})}}xe(Pe,"Component",(({model:e})=>{const t=(0,o.useStyles2)(Ce),{sortBy:r}=e.useState(),n=_e.find((e=>e.value===r));return l().createElement(o.Field,{htmlFor:"sort-by-criteria",label:l().createElement("div",{className:t.sortByTooltip},"Sort by",l().createElement(o.IconButton,{name:"info-circle",size:"sm",variant:"secondary",tooltip:"Sorts values using standard or smart time series calculations."}))},l().createElement(o.Combobox,{id:"sort-by-criteria",value:n,width:20,options:_e,placeholder:"Choose criteria",onChange:e.onCriteriaChange,isClearable:!1}))}));class Le extends U.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Le,"type","selected-metric-query-results-event");const Be=e=>{const t=i.jh.getAncestor(e,Ve);function r(e){e&&(e.isActive||e.addActivationHandler((()=>{r(e)})),e.state.data&&t.reportBreakdownPanelData(e.state.data),e.subscribeToState((({data:e},r)=>{t.reportBreakdownPanelData(e)})))}Z(e,i.Eb).forEach((e=>{e.isActive?r(e.state.$data):e.addActivationHandler((()=>{r(e.state.$data)}))})),Z(e,i.Eb).forEach((e=>r(e.state.$data)));const n=t.subscribeToEvent(Le,(t=>{if(!e.isActive)return void n.unsubscribe();const r=i.No.timeseries().setCustomFieldConfig("axisSoftMin",t.payload.min).setCustomFieldConfig("axisSoftMax",t.payload.max).build();Z(e,i.Eb).forEach((e=>{function t(){e.onFieldConfigChange(r)}e.isActive?t():e.addActivationHandler(t)}))}))};var Ne=r(6099);const De="$__all";var Te=r(8203);function Ae(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function Re(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $e(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Re(e,t,r[t])}))}return e}function Me(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}class Ve extends i.Bs{_onActivate(){var e;(0,z.default)().then((()=>console.debug("Grafana ML initialized")));const t=this.getVariable();a.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(this.subscribeToEvent(f.H0,(()=>{this.updateBody(this.getVariable())}))),t.subscribeToState(((e,r)=>{e.options===r.options&&e.value===r.value&&e.loading===r.loading||this.updateBody(t)})),this._subs.add(this.subscribeToEvent(be,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent(je,this.handleSortByChange));const r=i.jh.getAncestor(this,Tt).state.metric;this._query=I(r).breakdown;const n=(0,g.kj)(this);null===(e=n.state.$timeRange)||void 0===e||e.subscribeToState((()=>{this.clearBreakdownPanelAxisValues()})),this._subs.add(n.subscribeToState((({useOtelExperience:e},r)=>{e!==r.useOtelExperience&&this.updateBody(t)})));const o=i.jh.lookupVariable(f.lr,n);(0,Y.DJ)(o)&&(null==o||o.subscribeToState(((e,r)=>{e.value!==r.value&&this.updateBody(t)}))),this.updateBody(t)}reportBreakdownPanelData(e){if(!e)return;let t=this.breakdownPanelMinValue,r=this.breakdownPanelMaxValue;e.series.forEach((e=>{e.fields.forEach((e=>{if(e.type!==U.FieldType.number)return;const n=e.values.filter(q.isNumber),a=(0,q.max)(n),i=(0,q.min)(n);r=(0,q.max)([r,a].filter(q.isNumber)),t=(0,q.min)([t,i].filter(q.isNumber))}))})),void 0!==r&&void 0!==t&&Number.isFinite(r+t)&&(this.breakdownPanelMaxValue===r&&this.breakdownPanelMinValue===t||(this.breakdownPanelMaxValue=r,this.breakdownPanelMinValue=t,this._triggerAxisChangedEvent()))}clearBreakdownPanelAxisValues(){this.breakdownPanelMaxValue=void 0,this.breakdownPanelMinValue=void 0}getVariable(){const e=i.jh.lookupVariable(f.yr,this);if(!(0,Y.bA)(e))throw new Error("Group by variable not found");return e}onReferencedVariableValueChanged(){const e=this.getVariable();e.changeValueTo(De),this.updateBody(e)}updateBody(e){const t=function(e,t){const r=i.jh.lookupVariable(f.Ao,e),n=[];if(!(0,Y.BE)(r))return[];const a=r.state.filters;for(const e of t.getOptionsForSelect()){const t=a.find((t=>t.key===e.value));"le"!==e.label&&(t||n.push({label:e.label,value:String(e.value)}))}return n}(this,e),r=(0,g.kj)(this);let n=t;r.state.useOtelExperience&&(n=this.updateLabelOptions(r,n));const a={loading:e.state.loading,value:String(e.state.value),labels:n,error:e.state.error,blockingMessage:void 0};!e.state.loading&&e.state.options.length?a.body=e.hasAllValue()?function(e,t,r){const n=[];for(const r of e){if(r.value===De)continue;if(60===n.length)break;const e=t.queries[0].expr.replaceAll(f.aZ,(0,O.Nc)(String(r.value))),a=t.unit,o=i.d0.timeseries().setOption("tooltip",{mode:G.$N.Multi,sort:G.xB.Descending}).setOption("legend",{showLegend:!1}).setTitle(r.label).setData(new i.dt({maxDataPoints:f.up,datasource:f.GH,queries:[{refId:`A-${r.label}`,expr:e,legendFormat:`{{${r.label}}}`,fromExploreMetrics:!0}]})).setHeaderActions([new ze({labelName:String(r.value)})]).setShowMenuAlways(!0).setMenu(new m.G({labelName:String(r.value)})).setUnit(a).setBehaviors([Ge]).build();n.push(new i.xK({$behaviors:[Be],body:o}))}return new Ee({breakdownLayoutOptions:[{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],onBreakdownLayoutChange:r,breakdownLayouts:[new i.gF({templateColumns:He,autoRows:"200px",children:n,isLazy:!0}),new i.gF({templateColumns:"1fr",autoRows:"200px",children:n.map((e=>e.clone())),isLazy:!0})]})}(n,this._query,this.onBreakdownLayoutChange,r.state.useOtelExperience):function(e,t,r){const n=e.unit;function a(t,r,a){const o=e.vizBuilder().setTitle(Ie(r)).setData(new i.Zv({data:Me($e({},t),{series:[r]})})).setColor({mode:"fixed",fixedColor:(0,g.Vy)(a)}).setHeaderActions([new X({frame:r})]).setShowMenuAlways(!0).setMenu(new m.G({labelName:Ie(r)})).setUnit(n).build(),s=r.length<=1;return new i.xK({$behaviors:[Be],body:o,isHidden:s})}const{sortBy:s}=(0,we.vs)("labels","outliers"),c=()=>{var e;return null!==(e=r.state.filter)&&void 0!==e?e:""};return new Ee({$data:new i.dt({datasource:f.GH,maxDataPoints:f.up,queries:e.queries}),breakdownLayoutOptions:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],onBreakdownLayoutChange:t,breakdownLayouts:[new i.G1({direction:"column",children:[new i.vA({minHeight:300,body:i.d0.timeseries().setOption("tooltip",{mode:G.$N.Multi,sort:G.xB.Descending}).setOption("legend",{showLegend:!1}).setTitle("$metric").build()})]}),new ce({body:new i.gF({templateColumns:He,autoRows:"200px",children:[new i.vA({body:new i.dM({reactNode:l().createElement(o.LoadingPlaceholder,{text:"Loading..."})})})]}),getLayoutChild:a,sortBy:s,getFilter:c}),new ce({body:new i.gF({templateColumns:"1fr",autoRows:"200px",children:[]}),getLayoutChild:a,sortBy:s,getFilter:c})]})}(this._query,this.onBreakdownLayoutChange,this.state.search):e.state.loading||(a.body=void 0,a.blockingMessage="There are no labels found for this metric."),this.clearBreakdownPanelAxisValues(),this.setState(a)}updateOtelGroupLeft(){var e,t=this;return(e=function*(){const e=(0,g.kj)(t);var r;e.state.useOtelExperience&&(yield(0,Ne.KO)(e,null!==(r=e.state.metric)&&void 0!==r?r:""))},function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Ae(i,n,a,o,s,"next",e)}function s(e){Ae(i,n,a,o,s,"throw",e)}o(void 0)}))})()}updateLabelOptions(e,t){var r;const n=null===(r=i.jh.lookupVariable(f.lr,e))||void 0===r?void 0:r.getValue();if("string"!=typeof n)return[];const a=n.split(",").map((e=>{let t=e;return(0,O.Rq)(e)||(t=e.slice(1,-1)),{label:t,value:e}})),o=[{label:"All",value:De}].concat(a);return t=t.filter((e=>e.value!==De)),t=o.concat(t)}constructor(e){var t;super(Me($e({},e),{labels:null!==(t=e.labels)&&void 0!==t?t:[],sortBy:new Pe({target:"labels"}),search:new ve("labels")})),Re(this,"_variableDependency",new i.Sh(this,{variableNames:[f.Ao],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),Re(this,"_query",void 0),Re(this,"breakdownPanelMaxValue",void 0),Re(this,"breakdownPanelMinValue",void 0),Re(this,"_triggerAxisChangedEvent",(0,q.throttle)((()=>{const{breakdownPanelMinValue:e,breakdownPanelMaxValue:t}=this;void 0!==e&&void 0!==t&&this.publishEvent(new Le({min:e,max:t}))}),1e3)),Re(this,"handleSortByChange",(e=>{"labels"===e.target&&(this.state.body instanceof Ee&&this.state.body.state.breakdownLayouts.forEach((t=>{t instanceof ce&&t.sort(e.sortBy)})),(0,Q.z)("sorting_changed",{from:"label-breakdown",sortBy:e.sortBy}))})),Re(this,"onBreakdownLayoutChange",(e=>{this.clearBreakdownPanelAxisValues()})),Re(this,"onChange",(e=>{if(!e)return;(0,Q.z)("label_selected",{label:e,cause:"selector"});this.getVariable().changeValueTo(e)})),this.addActivationHandler(this._onActivate.bind(this))}}function Fe(e){return{container:(0,n.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",paddingTop:e.spacing(1)}),content:(0,n.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),searchField:(0,n.css)({flexGrow:1}),controls:(0,n.css)({flexGrow:0,display:"flex",alignItems:"flex-end",gap:e.spacing(2),justifyContent:"space-between"}),truncatedOTelResources:(0,n.css)({minWidth:"30vw",flexGrow:0})}}Re(Ve,"Component",(({model:e})=>{var t;const{labels:r,body:n,search:a,sortBy:c,loading:u,value:d,blockingMessage:p}=e.useState(),h=(0,o.useStyles2)(Fe),m=(0,g.kj)(e),{useOtelExperience:b}=m.useState();let y=r;if(m.state.useOtelExperience){const e=[{label:"All",value:De}];y.filter((e=>e.value!==De)).unshift(e)}const[v,w]=(0,s.useState)(!1),O=null===(t=i.jh.lookupVariable(f.jl,m))||void 0===t?void 0:t.getValue();return O&&!v&&(0,Q.z)("missing_otel_labels_by_truncating_job_and_instance",{metric:m.state.metric}),(0,s.useEffect)((()=>{b&&e.updateOtelGroupLeft()}),[e,b]),l().createElement("div",{className:h.container},l().createElement(Te.O,{isLoading:u,blockingMessage:p},l().createElement("div",{className:h.controls},!u&&r.length&&l().createElement(o.Field,{label:b?"By attribute":"By label"},l().createElement(W,{options:y,value:d,onChange:e.onChange})),d!==De&&l().createElement(l().Fragment,null,l().createElement(o.Field,{label:"Search",className:h.searchField},l().createElement(a.Component,{model:a})),l().createElement(c.Component,{model:c})),n instanceof Ee&&l().createElement(o.Field,{label:"View"},l().createElement(n.Selector,{model:n}))),O&&!v&&l().createElement(o.Alert,{title:"Warning: There may be missing Open Telemetry resource attributes.",severity:"warning",key:"warning",onRemove:()=>w(!0),className:h.truncatedOTelResources},"This metric has too many job and instance label values to call the Prometheus label_values endpoint with the match[] parameter. These label values are used to join the metric with target_info, which contains the resource attributes. Please include more resource attributes filters."),l().createElement("div",{className:h.content},n&&l().createElement(n.Component,{model:n}))))}));const He="repeat(auto-fit, minmax(400px, 1fr))";function Ie(e){var t;const r=(null===(t=e.fields[1])||void 0===t?void 0:t.labels)||{},n=Object.keys(r);return 0===n.length?"<unspecified>":r[n[0]]}class ze extends i.Bs{constructor(...e){super(...e),Re(this,"onClick",(()=>{var e;const t=this.state.labelName,r=(0,g.kj)(this),n=null===(e=i.jh.lookupVariable(f.lr,r))||void 0===e?void 0:e.getValue();let a=!1;"string"==typeof n&&(a=null==n?void 0:n.split(",").includes(t)),(0,Q.z)("label_selected",{label:t,cause:"breakdown_panel",otel_resource_attribute:a}),Ue(this).onChange(t)}))}}function Ue(e){if(e instanceof Ve)return e;if(e.parent)return Ue(e.parent);throw new Error("Unable to find breakdown scene")}function Ge(e){var t;null===(t=e.state.$data)||void 0===t||t.subscribeToState(((e,t)=>{var r,n;const a=null===(n=e.data)||void 0===n||null===(r=n.request)||void 0===r?void 0:r.targets[0];if(function(e){return void 0!==e&&"legendFormat"in e&&"string"==typeof e.legendFormat}(a)){var i;const{legendFormat:t}=a,r=t.slice(2,-2);null===(i=e.data)||void 0===i||i.series.forEach((e=>{var t,n;if(!(null===(n=e.fields[1])||void 0===n||null===(t=n.labels)||void 0===t?void 0:t[r])){var a;const t=null===(a=e.fields[1])||void 0===a?void 0:a.labels;t&&(t[r]=`<unspecified ${r}>`)}}))}}))}Re(ze,"Component",(({model:e})=>l().createElement(o.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Select")));var qe=r(6082);function We(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Ke="metric-graph";class Qe extends i.Bs{constructor(e){var t;super(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){We(e,t,r[t])}))}return e}({topView:null!==(t=e.topView)&&void 0!==t?t:new i.G1({direction:"column",$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:U.DashboardCursorSync.Crosshair})],children:[new i.vA({minHeight:280,maxHeight:"40%",body:new w({key:Ke})}),new i.vA({ySizing:"content",body:new Rt({})})]})},e))}}function Ye(e,t){return{container:(0,n.css)({display:"flex",flexDirection:"column",position:"relative",flexGrow:1}),sticky:(0,n.css)({display:"flex",flexDirection:"row",background:e.isLight?e.colors.background.primary:e.colors.background.canvas,position:"sticky",paddingTop:e.spacing(1),marginTop:`-${e.spacing(1)}`,top:`${t+70}px`,zIndex:10}),nonSticky:(0,n.css)({display:"flex",flexDirection:"row"})}}function Je(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function Xe(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Je(i,n,a,o,s,"next",e)}function s(e){Je(i,n,a,o,s,"throw",e)}o(void 0)}))}}We(Qe,"Component",(({model:e})=>{const{topView:t,selectedTab:r}=e.useState(),{stickyMainGraph:n}=(0,g.KE)(e).useState(),i=(0,a.useChromeHeaderHeight)(),s=(0,g.kj)(e),c=(0,o.useStyles2)(Ye,s.state.embedded?0:null!=i?i:0);return l().createElement("div",{className:c.container},l().createElement("div",{className:n?c.sticky:c.nonSticky},l().createElement(t.Component,{model:t})),r&&l().createElement(r.Component,{model:r}))}));const Ze={job:"service_name",instance:"service_instance_id"};function et(e){return e in Ze?Ze[e]:e}function tt(){return tt=Xe((function*(e,t,r){var n;const i=yield(0,a.getDataSourceSrv)().get(e),o=yield null===(n=i.getTagKeys)||void 0===n?void 0:n.call(i,{timeRange:r,filters:t.map((({key:e,operator:t,value:r})=>({key:et(e),operator:t,value:r})))});if(!Array.isArray(o))return!1;const s=new Set(o.map((e=>e.text)));if(!t.map((e=>et(e.key))).every((e=>s.has(e))))return!1;const l=yield Promise.all(t.map(function(){var e=Xe((function*(e){var n;const a=et(e.key),o=yield null===(n=i.getTagValues)||void 0===n?void 0:n.call(i,{key:a,timeRange:r,filters:t});return!!Array.isArray(o)&&o.some((t=>t.text===e.value))}));return function(t){return e.apply(this,arguments)}}()));return l.every(Boolean)})),tt.apply(this,arguments)}const rt=e=>{let t=!1;return{name:"labelsCrossReference",checkConditionsMetForRelatedLogs:()=>t,getDataSources:()=>Xe((function*(){var r;const n=(0,g.kj)(e),a=i.jh.lookupVariable(f.Ao,n);if(!(0,Y.BE)(a)||!a.state.filters.length)return t=!1,[];t=!0;const o=a.state.filters.map((({key:e,operator:t,value:r})=>({key:e,operator:t,value:r}))),s=null===(r=e.state.$timeRange)||void 0===r?void 0:r.state.value,l=yield bt(),c=yield Promise.all(l.map(function(){var e=Xe((function*({uid:e,name:t}){const r=yield function(e,t,r){return tt.apply(this,arguments)}(e,o,s);return r?{uid:e,name:t}:null}));return function(t){return e.apply(this,arguments)}}()));return c.filter((e=>null!==e))}))(),getLokiQueryExpr(){const t=(0,g.kj)(e),r=i.jh.lookupVariable(f.Ao,t);if(!(0,Y.BE)(r)||!r.state.filters.length)return"";return`{${r.state.filters.map((e=>`${et(e.key)}${e.operator}"${e.value}"`)).join(",")}}`}}};var nt=r(6365),at=r(1269);function it(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function ot(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){it(i,n,a,o,s,"next",e)}function s(e){it(i,n,a,o,s,"throw",e)}o(void 0)}))}}function st(){return(st=ot((function*(e){const t={url:`api/prometheus/${e.uid}/api/v1/rules`,showErrorAlert:!1,showSuccessAlert:!1},r=yield(0,at.lastValueFrom)((0,a.getBackendSrv)().fetch(t));return r.ok?r.data.data.groups:(console.warn(`Failed to fetch recording rules from Loki data source: ${e.name}`),[])}))).apply(this,arguments)}function lt(e,t,r){if(!t||!r[t])return"";const n=r[t].find((t=>t.name===e));if(!n)return"";return function(e){if(function(e){if(e.trim().length<=2)return!1;let t=!1;const r=nt.K3.parse(e);return r.iterate({enter:({type:e})=>{if(e.id===nt.Yw)return t=!0,!1}}),!t}(e))return e;const t=dt(e,nt.MD);if(!t)return"";const r=e.substring(t.from,t.to),n=dt(e,nt.AL),a=n?e.substring(n.from,n.to):"";return`${r} ${a}`.trim()}(n.query)}function ct(){return ct=ot((function*(){const e=yield bt(),t={};return yield Promise.all(e.map(function(){var e=ot((function*(e){try{const r=yield function(e){return st.apply(this,arguments)}(e),n=function(e,t){if(0===e.length)return[];const r=new Map;return e.forEach((e=>{e.rules.filter((e=>"recording"===e.type)).forEach((({type:e,name:n,query:a})=>{if(r.has(n)){const e=r.get(n);e&&(e.hasMultipleOccurrences=!0,r.set(n,e))}else r.set(n,{type:e,name:n,query:a,datasource:{name:t.name,uid:t.uid},hasMultipleOccurrences:!1})}))})),Array.from(r.values())}(r,e);t[e.uid]=n}catch(e){console.warn(e)}}));return function(t){return e.apply(this,arguments)}}())),t})),ct.apply(this,arguments)}const ut=(()=>{let e={},t=!1;return{name:"lokiRecordingRules",checkConditionsMetForRelatedLogs:()=>t,getDataSources:r=>ot((function*(){e=yield function(){return ct.apply(this,arguments)}();const n=function(e,t){const r=[];return Object.values(t).forEach((t=>{t.filter((t=>t.name===e)).forEach((e=>{r.push(e.datasource)}))})),r}(r,e);return t=Boolean(n.length),n}))(),getLokiQueryExpr:(t,r)=>lt(t,r,e)}})();function dt(e,t){let r;return nt.K3.parse(e).iterate({enter:e=>{if(e.type.id===t)return r=e.node,!1}}),r}var pt=r(2533);function ht(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function mt(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){ht(i,n,a,o,s,"next",e)}function s(e){ht(i,n,a,o,s,"throw",e)}o(void 0)}))}}function ft(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class gt{get lokiDataSources(){return this._internalState.lokiDataSources}set lokiDataSources(e){const t=this._internalState.lokiDataSources.map((e=>e.uid)).join(","),r=e.map((e=>e.uid)).join(",");t&&t===r||(this._internalState.lokiDataSources=e,this._changeHandlers.lokiDataSources.forEach((e=>e(this._internalState.lokiDataSources))))}set relatedLogsCount(e){this._internalState.relatedLogsCount=e,this._changeHandlers.relatedLogsCount.forEach((e=>e(this._internalState.relatedLogsCount)))}addLokiDataSourcesChangeHandler(e){this._changeHandlers.lokiDataSources.push(e)}addRelatedLogsCountChangeHandler(e){this._changeHandlers.relatedLogsCount.push(e)}handleFiltersChange(){this.lokiDataSources&&(this.lokiDataSources=[],this.relatedLogsCount=0,this.findAndCheckAllDatasources())}findAndCheckAllDatasources(){var e=this;return mt((function*(){const t=yield bt();t.length>0?e.checkLogsInDataSources(t):(e.lokiDataSources=[],e.relatedLogsCount=0)}))()}getLokiQueries(e,t=100){const{metric:r}=this._metricScene.state,n=this._logsConnectors.reduce(((t,n,a)=>{const i=n.getLokiQueryExpr(r,e);var o;i&&(t[null!==(o=n.name)&&void 0!==o?o:`connector-${a}`]=i);return t}),{});return Object.keys(n).map((e=>({refId:`RelatedLogs-${e}`,expr:n[e],maxLines:t,supportingQueryType:pt.id})))}checkLogsInDataSources(e){const t=[];let r=0,n=0;if(0===e.length)return this.lokiDataSources=[],void(this.relatedLogsCount=0);e.forEach((a=>{const o=new i.dt({datasource:{uid:a.uid},queries:[],key:`related_logs_check_${a.uid}`});o.setState({queries:this.getLokiQueries(a.uid)}),o.subscribeToState((i=>{var o;if((null===(o=i.data)||void 0===o?void 0:o.state)===U.LoadingState.Done){var s;if(n++,null===(s=i.data)||void 0===s?void 0:s.series){const e=i.data.series.reduce(((e,t)=>e+t.length),0);e>0&&(t.push(a),r+=e)}n===e.length&&(this.lokiDataSources=t,this.relatedLogsCount=r)}})),o.activate()}))}checkConditionsMetForRelatedLogs(){return this._logsConnectors.some((e=>e.checkConditionsMetForRelatedLogs()))}constructor(e){ft(this,"_logsConnectors",void 0),ft(this,"_metricScene",void 0),ft(this,"_changeHandlers",{lokiDataSources:[],relatedLogsCount:[]}),ft(this,"_internalState",{relatedLogsCount:0,lokiDataSources:[]}),this._metricScene=e,this._logsConnectors=[ut,rt(e)]}}function bt(){return yt.apply(this,arguments)}function yt(){return(yt=mt((function*(){const e=(0,a.getDataSourceSrv)().getList({logs:!0,type:"loki",filter:e=>"grafana"!==e.uid}),t=[],r=[];return yield Promise.all(e.map((e=>(0,a.getBackendSrv)().get(`/api/datasources/${e.id}/health`,void 0,void 0,{showSuccessAlert:!1,showErrorAlert:!1}).then((n=>"OK"===(null==n?void 0:n.status)?t.push(e):r.push(e))).catch((()=>r.push(e)))))),r.length&&console.warn(`Found ${r.length} unhealthy Loki data sources: ${r.map((e=>e.name)).join(", ")}`),t}))).apply(this,arguments)}function vt(){const e=(0,o.useStyles2)(wt);return l().createElement(o.Stack,{direction:"column",gap:2},l().createElement(o.Alert,{title:"No related logs found",severity:"info"},"We couldn't find any logs related to the current metric with your selected filters."),l().createElement(o.Text,null,"To find related logs, try the following:",l().createElement("ul",{className:e.list},l().createElement("li",null,"Adjust your label filters to include labels that exist in both the current metric and your logs"),l().createElement("li",null,"Select a metric created by a"," ",l().createElement(o.TextLink,{external:!0,href:"https://grafana.com/docs/loki/latest/alert/#recording-rules"},"Loki Recording Rule")),l().createElement("li",null,"Broaden the time range to include more data"))),l().createElement(o.Text,{variant:"bodySmall",color:"secondary"},"Note: Related logs is an experimental feature."))}function wt(e){return{list:(0,n.css)({paddingLeft:e.spacing(2),marginTop:e.spacing(1)})}}function Ot(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const St="related_logs/logs_panel_container";class kt extends i.Bs{_onActivate(){this.state.orchestrator.addLokiDataSourcesChangeHandler((()=>this.setupLogsPanel())),this.state.orchestrator.lokiDataSources.length?this.setupLogsPanel():this.state.orchestrator.findAndCheckAllDatasources()}showNoLogsFound(){i.jh.findByKeyAndType(this,St,i.xK).setState({body:new i.dM({component:vt})}),this.setState({controls:void 0}),this.state.orchestrator.relatedLogsCount=0}setupLogsPanel(){if(!this.state.orchestrator.lokiDataSources.length)return void this.showNoLogsFound();this._queryRunner&&(this._queryRunner.setState({queries:[]}),this._queryRunner=void 0),this._queryRunner=new i.dt({datasource:{uid:f.Kf},queries:[],key:"related_logs/logs_query"}),this._subs.add(this._queryRunner.subscribeToState((e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===U.LoadingState.Done){var r;0!==(e.data.series?e.data.series.reduce(((e,t)=>e+t.length),0):0)&&(null===(r=e.data.series)||void 0===r?void 0:r.length)||this.showNoLogsFound()}})));i.jh.findByKeyAndType(this,St,i.xK).setState({body:i.d0.logs().setTitle("Logs").setData(this._queryRunner).build()});const e=new i.yP({name:f.Az,label:"Logs data source",query:this.state.orchestrator.lokiDataSources.map((e=>`${e.name} : ${e.uid}`)).join(",")});this.setState({$variables:new i.Pj({variables:[e]}),controls:[new i.K8({layout:"vertical"})]}),this._subs.add(e.subscribeToState(((e,t)=>{e.value!==t.value&&(0,Q.z)("related_logs_action_clicked",{action:"logs_data_source_changed"})}))),this.updateLokiQuery()}updateLokiQuery(){if(!this._queryRunner)return;const e=i.jh.lookupVariable(f.Az,this);let t;if((0,Y.UG)(e)&&(t=e.getValue()),!t)return;const r=this.state.orchestrator.getLokiQueries(t);0!==r.length?this._queryRunner.setState({queries:r}):this.showNoLogsFound()}constructor(e){super({controls:[],body:new i.gF({templateColumns:"1fr",autoRows:"minmax(300px, 1fr)",children:[new i.xK({key:St,body:void 0})]}),orchestrator:e.orchestrator}),Ot(this,"_queryRunner",void 0),Ot(this,"_variableDependency",new i.Sh(this,{variableNames:[f.Az,f.Ao],onReferencedVariableValueChanged:e=>{e.state.name===f.Ao?this.state.orchestrator.handleFiltersChange():e.state.name===f.Az&&this.updateLokiQuery()}})),this.addActivationHandler(this._onActivate.bind(this))}}Ot(kt,"Component",(({model:e})=>{const{controls:t,body:r}=e.useState();return l().createElement(o.Stack,{gap:1,direction:"column",grow:1},l().createElement(o.Stack,{gap:1,direction:"row",justifyContent:"space-between",alignItems:"start"},l().createElement(o.Stack,{gap:1},null==t?void 0:t.map((e=>l().createElement(e.Component,{key:e.state.key,model:e})))),l().createElement(o.LinkButton,{href:`${a.config.appSubUrl}/a/grafana-lokiexplore-app`,target:"_blank",tooltip:"Navigate to the Logs Drilldown app",variant:"secondary",size:"sm",onClick:()=>(0,Q.z)("related_logs_action_clicked",{action:"open_logs_drilldown"})},"Open Logs Drilldown")),l().createElement(r.Component,{model:r}))}));var Et=r(4137);const xt=qe.L.METRIC_SELECT_SCENE.COPY_URL_LABEL,jt=({trail:e})=>{const[t,r]=(0,s.useState)(xt);return l().createElement(o.ToolbarButton,{variant:"canvas",icon:"share-alt",tooltip:t,onClick:()=>{if(navigator.clipboard){(0,Q.z)("selected_metric_action_clicked",{action:"share_url"});const t=`${a.config.appUrl.endsWith("/")?a.config.appUrl.slice(0,-1):a.config.appUrl}${Et.Gy}/${(0,g.xi)(e)}`;navigator.clipboard.writeText(t),r("Copied!"),setTimeout((()=>{r(xt)}),2e3)}}})};var _t=r(2425);function Pt(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function Ct(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Pt(i,n,a,o,s,"next",e)}function s(e){Pt(i,n,a,o,s,"throw",e)}o(void 0)}))}}function Lt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Bt="breakdown",Nt="related",Dt="logs";class Tt extends i.Bs{_onActivate(){void 0===this.state.actionView&&this.setActionView(Bt),this.relatedLogsOrchestrator.findAndCheckAllDatasources(),this.relatedLogsOrchestrator.addRelatedLogsCountChangeHandler((e=>{this.setState({relatedLogsCount:e})})),a.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(this.subscribeToEvent(f.H0,(e=>{var t;null===(t=this.state.body.state.selectedTab)||void 0===t||t.publishEvent(e)})))}getUrlState(){return{actionView:this.state.actionView}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=At.find((t=>t.value===e.actionView));t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView(void 0)}setActionView(e){const{body:t}=this.state,r=At.find((t=>t.value===e));r&&r.value!==this.state.actionView?(t.state.topView.state.children[0].setState({maxHeight:280}),t.setState({selectedTab:r.getScene(this)}),this.setState({actionView:r.value})):(t.state.topView.state.children[0].setState({maxHeight:"40%"}),t.setState({selectedTab:void 0}),this.setState({actionView:void 0}))}createRelatedLogsScene(){return e={orchestrator:this.relatedLogsOrchestrator},new kt(e);var e}constructor(e){var t;const r=null!==(t=e.autoQuery)&&void 0!==t?t:I(e.metric,e.nativeHistogram);var n,a,o,s;super(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Lt(e,t,r[t])}))}return e}({$variables:null!==(n=e.$variables)&&void 0!==n?n:(s=e.metric,new i.Pj({variables:[...(0,f.ym)(s),new i.fS({name:f.yr,label:"Group by",datasource:f.GH,includeAll:!0,defaultToAll:!0,query:{query:`label_names(${f.Rp})`,refId:"A"},value:"",text:""})]})),body:null!==(a=e.body)&&void 0!==a?a:new Qe({}),autoQuery:r,queryDef:null!==(o=e.queryDef)&&void 0!==o?o:r.main},e)),Lt(this,"relatedLogsOrchestrator",new gt(this)),Lt(this,"_urlSync",new i.So(this,{keys:["actionView"]})),Lt(this,"_variableDependency",new i.Sh(this,{variableNames:[f.Ao],onReferencedVariableValueChanged:()=>{this.relatedLogsOrchestrator.handleFiltersChange()}})),this.addActivationHandler(this._onActivate.bind(this))}}Lt(Tt,"Component",(({model:e})=>{const{body:t}=e.useState();return l().createElement("div",{"data-testid":"metric-scene"},l().createElement(t.Component,{model:t}))}));const At=[{displayName:"Breakdown",value:Bt,getScene:function(){return new Ve({})}},{displayName:"Related metrics",value:Nt,getScene:function(){return new c.t({})},description:"Relevant metrics based on current label filters"},{displayName:"Related logs",value:Dt,getScene:e=>e.createRelatedLogsScene(),description:"Relevant logs based on current label filters and time range"}];class Rt extends i.Bs{constructor(...e){var t;super(...e),t=this,Lt(this,"getLinkToExplore",Ct((function*(){const e=i.jh.getAncestor(t,Tt),r=i.jh.findByKeyAndType(t,Ke,w),n=void 0!==r.state.panel?i.jh.getData(r.state.panel).state.data:void 0;if(!n)throw new Error("Cannot get link to explore, no panel data found");return(0,i.pN)(n,e,n.timeRange)}))),Lt(this,"openExploreLink",Ct((function*(){(0,Q.z)("selected_metric_action_clicked",{action:"open_in_explore"}),t.getLinkToExplore().then((e=>{window.open(e,"_blank")}))})))}}function $t(e){return{actions:(0,n.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:16,zIndex:2}}),customTabsBar:(0,n.css)({paddingBottom:e.spacing(1)})}}Lt(Rt,"Component",(({model:e})=>{const t=i.jh.getAncestor(e,Tt),r=(0,o.useStyles2)($t),n=(0,g.kj)(e),[a,c]=function(e){const t=()=>(0,_t._r)().getBookmarkIndex(e),r=t(),[n,a]=(0,s.useState)(r);(0,s.useEffect)((()=>{const t=e.subscribeToEvent(i.bZ,(({payload:{prevState:t,newState:r}})=>{a((0,_t._r)().getBookmarkIndex(e))}));return()=>t.unsubscribe()}),[e]),r!==n&&a(r);const o=null!=n;return[o,()=>{if((0,Q.z)("bookmark_changed",{action:o?"toggled_off":"toggled_on"}),o){let e=t();for(;null!=e;)(0,_t._r)().removeBookmark(e),e=t()}else(0,_t._r)().addBookmark(e);a(t())}]}(n),{actionView:u}=t.useState();return l().createElement(o.Box,{paddingY:1,"data-testid":"metric-scene-details"},l().createElement("div",{className:r.actions},l().createElement(o.Stack,{gap:1},l().createElement(o.ToolbarButton,{variant:"canvas",tooltip:qe.L.METRIC_SELECT_SCENE.SELECT_NEW_METRIC_TOOLTIP,onClick:()=>{(0,Q.z)("selected_metric_action_clicked",{action:"unselect"}),n.publishEvent(new f.OO(void 0))}},"Select new metric"),l().createElement(o.ToolbarButton,{variant:"canvas",icon:"compass",tooltip:qe.L.METRIC_SELECT_SCENE.OPEN_EXPLORE_LABEL,onClick:e.openExploreLink}),l().createElement(jt,{trail:n}),l().createElement(o.ToolbarButton,{variant:"canvas",icon:a?l().createElement(o.Icon,{name:"favorite",type:"mono",size:"lg"}):l().createElement(o.Icon,{name:"star",type:"default",size:"lg"}),tooltip:qe.L.METRIC_SELECT_SCENE.BOOKMARK_LABEL,onClick:c}),n.state.embedded&&l().createElement(o.LinkButton,{href:(0,g.xi)(n),variant:"secondary",onClick:()=>(0,Q.z)("selected_metric_action_clicked",{action:"open_from_embedded"})},"Open"))),l().createElement(o.TabsBar,{className:r.customTabsBar},At.map(((e,r)=>{const n=e.displayName,a=e.value===Dt?t.state.relatedLogsCount:void 0,i=l().createElement(o.Tab,{key:r,label:n,counter:a,active:u===e.value,onChangeTab:()=>{const r={view:e.value};t.relatedLogsOrchestrator.checkConditionsMetForRelatedLogs()&&(r.related_logs_count=a),(0,Q.z)("metric_action_view_changed",r),t.setActionView(e.value)}});return e.description?l().createElement(o.Tooltip,{key:r,content:e.description,placement:"bottom-start",theme:"info"},i):i}))))}))},203:(e,t,r)=>{r.d(t,{t:()=>me});var n=r(6089),a=r(8531),i=r(6680),o=r(2007),s=r(3241),l=r(5959),c=r.n(l),u=r(6082),d=r(8371),p=r(6099),h=r(4351);class m{parse(e){if(this.config.maxDepth<=0)throw new Error("Max depth must be greater than 0");if(this.config.minGroupSize<1)throw new Error("Min group size must be greater than 0");if(this.config.idealMaxGroupSize<this.config.minGroupSize)throw new Error("Max group size must be greater than min group size");if(this.config.miscGroupKey&&""===this.config.miscGroupKey)throw new Error("miscGroupKey cannot be empty");return{root:this.parseStrings(e,0)}}parseStrings(e,t){const r={groups:new Map,values:[],descendants:0};for(const n of e){if(""===n.trim())continue;const e=f(n,t);let a=r.groups.get(e);a||(a={groups:new Map,values:[],descendants:0},r.groups.set(e,a)),a.values.push(n),a.descendants++}const n=[];for(let[e,a]of r.groups.entries())a.values.length<this.config.minGroupSize?(this.config.miscGroupKey?n.push(...a.values):r.values.push(...a.values),r.descendants+=a.values.length,r.groups.delete(e)):a.values.length>this.config.idealMaxGroupSize&&t<this.config.maxDepth-1?(a=this.parseStrings(a.values,t+1),r.groups.set(e,a),r.descendants+=a.descendants):r.descendants+=a.descendants;if(this.config.miscGroupKey&&n.length>0){const e={groups:new Map,values:n,descendants:n.length};r.groups.set(this.config.miscGroupKey,e)}return r}constructor(){var e,t,r;r=void 0,(t="config")in(e=this)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,this.config={minGroupSize:3,idealMaxGroupSize:30,maxDepth:100}}}function f(e,t){let r=0;for(let n=0;n<e.length;n++){const a=e.charCodeAt(n);if(!(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||a>=192&&a<=214||a>=216&&a<=246||a>=248&&a<=255||a>=256&&a<=383)&&(r++,r>t))return e.slice(0,n)}return e}var g=r(5749),b=r(3347),y=r(1385),v=r(2127),w=r(8203),O=r(4169),S=r(2646),k=r(2872),E=r(7139);function x(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function j(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){x(i,n,a,o,s,"next",e)}function s(e){x(i,n,a,o,s,"throw",e)}o(void 0)}))}}function _(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){_(e,t,r[t])}))}return e}function C(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const L="results truncated due to limit",B=new S.l;function N(){return N=j((function*(e,t,r,n,i,o,s){return a.config.featureToggles.enableScopesInMetricsExplore?function(e,t,r,n,a,i,o){return T.apply(this,arguments)}(e,t,r,n,i,o,s):yield function(e,t,r,n,a,i){return D.apply(this,arguments)}(e,t,n,i,o,s)})),N.apply(this,arguments)}function D(){return(D=j((function*(e,t,r,n,i,o){var s;const l=a.config.featureToggles.prometheusSpecialCharsInLabelValues?r.map((e=>function(e){const t=e.match(/^\{(.*)\}$/);var r;return null!==(r=null==t?void 0:t[1])&&void 0!==r?r:""}(B.renderLabels([{label:e.key,op:e.operator,value:e.value}])))):r.map((e=>`${(0,k.Nc)(e.key)}${e.operator}"${e.value}"`));let c=!1;if(n.length>0&&i.length>0){const e=(0,p.GX)(l,n,i);c=e.missingOtelTargets,l.push(e.jobsRegex),l.push(e.instancesRegex)}const u=`{${l.join(",")}}`,d=`/api/datasources/uid/${e}/resources/api/v1/label/__name__/values`,h=P({start:(0,E.mv)(t.from,!1),end:(0,E.mv)(t.to,!0)},u&&"{}"!==u?{"match[]":u}:{},o?{limit:o}:{}),m=yield(0,a.getBackendSrv)().get(d,h,"metrics-drilldown-names");return o&&(null===(s=m.warnings)||void 0===s?void 0:s.includes(L))?C(P({},m),{limitReached:!0,missingOtelTargets:c}):C(P({},m),{limitReached:!1,missingOtelTargets:c})}))).apply(this,arguments)}function T(){return(T=j((function*(e,t,r,n,a,i,o){var s;const l=yield(0,O.WD)(e,t,r,n,"__name__",o,"metrics-drilldown-names");return a.length>0&&i.length>0&&(n.push({key:"job",operator:"=~",value:(null==a?void 0:a.join("|"))||""}),n.push({key:"instance",operator:"=~",value:(null==i?void 0:i.join("|"))||""})),C(P({},l.data),{limitReached:!!o&&!!(null===(s=l.data.warnings)||void 0===s?void 0:s.includes(L)),missingOtelTargets:!1})}))).apply(this,arguments)}var A,R,$,M=r(384),V=r(3552),F=r(8222),H=r(7437),I=r(7265),z=r(7781);function U(e){return t=>{const r=i.jh.getData(t);r&&r.subscribeToState((r=>{var n,a,o;if((null===(n=r.data)||void 0===n?void 0:n.state)===z.LoadingState.Loading||(null===(a=r.data)||void 0===a?void 0:a.state)===z.LoadingState.Error)return;const s=i.jh.getAncestor(t,me);if(!(null===(o=r.data)||void 0===o?void 0:o.series.length))return void s.updateMetricPanel(e,!0,!0);let l=!1;for(const e of r.data.series){for(const t of e.fields)if(t.type===z.FieldType.number&&(l=t.values.some((e=>null!=e&&!isNaN(e)&&0!==e)),l))break;if(l)break}s.updateMetricPanel(e,!0,!l)}))}}class G extends i.Bs{}function q(e){return{badge:(0,n.css)({borderRadius:e.shape.radius.pill,border:`1px solid ${e.colors.info.text}`,background:e.colors.info.transparent,cursor:"auto",width:"112px",padding:"0rem 0.25rem 0 0.35rem"})}}function W(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}$=()=>{const e=(0,o.useStyles2)(q);return c().createElement(o.Badge,{className:e.badge,color:"blue",text:"Native Histogram"})},(R="Component")in(A=G)?Object.defineProperty(A,R,{value:$,enumerable:!0,configurable:!0,writable:!0}):A[R]=$;class K extends i.Bs{constructor(...e){super(...e),W(this,"onClick",(()=>{this.publishEvent(new v.OO(this.state.metric),!0)}))}}function Q(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Q(e,t,r[t])}))}return e}W(K,"Component",(({model:e})=>{const{title:t,metric:r}=e.useState();return c().createElement(o.Button,{variant:"primary",size:"sm",fill:"outline",onClick:e.onClick,"data-testid":`select ${r}`},t)}));class J extends i.Bs{updateQuery(){const e=i.jh.lookupVariable(v.Ao,this),t=(0,M.BE)(e)?e.state.filters:[],{isRateQuery:r,groupings:n}=this.determineQueryProperties(),a=(0,V.$5)({metric:this.state.metric,filters:t,isRateQuery:r,groupings:n,ignoreUsage:!0,useOtelJoin:this.state.hasOtelResources});this.setState({$data:new i.dt({datasource:v.GH,maxDataPoints:v.up,queries:[Y({refId:"A",expr:a,legendFormat:this.state.metric,fromExploreMetrics:!0},this.state.isHistogram?{format:"heatmap"}:{})]})})}determineQueryProperties(){const e=this.state.metric.split("_").at(-1),t=new Set(["count","total","sum","bucket"]).has(e||"");let r;return this.state.isHistogram&&(r=["le"]),{isRateQuery:t,groupings:r}}constructor(e){super(e),Q(this,"_variableDependency",void 0),this._variableDependency=new i.Sh(this,{variableNames:[v.Ao],onReferencedVariableValueChanged:()=>{this.updateQuery()}}),this.updateQuery()}}function X(e,t,r,n,a,o){const s=e.split("_"),l=s.at(-1),c=s.at(-2),u=(0,H.l_)(c);let d=[new K({metric:e,title:"Select"})];a&&d.unshift(new G({}));const p=Boolean("bucket"===l||a);let h=p?(0,F.sp)({title:e,unit:u}):(0,F.sj)({title:e,unit:u});h=h.setColor({mode:"fixed",fixedColor:(0,O.Vy)(t)}).setDescription(n).setHeaderActions(d).setShowMenuAlways(!0),o||(h=h.setMenu(new I.G({labelName:e})));const m=h.build();return new i.xK({body:new J({$behaviors:[U(e)],$variables:new i.Pj({variables:(0,v.ym)(e)}),body:m,metric:e,hasOtelResources:r,isHistogram:p})})}Q(J,"Component",(({model:e})=>{const{body:t}=e.useState();return c().createElement(t.Component,{model:t})}));var Z=r(3823),ee=r.n(Z);const te=new Map;function re(e,t){let r=te.get(e);r||(r=new Map,te.set(e,r));let n=r.get(t);if(!n){const a=e.split("_"),i=a.slice(0,a.length/2).join("_");n={halfLeven:ee()(i,t)||0,wholeLeven:ee()(e,t)||0},r.set(t,n)}return n}const ne=/[^a-z0-9_:]+/;function ae(e){return(null==e?void 0:e.toLowerCase().split(ne).filter((e=>e.length>0)))||[]}function ie(e){const t=function(e=[]){const t=new Set(e.map((e=>e.toLowerCase().trim())));return Array.from(t)}(ae(e)).filter((e=>e.length>0)).map((e=>`(.*${e.toLowerCase()}.*)`)),r=t.length;if(0===t.length)return null;return`(?i:${t.join("|")}){${r}}`}var oe=r(2188);function se(e){return null!=e&&"value"in e&&"from"in e&&"to"in e}function le(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function ce(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){le(i,n,a,o,s,"next",e)}function s(e){le(i,n,a,o,s,"throw",e)}o(void 0)}))}}function ue(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function de(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){ue(e,t,r[t])}))}return e}function pe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const he="175px";class me extends i.Bs{getUrlState(){return{metricPrefix:this.state.metricPrefix}}updateFromUrl(e){"string"==typeof e.metricPrefix&&this.state.metricPrefix!==e.metricPrefix&&this.setState({metricPrefix:e.metricPrefix})}_onActivate(){0===this.state.body.state.children.length?this.buildLayout():this.ignoreNextUpdate=!0;const e=(0,O.kj)(this);this._subs.add(e.subscribeToEvent(v.OO,(t=>{if(void 0!==t.payload){const t=ae(ge(e)).length;(0,b.z)("metric_selected",{from:"metric_list",searchTermCount:t})}}))),this._subs.add(e.subscribeToEvent(i.bZ,(e=>{if(null!=(t=e.payload.changedObject)&&"getTimeZone"in t&&se(t.state)){const{prevState:t,newState:r}=e.payload;if(se(t)&&se(r)&&t.from===r.from&&t.to===r.to)return}var t}))),this._subs.add(e.subscribeToState((({metricSearch:e},t)=>{const r=ae(t.metricSearch),n=ae(e);(0,s.isEqual)(r,n)||this._debounceRefreshMetricNames()}))),this.subscribeToState(((e,t)=>{e.metricNames!==t.metricNames&&this.onMetricNamesChanged()}));const t=i.jh.lookupVariable(v.DU,e);(0,M.BE)(t)&&this._subs.add(t.subscribeToState(((e,t)=>{(0,s.isEqual)(e.filters,t.filters)||this._debounceRefreshMetricNames()}))),this._subs.add(e.subscribeToState((()=>{this._debounceRefreshMetricNames()}))),this._subs.add(e.subscribeToState((()=>{this.buildLayout()}))),a.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(e.subscribeToEvent(v.H0,(()=>{this._debounceRefreshMetricNames()})));const r=i.jh.lookupVariable(v.Ao,this);(0,M.BE)(r)&&this._subs.add(null==r?void 0:r.subscribeToState(((e,t)=>{(0,s.isEqual)(t,e)||this._debounceRefreshMetricNames()}))),this._debounceRefreshMetricNames()}_refreshMetricNames(){var e=this;return ce((function*(){var t;const r=(0,O.kj)(e),n=null===(t=r.state.$timeRange)||void 0===t?void 0:t.state;if(!n)return;const o=[],s=i.jh.lookupVariable(v.Ao,e);var l;const c=(0,M.BE)(s)&&null!==(l=null==s?void 0:s.state.filters)&&void 0!==l?l:[];c.length>0&&o.push(...c);const u=ie(r.state.metricSearch);u&&o.push({key:"__name__",operator:"=~",value:u});const h=i.jh.interpolate(r,v.gR);e.setState({metricNamesLoading:!0,metricNamesError:void 0,metricNamesWarning:void 0});try{let t=[],a=[];if(r.state.useOtelExperience){const e=(0,p.Dz)(r),i=yield(0,d.a8)(h,n,e.filters);var m,f;t=null!==(m=null==i?void 0:i.jobs)&&void 0!==m?m:[],a=null!==(f=null==i?void 0:i.instances)&&void 0!==f?f:[]}const i=yield function(e,t,r,n,a,i,o){return N.apply(this,arguments)}(h,n,(0,oe.f_)(),o,t,a,2e4),s=function(e){const t=ae(e).map((e=>`(?=(.*${e.toLowerCase()}.*))`));if(0===t.length)return null;const r=t.join("");return new RegExp(r,"igy")}(ge(e));let l=s?i.data.filter((e=>!s||s.test(e))):i.data;const c=l,u=e.state.metricPrefix;if(u&&"all"!==u){const e=new RegExp(`(^${u}.*)`,"igy");l=l.filter((t=>!e||e.test(t)))}let g=i.limitReached?"This feature will only return up to 20000 metric names for performance reasons. This limit is being exceeded for the current data source. Add search terms or label filters to narrow down the number of metric names returned.":void 0;!r.state.useOtelExperience||0!==t.length&&0!==a.length||(l=[],g=void 0);let b=e.state.body,y=yield e.generateGroups(c);e.setState({metricNames:l,rootGroup:y,body:b,metricNamesLoading:!1,metricNamesWarning:g,metricNamesError:i.error,missingOtelTargets:i.missingOtelTargets})}catch(t){let r="Unknown error";(0,a.isFetchError)(t)&&(t.cancelled?r="Request cancelled":t.statusText&&(r=t.statusText)),e.setState({metricNames:void 0,metricNamesLoading:!1,metricNamesError:r})}}))()}generateGroups(e=[]){return ce((function*(){const t=new m;t.config=pe(de({},t.config),{maxDepth:2,minGroupSize:2,miscGroupKey:"misc"});const{root:r}=t.parse(e);return r}))()}onMetricNamesChanged(){const e=this.state.metricNames||[],t=new Set(e);Object.values(this.previewCache).forEach((e=>{t.has(e.name)||(e.isEmpty=!0)}));const r=(0,O.kj)(this),n=void 0!==r.state.metric?(a=e,o=r.state.metric,a.sort(((e,t)=>{const r=re(e,o),n=re(t,o);return r.halfLeven+r.wholeLeven-(n.halfLeven+n.wholeLeven)}))):e;var a,o;const s={};Object.keys(this.previewCache).forEach((e=>{t.has(e)||delete this.previewCache[e]}));for(let e=0;e<n.length;e++){const t=n[e];if(Object.keys(s).length>120)break;const r=this.previewCache[t];s[t]=r||{name:t,index:e,loaded:!1}}try{delete s[i.jh.getAncestor(this,y.Rd).state.metric]}catch(e){}this.previewCache=s,this.buildLayout()}sortedPreviewMetrics(){return Object.values(this.previewCache).sort(((e,t)=>e.isEmpty&&t.isEmpty?e.index-t.index:e.isEmpty?1:t.isEmpty?-1:e.index-t.index))}buildLayout(){var e=this;return ce((function*(){const t=(0,O.kj)(e);if(e.ignoreNextUpdate)return void(e.ignoreNextUpdate=!1);const r=[],n=e.sortedPreviewMetrics();for(let e=0;e<n.length;e++){const a=n[e],i=yield t.getMetricMetadata(a.name),o=(0,g.R)(i);if(a.itemRef&&a.isPanel){r.push(a.itemRef.resolve());continue}const s=t.isNativeHistogram(a.name),l=Boolean(t.state.hasOtelResources),c=X(a.name,e,l,o,s,!0);a.itemRef=c.getRef(),a.isPanel=!0,r.push(c)}e.state.body.setState({children:r,autoRows:he})}))()}constructor(e){var t,r;super(de({$variables:e.$variables,metricPrefix:null!==(t=e.metricPrefix)&&void 0!==t?t:"all",body:null!==(r=e.body)&&void 0!==r?r:new i.gF({children:[],templateColumns:"repeat(auto-fill, minmax(450px, 1fr))",autoRows:he,isLazy:!0})},e)),ue(this,"previewCache",{}),ue(this,"ignoreNextUpdate",!1),ue(this,"_debounceRefreshMetricNames",(0,s.debounce)((()=>this._refreshMetricNames()),1e3)),ue(this,"_urlSync",new i.So(this,{keys:["metricPrefix"]})),ue(this,"_variableDependency",new i.Sh(this,{variableNames:[v.EY,v.DU],onReferencedVariableValueChanged:()=>{this._debounceRefreshMetricNames()}})),ue(this,"updateMetricPanel",((e,t,r)=>{const n=this.previewCache[e];n&&(n.isEmpty=r,n.loaded=t,this.previewCache[e]=n,"All"===this.state.metricPrefix&&this.buildLayout())})),ue(this,"onSearchQueryChange",(e=>{const t=e.currentTarget.value;(0,O.kj)(this).setState({metricSearch:t})})),ue(this,"onPrefixFilterChange",(e=>{this.setState({metricPrefix:e.value}),this._refreshMetricNames()})),ue(this,"reportPrefixFilterInteraction",(e=>{(0,b.z)("prefix_filter_clicked",{from:"metric_list",action:e?"open":"close"})})),ue(this,"onToggleOtelExperience",(()=>{const e=(0,O.kj)(this),t=e.state.useOtelExperience;let r=!0;t?((0,b.z)("otel_experience_toggled",{value:"off"}),r=!1,e.resetOtelExperience()):((0,b.z)("otel_experience_toggled",{value:"on"}),e.checkDataSourceForOTelResources()),(0,h.yx)(!t),e.setState({useOtelExperience:!t,resettingOtel:r,startButtonClicked:!1})})),this.addActivationHandler(this._onActivate.bind(this))}}function fe(e){return{container:(0,n.css)({display:"flex",flexDirection:"column"}),headingWrapper:(0,n.css)({marginBottom:e.spacing(.5)}),header:(0,n.css)({flexGrow:0,display:"flex",gap:e.spacing(2),marginBottom:e.spacing(2),alignItems:"flex-end"}),searchField:(0,n.css)({flexGrow:1,marginBottom:0}),metricTabGroup:(0,n.css)({marginBottom:e.spacing(2)}),displayOption:(0,n.css)({flexGrow:0,marginBottom:0,minWidth:"184px"}),displayOptionTooltip:(0,n.css)({display:"flex",gap:e.spacing(1)}),warningIcon:(0,n.css)({color:e.colors.warning.main}),badgeStyle:(0,n.css)({display:"flex",height:"1rem",padding:"0rem 0.25rem 0 0.30rem",alignItems:"center",borderRadius:e.shape.radius.pill,border:`1px solid ${e.colors.warning.text}`,color:`${e.colors.warning.text}`,background:e.colors.info.transparent,marginTop:"4px",marginLeft:"-3px"})}}function ge(e){return(0,O.kj)(e).state.metricSearch||""}ue(me,"Component",(({model:e})=>{const{body:t,metricNames:r,metricNamesError:n,metricNamesLoading:a,metricNamesWarning:i,rootGroup:s,metricPrefix:d,missingOtelTargets:p}=e.useState(),{children:h}=t.useState(),m=(0,O.kj)(e),f=(0,o.useStyles2)(fe),[g,b]=(0,l.useReducer)((()=>!0),!1),{metricSearch:y,useOtelExperience:v,hasOtelResources:S,isStandardOtel:k,metric:E}=m.useState(),x=0===h.length&&y,j=!a&&r&&0===r.length,_=a&&0===h.length,P=_?void 0:p?"There are no metrics found. Please adjust your filters based on your OTel resource attributes.":(j?"There are no results found. Try a different time range or a different data source.":x&&"There are no results found. Try adjusting your search or filters.")||void 0,C=i?c().createElement(o.Tooltip,{content:c().createElement(c().Fragment,null,c().createElement("h4",null,"Unable to retrieve metric names"),c().createElement("p",null,i))},c().createElement(o.Icon,{className:f.warningIcon,name:"exclamation-triangle"})):void 0;var L,B;return c().createElement("div",{className:f.container,"data-testid":"scene"},c().createElement("div",{className:f.header,"data-testid":"scene-header"},c().createElement(o.Field,{label:u.L.SEARCH.TITLE,className:f.searchField},c().createElement(o.Input,{placeholder:u.L.SEARCH.TITLE,prefix:c().createElement(o.Icon,{name:"search"}),value:y,onChange:e.onSearchQueryChange,suffix:C})),c().createElement(o.Field,{label:c().createElement("div",{className:f.displayOptionTooltip},"View by",c().createElement(o.IconButton,{name:"info-circle",size:"sm",variant:"secondary",tooltip:"View by the metric prefix. A metric prefix is a single word at the beginning of the metric name, relevant to the domain the metric belongs to."})),className:f.displayOption},c().createElement(o.Combobox,{value:d,onChange:t=>e.onPrefixFilterChange(t),options:[{label:"All metric names",value:"all"},...Array.from(null!==(L=null==s?void 0:s.groups.keys())&&void 0!==L?L:[]).map((e=>({label:`${e}_`,value:e})))],width:16})),!E&&S&&c().createElement(o.Field,{label:c().createElement(c().Fragment,null,c().createElement("div",{className:f.displayOptionTooltip},"Filter by",c().createElement(o.Tooltip,{content:c().createElement("div",null,c().createElement("p",null,"The OTel experience is deprecated in Grafana Metrics Drilldown."),c().createElement("p",null,"Please use the following docs to promote your OTel resource attributes as metric labels with"," ",c().createElement("a",{href:"https://grafana.com/docs/mimir/latest/configure/configure-otel-collector/#work-with-default-opentelemetry-labels",target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"underline"}},"Mimir")," ","and"," ",c().createElement("a",{href:"https://prometheus.io/docs/guides/opentelemetry/#promoting-resource-attributes",target:"_blank",rel:"noopener noreferrer",style:{textDecoration:"underline"}},"Prometheus"),".")),placement:"bottom",interactive:!0},c().createElement(o.IconButton,{name:"info-circle",size:"sm",variant:"secondary","aria-label":"Information about OTel experience"})),c().createElement("div",null,c().createElement(o.Badge,{text:"Deprecated",color:"orange",className:f.badgeStyle})))),className:f.displayOption},c().createElement("div",{title:k?"":"This setting is disabled because this is not an OTel native data source."},c().createElement(o.InlineSwitch,{disabled:!k,showLabel:!0,label:u.L.METRIC_SELECT_SCENE.OTEL_LABEL,value:v,onChange:e.onToggleOtelExperience})))),n&&c().createElement(o.Alert,{title:"Unable to retrieve metric names",severity:"error"},c().createElement("div",null,"We are unable to connect to your data source. Double check your data source URL and credentials."),c().createElement("div",null,"(",n,")")),i&&!g&&c().createElement(o.Alert,{title:"Unable to retrieve all metric names",severity:"warning",onSubmit:b,onRemove:b},c().createElement("div",null,i)),c().createElement(w.O,{isLoading:_,blockingMessage:P},c().createElement("div",{"data-testid":"scene-body"},null!=(B=t)&&"toggleDirection"in B&&"children"in B.state&&c().createElement(t.Component,{model:t}),function(e){return null!=e&&"isDraggable"in e&&"templateColumns"in e.state}(t)&&c().createElement(t.Component,{model:t}))))}))},8203:(e,t,r)=>{r.d(t,{O:()=>s});var n=r(6089),a=r(2007),i=r(5959),o=r.n(i);function s({blockingMessage:e,isLoading:t,children:r}){const n=(0,a.useStyles2)(l);return t&&!e&&(e="Loading..."),t?o().createElement(a.LoadingPlaceholder,{className:n.statusMessage,text:e}):e?o().createElement("div",{className:n.statusMessage},e):r}function l(e){return{statusMessage:(0,n.css)({fontStyle:"italic",marginTop:e.spacing(7),textAlign:"center",width:"100%"})}}},2425:(e,t,r)=>{r.d(t,{yn:()=>y,_r:()=>w});var n=r(7781),a=r(6680),i=r(3241),o=r(8531),s=r(2007),l=r(5959),c=r.n(l),u=r(4137),d=r(2127),p=r(4169);var h=r(7570);function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class f{_loadRecentTrailsFromStorage(){const e=[],t=localStorage.getItem(d.sQ);if(t){const r=JSON.parse(t);for(const t of r){const r=this._deserializeTrail(t);e.push(r.getRef())}}return e}_loadBookmarksFromStorage(){const e=localStorage.getItem(d.jT);return(e?JSON.parse(e):[]).map((e=>{if(null!=(t=e)&&"object"==typeof t&&"history"in t){const t=null!=e.currentStep?e.currentStep:e.history.length-1;return{urlValues:e.history[t].urlValues,createdAt:e.createdAt||Date.now()}}var t;return e}))}_deserializeTrail(e){const t=new h.b8({}),r="urlValues"in e;return"history"in e?e.history.map((e=>{this._loadFromUrl(t,e.urlValues)})):r&&this._loadFromUrl(t,e.urlValues),t.setState(a.Go.cloneSceneObjectState(t.state,{})),t}_serializeTrail(e){return{urlValues:a.Go.getUrlState(e)}}getTrailForBookmarkIndex(e){const t=this._bookmarks[e];return t?this.getTrailForBookmark(t):(0,p.ef)()}getTrailForBookmark(e){const t=y(e);for(const e of this._recent){const r=e.resolve();if(y(r)===t)return r}const r=new h.b8({});return this._loadFromUrl(r,e.urlValues),r}_loadFromUrl(e,t){const r=n.urlUtil.renderUrl("",t);a.Go.syncStateFromSearchParams(e,new URLSearchParams(r))}get recent(){return this._recent}get lastModified(){return this._lastModified}load(){this._recent=this._loadRecentTrailsFromStorage(),this._bookmarks=this._loadBookmarksFromStorage(),this._refreshBookmarkIndexMap(),this._lastModified=Date.now()}setRecentTrail(e,t){if(!e.state.trailActivated||t)return;this._recent=this._recent.filter((t=>t!==e.getRef()));const r=g(e);this._recent=this._recent.filter((e=>{const t=g(e.resolve());return!(0,i.isEqual)(r,t)})),this._recent.unshift(e.getRef()),this._save()}get bookmarks(){return this._bookmarks}addBookmark(e){const t={urlValues:a.Go.getUrlState(e),createdAt:Date.now()};this._bookmarks.unshift(t),this._refreshBookmarkIndexMap(),this._save(),function(){const e=(0,o.getAppEvents)(),t=(0,p.y)(u.bw.Drilldown),r=t?c().createElement("i",null,"the Metrics Reducer sidebar"):c().createElement("i",null,"Drilldown > Metrics");e.publish({type:n.AppEvents.alertSuccess.name,payload:["Bookmark created",c().createElement(s.Stack,{gap:2,direction:"row",key:"bookmark-notification"},c().createElement("div",null,"You can view bookmarks under ",r),!t&&c().createElement(s.LinkButton,{fill:"solid",variant:"secondary",href:d.QX},"View bookmarks"))]})}()}removeBookmark(e){e<this._bookmarks.length&&(this._bookmarks.splice(e,1),this._refreshBookmarkIndexMap(),this._save())}getBookmarkIndex(e){const t=y(e);return this._bookmarkIndexMap.get(t)}_refreshBookmarkIndexMap(){this._bookmarkIndexMap.clear(),this._bookmarks.forEach(((e,t)=>{const r=y(e);this._bookmarkIndexMap.set(r,t)}))}constructor(){m(this,"_recent",[]),m(this,"_bookmarks",[]),m(this,"_save",void 0),m(this,"_lastModified",void 0),m(this,"_bookmarkIndexMap",new Map),this.load(),this._lastModified=Date.now();const e=()=>{const e=this._recent.slice(0,20).map((e=>this._serializeTrail(e.resolve())));localStorage.setItem(d.sQ,JSON.stringify(e)),localStorage.setItem(d.jT,JSON.stringify(this._bookmarks)),this._lastModified=Date.now()};this._save=(0,i.debounce)(e,1e3),window.addEventListener("beforeunload",(()=>{this._save=e}))}}function g(e){const t=a.Go.getUrlState(e);return b(t),t}function b(e){var t;(delete e.actionView,delete e.layout,delete e.metricSearch,delete e.refresh,""!==e["var-groupby"]&&void 0!==e["var-groupby"]||(e["var-groupby"]="$__all"),"string"!=typeof e["var-filters"])&&(e["var-filters"]=null===(t=e["var-filters"])||void 0===t?void 0:t.filter((e=>""!==e)));return e}function y(e){return e instanceof h.b8?JSON.stringify(g(e)):JSON.stringify(b(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){m(e,t,r[t])}))}return e}({},e.urlValues)))}let v;function w(){return v||(v=new f),v}},5521:(e,t,r)=>{r.d(t,{O:()=>v,C:()=>y});var n=r(6089),a=r(6680),i=r(2007),o=r(5959),s=r.n(o),l=r(1053),c=r(7781),u=r(2127),d=r(2425),p=r(4169),h=r(384);const m=(e,t,r)=>e.length+2+t.length>r?t.substring(0,r-e.length-5)+"...":t;function f(e){const{onSelect:t,onDelete:r,bookmark:n}=e,l=(0,i.useStyles2)(g),f=(0,o.useMemo)((()=>{let t=e.trail||n&&(0,d._r)().getTrailForBookmark(n);if(!t)return null;const r=a.jh.lookupVariable(u.Ao,t);if(!(0,h.BE)(r))return null;const i=(null==n?void 0:n.createdAt)||t.state.createdAt;return{filters:r.state.filters,metric:t.state.metric,createdAt:i}}),[e.trail,n]);if(!f)return null;const{filters:b,metric:y,createdAt:v}=f,w=m("",(0,p.aO)(y),27),O=`${e.compactHeight&&b.length>0?l.cardTall:""}`,S=`${l.card} ${e.wide?l.cardWide:""} ${O}`;return s().createElement("article",{"data-testid":`data-trail-card ${w}`},s().createElement(i.Card,{onClick:t,className:S},s().createElement(i.Card.Heading,null,s().createElement("div",{className:l.metricValue},w)),s().createElement(i.Card.Meta,{className:l.meta},b.map((e=>s().createElement("span",{key:e.key},s().createElement("div",{className:l.secondaryFont},e.key,": "),s().createElement("div",{className:l.primaryFont},m(e.key,e.value,44)))))),s().createElement("div",{className:l.deleteButton},r&&s().createElement(i.Card.SecondaryActions,null,s().createElement(i.IconButton,{key:"delete",name:"trash-alt",className:l.secondary,tooltip:"Remove bookmark",onClick:r,"data-testid":"deleteButton"})))),s().createElement("div",{className:l.date},s().createElement("div",{className:l.secondaryFont},"Date created: "),s().createElement("div",{className:l.primaryFont},v&&(0,c.dateTimeFormat)(v,{format:"YYYY-MM-DD"}))))}function g(e){return{metricValue:(0,n.css)({display:"inline",color:e.colors.text.primary,fontWeight:500,wordBreak:"break-all"}),card:(0,n.css)({position:"relative",width:"318px",padding:`12px ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`,alignItems:"start",marginBottom:0,borderTop:`1px solid ${e.colors.border.weak}`,borderRight:`1px solid ${e.colors.border.weak}`,borderLeft:`1px solid ${e.colors.border.weak}`,borderBottom:"none",borderRadius:"2px 2px 0 0"}),cardWide:(0,n.css)({width:"100%"}),cardTall:(0,n.css)({height:"110px"}),secondary:(0,n.css)({color:e.colors.text.secondary,fontSize:"12px"}),date:(0,n.css)({border:`1px solid ${e.colors.border.weak}`,borderRadius:"0 0 2px 2px",padding:`${e.spacing(1)} ${e.spacing(2)}`,backgroundColor:e.colors.background.primary}),meta:(0,n.css)({flexWrap:"wrap",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"36px",margin:0,gridArea:"Meta",color:e.colors.text.secondary,whiteSpace:"nowrap"}),primaryFont:(0,n.css)({display:"inline",color:e.colors.text.primary,fontSize:"12px",fontWeight:"500",letterSpacing:"0.018px"}),secondaryFont:(0,n.css)({display:"inline",color:e.colors.text.secondary,fontSize:"12px",fontWeight:"400",lineHeight:"18px",letterSpacing:"0.018px"}),deleteButton:(0,n.css)({position:"absolute",bottom:e.spacing(1),right:e.spacing(1)})}}var b=r(3347);const y={listeners:new Set,emit:function(e){this.listeners.forEach((t=>t(e)))},subscribe:function(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}};class v extends a.Bs{onActivate(){}constructor({key:e,title:t,description:r,icon:n,disabled:a}){super({key:e,title:t,description:r,icon:n,disabled:null!=a&&a,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}var w,O,S;function k(e){return{container:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%"}),bookmarksList:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1.5),overflowY:"auto",paddingRight:e.spacing(1)}),emptyState:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",height:"100px",color:e.colors.text.secondary,fontStyle:"italic"})}}S=({model:e})=>{const t=(0,i.useStyles2)(k),{title:r,description:n}=e.useState(),{bookmarks:a}=(0,d._r)(),[c,u]=(0,o.useState)(Date.now()),p=e=>{(0,b.z)("exploration_started",{cause:"bookmark_clicked"});const t=(0,d._r)().getTrailForBookmarkIndex(e);(0,d._r)().setRecentTrail(t),function(e){y.emit(e)}(t)};return s().createElement("div",{className:t.container},s().createElement(l._,{title:r,description:n,"data-testid":"bookmarks-list-sidebar"}),a.length>0?s().createElement("div",{className:t.bookmarksList},a.map(((e,t)=>s().createElement(f,{key:(0,d.yn)(e),bookmark:e,onSelect:()=>p(t),onDelete:()=>(e=>{(0,d._r)().removeBookmark(e),(0,b.z)("bookmark_changed",{action:"deleted"}),u(Date.now())})(t),wide:!0,compactHeight:!0})))):s().createElement("div",{className:t.emptyState},"No bookmarks yet"))},(O="Component")in(w=v)?Object.defineProperty(w,O,{value:S,enumerable:!0,configurable:!0,writable:!0}):w[O]=S},1053:(e,t,r)=>{r.d(t,{_:()=>s});var n=r(6089),a=r(2007),i=r(5959),o=r.n(i);function s({title:e,description:t}){const r=(0,a.useStyles2)(l);return o().createElement("h6",{className:r.title},o().createElement("span",null,e),o().createElement(a.Tooltip,{content:t,placement:"top"},o().createElement(a.Icon,{name:"info-circle",size:"sm",className:r.infoIcon})))}function l(e){return{title:(0,n.css)({fontSize:"15px",fontWeight:e.typography.fontWeightLight,borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(.5)}),infoIcon:(0,n.css)({marginLeft:e.spacing(1),cursor:"pointer",color:e.colors.text.secondary,position:"relative",top:"-4px"})}}},3616:(e,t,r)=>{r.d(t,{HA:()=>c,jx:()=>l});var n=r(7781),a=r(8531);function i(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function o(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}const s=console;function l(e,t){const r=t.reduce(((e,t,r)=>o(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){i(e,t,r[t])}))}return e}({},e),{[`info${r+1}`]:t})),{handheldBy:"displayError"});s.error(e,r),(0,a.getAppEvents)().publish({type:n.AppEvents.alertError.name,payload:t})}function c(e){s.warn(e),(0,a.getAppEvents)().publish({type:n.AppEvents.alertWarning.name,payload:e})}},3552:(e,t,r)=>{r.d(t,{$5:()=>c,UL:()=>l});var n=r(2872),a=r(8162),i=r(2127);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const s="avg";function l(e,t=s){return e?"sum":t}function c({metric:e,filters:t,isRateQuery:r,useOtelJoin:c,groupings:u,ignoreUsage:d=!1,nonRateQueryFunction:p=s}){const h=!(0,n.Rq)(e);let m=new a.r4({metric:h?"":e,values:{},defaultOperator:a.md.equal,defaultSelectors:[...h?[{label:(0,n.Nc)(e),operator:a.md.equal,value:"__REMOVE__"}]:[],...d?[{label:"__ignore_usage__",operator:a.md.equal,value:""}]:[],...t.map((({key:e,value:t,operator:r})=>({label:(0,n.Nc)(e),operator:r,value:t})))]}).toString();h&&(m=m.replace('="__REMOVE__"',"")),r&&(m=a.GH.rate({expr:m,interval:"$__rate_interval"}));const f=c?`${m} ${i.tM}`:m;return a.GH[l(r,p)](function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){o(e,t,r[t])}))}return e}({expr:f},(null==u?void 0:u.length)?{by:u}:{}))}},8222:(e,t,r)=>{r.d(t,{s1:()=>l,sj:()=>o,sp:()=>s});var n=r(6680),a=r(1625),i=r(5938);function o({title:e,unit:t}){return n.d0.timeseries().setTitle(e).setUnit(t).setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:a.$N.Multi,sort:a.xB.Descending}).setCustomFieldConfig("fillOpacity",9)}function s({title:e,unit:t}){return n.d0.heatmap().setTitle(e).setUnit(t).setOption("calculate",!1).setOption("color",{mode:i.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1})}function l({title:e,unit:t}){return n.d0.timeseries().setTitle(e).setUnit(t).setCustomFieldConfig("fillOpacity",9).setOption("tooltip",{mode:a.$N.Multi,sort:a.xB.Descending}).setOption("legend",{showLegend:!1})}},7437:(e,t,r)=>{r.d(t,{MM:()=>m,l_:()=>h});const n="short",a="cps",i="bytes",o="seconds",s="percent",l="count",c={[i]:i,[o]:"s",[s]:s,[l]:n},u=Object.keys(c),d={[i]:"Bps",[o]:n,[l]:a,[s]:s};function p(e){if(!e)return null;const t=e.toLowerCase().split("_").slice(-2);for(let e=t.length-1;e>=Math.max(0,t.length-2);e--){const r=t[e];if(u.includes(r))return r}return null}function h(e){if(!e)return n;const t=p(e);return t&&c[t.toLowerCase()]||n}function m(e){if(!e)return a;const t=p(e);return t&&d[t]||a}},6082:(e,t,r)=>{r.d(t,{L:()=>n});const n={HOME:{TITLE:"Start your metrics exploration!",SUBTITLE:"Explore your Prometheus-compatible metrics without writing a query.",START_BUTTON:"Let's start!",RECENT_HEADER:"Or view a recent exploration",BOOKMARKS_HEADER:"Or view bookmarks"},SEARCH:{TITLE:"Search metrics"},METRIC_SELECT_SCENE:{OTEL_LABEL:"OTel experience",OPEN_EXPLORE_LABEL:"Open in explore",COPY_URL_LABEL:"Copy url",BOOKMARK_LABEL:"Bookmark",SELECT_NEW_METRIC_TOOLTIP:"Remove existing metric and choose a new metric"}}},5749:(e,t,r)=>{r.d(t,{R:()=>u,q:()=>c});var n=r(8531),a=r(2127),i=r(7476);function o(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function s(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function s(e){o(i,n,a,s,l,"next",e)}function l(e){o(i,n,a,s,l,"throw",e)}s(void 0)}))}}function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c{reset(){this._datasource=void 0,this._metricsMetadata=void 0,this._classicHistograms={},this._nativeHistograms=[]}getDatasource(){var e=this;return s((function*(){if(e._datasource)return e._datasource;const t=yield(0,n.getDataSourceSrv)().get(a.gR,{__sceneObject:{value:e._trail}});return(0,i.a)(t)&&(e._datasource=t),e._datasource}))()}_getMetricsMetadata(){var e=this;return s((function*(){const t=yield e.getDatasource();if(t)return t.languageProvider.metricsMetadata||(yield t.languageProvider.loadMetricsMetadata()),t.languageProvider.metricsMetadata}))()}getMetricMetadata(e){var t=this;return s((function*(){if(!e)return;t._metricsMetadata||(t._metricsMetadata=yield t._getMetricsMetadata());const r=yield t._metricsMetadata;return null==r?void 0:r[e]}))()}listNativeHistograms(){return this._nativeHistograms}initializeHistograms(){var e=this;return s((function*(){const t=yield e.getDatasource();if(t&&0===Object.keys(e._classicHistograms).length){const r=t.metricFindQuery("metrics(.*_bucket)"),n=t.metricFindQuery("metrics(.*)"),[a,i]=yield Promise.all([r,n]);a.forEach((t=>{e._classicHistograms[t.text]=1})),e._metricsMetadata||(t.languageProvider.metricsMetadata||(yield t.languageProvider.loadMetricsMetadata()),e._metricsMetadata=t.languageProvider.metricsMetadata),i.forEach((t=>{e.isNativeHistogram(t.text)&&e.addNativeHistogram(t.text)}))}}))()}isNativeHistogram(e){var t;if(!e)return!1;const r=this._metricsMetadata;var n;const a="bucket"!==(null!==(n=e.split("_").pop())&&void 0!==n?n:"");return!("histogram"!==(null==r||null===(t=r[e])||void 0===t?void 0:t.type)||!a)||!!this._classicHistograms[`${e}_bucket`]}addNativeHistogram(e){this._nativeHistograms.includes(e)||this._nativeHistograms.push(e)}getTagKeys(e){var t=this;return s((function*(){const r=yield t.getDatasource();if(!r)return[];return yield r.getTagKeys(e)}))()}getTagValues(e){var t=this;return s((function*(){const r=yield t.getDatasource();if(!r)return[];e.key=function(e){if(""===e||!function(e){return/^".*"$/.test(e)}(e))return e;return e.slice(1,-1)}(e.key);return yield r.getTagValues(e)}))()}static datasourceUsesTimeRangeInLanguageProviderMethods(e){return e.languageProvider.fetchLabelValues.length>1}constructor(e){l(this,"_trail",void 0),l(this,"_datasource",void 0),l(this,"_metricsMetadata",void 0),l(this,"_classicHistograms",{}),l(this,"_nativeHistograms",[]),this._trail=e}}function u(e){if(!e)return;const{type:t,help:r,unit:n}=e;return[r,t&&`**Type:** *${t}*`,n&&`**Unit:** ${n}`].join("\n\n")}},3347:(e,t,r)=>{r.d(t,{h:()=>o,z:()=>i});var n=r(8531);const a="grafana_explore_metrics_";function i(e,t){(0,n.reportInteraction)(`${a}${e}`,t)}function o(e,t,r){if(e.length===t.length)for(const n of t)for(const t of e)n.key===t.key&&n.value!==t.value&&i("label_filter_changed",{label:n.key,action:"changed",cause:"adhoc_filter",otel_resource_attribute:null!=r&&r});else if(e.length<t.length)for(const r of t){let t=!1;for(const n of e)if(r.key===n.key){t=!0;break}t||i("label_filter_changed",{label:r.key,action:"removed",cause:"adhoc_filter"})}else for(const r of e){let e=!1;for(const n of t)if(n.key===r.key){e=!0;break}e||i("label_filter_changed",{label:r.key,action:"added",cause:"adhoc_filter"})}}},8371:(e,t,r)=>{r.d(t,{a8:()=>p,hm:()=>w,tE:()=>y,wI:()=>m});var n=r(7139),a=r(2872),i=r(8531),o=r(3616),s=r(4169),l=r(6099);function c(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){c(i,n,a,o,s,"next",e)}function s(e){c(i,n,a,o,s,"throw",e)}o(void 0)}))}}const d=["__name__"];function p(e,t,r,n){return h.apply(this,arguments)}function h(){return(h=u((function*(e,t,r,s){const l=(0,n.mv)(t.from,!1),c=(0,n.mv)(t.to,!0);s&&!(0,a.Rq)(s)&&(s=`{"${s}"}`);const u=s?(e=>`count(${e}) by (job, instance)`)(s):(e=>`count(target_info{${null!=e?e:""}}) by (job, instance)`)(r),d=`/api/datasources/uid/${e}/resources/api/v1/query`,p={start:l,end:c,query:u},h=yield(0,i.getBackendSrv)().get(d,p,`metrics-drilldown-otel-check-total-${u}`).catch((e=>{const{type:t,statusText:r,status:n}=e;return"cancelled"!==t&&(0,o.HA)(["Error while fetching OTel resources! Defaulting to an empty array.",`${r} (${n})`]),{data:{result:[]}}}));let m=[],f=[];return h.data.result.forEach((e=>{e.metric.job&&e.metric.instance&&(m.push(e.metric.job),f.push(e.metric.instance))})),{jobs:m,instances:f}}))).apply(this,arguments)}function m(e,t,r){return f.apply(this,arguments)}function f(){return f=u((function*(e,t,r){return i.config.featureToggles.enableScopesInMetricsExplore?function(e,t,r){return b.apply(this,arguments)}(e,t,r):function(e,t){return g.apply(this,arguments)}(e,t)})),f.apply(this,arguments)}function g(){return(g=u((function*(e,t){const r=`/api/datasources/uid/${e}/resources/api/v1/label/deployment_environment/values`,a={start:(0,n.mv)(t.from,!1),end:(0,n.mv)(t.to,!0)};return(yield(0,i.getBackendSrv)().get(r,a,"metrics-drilldown-otel-resources-deployment-env")).data}))).apply(this,arguments)}function b(){return(b=u((function*(e,t,r){return(yield(0,s.WD)(e,t,r,[],"deployment_environment",void 0,"metrics-drilldown-otel-resources-deployment-env")).data.data}))).apply(this,arguments)}function y(e,t,r,n){return v.apply(this,arguments)}function v(){return(v=u((function*(e,t,r,o){const s=(null!=o?o:[]).concat(d),c=yield p(e,t,void 0,r);if(0===c.jobs.length||0===c.instances.length)return{attributes:[],missingOtelTargets:!1};const u=`/api/datasources/uid/${e}/resources/api/v1/labels`,h=(0,l.GX)([],c.jobs,c.instances);let m="";m=(0,a.Rq)(r)?`${r}{${h.jobsRegex},${h.instancesRegex}}`:`{'${r}',${h.jobsRegex},${h.instancesRegex}}`;const f=(0,n.mv)(t.from,!1),g=(0,n.mv)(t.to,!0),b={start:f,end:g,"match[]":m};var y;const v=null!==(y=(yield(0,i.getBackendSrv)().get(u,b,`metrics-drilldown-otel-resources-metric-job-instance-${m}`)).data)&&void 0!==y?y:[];let w=`target_info{${h.jobsRegex},${h.instancesRegex}}`;const O={start:f,end:g,"match[]":w};var S;const k=(null!==(S=(yield(0,i.getBackendSrv)().get(u,O,`metrics-drilldown-otel-resources-metric-job-instance-${w}`)).data)&&void 0!==S?S:[]).filter((e=>!v.includes(e))).filter((e=>!s.includes(e))).map((e=>({text:e})));return{attributes:(0,l.ql)(k,["job"]).map((e=>e.text)),missingOtelTargets:h.missingOtelTargets}}))).apply(this,arguments)}function w(e,t){return O.apply(this,arguments)}function O(){return(O=u((function*(e,t){const r=(0,n.mv)(t.from,!1),a=(0,n.mv)(t.to,!0),o=`/api/datasources/uid/${e}/resources/api/v1/labels`,s={start:r,end:a,"match[]":'{__name__="target_info"}'},l=(0,i.getBackendSrv)().get(o,s,"metrics-drilldown-all-otel-resources-on-target_info"),c={start:r,end:a,"match[]":'{name!="",__name__!~"target_info"}'},u=yield(0,i.getBackendSrv)().get(o,c,"metrics-drilldown-all-metric-labels-not-otel-resource-attributes"),d=yield Promise.all([l,u]);var p;const h=null!==(p=d[0].data)&&void 0!==p?p:[];var m;const f=new Set(null!==(m=d[1].data)&&void 0!==m?m:[]);return h.filter((e=>!f.has(e)))}))).apply(this,arguments)}},6099:(e,t,r)=>{r.d(t,{Dz:()=>g,GX:()=>b,KO:()=>y,L4:()=>f,mc:()=>k,qJ:()=>O,ql:()=>m});var n=r(7781),a=r(2872),i=r(8531),o=r(6680),s=r(3347),l=r(8371),c=r(2127),u=r(384);function d(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){d(i,n,a,o,s,"next",e)}function s(e){d(i,n,a,o,s,"throw",e)}o(void 0)}))}}const h=()=>({cloud_availability_zone:0,cloud_region:0,container_name:0,k8s_cluster_name:0,k8s_container_name:0,k8s_cronjob_name:0,k8s_daemonset_name:0,k8s_deployment_name:0,k8s_job_name:0,k8s_namespace_name:0,k8s_pod_name:0,k8s_replicaset_name:0,k8s_statefulset_name:0,service_instance_id:0,service_name:0,service_namespace:0});function m(e,t){const r=h(),n=Object.keys(r);e=e.filter((e=>{var t;const a=(null!==(t=e.value)&&void 0!==t?t:"").toString();return!n.includes(a)||(r[a]=1,!1)}));return Object.keys(r).filter((e=>r[e]&&!t.includes(e))).map((e=>({text:e}))).concat(e)}function f(e,t){let r="";if(t){var n;const e=null===(n=o.jh.lookupVariable(c.lr,t))||void 0===n?void 0:n.getValue();r="string"==typeof e?e:""}let a="";return a=`* on (job, instance) group_left(${r}) topk by (job, instance) (1, target_info{${e.filters}})`,a}function g(e,t){const r=o.jh.lookupVariable(c.DU,e);let n={labels:"",filters:""};if((0,u.BE)(r)){const e=r.state.filters;let t="",o="";for(let r=0;r<(null==e?void 0:e.length);r++){let n=e[r].key;(0,a.Rq)(n)||(n=`'${n}'`);const s=e[r].operator,l=e[r].value;r>0&&(t+=","),i.config.featureToggles.prometheusSpecialCharsInLabelValues?t+=`${n}${s}'${l}'`:t+=`${n}${s}"${l}"`;"job"!==n&&"instance"!==n&&(o+=`${n}`)}return n.labels=o,n.filters=t,n}return n}function b(e,t,r){let n=!1;let a=e.join(",").length,i="job=~'",o="instance=~'";const s={},l={};for(let e=0;e<t.length;e++){const c=0===e?0:2;if(!(a+i.length+t[e].length+o.length+r[e].length+c<=2e3)){n=!0;break}0===e?(i+=`${t[e]}`,o+=`${r[e]}`):(i+=s[t[e]]?"":`|${t[e]}`,o+=l[r[e]]?"":`|${r[e]}`),s[t[e]]=!0,l[r[e]]=!0}return i+="'",o+="'",{missingOtelTargets:n,jobsRegex:i,instancesRegex:o}}function y(e,t){return v.apply(this,arguments)}function v(){return(v=p((function*(e,t){var r;const n=null===(r=e.state.$timeRange)||void 0===r?void 0:r.state;if(!n)return;const i=o.jh.lookupVariable(c.lr,e),s=o.jh.lookupVariable(c.Xt,e),d=o.jh.lookupVariable(c.jl,e);if(!(0,u.DJ)(i)||!(0,u.DJ)(s)||!(0,u.DJ)(d))return;if(!t||"target_info"===t){i.setState({value:""});const t=f(g(e),e);return void s.setState({value:t})}const p=o.jh.lookupVariable(c.DU,e),h=o.jh.lookupVariable(c.Ao,e);let m=[];if((0,u.BE)(h)&&(0,u.BE)(p)){const e=h.state.filters.map((e=>e.key)),t=p.state.filters.map((e=>e.key));m=e.concat(t),m=m.concat(["job","instance"])}const b=o.jh.interpolate(e,c.gR),{attributes:y,missingOtelTargets:v}=yield(0,l.tE)(b,n,t,m);if(y.length>0){const t=y.map((e=>(0,a.Rq)(e)?e:`'${e}'`));i.setState({value:t.join(",")});const r=f(g(e),e);s.setState({value:r})}d.setState({value:v})}))).apply(this,arguments)}function w(e,t=[],r=[],a,i){const s=o.jh.lookupVariable(c.DU,e),l=o.jh.lookupVariable(c.Ao,e),d=o.jh.lookupVariable(c.mW,e),p=o.jh.lookupVariable(c.Xt,e);if(!((0,u.BE)(s)&&(0,u.BE)(l)&&(0,u.BE)(d)&&(0,u.DJ)(p)))return;var h;let m=null!==(h=0===(b=t).length?null:null!==(y=b.find((e=>e.toLowerCase().indexOf("prod")>-1)))&&void 0!==y?y:b[0])&&void 0!==h?h:"";var b,y;let v=i?l.state.filters:[];const w=a&&!i?[]:d.state.filters,O=function(e,t=[]){const r=new Set(t),n=e.filter((e=>r.has(e.key))),a=e.filter((e=>!r.has(e.key)));return{nonPromoted:n,promoted:a}}(w,r),S=a?[]:O.nonPromoted,k=a?[]:O.promoted;if(!a||i){const e=w.filter((e=>"deployment_environment"===e.key)).length>0,t=""===m||e?[]:[{key:"deployment_environment",value:m,operator:m.includes(",")?"=~":"="}],a=null==r?void 0:r.includes("deployment_environment");v=a?v.filter((e=>"deployment_environment"!==e.key)):v,null==d||d.setState({filters:[...t,...v,...w],hide:n.VariableHide.hideLabel});const i=[...a?t:[],...S];s.setState({filters:i,hide:n.VariableHide.hideVariable});const o=!a,c=v.filter((e=>"deployment_environment"===e.key));o&&0===c.length&&(v=[...v,...t]),v=[...v,...k],l.setState({filters:v,hide:n.VariableHide.hideVariable})}const E=g(e),x=f(E);return p.setState({value:x}),E}function O(e,t,r,n,a,i){return S.apply(this,arguments)}function S(){return(S=p((function*(e,t,r,n,a,i){const o=e.state.initialOtelCheckComplete,s=e.state.resettingOtel;w(e,n,i,null!=o&&o,null!=s&&s)&&(a&&n&&!o?e.setState({hasOtelResources:a,isStandardOtel:(null!=i?i:[]).length>0,useOtelExperience:!0,nonPromotedOtelResources:i,initialOtelCheckComplete:!0,resettingOtel:!1,afterFirstOtelCheck:!0}):e.setState({resettingOtel:!1,nonPromotedOtelResources:i,afterFirstOtelCheck:!0}))}))).apply(this,arguments)}function k(e,t,r,n,a){if(e.length>t.length){const i=e[e.length-1];return void((null==r?void 0:r.includes(i.key))?(n.setState({filters:[...n.state.filters,i]}),(0,s.h)(e,t,!0)):a.setState({filters:[...a.state.filters,i]}))}if(e.length<t.length){const i=t.filter((t=>!e.includes(t)))[0];return void((null==r?void 0:r.includes(i.key))?(n.setState({filters:n.state.filters.filter((e=>e.key!==i.key))}),(0,s.h)(e,t,!0)):a.setState({filters:a.state.filters.filter((e=>e.key!==i.key))}))}let i=[];e.length===t.length&&e.some(((e,r)=>{const n=e.key,a=e.value,o=t[r].key===n&&t[r].value!==a;return o&&i.push(e),o}))&&((null==r?void 0:r.includes(i[0].key))?(n.setState({filters:n.state.filters.map((e=>e.key===i[0].key?i[0]:e))}),(0,s.h)(e,t,!0)):a.setState({filters:a.state.filters.map((e=>e.key===i[0].key?i[0]:e))}))}},5570:(e,t,r)=>{function n(e){var t;const r=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!r)return null;const n=Object.keys(r);return 0===n.length?null:r[n[0]]}r.d(t,{H:()=>n})},2601:(e,t,r)=>{r.r(t),r.d(t,{calculateOutlierValue:()=>d,sortSeries:()=>l,sortSeriesByName:()=>p,wasmSupported:()=>h});var n=r(6944),a=r(7781),i=r(3241),o=r(3347),s=r(5570);const l=(0,i.memoize)(((e,t,r="asc")=>{if("alphabetical"===t)return p(e,"asc");if("alphabetical-reversed"===t)return p(e,"desc");"outliers"===t&&c(e);const n=r=>{var n;try{if("outliers"===t)return d(e,r)}catch(e){console.error(e),t=a.ReducerID.stdDev}const i=a.fieldReducers.get(t);var o;var s;return null!==(s=(null!==(o=null===(n=i.reduce)||void 0===n?void 0:n.call(i,r.fields[1],!0,!0))&&void 0!==o?o:(0,a.doStandardCalcs)(r.fields[1],!0,!0))[t])&&void 0!==s?s:0},i=e.map((e=>({value:n(e),dataFrame:e})));return i.sort(((e,t)=>void 0!==e.value&&void 0!==t.value?t.value-e.value:0)),"asc"===r&&i.reverse(),i.map((({dataFrame:e})=>e))}),((e,t,r="asc")=>{const n=e.length>0?e[0].fields[0].values[0]:0,a=e.length>0?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0;return`${e.length>0?(0,s.H)(e[0]):""}_${e.length>0?(0,s.H)(e[e.length-1]):""}_${n}_${a}_${e.length}_${t}_${r}`})),c=e=>{if(!h())return;const t=(0,a.outerJoinDataFrames)({frames:e});if(!t)return;const r=t.fields.filter((e=>e.type===a.FieldType.number)).map((e=>new Float64Array(e.values)));try{const e=n.OutlierDetector.dbscan({sensitivity:.4}).preprocess(r);u=e.detect()}catch(e){console.error(e),u=void 0}};let u;const d=(e,t)=>{if(!h())throw new Error("WASM not supported, fall back to stdDev");if(!u)throw new Error("Initialize outlier detector first");const r=e.indexOf(t);return u.seriesResults[r].isOutlier?-u.seriesResults[r].outlierIntervals.length:0},p=(e,t)=>{const r=[...e];return r.sort(((e,t)=>{const r=(0,s.H)(e),n=(0,s.H)(t);return r&&n&&null!==(a=null==r?void 0:r.localeCompare(n))&&void 0!==a?a:0;var a})),"desc"===t&&r.reverse(),r},h=()=>{const e="object"==typeof WebAssembly;return e||(0,o.z)("wasm_not_supported",{}),e}},4351:(e,t,r)=>{r.d(t,{HU:()=>i,Hy:()=>a,fq:()=>s,gO:()=>l,vs:()=>o,yx:()=>c});var n=r(2127);function a(){var e;return null!==(e=localStorage.getItem(n.Pp))&&void 0!==e?e:"grid"}function i(e){return localStorage.setItem(n.Pp,null!=e?e:"grid")}function o(e,t){var r;const a=(null!==(r=localStorage.getItem(`${n.I1}.${e}.by`))&&void 0!==r?r:"").split(".");return a[0]&&a[1]?{sortBy:a[0],direction:a[1]}:{sortBy:t}}function s(e,t){t&&localStorage.setItem(`${n.I1}.${e}.by`,`${t}`)}function l(){const e=localStorage.getItem(n.i$);return null===e||JSON.parse(e)}function c(e){return localStorage.setItem(n.i$,e.toString())}},2127:(e,t,r)=>{r.d(t,{Ao:()=>l,Az:()=>g,DU:()=>y,EY:()=>m,GH:()=>_,H0:()=>M,I1:()=>B,Kf:()=>b,Mi:()=>R,OO:()=>$,Pp:()=>L,QX:()=>s,Rp:()=>d,Xt:()=>w,aZ:()=>h,dc:()=>T,gR:()=>f,hc:()=>x,i$:()=>N,jT:()=>C,jl:()=>k,lr:()=>S,mW:()=>E,p4:()=>v,sQ:()=>P,tM:()=>O,td:()=>j,ui:()=>c,up:()=>D,ym:()=>A,yr:()=>p});var n=r(7781),a=r(6680),i=r(2245);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const s="/explore/metrics",l="filters",c="${filters}",u="metric",d="${metric}",p="groupby",h="${groupby}",m="ds",f="${ds}",g="logsDs",b="${logsDs}",y="otel_resources",v="deployment_environment",w="otel_join_query",O="${otel_join_query}",S="otel_group_left",k="missing_otel_targets",E="otel_and_metric_filters",x="other_metric_filters",j="$__logs__",_={uid:f},P="grafana.trails.recent",C="grafana.trails.bookmarks",L="grafana.trails.breakdown.view",B="grafana.trails.breakdown.sort",N="grafana.trails.otel.experience.enabled",D=250,T=500;function A(e){return[new a.x0({name:u,value:e,hide:i.zL.hideVariable})]}function R(){return[new a.x0({name:w,value:"",hide:i.zL.hideVariable})]}class $ extends n.BusEventWithPayload{}o($,"type","metric-selected-event");class M extends n.BusEventBase{}o(M,"type","refresh-metrics-event")},2407:(e,t,r)=>{r.d(t,{Bx:()=>i,C:()=>c,D3:()=>l,MG:()=>u,UX:()=>s,Z_:()=>n,c$:()=>a,ot:()=>o});const n={},a={},i={},o={},s={},l={},c={},u={}},5774:(e,t,r)=>{r.d(t,{tz:()=>n});const n={}},4169:(e,t,r)=>{r.d(t,{FG:()=>j,KE:()=>y,UX:()=>C,Vy:()=>E,WD:()=>_,aM:()=>S,aO:()=>k,ef:()=>v,kj:()=>b,xi:()=>w,y:()=>O});var n=r(7781),a=r(7139),i=r(8531),o=r(6680),s=r(1269),l=r(4137),c=r(7570),u=r(1385),d=r(6099),p=r(2127),h=(r(2425),r(2188)),m=r(384);function f(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){f(i,n,a,o,s,"next",e)}function s(e){f(i,n,a,o,s,"throw",e)}o(void 0)}))}}function b(e){return o.jh.getAncestor(e,c.b8)}function y(e){return o.jh.getAncestor(e,c.b8).state.settings}function v(e,t){return new c.b8({initialDS:e,$timeRange:new o.JZ({from:"now-1h",to:"now"}),embedded:!1,startButtonClicked:t})}function w(e){const t=o.Go.getUrlState(e);return n.urlUtil.renderUrl(l.bw.Drilldown,t)}function O(e){return window.location.pathname.includes(e)}function S(e){if(e instanceof u.Rd)return e;if(e.parent)return S(e.parent);throw console.error("Unable to find graph view for",e),new Error("Unable to find trail")}function k(e){return e?e===p.td?"Logs":e:"All metrics"}function E(e){const t=i.config.theme2.visualization;return t.getColorByName(t.palette[e%8])}const x=1e4;function j(e,t,r){(0,m.BE)(t)&&t.setState({getTagKeysProvider:function(){var n=g((function*(n,a){var i;const o=t.state.filters,s={filters:o,scopes:null===(i=(0,h.DF)(n))||void 0===i?void 0:i.value,queries:e.getQueries()};s.queries.length>20&&(s.queries=[]);let l=(yield r.getTagKeys(s)).slice(0,x);return t.state.name===p.mW&&(l=(0,d.ql)(l,o.map((e=>e.key)))),{replace:!0,values:l}}));return function(e,t){return n.apply(this,arguments)}}(),getTagValuesProvider:function(){var n=g((function*(n,a){var i;const o=t.state.filters.filter((e=>e.key!==a.key)),s={key:a.key,filters:o,scopes:null===(i=(0,h.DF)(n))||void 0===i?void 0:i.value,queries:e.getQueries()};s.queries.length>20&&(s.queries=[]);return{replace:!0,values:(yield r.getTagValues(s)).slice(0,x)}}));return function(e,t){return n.apply(this,arguments)}}()})}function _(e,t,r,n,a,i,o){return P.apply(this,arguments)}function P(){return(P=g((function*(e,t,r,o,l,c,u){return yield(0,s.lastValueFrom)((0,i.getBackendSrv)().fetch({url:`/api/datasources/uid/${e}/resources/suggestions`,data:{labelName:l,queries:[],scopes:r.reduce(((e,t)=>(e.push(...t.spec.filters),e)),[]),adhocFilters:o.map((e=>({key:e.key,operator:n.scopeFilterOperatorMap[e.operator],value:e.value,values:e.values}))),start:(0,a.mv)(t.from,!1).toString(),end:(0,a.mv)(t.to,!0).toString(),limit:c},requestId:u,method:"POST",headers:{"Content-Type":"application/json"}}))}))).apply(this,arguments)}function C(e,t,r){const n=o.jh.findObject(e,t);return n instanceof r?n:(null!==n&&console.warn(`invalid return type: ${r.toString()}`),null)}},7476:(e,t,r)=>{function n(e){return"object"==typeof e&&null!==e&&"type"in e&&"prometheus"===e.type&&"uid"in e&&"string"==typeof e.uid}r.d(t,{a:()=>n})},9646:(e,t,r)=>{function n(e){var t,r;if(!e)return;const i=null!==(r=e.state.$data)&&void 0!==r?r:null===(t=e.parent)||void 0===t?void 0:t.state.$data;return a(i)?i:null!=(o=i)&&"state"in o&&"transformations"in o.state?n(i):void 0;var o}function a(e){return null!=e&&"state"in e&&"runQueries"in e}r.d(t,{un:()=>n,xT:()=>a})},2188:(e,t,r)=>{r.d(t,{DF:()=>o,f_:()=>i});var n=r(6680);function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(){return[]}function o(e){return new s({})}class s extends n.Bs{getSelectedScopes(){return this.selectedScopes}getSelectedScopesNames(){return this.selectedScopes.map((({scope:e})=>e.metadata.name))}setSelectedScopes(e){this.selectedScopes=e,this.notifySubscribers()}onScopesChange(e){return this.onScopesChangeCallbacks.push(e),()=>{this.onScopesChangeCallbacks=this.onScopesChangeCallbacks.filter((t=>t!==e))}}notifySubscribers(){for(const e of this.onScopesChangeCallbacks)e(this.selectedScopes)}get value(){return[]}constructor(e){super({}),a(this,"selectedScopes",[]),a(this,"onScopesChangeCallbacks",[])}}},384:(e,t,r)=>{function n(e){return null!==e&&"constant"===(null==e?void 0:e.state.type)}function a(e){return null!==e&&"adhoc"===(null==e?void 0:e.state.type)}function i(e){return null!==e&&"custom"===(null==e?void 0:e.state.type)}function o(e){return null!==e&&"query"===(null==e?void 0:e.state.type)}r.d(t,{BE:()=>a,DJ:()=>n,UG:()=>i,bA:()=>o})}}]);
//# sourceMappingURL=513.js.map?_cache=bd6b649be2e9bf43de55