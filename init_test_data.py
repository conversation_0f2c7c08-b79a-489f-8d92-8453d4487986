#!/usr/bin/env python3
"""
Initialize test data for Security Monitor
Run this inside the CTFd container
"""

import sys
sys.path.insert(0, '/opt/CTFd')

from CTFd import create_app
from CTFd.models import db
from CTFd.plugins.security_monitor.models import SecurityEvent, SecurityAlert
from datetime import datetime, timedelta
import random

app = create_app()

with app.app_context():
    # Create some test security events
    print("Creating test security events...")
    
    event_types = ['failed_login', 'rate_limit_exceeded', 'suspicious_activity']
    ips = ['*************', '*********', '***********', '*************']
    
    # Create events for the last 24 hours
    for i in range(50):
        event = SecurityEvent(
            timestamp=datetime.utcnow() - timedelta(hours=random.randint(0, 24)),
            event_type=random.choice(event_types),
            ip_address=random.choice(ips),
            details={'test': True, 'event_num': i}
        )
        db.session.add(event)
    
    # Create some test alerts
    print("Creating test security alerts...")
    
    severities = ['info', 'warning', 'critical']
    alert_types = ['excessive_failed_logins', 'rate_limit_violation', 'suspicious_behavior']
    
    for i in range(10):
        alert = SecurityAlert(
            timestamp=datetime.utcnow() - timedelta(hours=random.randint(0, 12)),
            alert_type=random.choice(alert_types),
            severity=random.choice(severities),
            title=f"Test Alert {i+1}",
            message=f"This is a test alert for demonstration purposes",
            ip_address=random.choice(ips),
            resolved=(i < 5)  # Mark first 5 as resolved
        )
        db.session.add(alert)
    
    db.session.commit()
    print("Test data created successfully!")
    
    # Verify data
    event_count = SecurityEvent.query.count()
    alert_count = SecurityAlert.query.count()
    print(f"Total events: {event_count}")
    print(f"Total alerts: {alert_count}")
