"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[601],{3347:(e,t,r)=>{r.d(t,{h:()=>n,z:()=>o});var l=r(8531);const a="grafana_explore_metrics_";function o(e,t){(0,l.reportInteraction)(`${a}${e}`,t)}function n(e,t,r){if(e.length===t.length)for(const l of t)for(const t of e)l.key===t.key&&l.value!==t.value&&o("label_filter_changed",{label:l.key,action:"changed",cause:"adhoc_filter",otel_resource_attribute:null!=r&&r});else if(e.length<t.length)for(const r of t){let t=!1;for(const l of e)if(r.key===l.key){t=!0;break}t||o("label_filter_changed",{label:r.key,action:"removed",cause:"adhoc_filter"})}else for(const r of e){let e=!1;for(const l of t)if(l.key===r.key){e=!0;break}e||o("label_filter_changed",{label:r.key,action:"added",cause:"adhoc_filter"})}}},5570:(e,t,r)=>{function l(e){var t;const r=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!r)return null;const l=Object.keys(r);return 0===l.length?null:r[l[0]]}r.d(t,{H:()=>l})},2601:(e,t,r)=>{r.r(t),r.d(t,{calculateOutlierValue:()=>d,sortSeries:()=>i,sortSeriesByName:()=>f,wasmSupported:()=>v});var l=r(6944),a=r(7781),o=r(3241),n=r(3347),s=r(5570);const i=(0,o.memoize)(((e,t,r="asc")=>{if("alphabetical"===t)return f(e,"asc");if("alphabetical-reversed"===t)return f(e,"desc");"outliers"===t&&c(e);const l=r=>{var l;try{if("outliers"===t)return d(e,r)}catch(e){console.error(e),t=a.ReducerID.stdDev}const o=a.fieldReducers.get(t);var n;var s;return null!==(s=(null!==(n=null===(l=o.reduce)||void 0===l?void 0:l.call(o,r.fields[1],!0,!0))&&void 0!==n?n:(0,a.doStandardCalcs)(r.fields[1],!0,!0))[t])&&void 0!==s?s:0},o=e.map((e=>({value:l(e),dataFrame:e})));return o.sort(((e,t)=>void 0!==e.value&&void 0!==t.value?t.value-e.value:0)),"asc"===r&&o.reverse(),o.map((({dataFrame:e})=>e))}),((e,t,r="asc")=>{const l=e.length>0?e[0].fields[0].values[0]:0,a=e.length>0?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0;return`${e.length>0?(0,s.H)(e[0]):""}_${e.length>0?(0,s.H)(e[e.length-1]):""}_${l}_${a}_${e.length}_${t}_${r}`})),c=e=>{if(!v())return;const t=(0,a.outerJoinDataFrames)({frames:e});if(!t)return;const r=t.fields.filter((e=>e.type===a.FieldType.number)).map((e=>new Float64Array(e.values)));try{const e=l.OutlierDetector.dbscan({sensitivity:.4}).preprocess(r);u=e.detect()}catch(e){console.error(e),u=void 0}};let u;const d=(e,t)=>{if(!v())throw new Error("WASM not supported, fall back to stdDev");if(!u)throw new Error("Initialize outlier detector first");const r=e.indexOf(t);return u.seriesResults[r].isOutlier?-u.seriesResults[r].outlierIntervals.length:0},f=(e,t)=>{const r=[...e];return r.sort(((e,t)=>{const r=(0,s.H)(e),l=(0,s.H)(t);return r&&l&&null!==(a=null==r?void 0:r.localeCompare(l))&&void 0!==a?a:0;var a})),"desc"===t&&r.reverse(),r},v=()=>{const e="object"==typeof WebAssembly;return e||(0,n.z)("wasm_not_supported",{}),e}}}]);
//# sourceMappingURL=601.js.map?_cache=4787f18fd19f3b2ec3ea