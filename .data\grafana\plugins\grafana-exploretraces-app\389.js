(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[389],{1738:(e,t,n)=>{var a={"./af":9805,"./af.js":9805,"./ar":4449,"./ar-dz":4468,"./ar-dz.js":4468,"./ar-kw":3480,"./ar-kw.js":3480,"./ar-ly":4197,"./ar-ly.js":4197,"./ar-ma":2180,"./ar-ma.js":2180,"./ar-ps":9343,"./ar-ps.js":9343,"./ar-sa":230,"./ar-sa.js":230,"./ar-tn":2808,"./ar-tn.js":2808,"./ar.js":4449,"./az":5865,"./az.js":5865,"./be":6627,"./be.js":6627,"./bg":901,"./bg.js":901,"./bm":3179,"./bm.js":3179,"./bn":1966,"./bn-bd":969,"./bn-bd.js":969,"./bn.js":1966,"./bo":6317,"./bo.js":6317,"./br":6474,"./br.js":6474,"./bs":5961,"./bs.js":5961,"./ca":7270,"./ca.js":7270,"./cs":1564,"./cs.js":1564,"./cv":3239,"./cv.js":3239,"./cy":2366,"./cy.js":2366,"./da":2453,"./da.js":2453,"./de":6601,"./de-at":5027,"./de-at.js":5027,"./de-ch":8101,"./de-ch.js":8101,"./de.js":6601,"./dv":6080,"./dv.js":6080,"./el":2655,"./el.js":2655,"./en-au":6836,"./en-au.js":6836,"./en-ca":2086,"./en-ca.js":2086,"./en-gb":2103,"./en-gb.js":2103,"./en-ie":5964,"./en-ie.js":5964,"./en-il":4379,"./en-il.js":4379,"./en-in":765,"./en-in.js":765,"./en-nz":1502,"./en-nz.js":1502,"./en-sg":1152,"./en-sg.js":1152,"./eo":50,"./eo.js":50,"./es":3350,"./es-do":9338,"./es-do.js":9338,"./es-mx":1326,"./es-mx.js":1326,"./es-us":9947,"./es-us.js":9947,"./es.js":3350,"./et":8231,"./et.js":8231,"./eu":8512,"./eu.js":8512,"./fa":9083,"./fa.js":9083,"./fi":5059,"./fi.js":5059,"./fil":2607,"./fil.js":2607,"./fo":3369,"./fo.js":3369,"./fr":7390,"./fr-ca":6711,"./fr-ca.js":6711,"./fr-ch":6152,"./fr-ch.js":6152,"./fr.js":7390,"./fy":2419,"./fy.js":2419,"./ga":3002,"./ga.js":3002,"./gd":4914,"./gd.js":4914,"./gl":6557,"./gl.js":6557,"./gom-deva":8944,"./gom-deva.js":8944,"./gom-latn":5387,"./gom-latn.js":5387,"./gu":7462,"./gu.js":7462,"./he":9237,"./he.js":9237,"./hi":9617,"./hi.js":9617,"./hr":6544,"./hr.js":6544,"./hu":341,"./hu.js":341,"./hy-am":1388,"./hy-am.js":1388,"./id":5251,"./id.js":5251,"./is":1146,"./is.js":1146,"./it":7891,"./it-ch":7,"./it-ch.js":7,"./it.js":7891,"./ja":3727,"./ja.js":3727,"./jv":5198,"./jv.js":5198,"./ka":8974,"./ka.js":8974,"./kk":7308,"./kk.js":7308,"./km":7786,"./km.js":7786,"./kn":4807,"./kn.js":4807,"./ko":1584,"./ko.js":1584,"./ku":1906,"./ku-kmr":5305,"./ku-kmr.js":5305,"./ku.js":1906,"./ky":9190,"./ky.js":9190,"./lb":7396,"./lb.js":7396,"./lo":8503,"./lo.js":8503,"./lt":3010,"./lt.js":3010,"./lv":5192,"./lv.js":5192,"./me":1944,"./me.js":1944,"./mi":6492,"./mi.js":6492,"./mk":2934,"./mk.js":2934,"./ml":1463,"./ml.js":1463,"./mn":8377,"./mn.js":8377,"./mr":8733,"./mr.js":8733,"./ms":8030,"./ms-my":9445,"./ms-my.js":9445,"./ms.js":8030,"./mt":5887,"./mt.js":5887,"./my":7228,"./my.js":7228,"./nb":8294,"./nb.js":8294,"./ne":9559,"./ne.js":9559,"./nl":600,"./nl-be":8796,"./nl-be.js":8796,"./nl.js":600,"./nn":9570,"./nn.js":9570,"./oc-lnc":5662,"./oc-lnc.js":5662,"./pa-in":7101,"./pa-in.js":7101,"./pl":6118,"./pl.js":6118,"./pt":9198,"./pt-br":7203,"./pt-br.js":7203,"./pt.js":9198,"./ro":5565,"./ro.js":5565,"./ru":3315,"./ru.js":3315,"./sd":8473,"./sd.js":8473,"./se":1258,"./se.js":1258,"./si":8798,"./si.js":8798,"./sk":6404,"./sk.js":6404,"./sl":7057,"./sl.js":7057,"./sq":5718,"./sq.js":5718,"./sr":5363,"./sr-cyrl":478,"./sr-cyrl.js":478,"./sr.js":5363,"./ss":7260,"./ss.js":7260,"./sv":2231,"./sv.js":2231,"./sw":7104,"./sw.js":7104,"./ta":7493,"./ta.js":7493,"./te":7705,"./te.js":7705,"./tet":4457,"./tet.js":4457,"./tg":2727,"./tg.js":2727,"./th":2206,"./th.js":2206,"./tk":3419,"./tk.js":3419,"./tl-ph":7243,"./tl-ph.js":7243,"./tlh":16,"./tlh.js":16,"./tr":7020,"./tr.js":7020,"./tzl":8026,"./tzl.js":8026,"./tzm":8537,"./tzm-latn":7899,"./tzm-latn.js":7899,"./tzm.js":8537,"./ug-cn":818,"./ug-cn.js":818,"./uk":8478,"./uk.js":8478,"./ur":7893,"./ur.js":7893,"./uz":9133,"./uz-latn":311,"./uz-latn.js":311,"./uz.js":9133,"./vi":2179,"./vi.js":2179,"./x-pseudo":2455,"./x-pseudo.js":2455,"./yo":3310,"./yo.js":3310,"./zh-cn":7244,"./zh-cn.js":7244,"./zh-hk":76,"./zh-hk.js":76,"./zh-mo":2305,"./zh-mo.js":2305,"./zh-tw":8588,"./zh-tw.js":8588};function r(e){var t=i(e);return n(t)}function i(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=i,e.exports=r,r.id=1738},9677:(e,t,n)=>{"use strict";n.r(t),n.d(t,{TraceExplorationView:()=>hr,default:()=>gr});var a=n(5959),r=n.n(a),i=n(3726),s=n(7781),o=n(7286),l=n(6089),c=n(8531),u=n(2007),d=n(1454),m=n(1575),p=n(3241);const f=({width:e="auto",height:t,show404:n=!1})=>{const i=(0,u.useTheme2)(),{x:s,y:o}=((e=50)=>{const[t,n]=(0,a.useState)({x:null,y:null});return(0,a.useEffect)((()=>{const t=(0,p.throttle)((e=>{n({x:e.clientX,y:e.clientY})}),e);return window.addEventListener("mousemove",t),()=>{window.removeEventListener("mousemove",t)}}),[e]),t})(),l=(0,u.useStyles2)(v,s,o,n);return r().createElement(m.A,{src:i.isDark?"public/plugins/grafana-exploretraces-app/img/944c737f589d02ecf603.svg":"public/plugins/grafana-exploretraces-app/img/e79edcfbe2068fae2364.svg",className:l.svg,height:t,width:e})};f.displayName="GrotNotFound";const v=(e,t,n,a)=>{const{innerWidth:r,innerHeight:i}=window,s=n&&n/i,o=t&&t/r,c=null!==s?g(s,-20,5):0,u=null!==o?g(o,-5,5):0;return{svg:(0,l.css)({"#grot-404-arm, #grot-404-magnifier":{transform:`rotate(${c}deg) translateX(${u}%)`,transformOrigin:"center",transition:"transform 50ms linear"},"#grot-404-text":{display:a?"block":"none"}})}},g=(e,t,n)=>e*(n-t)+t,h=({message:e,remedyMessage:t,imgWidth:n,padding:a})=>{const i=(0,u.useStyles2)(b,a);return r().createElement("div",{className:i.container,"data-testid":"data-testid empty-state"},r().createElement(u.Stack,{direction:"column",alignItems:"center",gap:3},r().createElement(f,{width:null!=n?n:300}),"string"==typeof e&&r().createElement(u.Text,{textAlignment:"center",variant:"h5"},e),"string"!=typeof e&&e,t&&r().createElement("div",{className:i.remedy},r().createElement(u.Stack,{gap:.5,alignItems:"center"},r().createElement(u.Icon,{name:"info-circle"}),r().createElement(u.Text,{textAlignment:"center",variant:"body"},t)))))};function b(e,t){return{container:(0,l.css)({width:"100%",display:"flex",justifyContent:"space-evenly",flexDirection:"column",padding:t||0}),remedy:(0,l.css)({marginBottom:e.spacing(4)})}}h.displayName="EmptyState";class y extends o.Bs{}var w,S,x;x=({model:e})=>{const{message:t,remedyMessage:n,imgWidth:a,padding:i}=e.useState();return r().createElement(h,{message:t,remedyMessage:n,imgWidth:a,padding:i})},(S="Component")in(w=y)?Object.defineProperty(w,S,{value:x,enumerable:!0,configurable:!0,writable:!0}):w[S]=x;var j=n(3049);class O extends o.Bs{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(O,"Component",(({model:e})=>{const t=(0,u.useTheme2)(),n=(0,u.useStyles2)(k),{component:a}=e.useState();return r().createElement("div",{className:n.container,"data-testid":"data-testid loading-state"},r().createElement(j.z,{baseColor:t.colors.emphasize(t.colors.background.secondary),highlightColor:t.colors.emphasize(t.colors.background.secondary,.1),borderRadius:t.shape.radius.default},a()))}));const E=(0,l.keyframes)({"0%":{opacity:0},"100%":{opacity:1}});function k(){return{container:(0,l.css)({label:"loading-state-scene",animationName:E,animationDelay:"100ms",animationTimingFunction:"ease-in",animationDuration:"100ms",animationFillMode:"backwards"})}}class C extends o.Bs{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(C,"Component",(({model:e})=>{const{message:t}=e.useState();return r().createElement(u.Alert,{title:"Query error",severity:"error","data-testid":"data-testid error-state"},t)}));const P=e=>{const t=(0,u.useStyles2)(_),{searchQuery:n,onSearchQueryChange:a}=e;return r().createElement(u.Field,{className:t.searchField},r().createElement(u.Input,{placeholder:"Search",prefix:r().createElement(u.Icon,{name:"search"}),value:n,onChange:a,id:"searchFieldInput"}))};function _(e){return{searchField:(0,l.css)({marginBottom:e.spacing(1)})}}function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){D(e,t,n[t])}))}return e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){I(e,t,n[t])}))}return e}function $(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class L extends o.Bs{renderFilteredData(e){e.series&&e.series.length>0?this.performRepeat(e):this.state.body.setState({children:[new o.vA({body:new y({message:"No data for search term",padding:"32px"})})]})}groupSeriesBy(e,t){const n=e.series.reduce(((e,n)=>{var a,r;const i=null===(r=n.fields.find((e=>e.type===s.FieldType.number)))||void 0===r||null===(a=r.labels)||void 0===a?void 0:a[t];return i?(e[i]||(e[i]=[]),e[i].push(n),e):e}),{}),a=[];for(const e in n){const t=n[e].sort(((e,t)=>{var n;return(null===(n=e.name)||void 0===n?void 0:n.localeCompare(t.name))||0})),i=T(N({},r=t[0]),{fields:r.fields.map((e=>T(N({},e),{values:e.values})))});t.slice(1,t.length).forEach((e=>i.fields.push(e.fields[1]))),a.push((0,s.sortDataFrame)(i,0))}var r;return a}performRepeat(e){const t=[];let n=e.series;this.state.groupBy&&(n=this.groupSeriesBy(e,ar(this).getValueText()));for(let a=0;a<n.length;a++){if(0===n[a].fields.filter((e=>e.type===s.FieldType.number)).reduce(((e,t)=>e+t.values.reduce(((e,t)=>e+(t||0)),0)||0),0))continue;const r=this.state.getLayoutChild(e,n[a],a);t.push(r)}this.state.body.setState({children:t})}constructor(e){super(e),I(this,"onSearchQueryChange",(e=>{this.setState({searchQuery:e.currentTarget.value})})),I(this,"onSearchQueryChangeDebounced",(0,p.debounce)((e=>{var t;const n=o.jh.getData(this),a=$(A({},n.state.data),{series:null===(t=n.state.data)||void 0===t?void 0:t.series.filter(z(e))});this.renderFilteredData(a)}),250)),this.addActivationHandler((()=>{const e=o.jh.getData(this);this._subs.add(e.subscribeToState((e=>{var t,n,a,r,i;if((null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Done||(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Streaming){if(0===e.data.series.length&&(null===(r=e.data)||void 0===r?void 0:r.state)!==s.LoadingState.Streaming)this.state.body.setState({children:[new o.vA({body:new y({message:d.PL,remedyMessage:d.a5,padding:"32px"})})]});else if((null===(i=e.data)||void 0===i?void 0:i.state)===s.LoadingState.Done){var l;const t=$(A({},e.data),{series:null===(l=e.data)||void 0===l?void 0:l.series.filter(z(this.state.searchQuery))});this.renderFilteredData(t),this.publishEvent(new d.sv({series:e.data.series}),!0)}}else if((null===(a=e.data)||void 0===a?void 0:a.state)===s.LoadingState.Error){var c,u,m;this.state.body.setState({children:[new o.gF({children:[new C({message:null!==(m=null===(u=e.data.errors)||void 0===u||null===(c=u[0])||void 0===c?void 0:c.message)&&void 0!==m?m:"An error occurred in the query"})]})]})}else this.state.body.setState({children:[new o.gF({children:[new O({component:()=>B(8)})]})]})}))),this.subscribeToState(((e,t)=>{var n;e.searchQuery!==t.searchQuery&&this.onSearchQueryChangeDebounced(null!==(n=e.searchQuery)&&void 0!==n?n:"")})),e.state.data&&this.performRepeat(e.state.data)}))}}function V(){return{container:(0,l.css)({display:"flex",flexDirection:"column",flexGrow:1})}}I(L,"Component",(({model:e})=>{const{body:t,searchQuery:n}=e.useState(),a=(0,u.useStyles2)(V);return r().createElement("div",{className:a.container},r().createElement(P,{searchQuery:null!=n?n:"",onSearchQueryChange:e.onSearchQueryChange}),r().createElement(t.Component,{model:t}))}));const B=e=>{const t=(0,u.useStyles2)(R);return r().createElement("div",{className:t.container},[...Array(e)].map(((e,n)=>r().createElement("div",{className:t.itemContainer,key:n},r().createElement("div",{className:t.header},r().createElement("div",{className:t.title},r().createElement(j.A,{count:1})),r().createElement("div",{className:t.action},r().createElement(j.A,{count:1}))),r().createElement("div",{className:t.yAxis},[...Array(2)].map(((e,n)=>r().createElement("div",{className:t.yAxisItem,key:n},r().createElement(j.A,{count:1}))))),r().createElement("div",{className:t.xAxis},[...Array(2)].map(((e,n)=>r().createElement("div",{className:t.xAxisItem,key:n},r().createElement(j.A,{count:1})))))))))};function R(e){return{container:(0,l.css)({display:"grid",gridTemplateColumns:d.MV,gridAutoRows:"200px",rowGap:e.spacing(1),columnGap:e.spacing(1)}),itemContainer:(0,l.css)({backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.background.secondary}`,padding:"5px"}),header:(0,l.css)({display:"flex",justifyContent:"space-between"}),title:(0,l.css)({width:"100px"}),action:(0,l.css)({width:"60px"}),yAxis:(0,l.css)({display:"flex",flexDirection:"column",justifyContent:"space-around",marginTop:"35px"}),yAxisItem:(0,l.css)({width:"60px",height:"55px"}),xAxis:(0,l.css)({display:"flex",justifyContent:"space-evenly"}),xAxisItem:(0,l.css)({width:"55px"})}}const z=e=>t=>{const n=null==e?void 0:e.trim();if(!n)return!0;const a=new RegExp(n,"i");return t.fields.some((e=>!!e.labels&&Object.values(e.labels).find((e=>a.test(e)))))},F=()=>o.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("drawStyle",u.DrawStyle.Bars).setCustomFieldConfig("stacking",{mode:u.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",75).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("axisLabel","Rate").setOverrides((e=>{e.matchFieldsWithNameByRegex('(^error$|.*status="error".*)').overrideColor({mode:"fixed",fixedColor:"semi-dark-red"}),e.matchFieldsWithNameByRegex('(^unset$|.*status="unset".*)').overrideColor({mode:"fixed",fixedColor:"green"}),e.matchFieldsWithNameByRegex('(^ok$|.*status="ok".*)').overrideColor({mode:"fixed",fixedColor:"dark-green"})})).setOption("tooltip",{mode:u.TooltipDisplayMode.Multi});function M({metric:e,groupByKey:t,extraFilters:n,groupByStatus:a}){let r=`${d.ui}`;"rate"===e?r+=" && status!=error":"errors"===e&&(r+=" && status=error"),n&&(r+=` && ${n}`),t&&t!==d.y2&&(r+=` && ${t} != nil`);let i="rate()";switch(e){case"errors":i="rate()";break;case"duration":i="quantile_over_time(duration, 0.9)"}let s=[];return t&&t!==d.y2&&s.push(t),"duration"!==e&&a&&s.push("status"),`{${r}} | ${i} ${s.length?`by(${s.join(", ")})`:""}`}function q(e,t){return{refId:"A",query:M({metric:e,groupByKey:t,groupByStatus:!0}),queryType:"traceql",tableType:"spans",limit:100,spss:10,filters:[]}}var H=n(2689);const W=(Math.log10(1e3),[{unit:"d",microseconds:864e8,ofPrevious:24},{unit:"h",microseconds:36e8,ofPrevious:60},{unit:"m",microseconds:6e7,ofPrevious:60},{unit:"s",microseconds:1e6,ofPrevious:1e3},{unit:"ms",microseconds:1e3,ofPrevious:1e3},{unit:"μs",microseconds:1,ofPrevious:1e3}]),U=e=>{const[t,n]=(0,p.dropWhile)(W,(({microseconds:t},n)=>n<W.length-1&&t>e));if(1e3===t.ofPrevious)return`${(0,p.round)(e/t.microseconds,2)}${t.unit}`;const a=`${Math.floor(e/t.microseconds)}${t.unit}`,r=Math.round(e/n.microseconds%t.ofPrevious),i=`${r}${n.unit}`;return 0===r?a:`${a} ${i}`},G=(e,t)=>{const n=o.jh.getTimeRange(e),a=n.state.value.from.unix(),r=n.state.value.to.unix(),i=(0,H.duration)(r-a,"s");return`${Math.floor(i.asSeconds()/(null!=t?t:50))||1}s`};function K(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){K(e,t,n[t])}))}return e}function Z(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class J extends o.dt{_onActivateStep(){const e=G(this,this.state.maxDataPoints);this.setState({queries:this.state.queries.map((t=>Z(Q({},t),{step:e})))}),o.jh.getTimeRange(this).subscribeToState(((e,t)=>{if(e.value.from!==t.value.from||e.value.to!==t.value.to){const e=G(this,this.state.maxDataPoints);this.setState({queries:this.state.queries.map((t=>Z(Q({},t),{step:e})))})}}))}constructor(e){super(e),this.addActivationHandler(this._onActivateStep.bind(this))}}const X=({isStreaming:e,iconSize:t=14})=>{const n=(0,u.useStyles2)(Y,t);return e?r().createElement(u.Tooltip,{content:"Streaming"},r().createElement(u.Icon,{name:"circle-mono",size:"md",className:n.streamingIndicator})):null},Y=(e,t)=>({streamingIndicator:(0,l.css)({width:`${t}px`,height:`${t}px`,backgroundColor:e.colors.success.text,fill:e.colors.success.text,borderRadius:"50%",display:"inline-block"})});var ee=n(2533);const te=(e,t,n)=>{(0,c.reportInteraction)(((e,t)=>`${ee.id.replace(/-/g,"_")}_${e}_${t}`)(e,t),n)},ne={analyse_traces:"analyse_traces",home:"home",common:"common"},ae={[ne.analyse_traces]:{action_view_changed:"action_view_changed",breakdown_group_by_changed:"breakdown_group_by_changed",breakdown_add_to_filters_clicked:"breakdown_add_to_filters_clicked",comparison_add_to_filters_clicked:"comparison_add_to_filters_clicked",select_attribute_in_comparison_clicked:"select_attribute_in_comparison_clicked",layout_type_changed:"layout_type_changed",start_investigation:"start_investigation",stop_investigation:"stop_investigation",open_trace:"open_trace",open_in_explore_clicked:"open_in_explore_clicked",add_to_investigation_clicked:"add_to_investigation_clicked",add_to_investigation_trace_view_clicked:"add_to_investigation_trace_view_clicked",span_list_columns_changed:"span_list_columns_changed",toggle_bookmark_clicked:"toggle_bookmark_clicked"},[ne.home]:{homepage_initialized:"homepage_initialized",panel_row_clicked:"panel_row_clicked",explore_traces_clicked:"explore_traces_clicked",read_documentation_clicked:"read_documentation_clicked",filter_changed:"filter_changed",go_to_bookmark_clicked:"go_to_bookmark_clicked"},[ne.common]:{metric_changed:"metric_changed",new_filter_added_manually:"new_filter_added_manually",app_initialized:"app_initialized",global_docs_link_clicked:"global_docs_link_clicked",metric_docs_link_clicked:"metric_docs_link_clicked",feedback_link_clicked:"feedback_link_clicked"}},re=()=>o.d0.heatmap().setOption("legend",{show:!1}).setOption("yAxis",{unit:"s",axisLabel:"duration"}).setOption("color",{scheme:"Blues",steps:16}).setOption("rowsFrame",{value:"Spans"});function ie(e,t,n){if(!t)return"";if(e<0)return"0";const a=t[Math.floor(e)]*(n||1);return!a||isNaN(a)?"":a>=1?`${a.toFixed(0)}s`:`${(1e3*a).toFixed(0)}ms`}var se=n(3321);const oe=[{label:"Root spans",value:"nestedSetParent<0",filter:{key:"nestedSetParent",operator:"<",value:"0"},description:"Focus your analysis on the root span of each trace"},{label:"All spans",value:"true",filter:{key:"",operator:"",value:!0},description:"View and analyse raw span data. This option may result in long query times."}];function le(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ce extends o.Bs{constructor(...e){super(...e),le(this,"onClick",(()=>{var e;const t=sr(this);var n;const a=null!==(n=null===(e=this.state.frame.fields.find((e=>e.labels)))||void 0===e?void 0:e.labels)&&void 0!==n?n:{};if(this.state.labelKey){if(!a[this.state.labelKey])return}else if(1!==Object.keys(a).length)return;var r;const i=null!==(r=this.state.labelKey)&&void 0!==r?r:Object.keys(a)[0],s=nr(this.state.frame,this.state.labelKey);ue(t,i,s),this.state.onClick({labelName:i})}))}}le(ce,"Component",(({model:e})=>{var t,n,a,i,s;const o=null!==(s=null===(t=e.state)||void 0===t?void 0:t.labelKey)&&void 0!==s?s:"",l=null===(n=e.state)||void 0===n?void 0:n.frame.fields.filter((e=>"time"!==e.type));var c;const d=null!==(c=null==l||null===(i=l[0])||void 0===i||null===(a=i.labels)||void 0===a?void 0:a[o])&&void 0!==c?c:"";return de(sr(e),o,d.replace(/"/g,""))?r().createElement(r().Fragment,null):r().createElement(u.Button,{variant:"primary",size:"sm",fill:"text",onClick:e.onClick,icon:"search-plus"},"Add to filters")}));const ue=(e,t,n)=>{const a=e.state.filters.filter((e=>"span.db.name"===e.key||e.key!==t));history.pushState(null,""),e.setState({filters:[...a,{key:t,operator:"=",value:n}]})},de=(e,t,n)=>sr(e).state.filters.find((e=>e.key===t&&e.value===n)),me=e=>{var t;const n=e.fields.find((e=>"Baseline"===e.name)),a=e.fields.find((e=>"Selection"===e.name));let r=0,i=0;for(let e=0;e<((null==n||null===(t=n.values)||void 0===t?void 0:t.length)||0);e++){const t=((null==a?void 0:a.values[e])||0)-((null==n?void 0:n.values[e])||0);Math.abs(t)>Math.abs(r||0)&&(r=t,i=e)}return{maxDifference:r,maxDifferenceIndex:i}},pe=e=>{if("duration"!==e)return{query:"status = error",type:"auto"}};function fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ve(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){fe(e,t,n[t])}))}return e}class ge extends o.Bs{_onActivate(){const{frame:e}=this.state;this.setState(ve({},me(e))),this._subs.add(this.subscribeToState(((e,t)=>{if(e.frame!==t.frame){const{frame:t}=e;this.setState(ve({},me(t)))}})))}getAttribute(){return this.state.frame.name}getValue(){const e=this.state.frame.fields.find((e=>"Value"===e.name));return null==e?void 0:e.values[this.state.maxDifferenceIndex||0]}onAddToFilters(){const e=sr(this),t=this.getAttribute();t&&ue(e,t,this.getValue())}constructor(e){super(ve({},e)),this.addActivationHandler((()=>this._onActivate()))}}function he(e){return{container:(0,l.css)({display:"flex",flexDirection:"column",flexGrow:1,height:"100%"}),differenceContainer:(0,l.css)({display:"flex",flexDirection:"column",flexGrow:1,border:`1px solid ${e.colors.secondary.border}`,background:e.colors.background.primary,padding:"8px",marginBottom:e.spacing(2),fontSize:"12px",height:"116px"}),differenceValue:(0,l.css)({fontSize:"36px",fontWeight:"bold",textAlign:"center"}),value:(0,l.css)({textAlign:"center",color:e.colors.secondary.text,textWrap:"nowrap",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}),title:(0,l.css)({fontWeight:500})}}function be(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ye(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){be(e,t,n[t])}))}return e}fe(ge,"Component",(({model:e})=>{const{maxDifference:t,maxDifferenceIndex:n,panel:a}=e.useState(),i=(0,u.useStyles2)(he),s=e.getValue();var o;const l=null!==(o=e.state.frame.name)&&void 0!==o?o:"",c=de(sr(e),l,s.replace(/"/g,""));return r().createElement("div",{className:i.container},r().createElement(a.Component,{model:a}),r().createElement("div",{className:i.differenceContainer},void 0!==t&&void 0!==n&&r().createElement(r().Fragment,null,r().createElement(u.Stack,{gap:1,justifyContent:"space-between",alignItems:"center"},r().createElement("div",{className:i.title},"Highest difference"),!c&&r().createElement(u.Button,{size:"sm",variant:"primary",icon:"search-plus",fill:"text",onClick:()=>e.onAddToFilters()},"Add to filters")),r().createElement("div",{className:i.differenceValue},(100*Math.abs(t)).toFixed(0===t?0:2),"%"),r().createElement("div",{className:i.value},s))))}));const we="#5794F299",Se="#FF9930",xe=e=>e.name||"No name available";function je(e,t,n,a){return(r,i)=>{const s=i.name?e[i.name]:void 0,l=new o.Zv({data:(c=ye({},r),u={series:[ye({},i)]},u=null!=u?u:{},Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(u)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(u)).forEach((function(e){Object.defineProperty(c,e,Object.getOwnPropertyDescriptor(u,e))})),c)});var c,u;if(s){const e=s.state.body;return e.setState({frame:i}),e.state.panel.setState({$data:l}),s}const d=Oe(a).setTitle(t(i)).setData(l),m=n(i);m&&d.setHeaderActions(m);const p=new o.xK({body:new ge({frame:i,panel:d.build()})});return i.name&&(e[i.name]=p),p}}function Oe(e){return o.d0.barchart().setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:se.$N.Multi}).setMax(1).setOverrides((t=>{t.matchFieldsWithName("Value").overrideCustomFieldConfig("axisPlacement",u.AxisPlacement.Hidden),t.matchFieldsWithName("Baseline").overrideColor({mode:"fixed",fixedColor:"duration"===e?we:"semi-dark-green"}).overrideUnit("percentunit"),t.matchFieldsWithName("Selection").overrideColor({mode:"fixed",fixedColor:"duration"===e?Se:"semi-dark-red"}).overrideUnit("percentunit")}))}function Ee(){return{refId:"A",query:`{${d.ui}} | histogram_over_time(duration)`,queryType:"traceql",tableType:"spans",limit:1e3,spss:10,filters:[]}}function ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ce extends o.Bs{constructor({selection:e}){super({selection:e}),ke(this,"startInvestigation",(()=>{const e=Xa(this);e.setState({selection:this.state.selection}),ur(e.state.actionView)||e.setActionView("comparison"),te(ne.analyse_traces,ae.analyse_traces.start_investigation,{selection:this.state.selection,metric:ir(this).useState().value})}))}}function Pe(e){return{wrapper:(0,l.css)({display:"flex",gap:"16px",alignItems:"center"}),placeholder:(0,l.css)({color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,display:"flex",gap:e.spacing.x0_5})}}ke(Ce,"Component",(({model:e})=>{const{selection:t}=Xa(e).useState(),n=(0,u.useStyles2)(Pe),a="auto"===(null==t?void 0:t.type),i=a?"Slowest traces are selected, navigate to the Comparison or Slow Traces tab for more details.":void 0;return r().createElement("div",{className:n.wrapper},r().createElement(u.Button,{variant:"secondary",size:"sm",fill:"solid",disabled:a,icon:"bolt",onClick:e.startInvestigation,tooltip:i},a?"Slowest traces selected":"Select slowest traces"))}));var _e=n(1269);const De=e=>[{topic:s.DataTopic.Annotations,operator:()=>t=>t.pipe((0,_e.map)((t=>t.map((t=>{if("exemplar"===t.name){const n=t.fields.find((e=>"traceId"===e.name));n&&(n.config.links=[{title:"View trace",url:"#${__value.raw}",onClick:t=>{var n,a,r;t.e.stopPropagation();const i=null===(r=t.e.target)||void 0===r||null===(a=r.parentElement)||void 0===a||null===(n=a.parentElement)||void 0===n?void 0:n.href;if(!i||-1===i.indexOf("#"))return;const s=i.split("#")[1];s&&""!==s&&(null==e||e(s))}}])}return t})))))}],Ne=()=>[{topic:s.DataTopic.Annotations,operator:()=>e=>e.pipe((0,_e.map)((e=>e.filter((e=>"exemplar"!==e.name)))))}];function Te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Te(e,t,n[t])}))}return e}function Ae(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class $e extends o.Bs{isDuration(){return"duration"===ir(this).state.value}_onActivate(){const e=ir(this).state.value;this.setState({$data:new o.Es({$data:new J({maxDataPoints:this.isDuration()?24:64,datasource:d.Vl,queries:[this.isDuration()?Ee():q(e)]}),transformations:this.isDuration()?[...Ne()]:[...De(vr(this))]}),panel:this.getVizPanel()})}getVizPanel(){const e=ir(this).state.value;var t;return this.isDuration()?function(e,t){const n=Xa(e),a=re().setHoverHeader(!0).setOption("selectionMode","xy").build();return a.setState({extendPanelContext:(e,a)=>{a.onSelectRange=e=>{var a,r,i,s;if(0===e.length)return void n.setState({selection:void 0});const o=e[0],l={type:"manual",raw:o};if(l.timeRange={from:Math.round(((null===(a=o.x)||void 0===a?void 0:a.from)||0)/1e3),to:Math.round(((null===(r=o.x)||void 0===r?void 0:r.to)||0)/1e3)},l.timeRange.from===l.timeRange.to)return;const c=ie(((null===(i=e[0].y)||void 0===i?void 0:i.from)||0)-1,t),u=ie((null===(s=e[0].y)||void 0===s?void 0:s.to)||0,t);l.duration={from:c,to:u},n.onUserUpdateSelection(l),ur(n.state.actionView)||n.setActionView("comparison"),te(ne.analyse_traces,ae.analyse_traces.start_investigation,{selection:l,metric:"duration"})}}}),new o.G1({direction:"row",children:[new o.vA({body:a})]})}(this,null!==(t=this.state.yBuckets)&&void 0!==t?t:[]):this.getRateOrErrorVizPanel(e)}getRateOrErrorVizPanel(e){const t=F().setHoverHeader(!0).setDisplayMode("transparent");return"rate"===e?t.setCustomFieldConfig("axisLabel","span/s"):"errors"===e&&t.setCustomFieldConfig("axisLabel","error/s").setColor({fixedColor:"semi-dark-red",mode:"fixed"}),new o.G1({direction:"row",children:[new o.vA({body:t.build()})]})}buildSelectionAnnotation(e){var t,n,a,r;if(!ur(e.actionView))return;const i=null===(n=e.selection)||void 0===n||null===(t=n.raw)||void 0===t?void 0:t.x,o=null===(r=e.selection)||void 0===r||null===(a=r.raw)||void 0===a?void 0:a.y,l=(0,s.arrayToDataFrame)([{time:(null==i?void 0:i.from)||0,xMin:(null==i?void 0:i.from)||0,xMax:(null==i?void 0:i.to)||0,timeEnd:(null==i?void 0:i.to)||0,yMin:null==o?void 0:o.from,yMax:null==o?void 0:o.to,isRegion:!0,fillOpacity:.15,lineWidth:1,lineStyle:"solid",color:Se,text:"Comparison selection"}]);return l.name="xymark",[l]}constructor(e){super(Ie({yBuckets:[],actions:[],isStreaming:!1},e)),this.addActivationHandler((()=>{this._onActivate();const e=o.jh.getData(this),t=Xa(this),n=o.jh.getTimeRange(this);this._subs.add(e.subscribeToState((a=>{var r,i,l;if(this.setState({isStreaming:(null===(r=a.data)||void 0===r?void 0:r.state)===s.LoadingState.Streaming}),(null===(i=a.data)||void 0===i?void 0:i.state)===s.LoadingState.Done)if(0===a.data.series.length||0===a.data.series[0].length||dr(a))this.setState({panel:new o.G1({children:[new o.vA({body:new y({message:d.PL,imgWidth:150})})]})});else{let r=[];if(this.isDuration()){var c,u;if(r=Le((null===(c=e.state.data)||void 0===c?void 0:c.series)||[]),t.state.selection&&(null===(u=a.data)||void 0===u?void 0:u.state)===s.LoadingState.Done){var m,p;const n=this.buildSelectionAnnotation(t.state);n&&!(null===(p=e.state.data)||void 0===p||null===(m=p.annotations)||void 0===m?void 0:m.length)&&e.setState({data:Ae(Ie({},e.state.data),{annotations:n})})}if(null==r?void 0:r.length){var f;const{minDuration:e,minBucket:a}=Ve(r),i={type:"auto"};(function(e){const t=o.jh.lookupVariable(d.pf,e);if(!(t instanceof o.yP))throw new Error("Latency threshold variable not found");return t})(this).changeValueTo(e),function(e){const t=o.jh.lookupVariable(d.xc,e);if(!(t instanceof o.yP))throw new Error("Partial latency threshold variable not found");return t}(this).changeValueTo(ie(a-1,r,.3)),i.duration={from:e,to:""},i.raw={x:{from:1e3*n.state.value.from.unix(),to:1e3*n.state.value.to.unix()},y:{from:a-.5,to:r.length-.5}},this.setState({actions:[new Ce({selection:i})]}),(null===(f=t.state.selection)||void 0===f?void 0:f.duration)&&"auto"!==t.state.selection.type||t.setState({selection:i})}}this.setState({yBuckets:r,panel:this.getVizPanel()})}else(null===(l=a.data)||void 0===l?void 0:l.state)===s.LoadingState.Loading&&this.setState({panel:new o.G1({direction:"column",children:[new O({component:()=>B(1)})]})})}))),this._subs.add(t.subscribeToState(((t,n)=>{var a;if((null===(a=e.state.data)||void 0===a?void 0:a.state)===s.LoadingState.Done&&(!(0,p.isEqual)(t.selection,n.selection)||t.actionView!==n.actionView)&&this.isDuration()){const n=this.buildSelectionAnnotation(t);e.setState({data:Ae(Ie({},e.state.data),{annotations:n})})}})))}))}}Te($e,"Component",(({model:e})=>{const{panel:t,actions:n,isStreaming:a}=e.useState(),{value:i}=ir(e).useState(),s=(0,u.useStyles2)(Be);if(!t)return;const o="duration"===i?"Click and drag to compare selection with baseline.":"";return r().createElement("div",{className:s.container},r().createElement("div",{className:s.headerContainer},r().createElement("div",{className:s.titleContainer},r().createElement("div",{className:s.titleRadioWrapper},r().createElement(u.RadioButtonList,{name:`metric-${i}`,options:[{title:"",value:"selected"}],value:"selected"}),r().createElement("span",null,(()=>{switch(i){case"errors":return"Errors rate";case"rate":return"Span rate";case"duration":return"Histogram by duration";default:return""}})())),o&&r().createElement("div",{className:s.subtitle},o)),r().createElement("div",{className:s.actions},a&&r().createElement(X,{isStreaming:!0,iconSize:10}),null==n?void 0:n.map((e=>r().createElement(e.Component,{model:e,key:e.state.key}))))),r().createElement(t.Component,{model:t}))}));const Le=e=>e.map((e=>parseFloat(e.fields[1].name))).sort(((e,t)=>e-t)),Ve=e=>{const t=Math.floor(e.length/4);let n=e.length-t-1;return n<0&&(n=0),{minDuration:ie(n-1,e),minBucket:n}};function Be(e){return{container:(0,l.css)({width:"100%",display:"flex",flexDirection:"column",border:`1px solid ${e.colors.border.weak}`,borderRadius:"2px",background:e.colors.background.primary,".show-on-hover":{display:"none"},"section, section:hover":{borderColor:"transparent"},"& .u-select":{border:"1px solid #ffffff75"}}),headerContainer:(0,l.css)({width:"100%",display:"flex",flexDirection:"row",padding:"8px",gap:"8px",justifyContent:"space-between",alignItems:"flex-start",fontWeight:e.typography.fontWeightBold}),titleContainer:(0,l.css)({display:"flex",flexDirection:"column",gap:"4px"}),titleRadioWrapper:(0,l.css)({display:"flex",alignItems:"center"}),actions:(0,l.css)({display:"flex",gap:"8px",alignItems:"center"}),subtitle:(0,l.css)({display:"flex",color:e.colors.text.secondary,fontSize:"12px",fontWeight:400,"& svg":{margin:"0 2px"}})}}var Re=n(940);const ze=({exploration:e})=>{const{origin:t}=(0,Re.A)(),[n,i]=(0,a.useState)("Copy url");return r().createElement(u.ToolbarButton,{variant:"canvas",icon:"share-alt",tooltip:n,onClick:()=>{navigator.clipboard&&(navigator.clipboard.writeText(t+function(e){return t=o.Go.getUrlState(e),s.urlUtil.renderUrl(d.D5,t);var t}(e)),i("Copied!"),setTimeout((()=>{i("Copy url")}),2e3))}})};function Fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Me(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Fe(e,t,n[t])}))}return e}function qe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const He=["span.http.method","span.http.request.method","span.http.route","span.http.path","span.http.status_code","span.http.response.status_code"],We=["Recommended","Resource","Span","Other"];function Ue({options:e,value:t,onChange:n}){var i;const s=(0,u.useStyles2)(Ge),o=(0,a.useMemo)((()=>Object.values(e.reduce(((e,t)=>{if(t.label){const s=t.label.slice(t.label.indexOf(".")+1);if(He.includes(t.label)){var n;const a=null!==(n=e.recommended)&&void 0!==n?n:{label:"Recommended",options:[]};a.options.push(qe(Me({},t),{label:s})),e.recommended=a}else if(t.label.startsWith("resource.")){var a;const n=null!==(a=e.resource)&&void 0!==a?a:{label:"Resource",options:[]};n.options.push(qe(Me({},t),{label:s})),e.resource=n}else if(t.label.startsWith("span.")){var r;const n=null!==(r=e.span)&&void 0!==r?r:{label:"Span",options:[]};n.options.push(qe(Me({},t),{label:s})),e.span=n}else{var i;const n=null!==(i=e.other)&&void 0!==i?i:{label:"Other",options:[]};n.options.push(t),e.other=n}}return e}),{})).sort(((e,t)=>We.indexOf(e.label)-We.indexOf(t.label)))),[e]);var l;return r().createElement("div",{className:s.container},r().createElement(u.Field,{label:"Add extra columns"},r().createElement(u.Select,{value:""!==(null==t?void 0:t.toString())&&null!==(l=null==t||null===(i=t.toString())||void 0===i?void 0:i.split(","))&&void 0!==l?l:"",placeholder:"Select an attribute",options:o,onChange:e=>n(e.map((e=>e.value)).join(",")),isMulti:!0,isClearable:!0,virtualized:!0,prefix:r().createElement(u.Icon,{name:"columns"})})))}const Ge=()=>({container:(0,l.css)({display:"flex",minWidth:"300px","& > div":{width:"100%"}})});function Ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Ke(e,t,n[t])}))}return e}class Ze extends o.Bs{setupTransformations(){return[()=>e=>e.pipe((0,_e.map)((e=>e.map((e=>{var t;const n=e.fields,a=n.find((e=>"traceName"===e.name)),i={type:u.TableCellDisplayMode.Custom,cellComponent:e=>{const t=e.frame,n=null==t?void 0:t.fields.find((e=>"traceIdHidden"===e.name)),a=null==t?void 0:t.fields.find((e=>"spanID"===e.name)),i=null==n?void 0:n.values[e.rowIndex],s=null==a?void 0:a.values[e.rowIndex];if(!i)return e.value;const o=e.value?e.value:"<name not yet available>";return r().createElement("div",{className:"cell-link-wrapper"},r().createElement("div",{className:"cell-link",title:o,onClick:()=>{this.publishEvent(new d.vR({traceId:i,spanId:s}),!0)}},o),r().createElement(u.Link,{href:this.getLinkToExplore(i,s),target:"_blank",title:"Open in new tab"},r().createElement(u.Icon,{name:"external-link-alt",size:"sm"})))}};return(null==a||null===(t=a.config)||void 0===t?void 0:t.custom)&&(a.config.custom.cellOptions=i),function(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}(Qe({},e),{fields:n})})))))]}updatePanel(e){var t,n;if((null==e?void 0:e.state)!==s.LoadingState.Loading&&(null==e?void 0:e.state)!==s.LoadingState.NotStarted&&(null==e?void 0:e.state)&&((null==e?void 0:e.state)!==s.LoadingState.Streaming||(null===(n=e.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length))){if((null==e?void 0:e.state)===s.LoadingState.Done||(null==e?void 0:e.state)===s.LoadingState.Streaming)if(0===e.series.length||0===e.series[0].length){if("empty"===this.state.dataState&&this.state.panel)return;this.setState({dataState:"empty",panel:new o.G1({children:[new o.vA({body:new y({message:d.PL,remedyMessage:d.a5,padding:"32px"})})]})})}else"done"!==this.state.dataState&&this.setState({dataState:"done",panel:new o.G1({direction:"row",children:[new o.vA({body:o.d0.table().setHoverHeader(!0).setOverrides((e=>e.matchFieldsWithName("spanID").overrideCustomFieldConfig("hidden",!0).matchFieldsWithName("traceService").overrideCustomFieldConfig("width",350).matchFieldsWithName("traceName").overrideCustomFieldConfig("width",350))).build()})]})})}else{if("loading"===this.state.dataState)return;this.setState({dataState:"loading",panel:new o.G1({direction:"row",children:[new O({component:Xe})]})})}}constructor(e){super(Qe({dataState:"empty"},e)),Ke(this,"getLinkToExplore",((e,t)=>{const n=er(Ja(this)),a=o.jh.getTimeRange(this).state.value,r=JSON.stringify({"explore-traces":{range:(0,s.toURLRange)(a.raw),queries:[{refId:"traceId",queryType:"traceql",query:e,datasource:n}],panelsState:{trace:{spanId:t}},datasource:n}});var i;const l=null!==(i=c.config.appSubUrl)&&void 0!==i?i:"";return s.urlUtil.renderUrl(`${l}/explore`,{panes:r,schemaVersion:1})})),Ke(this,"onChange",(e=>{const t=rr(this);t.getValue()!==e&&(t.changeValueTo(e),te(ne.analyse_traces,ae.analyse_traces.span_list_columns_changed,{columns:e}))})),this.addActivationHandler((()=>{this.setState({$data:new o.Es({transformations:this.setupTransformations()})});const e=o.jh.getData(this);this.updatePanel(e.state.data),this._subs.add(e.subscribeToState((e=>{this.updatePanel(e.data)})))}))}}Ke(Ze,"Component",(({model:e})=>{const{panel:t}=e.useState(),n=Je((0,u.useTheme2)()),a=rr(e),{attributes:i}=Xa(e).useState();var o;if(t)return r().createElement("div",{className:n.container},r().createElement("div",{className:n.header},r().createElement("div",{className:n.description},"View a list of spans for the current set of filters."),r().createElement(Ue,{options:null!==(o=null==i?void 0:i.map((e=>(0,s.toOption)(e))))&&void 0!==o?o:[],value:a.getValue(),onChange:e.onChange})),r().createElement(t.Component,{model:t}))}));const Je=e=>({container:(0,l.css)({display:"contents",'[role="cell"] > div':{display:"flex",width:"100%"},".cell-link-wrapper":{display:"flex",gap:"4px",justifyContent:"space-between",alignItems:"center",width:"100%",a:{padding:4,fontSize:0,":hover":{background:e.colors.background.secondary}}},".cell-link":{color:e.colors.text.link,cursor:"pointer",maxWidth:"300px",overflow:"hidden",textOverflow:"ellipsis",":hover":{textDecoration:"underline"}}}),description:(0,l.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0 ${e.spacing(2)} 0`}),header:(0,l.css)({display:"flex",justifyContent:"space-between",alignItems:"flex-start",gap:"10px"})}),Xe=()=>{const e=(0,u.useStyles2)(Ye);return r().createElement("div",{className:e.container},r().createElement("div",{className:e.title},r().createElement(j.A,{count:1,width:80})),[...Array(3)].map(((t,n)=>r().createElement("div",{className:e.row,key:n},[...Array(6)].map(((t,n)=>r().createElement("span",{className:e.rowItem,key:n},r().createElement(j.A,{count:1}))))))))};function Ye(e){return{container:(0,l.css)({height:"100%",width:"100%",position:"absolute",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:"5px"}),title:(0,l.css)({marginBottom:"20px"}),row:(0,l.css)({marginBottom:"5px",display:"flex",justifyContent:"space-around"}),rowItem:(0,l.css)({width:"14%"})}}function et(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class tt extends o.Bs{_onActivate(){var e;this._subs.add(null===(e=Xa(this).state.$data)||void 0===e?void 0:e.subscribeToState((()=>{this.updateBody()}))),this._subs.add(Xa(this).subscribeToState(((e,t)=>{var n,a;(null===(n=e.$data)||void 0===n?void 0:n.state.key)!==(null===(a=t.$data)||void 0===a?void 0:a.state.key)&&this.updateBody()}))),this._subs.add(ir(this).subscribeToState(((e,t)=>{e.value!==t.value&&this.updateBody()}))),this.updateBody()}updateBody(){this.setState({body:new Ze({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){et(e,t,n[t])}))}return e}({},e)),this.addActivationHandler(this._onActivate.bind(this))}}function nt(e){if(e.attributes)for(const n of e.attributes){var t;if("nestedSetLeft"===n.key)return parseInt(n.value.intValue||(null===(t=n.value.Value)||void 0===t?void 0:t.int_value)||"0",10)}throw new Error("nestedSetLeft not found!")}function at(e){if(e.attributes)for(const n of e.attributes){var t;if("nestedSetRight"===n.key)return parseInt(n.value.intValue||(null===(t=n.value.Value)||void 0===t?void 0:t.int_value)||"0",10)}throw new Error("nestedSetRight not found!")}function rt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}et(tt,"Component",(({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})}));class it{addSpan(e){this.left=Math.min(nt(e),this.left),this.right=Math.max(at(e),this.right),this.spans.push(e)}addChild(e){e.parent=this,this.children.push(e)}isChild(e){return nt(e)>this.left&&at(e)<this.right}findMatchingChild(e){const t=ot(e);for(const e of this.children)if(e.name===t)return e;return null}constructor({name:e,serviceName:t,operationName:n,spans:a,left:r,right:i,traceID:s}){rt(this,"name",void 0),rt(this,"serviceName",void 0),rt(this,"operationName",void 0),rt(this,"spans",void 0),rt(this,"left",void 0),rt(this,"right",void 0),rt(this,"children",void 0),rt(this,"parent",void 0),rt(this,"traceID",void 0),this.name=e,this.serviceName=t,this.operationName=n,this.spans=a,this.left=r,this.right=i,this.children=[],this.parent=null,this.traceID=s}}function st(e){var t,n,a;const r=null===(t=e.attributes)||void 0===t?void 0:t.find((e=>"service.name"===e.key));var i,s,o,l;return new it({left:nt(e),right:at(e),name:ot(e),serviceName:null!==(s=null!==(i=null==r?void 0:r.value.stringValue)&&void 0!==i?i:null==r||null===(a=r.value)||void 0===a||null===(n=a.Value)||void 0===n?void 0:n.string_value)&&void 0!==s?s:"",operationName:null!==(o=e.name)&&void 0!==o?o:"",spans:[e],traceID:null!==(l=e.traceId)&&void 0!==l?l:""})}function ot(e){let t="";for(const n of e.attributes||[])"service.name"===n.key&&n.value.stringValue&&(t=n.value.stringValue);return`${t}:${e.name}`}function lt(e){e.left=Number.MAX_SAFE_INTEGER,e.right=Number.MIN_SAFE_INTEGER;for(const t of e.children)lt(t)}function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){ct(e,t,n[t])}))}return e}const dt="0000000000000000";class mt extends o.Bs{_onActivate(){var e;this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{var t,n,a,r;if((null===(t=e.data)||void 0===t?void 0:t.state)!==s.LoadingState.Loading&&(null===(n=e.data)||void 0===n?void 0:n.state)!==s.LoadingState.Streaming){if((null===(a=e.data)||void 0===a?void 0:a.state)===s.LoadingState.Done&&(null===(r=e.data)||void 0===r?void 0:r.series.length)){var i;const t=null===(i=e.data)||void 0===i?void 0:i.series[0].fields[0].values[0];if(t){const e=function(e){const t=new it({name:"root",serviceName:"",operationName:"",left:Number.MIN_SAFE_INTEGER,right:Number.MAX_SAFE_INTEGER,spans:[],traceID:""});if(e&&e.length>0)for(const a of e){var n;if(1!==(null===(n=a.spanSets)||void 0===n?void 0:n.length))throw new Error("there should be only 1 spanset!");const e=parseInt(a.startTimeUnixNano||"0",10),r=a.spanSets[0];r.spans.sort(((e,t)=>nt(e)-nt(t)));let i=t;lt(t);for(const t of r.spans){for(t.traceId=a.traceID,t.startTimeUnixNano=""+(parseInt(t.startTimeUnixNano,10)-e);null!==i.parent&&!i.isChild(t);)i=i.parent;const n=i.findMatchingChild(t);if(n){n.addSpan(t),i=n;continue}const r=st(t);r.traceID=a.traceID,i.addChild(r),i=r}}return t}(JSON.parse(t));e.children.sort(((e,t)=>vt(t)-vt(e))),this.setState({loading:!1,tree:e,panel:new o.G1({height:"100%",wrap:"wrap",children:this.getPanels(e)})})}}}else this.setState({loading:!0})})))}getPanels(e){return e.children.map((e=>new o.vA({height:150,width:"100%",minHeight:"400px",body:this.getPanel(e)})))}getPanel(e){const t=o.jh.getTimeRange(this),n=t.state.value.from,a=t.state.value.to,r=vr(this);return o.d0.traces().setTitle(`Structure for ${e.serviceName} [${vt(e)} spans used]`).setOption("createFocusSpanLink",((e,t)=>({title:"Open trace",href:"#",onClick:()=>r(e,t),origin:{},target:"_self"}))).setData(new o.Zv({data:{state:s.LoadingState.Done,timeRange:{from:n,to:a,raw:{from:n,to:a}},series:[ut({},this.buildData(e))]}})).build()}buildData(e){const t=this.getTrace(e,dt),n=t[0].serviceName+":"+t[0].operationName;return(0,s.createDataFrame)({name:`Trace ${n}`,refId:`trace_${n}`,fields:[{name:"references",type:s.FieldType.other,values:t.map((e=>e.references))},{name:"traceID",type:s.FieldType.string,values:t.map((e=>e.traceID))},{name:"spanID",type:s.FieldType.string,values:t.map((e=>e.spanID))},{name:"parentSpanID",type:s.FieldType.string,values:t.map((e=>e.parentSpanId))},{name:"serviceName",type:s.FieldType.string,values:t.map((e=>e.serviceName))},{name:"operationName",type:s.FieldType.string,values:t.map((e=>e.operationName))},{name:"duration",type:s.FieldType.number,values:t.map((e=>e.duration))},{name:"startTime",type:s.FieldType.number,values:t.map((e=>e.startTime))},{name:"statusCode",type:s.FieldType.number,values:t.map((e=>e.statusCode))}]})}getTrace(e,t){const n=e.spans.reduce(((e,t)=>{var n,a;return"error"===(null===(a=t.attributes)||void 0===a||null===(n=a.find((e=>"status"===e.key)))||void 0===n?void 0:n.value.stringValue)?e+1:e}),0);let a=1e-4;t!==dt&&(a=e.spans.reduce(((e,t)=>e+parseInt(t.startTimeUnixNano,10)),0)/e.spans.length/1e6);const r=[{references:e.spans.slice(-5).map((e=>({refType:"EXTERNAL",traceID:e.traceId,spanID:e.spanID}))),traceID:e.traceID,spanID:e.spans[0].spanID,parentSpanId:t,serviceName:e.serviceName,operationName:e.operationName,statusCode:n>0?2:0,duration:e.spans.reduce(((e,t)=>e+parseInt(t.durationNanos,10)),0)/e.spans.length/1e6,startTime:a}];for(const t of e.children)r.push(...this.getTrace(t,e.spans[0].spanID));return r}constructor(e){super(ut({$data:new o.Es({$data:new o.dt({datasource:d.Vl,queries:[pt(e.metric)]}),transformations:d.s9}),loading:!0},e)),this.addActivationHandler(this._onActivate.bind(this))}}function pt(e){let t,n="";switch(e){case"errors":t="status = error",n="status = error";break;case"duration":t=`duration > ${d.Ld}`,n=`duration > ${d.xT}`;break;default:t="kind = server"}return{refId:"A",query:`{${d.ui} ${n.length?`&& ${n}`:""}} &>> { ${t} } | select(status, resource.service.name, name, nestedSetParent, nestedSetLeft, nestedSetRight)`,queryType:"traceql",tableType:"raw",limit:200,spss:20,filters:[]}}ct(mt,"Component",(({model:e})=>{var t,n;const{tree:a,loading:i,panel:o,$data:l}=e.useState(),c=ft((0,u.useTheme2)()),m=(0,u.useTheme2)(),p=Ja(e),{value:f}=p.getMetricVariable().useState(),v=f;let g,b=i||!(null==a?void 0:a.children.length);(null==l||null===(t=l.state.data)||void 0===t?void 0:t.state)===s.LoadingState.Done&&(b=!1);let y="";switch(v){case"rate":g=r().createElement(r().Fragment,null,r().createElement("div",null,"Analyse the service structure of the traces that match the current filters."),r().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),y="server";break;case"errors":g=r().createElement(r().Fragment,null,r().createElement("div",null,"Analyse the errors structure of the traces that match the current filters."),r().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),y="error";break;case"duration":g=r().createElement(r().Fragment,null,r().createElement("div",null,"Analyse the structure of slow spans from the traces that match the current filters."),r().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),y="slow"}const w=bn(v),S=r().createElement(r().Fragment,null,r().createElement(u.Text,{textAlignment:"center",variant:"h3"},d.PL),r().createElement(u.Text,{textAlignment:"center",variant:"body"},r().createElement("div",{className:c.longText},"The structure tab shows ",y," spans beneath what you are currently investigating. Currently, there are no descendant ",y," spans beneath the spans you are investigating.")),r().createElement(u.Stack,{gap:.5,alignItems:"center"},r().createElement(u.Icon,{name:"info-circle"}),r().createElement(u.Text,{textAlignment:"center",variant:"body"},"The structure tab works best with full traces.")),r().createElement("div",{className:c.actionContainer},"Read more about",r().createElement("div",{className:c.action},r().createElement(u.LinkButton,{icon:"external-link-alt",fill:"solid",size:"sm",target:"_blank",href:"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/concepts/#trace-structure"},`${w.toLowerCase()}`))));return r().createElement(u.Stack,{direction:"column",gap:1},r().createElement("div",{className:c.description},g),b&&r().createElement(u.Stack,{direction:"column",gap:2},r().createElement(j.A,{count:4,height:200,baseColor:m.colors.background.secondary,highlightColor:m.colors.background.primary})),!b&&a&&a.children.length>0&&r().createElement("div",{className:c.traceViewList},o&&r().createElement(o.Component,{model:o})),(null==l||null===(n=l.state.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&!(null==a?void 0:a.children.length)&&r().createElement(h,{message:S,padding:"32px"}))}));const ft=e=>({description:(0,l.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0`}),traceViewList:(0,l.css)({display:"flex",flexDirection:"column",gap:e.spacing.x1,'div[class*="panel-content"] > div':{overflow:"auto",'> :not([class*="TraceTimelineViewer"])':{display:"none"}},'div[data-testid="span-detail-component"] > :nth-child(4) > :nth-child(1)':{display:"none"},".span-detail-row":{display:"none"},'div[data-testid="TimelineRowCell"]':{'button[role="switch"]':{cursor:"text"}},'div[data-testid="span-view"]':{cursor:"text !important"}}),longText:(0,l.css)({maxWidth:"800px",margin:"0 auto"}),action:(0,l.css)({marginLeft:e.spacing(1)}),actionContainer:(0,l.css)({display:"flex",justifyContent:"space-between",alignItems:"center"})});function vt(e){let t=e.spans.length;for(const n of e.children)t+=vt(n);return t}var gt=n(5540);function ht({options:e,radioAttributes:t,value:n,onChange:i,showAll:s=!1,model:o}){const l=(0,u.useStyles2)(bt),c=(0,u.useTheme2)(),{fontSize:m}=c.typography,[p,f]=(0,a.useState)(""),[v,g]=(0,a.useState)(0),h=(0,a.useRef)(null),{filters:b}=sr(o).useState(),{value:y}=ir(o).useState(),w=y;(0,gt.w)({ref:h,onResize:()=>{const e=h.current;e&&g(e.clientWidth)}});const S=(0,a.useMemo)((()=>{let n=0;return t.filter((t=>{let n=!!e.find((e=>e.value===t));return!b.find((e=>e.key===t&&("="===e.operator||"!="===e.operator)))&&(b.find((e=>"nestedSetParent"===e.key))&&(n=n&&"rootName"!==t&&"rootServiceName"!==t),"rate"!==w&&"errors"!==w||(n=n&&"status"!==t),n)})).map((e=>({label:e.replace(d.zd,"").replace(d.$d,""),text:e,value:e}))).filter((e=>{const t=e.label||e.text||"",a=(0,u.measureText)(t,m).width;return n+a+40+180<v&&(n+=a+40,!0)}))}),[t,e,b,w,m,v]),x=(0,a.useMemo)((()=>{const t=e.filter((e=>!S.find((t=>{var n;return t.value===(null===(n=e.value)||void 0===n?void 0:n.toString())}))));return yt(t,p)}),[p,e,S]),j=e=>e.filter((e=>{var t;return!d.uK.includes(null===(t=e.value)||void 0===t?void 0:t.toString())})).map((e=>{var t;return{label:null===(t=e.label)||void 0===t?void 0:t.replace(d.zd,"").replace(d.$d,""),value:e.value}}));(0,a.useEffect)((()=>{var a,r;const o=null!==(r=t[0])&&void 0!==r?r:null===(a=e[0])||void 0===a?void 0:a.value;o&&(s||n&&n!==d.y2||i(o,!0))}));const O=s?[{label:d.y2,value:d.y2}]:[],E=s?d.y2:"";return r().createElement(u.Field,{label:"Group by"},r().createElement("div",{ref:h,className:l.container},S.length>0&&r().createElement(u.RadioButtonGroup,{options:[...O,...S],value:n,onChange:i}),r().createElement(u.Select,{value:n&&j(x).some((e=>e.value===n))?n:null,placeholder:"Other attributes",options:j(x),onChange:e=>{var t;const n=null!==(t=null==e?void 0:e.value)&&void 0!==t?t:E;i(n)},className:l.select,isClearable:!0,onInputChange:(e,{action:t})=>{"input-change"===t&&f(e)},onCloseMenu:()=>f(""),virtualized:!0})))}function bt(e){return{select:(0,l.css)({maxWidth:e.spacing(22)}),container:(0,l.css)({display:"flex",gap:e.spacing(1)})}}const yt=(e,t)=>{if(0===e.length)return[];if(0===t.length)return e.slice(0,d.nr);const n=t.toLowerCase();return e.filter((e=>!!(e.value&&e.value.length>0)&&e.value.toLowerCase().includes(n))).slice(0,d.nr)};function wt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class St extends o.Bs{Selector({model:e}){const{active:t,options:n}=e.useState();return r().createElement(u.Field,{label:"View"},r().createElement(u.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))}constructor(...e){super(...e),wt(this,"onLayoutChange",(e=>{this.setState({active:e}),te(ne.analyse_traces,ae.analyse_traces.layout_type_changed,{layout:e})}))}}wt(St,"Component",(({model:e})=>{const{layouts:t,options:n,active:a}=e.useState(),i=n.findIndex((e=>e.value===a));if(-1===i)return null;const s=t[i];return r().createElement(s.Component,{model:s})}));const xt=()=>o.d0.timeseries().setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:u.TooltipDisplayMode.Multi}).setCustomFieldConfig("fillOpacity",15);function jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ot(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){jt(e,t,n[t])}))}return e}function Et(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class kt extends o.Bs{constructor(e){super(Et(Ot({},e),{queries:[]})),jt(this,"_onActivate",(()=>{this._subs.add(this.subscribeToState((()=>{this.getQueries(),this.getContext()})))})),jt(this,"getQueries",(()=>{const e=o.jh.getData(this),t=o.jh.findObject(e,Ct);if(Ct(t)){const e=t.state.queries.map((e=>Et(Ot({},e),{query:this.state.query})));JSON.stringify(e)!==JSON.stringify(this.state.queries)&&this.setState({queries:e})}})),jt(this,"getContext",(()=>{const{queries:e,dsUid:t,labelValue:n,type:a="traceMetrics"}=this.state,r=o.jh.getTimeRange(this);if(!r||!e||!t)return;const i={origin:"Explore Traces",type:a,queries:e,timeRange:Ot({},r.state.value),datasource:{uid:t},url:window.location.href,id:`${JSON.stringify(e)}`,title:`${n}`,logoPath:"public/plugins/grafana-exploretraces-app/img/1382cadfeb81ccdaa67d.svg"};JSON.stringify(i)!==JSON.stringify(this.state.context)&&this.setState({context:i})})),this.addActivationHandler(this._onActivate.bind(this))}}function Ct(e){return e instanceof o.dt}function Pt(e,t,n,a,r,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function _t(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){Pt(i,a,r,s,o,"next",e)}function o(e){Pt(i,a,r,s,o,"throw",e)}s(void 0)}))}}function Dt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Nt="Add to investigation",Tt="grafana-exploretraces-app/investigation/v1",It="investigations_divider",At="Investigations";class $t extends o.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){super(e),this.addActivationHandler((()=>{const e=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",href:Lt(this),onClick:()=>Vt()}];this.setState({body:new o.Lw({items:e})});const t=er(Ja(this)),n=new kt({query:this.state.query,dsUid:t});var a,r;n.activate(),this.setState({addToInvestigationButton:n}),this._subs.add(null==n?void 0:n.subscribeToState((()=>{!function(e){Rt.apply(this,arguments)}(this)}))),n.setState((a=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Dt(e,t,n[t])}))}return e}({},n.state),r=null!=(r={labelValue:this.state.labelValue})?r:{},Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(r)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(r)).forEach((function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(r,e))})),a))}))}}Dt($t,"Component",(({model:e})=>{const{body:t}=e.useState();return t?r().createElement(t.Component,{model:t}):r().createElement(r().Fragment,null)}));const Lt=e=>{const t=er(Ja(e)),n=o.jh.getTimeRange(e).state.value,a=function(e){var t;const n=o.jh.getData(e).state.data,a=null==n||null===(t=n.request)||void 0===t?void 0:t.targets[0];return a?a.step:void 0}(e),r=JSON.stringify({"traces-explore":{range:(0,s.toURLRange)(n.raw),queries:[{refId:"A",datasource:t,query:e.state.query,step:a}]}});var i;const l=null!==(i=c.config.appSubUrl)&&void 0!==i?i:"";return s.urlUtil.renderUrl(`${l}/explore`,{panes:r,schemaVersion:1})},Vt=()=>{te(ne.analyse_traces,ae.analyse_traces.open_in_explore_clicked)},Bt=function(){var e=_t((function*(e){const t=e.state.context;return void 0!==c.getPluginLinkExtensions?(0,c.getPluginLinkExtensions)({extensionPointId:Tt,context:t}).extensions[0]:void 0!==c.getObservablePluginLinks?(yield(0,_e.firstValueFrom)((0,c.getObservablePluginLinks)({extensionPointId:Tt,context:t})))[0]:void 0}));return function(t){return e.apply(this,arguments)}}();function Rt(){return(Rt=_t((function*(e){const t=e.state.addToInvestigationButton;if(t){var n;const l=yield Bt(t);var a;const c=null!==(a=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==a?a:[],u=c.find((e=>e.text===Nt));var r,i,s,o;l&&(u?u&&(null===(r=e.state.body)||void 0===r||r.setItems(c.filter((e=>!1===[It,At,Nt].includes(e.text))))):(null===(i=e.state.body)||void 0===i||i.addItem({text:It,type:"divider"}),null===(s=e.state.body)||void 0===s||s.addItem({text:At,type:"group"}),null===(o=e.state.body)||void 0===o||o.addItem({text:Nt,iconClassName:"plus-square",onClick:e=>{l.onClick&&l.onClick(e),te(ne.analyse_traces,ae.analyse_traces.add_to_investigation_clicked)}})))}}))).apply(this,arguments)}function zt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ft(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){zt(e,t,n[t])}))}return e}function Mt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function qt(e,t,n){const a=Ja(e).getMetricVariable().getValue(),r=q(a,t.getValueText()),i={};return new St({$behaviors:[e=>{const t=new Map,n=e.subscribeToEvent(d.sv,(n=>{const a=n.payload.series;null==a||a.forEach((e=>{e.fields.slice(1).forEach((n=>{t.set(e.refId,Math.max(...n.values.filter((e=>e))))}))})),function(e,t){const n=o.jh.findAllObjects(e,(e=>e instanceof o.Eb));for(const e of n)e.clearFieldConfigCache(),e.setState({fieldConfig:(0,p.merge)((0,p.cloneDeep)(e.state.fieldConfig),{defaults:{max:t}})})}(e,Math.max(...t.values()))}));return()=>{n.unsubscribe()}}],$data:new o.Es({$data:new J({maxDataPoints:64,datasource:d.Vl,queries:[r]}),transformations:[...De(vr(e)),()=>e=>e.pipe((0,_e.map)((e=>(e.forEach((e=>(0,s.reduceField)({field:e.fields[1],reducers:[s.ReducerID.max]}))),e.sort(((e,t)=>{var n,a,r,i;return((null===(a=t.fields[1].state)||void 0===a||null===(n=a.calcs)||void 0===n?void 0:n.max)||0)-((null===(i=e.fields[1].state)||void 0===i||null===(r=i.calcs)||void 0===r?void 0:r.max)||0)}))))))]}),options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new o.G1({direction:"column",children:[new o.vA({minHeight:300,body:("duration"===a?xt().setUnit("s"):xt()).build()})]}),new L({body:new o.gF({templateColumns:d.MV,autoRows:"200px",isLazy:!0,children:[]}),groupBy:!0,getLayoutChild:Ht(i,nr,t,a,n)}),new L({body:new o.gF({templateColumns:"1fr",autoRows:"200px",isLazy:!0,children:[]}),groupBy:!0,getLayoutChild:Ht(i,nr,t,a,n)})]})}function Ht(e,t,n,a,r){return(i,s)=>{var l;const c=s.name?e[s.name]:void 0,u=new o.Zv({data:Mt(Ft({},i),{annotations:null===(l=i.annotations)||void 0===l?void 0:l.filter((e=>e.refId===s.refId)),series:[Mt(Ft({},s),{fields:s.fields.sort(((e,t)=>{var n,a,r;return(null===(r=e.labels)||void 0===r||null===(a=r.status)||void 0===a?void 0:a.localeCompare((null===(n=t.labels)||void 0===n?void 0:n.status)||""))||0}))})]})});var d;if(c)return null===(d=c.state.body)||void 0===d||d.setState({$data:u}),c;const m=o.jh.interpolate(n,M({metric:a,extraFilters:`${n.getValueText()}=${pr(nr(s))}`,groupByStatus:!0})),p=("duration"===a?xt().setUnit("s"):F()).setTitle(t(s,n.getValueText())).setMenu(new $t({query:m,labelValue:nr(s)})).setData(u),f=r(s);f&&p.setHeaderActions(f);const v=new o.xK({body:p.build()});return s.name&&(e[s.name]=v),v}}function Wt({description:e,tags:t}){const n=(a=(0,u.useTheme2)(),{infoFlex:(0,l.css)({display:"flex",gap:a.spacing(2),alignItems:"center",padding:`${a.spacing(1)} 0 ${a.spacing(2)} 0`}),tagsFlex:(0,l.css)({display:"flex",gap:a.spacing(1),alignItems:"center"}),tag:(0,l.css)({display:"inline-block",width:a.spacing(2),height:a.spacing(.5),borderRadius:a.spacing(.5)})});var a;return r().createElement("div",{className:n.infoFlex},r().createElement("div",{className:n.tagsFlex},e),t.length>0&&t.map((e=>r().createElement("div",{className:n.tagsFlex,key:e.label},r().createElement("div",{className:n.tag,style:{backgroundColor:e.color}}),r().createElement("div",null,e.label)))))}function Ut(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Gt extends o.Bs{_onActivate(){const e=ar(this);e.subscribeToState((()=>{this.setBody(e)})),Xa(this).subscribeToState((()=>{this.setBody(e)})),this.setBody(e)}onReferencedVariableValueChanged(){const e=ar(this);e.changeValueTo(d.u0[0]),this.setBody(e)}onAddToFiltersClick(e){te(ne.analyse_traces,ae.analyse_traces.breakdown_add_to_filters_clicked,e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Ut(e,t,n[t])}))}return e}({},e)),Ut(this,"_variableDependency",new o.Sh(this,{variableNames:[d.Ao,d.PU],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),Ut(this,"setBody",(e=>{this.setState({body:qt(this,e,(t=>[new ce({frame:t,labelKey:e.getValueText(),onClick:this.onAddToFiltersClick})]))})})),Ut(this,"onChange",((e,t)=>{const n=ar(this);n.getValueText()!==e&&(n.changeValueTo(e,void 0,!t),te(ne.analyse_traces,ae.analyse_traces.breakdown_group_by_changed,{groupBy:e}))})),this.addActivationHandler(this._onActivate.bind(this))}}function Kt(e){return{container:(0,l.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,l.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,l.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,l.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),scope:(0,l.css)({marginRight:e.spacing(2)}),groupBy:(0,l.css)({width:"100%"}),controlsLeft:(0,l.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"row"})}}function Qt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Ut(Gt,"Component",(({model:e})=>{const t=ar(e).getValueText(),n=t.includes(d.zd)||d.jx.includes(t)?d.BS:d.bD,[i,s]=(0,a.useState)(n),{body:o}=e.useState(),l=(0,u.useStyles2)(Kt),{attributes:c}=Xa(e).useState(),m=i===d.bD?d.$d:d.zd;let p=null==c?void 0:c.filter((e=>e.includes(m)));i===d.BS&&(p=null==p?void 0:p.concat(d.jx));const f=Ja(e),{value:v}=f.getMetricVariable().useState(),g=(e=>{switch(e){case"rate":return"Attributes are ordered by their rate of requests per second.";case"errors":return"Attributes are ordered by their rate of errors per second.";case"duration":return"Attributes are ordered by their average duration.";default:throw new Error("Metric not supported")}})(v);return r().createElement("div",{className:l.container},r().createElement(Wt,{description:g,tags:"duration"===v?[]:[{label:"Rate",color:"green"},{label:"Error",color:"red"}]}),r().createElement("div",{className:l.controls},(null==p?void 0:p.length)&&r().createElement("div",{className:l.controlsLeft},r().createElement("div",{className:l.scope},r().createElement(u.Field,{label:"Scope"},r().createElement(u.RadioButtonGroup,{options:tr([d.bD,d.BS]),value:i,onChange:s}))),r().createElement("div",{className:l.groupBy},r().createElement(ht,{options:tr(p),radioAttributes:i===d.bD?d.u0:d.jx,value:t,onChange:e.onChange,model:e}))),o instanceof St&&r().createElement("div",{className:l.controlsRight},r().createElement(o.Selector,{model:o}))),r().createElement("div",{className:l.content},o&&r().createElement(o.Component,{model:o})))}));class Zt extends o.Bs{_onActivate(){this.updateBody()}updateBody(){this.setState({body:new Gt({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Qt(e,t,n[t])}))}return e}({},e)),Qt(this,"_variableDependency",new o.Sh(this,{variableNames:[d.PU]})),this.addActivationHandler(this._onActivate.bind(this))}}Qt(Zt,"Component",(({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})}));var Jt=n(2468);function Xt(e){var t,n,a,r;let i="";if(!e)return"{}";e.query&&(i+=e.query);const s=[];(null===(t=e.duration)||void 0===t?void 0:t.from.length)&&s.push(`duration >= ${e.duration.from}`),(null===(n=e.duration)||void 0===n?void 0:n.to.length)&&s.push(`duration <= ${e.duration.to}`),s.length&&(i.length&&(i+=" && "),i+=s.join(" && "));const o=null===(a=e.timeRange)||void 0===a?void 0:a.from,l=null===(r=e.timeRange)||void 0===r?void 0:r.to;return`{${i}}, 10${o&&l?`, ${1e9*o}, ${1e9*l}`:""}`}function Yt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function en(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Yt(e,t,n[t])}))}return e}function tn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function nn(e,t,n,a){var r;const i=o.jh.getTimeRange(e),l=o.jh.getData(e),c=t.getValueText(),u=null===(r=l.state.data)||void 0===r?void 0:r.series.find((e=>e.name===c)),m=[],p=null==u?void 0:u.fields.find((e=>"Value"===e.name)),f=null==u?void 0:u.fields.find((e=>"Baseline"===e.name)),v=null==u?void 0:u.fields.find((e=>"Selection"===e.name));if(p&&f&&v)for(let e=0;e<p.values.length;e++)p.values[e]&&(f.values[e]||v.values[e])&&m.push({name:p.values[e].replace(/"/g,""),length:1,fields:[{name:"Value",type:s.FieldType.string,values:["Baseline","Comparison"],config:{}},tn(en({},f),{values:[f.values[e]],labels:{[c]:p.values[e]},config:{displayName:"Baseline"}}),tn(en({},v),{values:[v.values[e]]})]});return new L({$data:new o.Es({$data:new o.Zv({data:{timeRange:i.state.value,state:s.LoadingState.Done,series:m}}),transformations:[()=>e=>e.pipe((0,_e.map)((e=>(e.forEach((e=>(0,s.reduceField)({field:e.fields[2],reducers:[s.ReducerID.max]}))),e.sort(((e,t)=>{var n,a,r,i;return((null===(a=t.fields[2].state)||void 0===a||null===(n=a.calcs)||void 0===n?void 0:n.max)||0)-((null===(i=e.fields[2].state)||void 0===i||null===(r=i.calcs)||void 0===r?void 0:r.max)||0)}))))))]}),body:new o.gF({templateColumns:d.MV,autoRows:"200px",isLazy:!0,children:[]}),getLayoutChild:rn({},an,n,a)})}const an=e=>e.name||"No name available";function rn(e,t,n,a){return(r,i)=>{const s=i.name?e[i.name]:void 0,l=new o.Zv({data:tn(en({},r),{series:[en({},i)]})});var c;if(s)return null===(c=s.state.body)||void 0===c||c.setState({$data:l}),s;const u=Oe(a).setTitle(t(i)).setData(l),d=n(i);d&&u.setHeaderActions(d);const m=new o.xK({body:u.build()});return i.name&&(e[i.name]=m),m}}class sn extends o.Bs{}function on(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(sn,"Component",(({model:e})=>e.state.attribute?r().createElement(u.Button,{variant:"secondary",size:"sm",fill:"solid",onClick:()=>e.state.onClick()},"Inspect"):null));class ln extends o.Bs{_onActivate(){const e=ar(this);e.changeValueTo(d.y2),this.updateData(),e.subscribeToState(((t,n)=>{t.value!==n.value&&this.setBody(e)})),or(this).subscribeToState((()=>{this.updateData(),this.setBody(e)})),Xa(this).subscribeToState(((t,n)=>{(0,p.isEqual)(t.selection,n.selection)||(this.updateData(),this.setBody(e))})),o.jh.getTimeRange(this).subscribeToState((()=>{this.updateData()})),this.setBody(e)}updateData(){const e=Xa(this),t=o.jh.getTimeRange(this),n=t.state.value.from.unix(),a=t.state.value.to.unix(),r=or(this).state.value,i=this.getFilteredAttributes(r);this.setState({$data:new o.Es({$data:new o.dt({datasource:d.Vl,queries:[cn(n,a,Xt(e.state.selection))]}),transformations:[()=>e=>e.pipe((0,_e.map)((e=>{const t=un(e);return Object.entries(t).filter((([e,t])=>!i.includes(e))).map((([e,t])=>dn(e,t))).sort(((e,t)=>{const n=me(e),a=me(t);return Math.abs(a.maxDifference)-Math.abs(n.maxDifference)}))})))]})})}onReferencedVariableValueChanged(){const e=ar(this);e.changeValueTo(d.y2),this.setBody(e)}onAddToFiltersClick(e){te(ne.analyse_traces,ae.analyse_traces.comparison_add_to_filters_clicked,e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){on(e,t,n[t])}))}return e}({},e)),on(this,"_variableDependency",new o.Sh(this,{variableNames:[d.Ao,d.CE],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),on(this,"getFilteredAttributes",(e=>"nestedSetParent<0"===e?["rootName","rootServiceName"]:[])),on(this,"setBody",(e=>{const t=Ja(this);var n,a;this.setState({body:e.hasAllValue()||e.getValue()===d.y2?(n=e=>new sn({attribute:e.name,onClick:()=>this.onChange(e.name||"")}),a=t.getMetricFunction(),new L({body:new o.gF({templateColumns:d.MV,autoRows:"320px",children:[]}),getLayoutChild:je({},xe,n,a)})):nn(this,e,(t=>[new ce({frame:t,labelKey:e.getValueText(),onClick:this.onAddToFiltersClick})]),t.getMetricFunction())})})),on(this,"onChange",((e,t)=>{ar(this).changeValueTo(e,void 0,!t),te(ne.analyse_traces,ae.analyse_traces.select_attribute_in_comparison_clicked,{value:e})})),this.addActivationHandler(this._onActivate.bind(this))}}function cn(e,t,n){const a=`${(0,Jt.duration)(t-e,"s").asSeconds()}s`;return{refId:"A",query:`{${d.ui}} | compare(${n})`,step:a,queryType:"traceql",tableType:"spans",limit:100,spss:10,filters:[]}}on(ln,"Component",(({model:e})=>{const{body:t}=e.useState(),n=ar(e),a=Ja(e),{attributes:i}=Xa(e).useState(),s=(0,u.useStyles2)(pn);return r().createElement("div",{className:s.container},r().createElement(Wt,{description:"Attributes are ordered by the difference between the baseline and selection values for each value.",tags:[{label:"Baseline",color:"duration"===a.getMetricFunction()?we:(0,u.getTheme)().visualization.getColorByName("semi-dark-green")},{label:"Selection",color:"duration"===a.getMetricFunction()?Se:(0,u.getTheme)().visualization.getColorByName("semi-dark-red")}]}),r().createElement("div",{className:s.controls},(null==i?void 0:i.length)&&r().createElement("div",{className:s.controlsLeft},r().createElement(ht,{options:tr(i),radioAttributes:d.jx,value:n.getValueText(),onChange:e.onChange,showAll:!0,model:e})),t instanceof St&&r().createElement("div",{className:s.controlsRight},r().createElement(t.Selector,{model:t}))),r().createElement("div",{className:s.content},t&&r().createElement(t.Component,{model:t})))}));const un=e=>e.reduce(((e,t)=>{const n=t.fields.find((e=>"number"===e.type)),a=Object.keys((null==n?void 0:n.labels)||{}).find((e=>!e.startsWith("__")));return a&&(e[a]=[...e[a]||[],t]),e}),{}),dn=(e,t)=>{const n={name:e,refId:e,fields:[],length:0},a={name:"Value",type:s.FieldType.string,values:[],config:{},labels:{[e]:e}},r={name:"Baseline",type:s.FieldType.number,values:[],config:{}},i={name:"Selection",type:s.FieldType.number,values:[],config:{}},o=t.reduce(((t,n)=>{var a;const r=n.fields.find((e=>"number"===e.type)),i=null==r||null===(a=r.labels)||void 0===a?void 0:a[e];return i&&(t[i]=[...t[i]||[],r]),t}),{}),l=mn(t,"baseline",o),c=mn(t,"selection",o);return n.length=Object.keys(o).length,Object.entries(o).forEach((([e,t])=>{var n,s;a.values.push(e),r.values.push((null===(n=t.find((e=>{var t;return'"baseline"'===(null===(t=e.labels)||void 0===t?void 0:t.__meta_type)})))||void 0===n?void 0:n.values[0])/l),i.values.push((null===(s=t.find((e=>{var t;return'"selection"'===(null===(t=e.labels)||void 0===t?void 0:t.__meta_type)})))||void 0===s?void 0:s.values[0])/c)})),n.fields=[a,r,i],n};function mn(e,t,n){const a=Object.values(n).reduce(((e,n)=>{const a=n.find((e=>{var n;return(null===(n=e.labels)||void 0===n?void 0:n.__meta_type)===`"${t}"`}));return e+((null==a?void 0:a.values[0])||0)}),0);let r=e.reduce(((e,n)=>{var a;const r=n.fields.find((e=>"number"===e.type));return(null==r||null===(a=r.labels)||void 0===a?void 0:a.__meta_type)===`"${t}_total"`?r.values[0]:e}),1);return r<a||1===r||0===r?0===a?1:a:r}function pn(e){return{container:(0,l.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,l.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,l.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,l.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),controlsLeft:(0,l.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"column"})}}function fn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class vn extends o.Bs{_onActivate(){const e=ir(this).getValue(),t=Xa(this);if(!t.state.selection){const n=pe(e);n&&t.setState({selection:n})}this.updateBody()}updateBody(){this.setState({body:new ln({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){fn(e,t,n[t])}))}return e}({},e)),fn(this,"_variableDependency",new o.Sh(this,{variableNames:[d.PU]})),this.addActivationHandler(this._onActivate.bind(this))}}fn(vn,"Component",(({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})}));const gn=[{displayName:function(e){return"Breakdown"},value:"breakdown",getScene:function(){return new o.vA({body:new Zt({})})}},{displayName:bn,value:"structure",getScene:function(e){return new o.vA({body:new mt({metric:e})})}},{displayName:function(e){return"Comparison"},value:"comparison",getScene:function(){return new o.vA({body:new vn({})})}},{displayName:function(e){return"errors"===e?"Errored traces":"duration"===e?"Slow traces":"Traces"},value:"traceList",getScene:function(){return new o.vA({body:new tt({})})}}];class hn extends o.Bs{}function bn(e){switch(e){case"rate":return"Service structure";case"errors":return"Root cause errors";case"duration":return"Root cause latency"}}function yn(e){return{actions:(0,l.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:5,zIndex:2}})}}function wn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(hn,"Component",(({model:e})=>{var t,n,a;const i=Xa(e),s=(0,u.useStyles2)(yn),l=Ja(e),{actionView:c}=i.useState(),{value:d}=l.getMetricVariable().useState(),m=null===(a=o.jh.getData(e).useState().data)||void 0===a||null===(n=a.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length;return r().createElement(u.Box,null,r().createElement("div",{className:s.actions},r().createElement(u.Stack,{gap:1},r().createElement(ze,{exploration:l}))),r().createElement(u.TabsBar,null,gn.map(((e,t)=>r().createElement(u.Tab,{key:t,label:e.displayName(d),active:c===e.value,onChangeTab:()=>i.setActionView(e.value),counter:"traceList"===e.value?m:void 0})))))}));class Sn extends o.Bs{_onActivate(){this.setState({$data:new o.Es({$data:new J({maxDataPoints:"duration"===this.state.metric?24:64,datasource:d.Vl,queries:["duration"===this.state.metric?Ee():q(this.state.metric)]}),transformations:"duration"===this.state.metric?[...Ne()]:[...De(vr(this))]}),panel:this.getVizPanel(this.state.metric)})}getVizPanel(e){return new o.G1({direction:"row",children:[new o.vA({body:"duration"===e?this.getDurationVizPanel():this.getRateOrErrorPanel(e)})]})}getRateOrErrorPanel(e){const t=F().setHoverHeader(!0).setDisplayMode("transparent");return"rate"===e?t.setCustomFieldConfig("axisLabel","span/s"):"errors"===e&&t.setTitle("Errors rate").setCustomFieldConfig("axisLabel","error/s").setColor({fixedColor:"semi-dark-red",mode:"fixed"}),t.build()}getDurationVizPanel(){return re().setTitle("Histogram by duration").setHoverHeader(!0).setDisplayMode("transparent").build()}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){wn(e,t,n[t])}))}return e}({isStreaming:!1},e)),this.addActivationHandler((()=>{this._onActivate();const e=o.jh.getData(this);this._subs.add(e.subscribeToState((e=>{var t,n,a;this.setState({isStreaming:(null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Streaming}),(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done?0===e.data.series.length||0===e.data.series[0].length||dr(e)?this.setState({panel:new o.G1({children:[new o.vA({body:new y({imgWidth:110})})]})}):this.setState({panel:this.getVizPanel(this.state.metric)}):(null===(a=e.data)||void 0===a?void 0:a.state)===s.LoadingState.Loading&&this.setState({panel:new o.G1({direction:"column",maxHeight:Dn,height:Dn,children:[new O({component:()=>B(1)})]})})})))}))}}function xn(e){return{container:(0,l.css)({flex:1,width:"100%",display:"flex",flexDirection:"column",border:`1px solid ${e.colors.border.weak}`,borderRadius:"2px",background:e.colors.background.primary,paddingTop:"8px","section, section:hover":{borderColor:"transparent"},"& .show-on-hover":{display:"none"}}),headerWrapper:(0,l.css)({display:"flex",alignItems:"center",position:"absolute",top:"4px",left:"8px",zIndex:2}),clickable:(0,l.css)({cursor:"pointer",maxHeight:Dn,'[class*="loading-state-scene"]':{height:Dn,overflow:"hidden"},":hover":{background:e.colors.background.secondary,input:{backgroundColor:"#ffffff",border:"5px solid #3D71D9",cursor:"pointer"}}}),radioButton:(0,l.css)({display:"block"}),indicatorWrapper:(0,l.css)({position:"absolute",top:"4px",right:"8px",zIndex:2})}}function jn(e,t,n,a,r,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function On(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function En(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){On(e,t,n[t])}))}return e}wn(Sn,"Component",(({model:e})=>{const{panel:t,isStreaming:n}=e.useState(),a=(0,u.useStyles2)(xn),i=Ja(e),s=()=>{te(ne.common,ae.common.metric_changed,{metric:e.state.metric,location:"panel"}),i.onChangeMetricFunction(e.state.metric)};if(t)return r().createElement("div",{className:(0,l.css)([a.container,a.clickable]),onClick:s},r().createElement("div",{className:a.headerWrapper},r().createElement(u.RadioButtonList,{className:a.radioButton,name:`metric-${e.state.metric}`,options:[{title:"",value:"selected"}],onChange:()=>s(),value:"not-selected"})),n&&r().createElement("div",{className:a.indicatorWrapper},r().createElement(X,{isStreaming:!0,iconSize:10})),r().createElement(t.Component,{model:t}))}));class kn extends o.Bs{_onActivate(){const e=new URLSearchParams(window.location.search).get("actionView");e&&gn.find((t=>t.value===e))&&this.setState({actionView:e}),this.updateBody();const t=Ja(this).getMetricVariable();this._subs.add(t.subscribeToState(((e,t)=>{if(e.value!==t.value){const t=pe(e.value);t&&this.setState({selection:t}),this.updateQueryRunner(e.value),this.updateBody()}}))),this._subs.add(this.subscribeToState(((e,n)=>{var a,r;const i=o.jh.getTimeRange(this),s=null===(r=e.selection)||void 0===r||null===(a=r.timeRange)||void 0===a?void 0:a.from;s&&s<i.state.value.from.unix()&&this.setState({selection:void 0}),(0,p.isEqual)(e.selection,n.selection)||(ar(this).changeValueTo(d.y2),this.updateQueryRunner(t.getValue()))}))),this._subs.add(cr(this).subscribeToState((()=>{this.updateAttributes()}))),this._subs.add(rr(this).subscribeToState((()=>{this.updateQueryRunner(t.getValue())}))),this.updateQueryRunner(t.getValue()),this.updateAttributes()}updateBody(){const e=Ja(this).getMetricVariable().getValue(),t=gn.find((e=>e.value===this.state.actionView));this.setState({body:In(e,t?[null==t?void 0:t.getScene(e)]:void 0)}),void 0===this.state.actionView&&this.setActionView("breakdown")}updateAttributes(){var e,t=this;return(e=function*(){var e;const n=yield(0,c.getDataSourceSrv)().get(d.gR,{__sceneObject:{value:t}});n&&(null===(e=n.getTagKeys)||void 0===e||e.call(n).then((e=>{let n=[];n="data"in e?e.data:e;const a=n.map((e=>e.text));a!==t.state.attributes&&t.setState({attributes:a})})))},function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){jn(i,a,r,s,o,"next",e)}function o(e){jn(i,a,r,s,o,"throw",e)}s(void 0)}))})()}getUrlState(){return{actionView:this.state.actionView,selection:this.state.selection?JSON.stringify(this.state.selection):void 0}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=gn.find((t=>t.value===e.actionView));t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView("breakdown");if("string"==typeof e.selection){const t=JSON.parse(e.selection);(0,p.isEqual)(t,this.state.selection)||this.setState({selection:t})}}onUserUpdateSelection(e){this._urlSync.performBrowserHistoryAction((()=>{this.setState({selection:e})}))}setActionView(e){const{body:t}=this.state,n=gn.find((t=>t.value===e)),a=Ja(this).getMetricVariable().getValue();t.state.children.length>1&&n&&(t.setState({children:[...t.state.children.slice(0,2),n.getScene(a)]}),te(ne.analyse_traces,ae.analyse_traces.action_view_changed,{oldAction:this.state.actionView,newAction:e}),this.setState({actionView:n.value}))}updateQueryRunner(e){var t;const n=this.state.selection;var a;const r=null!==(a=null===(t=rr(this).getValue())||void 0===t?void 0:t.toString())&&void 0!==a?a:"";this.setState({$data:new o.Es({$data:new o.dt({datasource:d.Vl,queries:[Nn(e,r,n)],$timeRange:Tn(n)}),transformations:[...d.s9,...An]})})}constructor(e){var t;super(En({body:null!==(t=e.body)&&void 0!==t?t:new o.G1({children:[]})},e)),On(this,"_urlSync",new o.So(this,{keys:["actionView","selection"]})),this.addActivationHandler(this._onActivate.bind(this))}}On(kn,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,u.useStyles2)(Pn);return r().createElement(r().Fragment,null,r().createElement("div",{className:n.title},r().createElement(u.Tooltip,{content:r().createElement(Cn,null),placement:"right-start",interactive:!0},r().createElement("span",{className:n.hand},"Select metric type ",r().createElement(u.Icon,{name:"info-circle"})))),r().createElement(t.Component,{model:t}))}));const Cn=()=>{const e=(0,u.useStyles2)(Pn);return r().createElement(u.Stack,{direction:"column",gap:1},r().createElement("div",{className:e.tooltip.title},"RED metrics for traces"),r().createElement("span",{className:e.tooltip.subtitle},"Explore rate, errors, and duration (RED) metrics generated from traces by Tempo."),r().createElement("div",{className:e.tooltip.text},r().createElement("div",null,r().createElement("span",{className:e.tooltip.emphasize},"Rate")," - Spans per second that match your filter, useful to find unusual spikes in activity"),r().createElement("div",null,r().createElement("span",{className:e.tooltip.emphasize},"Errors")," -Spans that are failing, overall issues in tracing ecosystem"),r().createElement("div",null,r().createElement("span",{className:e.tooltip.emphasize},"Duration")," - Amount of time those spans take, represented as a heat map (responds time, latency)")),r().createElement("div",{className:e.tooltip.button},r().createElement(u.LinkButton,{icon:"external-link-alt",fill:"solid",size:"sm",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces/concepts/#rate-error-and-duration-metrics",onClick:()=>te(ne.common,ae.common.metric_docs_link_clicked)},"Read documentation")))};function Pn(e){return{title:(0,l.css)({label:"title",display:"flex",gap:e.spacing.x0_5,fontSize:e.typography.bodySmall.fontSize,paddingBottom:e.spacing.x0_5,alignItems:"center"}),hand:(0,l.css)({label:"hand",cursor:"pointer"}),tooltip:{label:"tooltip",title:(0,l.css)({fontSize:"14px",fontWeight:500}),subtitle:(0,l.css)({marginBottom:e.spacing.x1}),text:(0,l.css)({label:"text",color:e.colors.text.secondary,div:{marginBottom:e.spacing.x0_5}}),emphasize:(0,l.css)({label:"emphasize",color:e.colors.text.primary}),button:(0,l.css)({marginBottom:e.spacing.x0_5})}}}const _n=240,Dn=(_n-8)/2;function Nn(e,t,n){const a=""!==t?` | select(${t})`:"";let r="";switch(e){case"errors":r=" && status = error";break;case"duration":if(n){var i,s;const e=[];(null===(i=n.duration)||void 0===i?void 0:i.from.length)&&e.push(`duration >= ${n.duration.from}`),(null===(s=n.duration)||void 0===s?void 0:s.to.length)&&e.push(`duration <= ${n.duration.to}`),e.length&&(r+="&& "+e.join(" && "))}r.length||(r=`&& duration > ${d.xT}`)}return{refId:"A",query:`{${d.ui}${r}}${a}`,queryType:"traceql",tableType:"spans",limit:200,spss:10,filters:[]}}function Tn(e){var t,n;const a=1e3*((null==e||null===(t=e.timeRange)||void 0===t?void 0:t.from)||0),r=1e3*((null==e||null===(n=e.timeRange)||void 0===n?void 0:n.to)||0);return a&&r?new o.JZ({from:a.toFixed(0),to:r.toFixed(0),value:{from:(0,s.dateTime)(a),to:(0,s.dateTime)(r),raw:{from:(0,s.dateTime)(a),to:(0,s.dateTime)(r)}}}):void 0}function In(e,t){const n=new Sn("rate"===e?{metric:"errors"}:{metric:"rate"}),a=new Sn("duration"===e?{metric:"errors"}:{metric:"duration"});return new o.G1({direction:"column",$behaviors:[new o.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],children:[new o.G1({direction:"row",ySizing:"content",children:[new o.vA({minHeight:_n,maxHeight:_n,width:"60%",body:new $e({})}),new o.G1({direction:"column",minHeight:_n,maxHeight:_n,children:[new o.vA({minHeight:Dn,maxHeight:Dn,height:Dn,body:n}),new o.vA({minHeight:Dn,maxHeight:Dn,height:Dn,ySizing:"fill",body:a})]})]}),new o.vA({ySizing:"content",body:new hn({})}),...t||[]]})}const An=[()=>e=>e.pipe((0,_e.map)((e=>e.map((e=>function(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}(En({},e),{fields:e.fields.filter((e=>!e.name.startsWith("nestedSet")))})))))),{id:"sortBy",options:{fields:{},sort:[{field:"Duration",desc:!0}]}},{id:"organize",options:{indexByName:{"Start time":0,status:1,"Trace Service":2,"Trace Name":3,Duration:4,"Span ID":5,"span.http.method":6,"span.http.request.method":7,"span.http.path":8,"span.http.route":9,"span.http.status_code":10,"span.http.response.status_code":11}}}];function $n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ln extends o.Bs{getVizPanel(){const e=o.d0.traces().setHoverHeader(!0);return this.state.spanId&&e.setOption("focusedSpanId",this.state.spanId),e}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){$n(e,t,n[t])}))}return e}({$data:new o.dt({datasource:d.Vl,queries:[{refId:"A",query:e.traceId,queryType:"traceql"}]})},e)),this.addActivationHandler((()=>{const e=o.jh.getData(this);this._subs.add(e.subscribeToState((e=>{var t,n;(null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Done?this.setState({panel:this.getVizPanel().build()}):(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Loading&&this.setState({panel:new O({component:Vn})})})))}))}}$n(Ln,"Component",(({model:e})=>{const{panel:t}=e.useState(),n=(0,u.useStyles2)(Bn);if(t)return r().createElement("div",{className:n.panelContainer},r().createElement(t.Component,{model:t}))}));const Vn=()=>{const e=(0,u.useStyles2)(Bn);return r().createElement("div",{className:e.container},r().createElement("div",{className:e.header},r().createElement(j.A,{count:1,width:60}),r().createElement(j.A,{count:1,width:60})),r().createElement(j.A,{count:2,width:"80%"}),r().createElement("div",{className:e.map},r().createElement(j.A,{count:1}),r().createElement(j.A,{count:1,height:70})),r().createElement("div",{className:e.span},r().createElement("span",{className:e.service1},r().createElement(j.A,{count:1})),r().createElement("span",{className:e.bar1},r().createElement(j.A,{count:1}))),r().createElement("div",{className:e.span},r().createElement("span",{className:e.service2},r().createElement(j.A,{count:1})),r().createElement("span",{className:e.bar2},r().createElement(j.A,{count:1}))),r().createElement("div",{className:e.span},r().createElement("span",{className:e.service3},r().createElement(j.A,{count:1})),r().createElement("span",{className:e.bar3},r().createElement(j.A,{count:1}))),r().createElement("div",{className:e.span},r().createElement("span",{className:e.service4},r().createElement(j.A,{count:1})),r().createElement("span",{className:e.bar4},r().createElement(j.A,{count:1}))),r().createElement("div",{className:e.span},r().createElement("span",{className:e.service5},r().createElement(j.A,{count:1})),r().createElement("span",{className:e.bar5},r().createElement(j.A,{count:1}))),r().createElement("div",{className:e.span},r().createElement("span",{className:e.service6},r().createElement(j.A,{count:1})),r().createElement("span",{className:e.bar6},r().createElement(j.A,{count:1}))))};function Bn(e){return{panelContainer:(0,l.css)({display:"flex",height:"100%","& .show-on-hover":{display:"none"}}),container:(0,l.css)({height:"calc(100% - 32px)",width:"calc(100% - 32px)",position:"absolute",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:"5px"}),header:(0,l.css)({marginBottom:"20px",display:"flex",justifyContent:"space-between"}),map:(0,l.css)({marginTop:"20px",marginBottom:"20px"}),span:(0,l.css)({display:"flex"}),service1:(0,l.css)({width:"25%"}),bar1:(0,l.css)({marginLeft:"5%",width:"70%"}),service2:(0,l.css)({width:"25%"}),bar2:(0,l.css)({marginLeft:"10%",width:"15%"}),service3:(0,l.css)({width:"20%",marginLeft:"5%"}),bar3:(0,l.css)({marginLeft:"10%",width:"65%"}),service4:(0,l.css)({width:"20%",marginLeft:"5%"}),bar4:(0,l.css)({marginLeft:"15%",width:"60%"}),service5:(0,l.css)({width:"15%",marginLeft:"10%"}),bar5:(0,l.css)({marginLeft:"20%",width:"35%"}),service6:(0,l.css)({width:"15%",marginLeft:"10%"}),bar6:(0,l.css)({marginLeft:"30%",width:"15%"})}}function Rn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class zn extends o.Bs{_onActivate(){this.updateBody(),Ja(this).subscribeToState(((e,t)=>{e.traceId===t.traceId&&e.spanId===t.spanId||(this.updateBody(),te(ne.analyse_traces,ae.analyse_traces.open_trace,{traceId:e.traceId,spanId:e.spanId}))}))}updateBody(){const e=Ja(this);e.state.traceId?this.setState({body:new Ln({traceId:e.state.traceId,spanId:e.state.spanId})}):this.setState({body:new y({message:"No trace selected"})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Rn(e,t,n[t])}))}return e}({},e)),this.addActivationHandler(this._onActivate.bind(this))}}Rn(zn,"Component",(({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})}));var Fn=n(5435),Mn=n(3518);class qn extends o.yP{}function Hn(e){const t=e.filter((e=>e.key&&e.operator&&e.value)).map((e=>function(e){let t=e.value;return(["span.messaging.destination.partition.id","span.network.protocol.version"].includes(e.key)||!function(e){return null!=e&&""!==e&&!isNaN(Number(e.toString().trim()))}(t)&&!["status","kind","span:status","span:kind","duration","span:duration","trace:duration","event:timeSinceStart"].includes(e.key))&&("string"!=typeof t||t.startsWith('"')||t.endsWith('"')||(t=`"${t}"`)),`${e.key}${e.operator}${t}`}(e))).join("&&");return t.length?t:"true"}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(qn,"Component",(({model:e})=>{const{value:t}=e.useState();return(0,Mn.A)((()=>{t||e.changeValueTo(oe[0].value)})),r().createElement(u.RadioButtonGroup,{options:oe,value:t,onChange:t=>e.changeValueTo(t,void 0,!0)})}));class Wn extends o.Bs{_onActivate(){this.runIssueDetectionQuery();const e=cr(this);this._subs.add(e.subscribeToState(((e,t)=>{e.value!==t.value&&(this.resetIssues(),this.runIssueDetectionQuery())})))}runIssueDetectionQuery(){const e=cr(this),t=(0,s.dateTime)(),n=(0,s.dateTime)(t).subtract(1,"minute"),a=new o.JZ({from:n.toISOString(),to:t.toISOString()}),r=new o.dt({maxDataPoints:1,datasource:{uid:String(e.state.value)},$timeRange:a,queries:[{refId:"issueDetectorQuery",query:"{} | rate()",queryType:"traceql",tableType:"spans",limit:1,spss:1,filters:[]}]});this._subs.add(r.subscribeToState((e=>{var t,n,a,r;(null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Error&&(((null===(r=e.data)||void 0===r||null===(a=r.errors)||void 0===a||null===(n=a[0])||void 0===n?void 0:n.message)||"").includes("localblocks processor not found")&&this.setState({hasIssue:!0}))}))),r.activate()}resetIssues(){this.setState({hasIssue:!1})}constructor(){super({hasIssue:!1}),this.addActivationHandler(this._onActivate.bind(this))}}const Un=({detector:e})=>{const{hasIssue:t}=e.useState();return t?r().createElement(u.Alert,{severity:"warning",title:"TraceQL metrics not configured"},r().createElement("p",null,'We found an error running a TraceQL metrics query: "localblocks processor not found". This typically means the "local-blocks" processor is not configured in Tempo, which is required for Grafana Traces Drilldown to work.',r().createElement(u.LinkButton,{icon:"external-link-alt",fill:"text",size:"sm",target:"_blank",href:"https://grafana.com/docs/tempo/latest/operations/traceql-metrics"},"Read documentation"))):null};function Gn(e,t,n,a,r,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function Kn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Kn(e,t,n[t])}))}return e}function Zn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Jn=`${"2025-04-24T13:40:36.954Z".split("T")[0]} (93a398a)`;class Xn extends o.Bs{_onActivate(){this.state.topScene||this.setState({topScene:new kn({})}),this._subs.add(this.subscribeToEvent(d.vR,(e=>{this.setupInvestigationButton(e.payload.traceId),this.setState({traceId:e.payload.traceId,spanId:e.payload.spanId})}))),this.state.traceId&&this.setupInvestigationButton(this.state.traceId),o.jh.lookupVariable(d.EY,this).subscribeToState((e=>{e.value&&localStorage.setItem(d.cd,e.value.toString())})),this.state.issueDetector&&(this.state.issueDetector.isActive||this.state.issueDetector.activate())}getUrlState(){return{traceId:this.state.traceId,spanId:this.state.spanId}}updateFromUrl(e){const t={};(e.traceId||e.spanId)&&(t.traceId=e.traceId?e.traceId:void 0,t.spanId=e.spanId?e.spanId:void 0),this.setState(t)}getMetricVariable(){const e=o.jh.lookupVariable(d.PU,this);if(!(e instanceof o.yP))throw new Error("Metric variable not found");return e.getValue()||e.changeValueTo("rate"),e}getMetricFunction(){return this.getMetricVariable().getValue()}closeDrawer(){this.setState({traceId:void 0,spanId:void 0})}setupInvestigationButton(e){const t=er(Ja(this)),n=new o.dt({datasource:{uid:t},queries:[{refId:"A",query:e,queryType:"traceql"}]}),a=new kt({query:e,type:"trace",dsUid:t,$data:n});a.activate(),this.setState({addToInvestigationButton:a}),this._subs.add(a.subscribeToState((()=>{this.updateInvestigationLink()}))),n.activate(),this._subs.add(n.subscribeToState((e=>{var t,n,r;if((null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Done&&(null===(r=e.data)||void 0===r||null===(n=r.series)||void 0===n?void 0:n.length)>0){var i,o;const t=null===(o=e.data.series[0])||void 0===o||null===(i=o.fields)||void 0===i?void 0:i.find((e=>"serviceName"===e.name));t&&t.values[0]&&a.setState(Zn(Qn({},a.state),{labelValue:`${t.values[0]}`}))}}))),a.setState(Zn(Qn({},a.state),{labelValue:e}))}updateInvestigationLink(){var e,t=this;return(e=function*(){const{addToInvestigationButton:e}=t.state;if(!e)return;const n=yield Bt(e);n&&t.setState({investigationLink:n})},function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){Gn(i,a,r,s,o,"next",e)}function o(e){Gn(i,a,r,s,o,"throw",e)}s(void 0)}))})()}constructor(e){var t,n,a,r,i;super(Qn({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new o.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:(r=e.initialDS,i=e.initialFilters,new o.Pj({variables:[new o.mI({name:d.EY,label:"Data source",value:r,pluginId:"tempo"}),new qn({name:d.CE,value:oe[0].value}),new o.H9({addFilterButtonText:"Add filter",hide:Fn.zL.hideLabel,name:d.Ao,datasource:d.Vl,layout:"combobox",filters:null!=i?i:[],allowCustomValue:!0,expressionBuilder:Hn}),new o.yP({name:d.PU,hide:Fn.zL.hideVariable}),new o.yP({name:d.z,defaultToAll:!1}),new o.yP({name:d.gP,defaultToAll:!1}),new o.yP({name:d.pf,defaultToAll:!1,hide:Fn.zL.hideVariable}),new o.yP({name:d.xc,defaultToAll:!1,hide:Fn.zL.hideVariable})]})),controls:null!==(a=e.controls)&&void 0!==a?a:[new o.KE({}),new o.WM({})],body:new Yn({}),drawerScene:new zn({}),issueDetector:new Wn},e)),Kn(this,"_urlSync",new o.So(this,{keys:["primarySignal","traceId","spanId","metric"]})),Kn(this,"onChangeMetricFunction",(e=>{const t=this.getMetricVariable();e&&t.getValue()!==e&&t.changeValueTo(e,void 0,!0)})),this.addActivationHandler(this._onActivate.bind(this))}}Kn(Xn,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,u.useStyles2)(ea);return r().createElement("div",{className:n.bodyContainer}," ",t&&r().createElement(t.Component,{model:t})," ")}));class Yn extends o.Bs{}function ea(e){return{bodyContainer:(0,l.css)({label:"bodyContainer",flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),container:(0,l.css)({label:"container",flexGrow:1,display:"flex",gap:e.spacing(1),minHeight:"100%",flexDirection:"column",padding:`0 ${e.spacing(2)} ${e.spacing(2)} ${e.spacing(2)}`,overflow:"auto",maxHeight:"100%"}),drawerHeader:(0,l.css)({display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(2),marginBottom:e.spacing(2),h3:{margin:0}}),drawerHeaderButtons:(0,l.css)({display:"flex",justifyContent:"flex-end",gap:e.spacing(1.5)}),body:(0,l.css)({label:"body",flexGrow:1,display:"flex",flexDirection:"column",gap:e.spacing(1)}),headerContainer:(0,l.css)({label:"headerContainer",backgroundColor:e.colors.background.canvas,display:"flex",flexDirection:"column",position:"sticky",top:0,zIndex:3,padding:`${e.spacing(1.5)} 0`,gap:e.spacing(1)}),datasourceLabel:(0,l.css)({label:"datasourceLabel",fontSize:"12px",padding:`0 ${e.spacing(1)}`,height:"32px",display:"flex",alignItems:"center",justifyContent:"flex-start",fontWeight:e.typography.fontWeightMedium,position:"relative",right:-1,width:"90px"}),controls:(0,l.css)({label:"controls",display:"flex",gap:e.spacing(1),zIndex:3,flexWrap:"wrap"}),menu:(0,l.css)({label:"menu","svg, span":{color:e.colors.text.link}}),menuHeader:l.css`
      padding: ${e.spacing(.5,1)};
      white-space: nowrap;
    `,menuHeaderSubtitle:l.css`
      color: ${e.colors.text.secondary};
      font-size: ${e.typography.bodySmall.fontSize};
    `,tooltip:(0,l.css)({label:"tooltip",fontSize:"14px",lineHeight:"22px",width:"180px",textAlign:"center"}),helpIcon:(0,l.css)({label:"helpIcon",marginLeft:e.spacing(1)}),filters:(0,l.css)({label:"filters",marginTop:e.spacing(1),display:"flex",gap:e.spacing(1)})}}Kn(Yn,"Component",(({model:e})=>{const t=Ja(e),{controls:n,topScene:a,drawerScene:i,traceId:s,issueDetector:l,investigationLink:m,addToInvestigationButton:p}=t.useState(),{hasIssue:f}=(null==l?void 0:l.useState())||{hasIssue:!1},v=(0,u.useStyles2)(ea),[g,h]=r().useState(!1),b=o.jh.lookupVariable(d.EY,t),y=sr(t),w=or(t);function S(){const e=(0,u.useStyles2)(ea);return r().createElement("div",{className:e.menuHeader},r().createElement("h5",null,"Grafana Traces Drilldown v","1.0.0"),r().createElement("div",{className:e.menuHeaderSubtitle},"Last update: ",Jn))}const x=r().createElement(u.Menu,{header:r().createElement(S,null)},r().createElement("div",{className:v.menu},c.config.feedbackLinksEnabled&&r().createElement(u.Menu.Item,{label:"Give feedback",ariaLabel:"Give feedback",icon:"comment-alt-message",url:"https://grafana.qualtrics.com/jfe/form/SV_9LUZ21zl3x4vUcS",target:"_blank",onClick:()=>te(ne.common,ae.common.global_docs_link_clicked)}),r().createElement(u.Menu.Item,{label:"Documentation",ariaLabel:"Documentation",icon:"external-link-alt",url:"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/",target:"_blank",onClick:()=>te(ne.common,ae.common.feedback_link_clicked)})));return r().createElement(r().Fragment,null,r().createElement("div",{className:v.container},r().createElement("div",{className:v.headerContainer},f&&l&&r().createElement(Un,{detector:l}),r().createElement(u.Stack,{gap:1,justifyContent:"space-between",wrap:"wrap"},r().createElement(u.Stack,{gap:1,alignItems:"center",wrap:"wrap"},b&&r().createElement(u.Stack,{gap:0,alignItems:"center"},r().createElement("div",{className:v.datasourceLabel},"Data source"),r().createElement(b.Component,{model:b}))),r().createElement("div",{className:v.controls},r().createElement(u.Dropdown,{overlay:x,onVisibleChange:()=>h(!g)},r().createElement(u.Button,{variant:"secondary",icon:"info-circle"},"Need help",r().createElement(u.Icon,{className:v.helpIcon,name:g?"angle-up":"angle-down",size:"lg"}))),n.map((e=>r().createElement(e.Component,{key:e.state.key,model:e}))))),r().createElement(u.Stack,{gap:1,alignItems:"center",wrap:"wrap"},r().createElement(u.Stack,{gap:0,alignItems:"center"},r().createElement("div",{className:v.datasourceLabel},"Filters"),w&&r().createElement(w.Component,{model:w})),y&&r().createElement("div",null,r().createElement(y.Component,{model:y})))),r().createElement("div",{className:v.body},a&&r().createElement(a.Component,{model:a}))),i&&s&&r().createElement(u.Drawer,{size:"lg",onClose:()=>t.closeDrawer()},r().createElement("div",{className:v.drawerHeader},r().createElement("h3",null,"View trace ",s),r().createElement("div",{className:v.drawerHeaderButtons},p&&m&&r().createElement(u.Button,{variant:"secondary",size:"sm",icon:"plus-square",onClick:e=>{(null==m?void 0:m.onClick)&&m.onClick(e),te(ne.analyse_traces,ae.analyse_traces.add_to_investigation_trace_view_clicked),setTimeout((()=>t.closeDrawer()),100)}},Nt),r().createElement(u.IconButton,{name:"times",onClick:()=>t.closeDrawer(),tooltip:"Close drawer",size:"lg"}))),r().createElement(i.Component,{model:i})))}));const ta=e=>{const{index:t,type:n,label:a,labelTitle:i,value:s,valueTitle:o,url:l}=e,d=(0,u.useStyles2)(na);return r().createElement("div",{key:t},0===t&&r().createElement("div",{className:d.rowHeader},r().createElement("span",null,i),r().createElement("span",{className:d.valueTitle},o)),r().createElement("div",{className:d.row,key:t,onClick:()=>{te(ne.home,ae.home.panel_row_clicked,{type:n,index:t,value:s}),c.locationService.push(l)}},r().createElement("div",{className:"rowLabel"},a),r().createElement("div",{className:d.action},r().createElement("span",{className:d.actionText},s),r().createElement(u.Icon,{className:d.actionIcon,name:"arrow-right",size:"xl"}))))};function na(e){return{rowHeader:(0,l.css)({color:e.colors.text.secondary,display:"flex",justifyContent:"space-between",alignItems:"center",padding:`0 ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`}),valueTitle:(0,l.css)({margin:"0 45px 0 0"}),row:(0,l.css)({display:"flex",justifyContent:"space-between",alignItems:"center",gap:e.spacing(2),padding:`${e.spacing(.75)} ${e.spacing(2)}`,"&:hover":{backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,cursor:"pointer",".rowLabel":{textDecoration:"underline"}}}),action:(0,l.css)({display:"flex",alignItems:"center"}),actionText:(0,l.css)({color:"#d5983c",padding:`0 ${e.spacing(1)}`,width:"max-content"}),actionIcon:(0,l.css)({cursor:"pointer",margin:`0 ${e.spacing(.5)} 0 ${e.spacing(1)}`})}}const aa=e=>{var t;const{series:n,type:a}=e,i=(0,u.useStyles2)(ra),o=e=>{var t;const n=e.fields.find((e=>"time"!==e.name));var a;return null!==(a=null==n||null===(t=n.labels)||void 0===t?void 0:t["resource.service.name"].replace(/"/g,""))&&void 0!==a?a:"Service name not found"},l=e=>{const t={"var-filters":`resource.service.name|=|${o(e)}`,"var-metric":"errors"};return s.urlUtil.renderUrl(d.D5,t)},c=e=>{var t;const n=e.fields.find((e=>"time"!==e.name));var a;return null!==(a=null==n||null===(t=n.values)||void 0===t?void 0:t.reduce(((e,t)=>"number"!=typeof e||isNaN(e)?t:e+t),0))&&void 0!==a?a:1};return r().createElement("div",{className:i.container},null===(t=n.sort(((e,t)=>c(t)-c(e))).slice(0,10))||void 0===t?void 0:t.map(((e,t)=>r().createElement("span",{key:t},r().createElement(ta,{type:a,index:t,label:o(e),labelTitle:"Service",value:c(e),valueTitle:"Total errors",url:l(e)})))))};function ra(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}function ia(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const sa=e=>{const{series:t,type:n}=e,a=(0,u.useStyles2)(oa),i=t[0].fields.find((e=>"duration"===e.name));if(i&&i.values){var o,l;const e=null==i||null===(o=i.values.map(((e,t)=>t)))||void 0===o?void 0:o.sort(((e,t)=>(null==i?void 0:i.values[t])-(null==i?void 0:i.values[e]))),c=t[0].fields.map((t=>{return n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){ia(e,t,n[t])}))}return e}({},t),a=null!=(a={values:null==e?void 0:e.map((e=>t.values[e]))})?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach((function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(a,e))})),n;var n,a})),u=(e,t,n)=>{let a="";return(null==e?void 0:e.values[n])&&(a=e.values[n]),(null==t?void 0:t.values[n])&&(a=0===a.length?t.values[n]:`${a}: ${t.values[n]}`),0===a.length?"Trace service & name not found":a},m=(e,t,n,a)=>{if(!(t&&t.values[a]&&n&&n.values[a]))return console.error("SpanId or traceService not found"),d.bw.Explore;const r={traceId:e,spanId:t.values[a],"var-filters":`resource.service.name|=|${n.values[a]}`,"var-metric":"duration"};return s.urlUtil.renderUrl(d.D5,r)},p=(e,t)=>e&&e.values?U(e.values[t]/1e3):"Duration not found",f=c.find((e=>"traceIdHidden"===e.name)),v=c.find((e=>"spanID"===e.name)),g=c.find((e=>"traceName"===e.name)),h=c.find((e=>"traceService"===e.name)),b=c.find((e=>"duration"===e.name));return r().createElement("div",{className:a.container},null==f||null===(l=f.values)||void 0===l?void 0:l.map(((e,t)=>r().createElement("span",{key:t},r().createElement(ta,{type:n,index:t,label:u(h,g,t),labelTitle:"Trace",value:p(b,t),valueTitle:"Duration",url:m(e,v,h,t)})))))}return null};function oa(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}const la=e=>{var t;const{series:n,type:a}=e,i=(0,u.useStyles2)(ca),o=e=>{var t;const n=e.fields.find((e=>"time"!==e.name));var a;return null!==(a=null==n||null===(t=n.labels)||void 0===t?void 0:t["resource.service.name"].replace(/"/g,""))&&void 0!==a?a:"Service name not found"},l=e=>{const t={"var-filters":`resource.service.name|=|${o(e)}`,"var-metric":"duration"};return s.urlUtil.renderUrl(d.D5,t)},c=e=>{var t;const n=e.fields.find((e=>"time"!==e.name));var a;return null!==(a=null==n||null===(t=n.values)||void 0===t?void 0:t.reduce(((e,t)=>"number"!=typeof e||isNaN(e)?t:e+t),0))&&void 0!==a?a:1};return r().createElement("div",{className:i.container},null===(t=n.sort(((e,t)=>c(t)-c(e))).slice(0,10))||void 0===t?void 0:t.map(((e,t)=>r().createElement("span",{key:t},r().createElement(ta,{type:a,index:t,label:o(e),labelTitle:"Service",value:U(1e6*c(e)),valueTitle:"p90",url:l(e)})))))};function ca(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}const ua=e=>{const{series:t,type:n,message:a}=e,i=(0,u.useStyles2)(da);if(a)return r().createElement("div",{className:i.container},r().createElement("div",{className:i.message},r().createElement(u.Icon,{className:i.icon,name:"exclamation-circle",size:"xl"}),a));if(t&&t.length>0)switch(n){case"slowest-traces":return r().createElement(sa,{series:t,type:n});case"errored-services":return r().createElement(aa,{series:t,type:n});case"slowest-services":return r().createElement(la,{series:t,type:n})}return r().createElement("div",{className:i.container},"No series data")};function da(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`}),icon:(0,l.css)({margin:`0 ${e.spacing(.5)} 0 ${e.spacing(1)}`}),message:(0,l.css)({display:"flex",gap:e.spacing(1.5),margin:`${e.spacing(2)} auto`,width:"60%"})}}class ma extends o.Bs{}function pa(e){switch(e){case"slowest-services":return"clock-nine";case"slowest-traces":return"crosshair";default:return"exclamation-triangle"}}function fa(e){return{container:(0,l.css)({border:`1px solid ${e.isDark?e.colors.border.medium:e.colors.border.weak}`,borderRadius:e.spacing(.5),marginBottom:e.spacing(4),width:"100%"}),title:(0,l.css)({color:e.isDark?e.colors.text.secondary:e.colors.text.primary,backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,borderTopLeftRadius:e.spacing(.5),borderTopRightRadius:e.spacing(.5),display:"flex",justifyContent:"center",alignItems:"center",fontSize:"1.3rem",padding:`${e.spacing(1.5)} ${e.spacing(2)}`}),titleText:(0,l.css)({marginLeft:e.spacing(1)})}}function va(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ga(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){va(e,t,n[t])}))}return e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(ma,"Component",(({model:e})=>{const{series:t,title:n,type:a,message:i}=e.useState(),s=(0,u.useStyles2)(fa);return r().createElement("div",{className:s.container},r().createElement("div",{className:s.title},r().createElement(u.Icon,{name:pa(a),size:"lg"}),r().createElement("span",{className:s.titleText},n)),r().createElement(ua,{series:t,type:a,message:i}))}));class ha extends o.Bs{constructor(e){var t,n;super(ga({$data:new o.dt({datasource:d.Vl,queries:[(t=ga({refId:"A",queryType:"traceql",tableType:"spans",limit:10},e.query),n={exemplars:0},n=null!=n?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)]})},e)),this.addActivationHandler((()=>{const t=o.jh.getData(this);this._subs.add(t.subscribeToState((t=>{var n,a,r,i,l;if((null===(n=t.data)||void 0===n?void 0:n.state)===s.LoadingState.Done||(null===(a=t.data)||void 0===a?void 0:a.state)===s.LoadingState.Streaming)if((null===(i=t.data)||void 0===i?void 0:i.state)!==s.LoadingState.Done||0!==t.data.series.length&&0!==t.data.series[0].length){if(t.data.series.length>0){var c;if("slowest-traces"!==e.type||e.renderDurationPanel)this.setState({panel:new o.G1({children:[new ma({series:t.data.series,title:e.title,type:e.type})]})});else if((null===(c=t.data)||void 0===c?void 0:c.state)===s.LoadingState.Done){var u,d;let n=Le(null!==(d=null===(u=t.data)||void 0===u?void 0:u.series)&&void 0!==d?d:[]);if(null==n?void 0:n.length){const{minDuration:t}=Ve(n);var m;this.setState({panel:new o.G1({children:[new ha({query:{query:`{nestedSetParent<0 && duration > ${t} ${null!==(m=e.filter)&&void 0!==m?m:""}}`},title:e.title,type:e.type,renderDurationPanel:!0})]})})}}}}else this.setState({panel:new o.G1({children:[new ma({message:(l=e.title.toLowerCase(),`No data for selected data source and filter. Select another to see ${l}.`),title:e.title,type:e.type})]})});else(null===(r=t.data)||void 0===r?void 0:r.state)===s.LoadingState.Error?this.setState({panel:new o.G1({children:[new ma({message:Ya(t),title:e.title,type:e.type})]})}):this.setState({panel:new o.G1({direction:"column",maxHeight:Dn,height:Dn,children:[new O({component:()=>ya()})]})})})))}))}}function ba(){return{container:(0,l.css)({minWidth:"350px",width:"-webkit-fill-available"})}}va(ha,"Component",(({model:e})=>{const{panel:t}=e.useState(),n=(0,u.useStyles2)(ba);if(t)return r().createElement("div",{className:n.container},r().createElement(t.Component,{model:t}))}));const ya=()=>{const e=(0,u.useStyles2)(wa);return r().createElement("div",{className:e.container},r().createElement("div",{className:e.title},r().createElement(j.A,{count:1,width:200})),r().createElement("div",{className:e.tracesContainer},[...Array(11)].map(((t,n)=>r().createElement("div",{className:e.row,key:n},r().createElement("div",{className:e.rowLeft},r().createElement(j.A,{count:1})),r().createElement("div",{className:e.rowRight},r().createElement(j.A,{count:1})))))))};function wa(e){return{container:(0,l.css)({border:`1px solid ${e.isDark?e.colors.border.medium:e.colors.border.weak}`,borderRadius:e.spacing(.5),marginBottom:e.spacing(4),width:"100%"}),title:(0,l.css)({color:e.colors.text.secondary,backgroundColor:e.colors.background.secondary,fontSize:"1.3rem",padding:`${e.spacing(1.5)} ${e.spacing(2)}`,textAlign:"center"}),tracesContainer:(0,l.css)({padding:`13px ${e.spacing(2)}`}),row:(0,l.css)({display:"flex",justifyContent:"space-between"}),rowLeft:(0,l.css)({margin:"7px 0",width:"150px"}),rowRight:(0,l.css)({width:"50px"})}}const Sa=()=>r().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"73",height:"72",viewBox:"0 0 73 72",fill:"none"},r().createElement("path",{d:"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z",fill:"#24292E",fillOpacity:"0.75"})),xa=()=>r().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"73",height:"72",viewBox:"0 0 73 72",fill:"none"},r().createElement("path",{d:"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z",fill:"#CCCCDC",fillOpacity:"0.65"}));var ja=n(1159);function Oa(e,t,n,a,r,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function Ea(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){Oa(i,a,r,s,o,"next",e)}function o(e){Oa(i,a,r,s,o,"throw",e)}s(void 0)}))}}function ka(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Ca=e=>(e.delete(d.$V),e.delete(`var-${d.pf}`),e.delete(`var-${d.xc}`),e),Pa=()=>{const e=(0,c.usePluginUserStorage)();return{getBookmarks:()=>Na(e),removeBookmark:t=>Aa(e,t),bookmarkExists:t=>$a(e,t),toggleBookmark:()=>Ta(e)}},_a=e=>{if(!e||!e.params)return d.D5;const t=new URLSearchParams(e.params),n=Object.fromEntries(t.entries()),a=t.getAll(`var-${d.Ao}`),r=s.urlUtil.renderUrl(d.D5,(i=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){ka(e,t,n[t])}))}return e}({},n),o=null!=(o={[`var-${d.Ao}`]:a})?o:{},Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(o)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(o)).forEach((function(e){Object.defineProperty(i,e,Object.getOwnPropertyDescriptor(o,e))})),i));var i,o;return r},Da=function(){var e=Ea((function*(e,t){try{yield e.setItem(d.Sr,JSON.stringify(t))}catch(e){console.error("Failed to save bookmarks to storage:",e)}}));return function(t,n){return e.apply(this,arguments)}}(),Na=function(){var e=Ea((function*(e){try{const t=yield e.getItem(d.Sr);return t?JSON.parse(t):[]}catch(e){return console.error("Failed to get bookmarks from storage:",e),[]}}));return function(t){return e.apply(this,arguments)}}(),Ta=function(){var e=Ea((function*(e){const t={params:Ca(new URLSearchParams(window.location.search)).toString()};return(yield $a(e,t))?(yield Aa(e,t),!1):(yield Ia(e,t),!0)}));return function(t){return e.apply(this,arguments)}}(),Ia=function(){var e=Ea((function*(e,t){const n=yield Na(e);n.push(t),yield Da(e,n)}));return function(t,n){return e.apply(this,arguments)}}(),Aa=function(){var e=Ea((function*(e,t){const n=(yield Na(e)).filter((e=>!La(t,e)));yield Da(e,n)}));return function(t,n){return e.apply(this,arguments)}}(),$a=function(){var e=Ea((function*(e,t){return(yield Na(e)).some((e=>La(t,e)))}));return function(t,n){return e.apply(this,arguments)}}(),La=(e,t)=>{const n=Ca(new URLSearchParams(e.params)),a=Ca(new URLSearchParams(t.params)),r=`var-${d.Ao}`,i=Array.from(n.keys()).filter((e=>e!==r)),s=Array.from(a.keys()).filter((e=>e!==r));if(i.length!==s.length)return!1;const o=i.every((e=>a.has(e)&&n.get(e)===a.get(e)));if(!o)return!1;const l=n.getAll(r),c=a.getAll(r);return l.length===c.length&&l.every((e=>c.includes(e)))},Va=({bookmark:e})=>{let{actionView:t,primarySignal:n,metric:a,filters:i}=(e=>{if(!e||!e.params)return{actionView:"",primarySignal:"",filters:"",metric:""};const t=new URLSearchParams(e.params);var n,a,r;return{actionView:null!==(n=t.get(d.V2))&&void 0!==n?n:"",primarySignal:null!==(a=t.get(d.W5))&&void 0!==a?a:"",filters:t.getAll(`var-${d.Ao}`).join(d.x5),metric:null!==(r=t.get(`var-${d.PU}`))&&void 0!==r?r:""}})(e);const s=(0,u.useStyles2)(Ba);return i=((e,t)=>{const n=(e=>{const t=(e=>oe.find((t=>t.value===e)))(e);if(!t||!t.filter)return"";const n=t.filter;return n.key&&n.operator&&void 0!==n.value?`${n.key}|${n.operator}|${n.value}`:""})(t);let a=e.split(d.x5);return a=a.filter((e=>e!==n)),a.join(d.x5)})(i,n),i=i.replace(/\|=\|/g," = "),i=i.replace(d.$d,"").replace(d.zd,"").replace(d.X0,""),r().createElement("div",{title:i},r().createElement("div",null,r().createElement("b",null,fr(a))," of ",r().createElement("b",null,n.replace("_"," "))," (",t,")"),r().createElement("div",{className:s.filters},i))};function Ba(){return{filters:(0,l.css)({textOverflow:"ellipsis",overflow:"hidden",WebkitLineClamp:2,display:"-webkit-box",WebkitBoxOrient:"vertical"})}}function Ra(e,t,n,a,r,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function za(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){Ra(i,a,r,s,o,"next",e)}function o(e){Ra(i,a,r,s,o,"throw",e)}s(void 0)}))}}const Fa=()=>{const e=(0,u.useStyles2)(Ma),{getBookmarks:t,removeBookmark:n}=Pa(),[i,s]=(0,a.useState)([]),[o,l]=(0,a.useState)(!0),[d,m]=(0,a.useState)(!1);(0,a.useEffect)((()=>{!function(){var e=za((function*(){l(!0);try{const e=yield t();s(e)}catch(e){console.error("Error loading bookmarks:",e),s([])}finally{l(!1)}}));return function(){return e.apply(this,arguments)}}()()}),[]);const p=function(){var e=za((function*(e,a){a.stopPropagation(),m(!0);try{yield n(e);const a=yield t();s(a)}catch(e){console.error("Error removing bookmark:",e)}finally{m(!1)}}));return function(t,n){return e.apply(this,arguments)}}();return o?r().createElement("div",null,r().createElement("div",{className:e.header},r().createElement("h4",null,"Or view bookmarks")),r().createElement("div",{className:e.loading},r().createElement(u.LoadingPlaceholder,{text:"Loading bookmarks..."}))):r().createElement("div",null,r().createElement("div",{className:e.header},r().createElement("h4",null,"Or view bookmarks")),0===i.length?r().createElement("p",{className:e.noBookmarks},"Bookmark your favorite queries to view them here."):r().createElement("div",{className:e.bookmarks},i.map(((t,n)=>r().createElement("div",{className:e.bookmark,key:n,onClick:()=>(e=>{te(ne.home,ae.home.go_to_bookmark_clicked);const t=_a(e);c.locationService.push(t)})(t)},r().createElement("div",{className:e.bookmarkItem},r().createElement(Va,{bookmark:t})),r().createElement("div",{className:e.remove},r().createElement(u.Button,{variant:"secondary",fill:"text",icon:"trash-alt",disabled:d,onClick:e=>p(t,e)})))))))};function Ma(e){return{header:(0,l.css)({textAlign:"center",h4:{margin:0}}),bookmarks:(0,l.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(2),margin:`${e.spacing(4)} 0 ${e.spacing(2)} 0`,justifyContent:"center"}),bookmark:(0,l.css)({display:"flex",flexDirection:"column",justifyContent:"space-between",cursor:"pointer",width:"318px",border:`1px solid ${e.colors.border.medium}`,borderRadius:e.shape.radius.default,"&:hover":{backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary}}),bookmarkItem:(0,l.css)({padding:`${e.spacing(1.5)} ${e.spacing(1.5)} 0 ${e.spacing(1.5)}`,overflow:"hidden"}),filters:(0,l.css)({textOverflow:"ellipsis",overflow:"hidden",WebkitLineClamp:2,display:"-webkit-box",WebkitBoxOrient:"vertical"}),remove:(0,l.css)({display:"flex",justifyContent:"flex-end"}),noBookmarks:(0,l.css)({margin:`${e.spacing(4)} 0 ${e.spacing(2)} 0`,textAlign:"center"}),loading:(0,l.css)({display:"flex",justifyContent:"center",margin:`${e.spacing(4)} 0`})}}class qa extends o.Bs{}function Ha(e){return{container:(0,l.css)({display:"flex",gap:e.spacing(7),flexDirection:"column",margin:`0 0 ${e.spacing(4)} 0`,justifyContent:"center"}),header:(0,l.css)({display:"flex",alignItems:"center",backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,borderRadius:e.spacing(.5),flexWrap:"wrap",justifyContent:"center",padding:e.spacing(3),gap:e.spacing(4)}),headerTitleContainer:(0,l.css)({display:"flex",alignItems:"center"}),title:(0,l.css)({margin:`0 0 0 ${e.spacing(2)}`}),headerActions:(0,l.css)({alignItems:"center",justifyContent:"flex-start",display:"flex",gap:e.spacing(2)}),documentationLink:(0,l.css)({textDecoration:"underline","&:hover":{textDecoration:"underline"}}),subHeader:(0,l.css)({textAlign:"center",h4:{margin:`0 0 -${e.spacing(2)} 0`}}),label:(0,l.css)({fontSize:"12px"}),variablesAndControls:(0,l.css)({alignItems:"center",gap:e.spacing(2),display:"flex",justifyContent:"space-between",width:"100%"}),variables:(0,l.css)({display:"flex",gap:e.spacing(2)}),controls:(0,l.css)({display:"flex",gap:e.spacing(1)})}}function Wa(e,t,n,a,r,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function Ua(e){return Ga.apply(this,arguments)}function Ga(){var e;return e=function*(e){const t=o.jh.interpolate(e,d.gR),n=yield(0,c.getDataSourceSrv)().get(t);if(!(n instanceof c.DataSourceWithBackend))throw console.error(new Error("getTagKeysProvider: invalid datasource!")),new Error("getTagKeysProvider: invalid datasource!");const a=n;if(a&&a.getTagKeys){const e=yield a.getTagKeys();return Array.isArray(e)?{replace:!0,values:(r=e,[...r.filter((e=>{var t;return null===(t=e.text)||void 0===t?void 0:t.includes(d.$d)})),...r.filter((e=>{var t;return null===(t=e.text)||void 0===t?void 0:t.includes(d.zd)})),...r.filter((e=>{var t,n,a,r;return!((null===(t=e.text)||void 0===t?void 0:t.includes(d.$d))||(null===(n=e.text)||void 0===n?void 0:n.includes(d.zd))||(null===(a=e.text)||void 0===a?void 0:a.includes(d.X0))||(null===(r=e.text)||void 0===r?void 0:r.includes(d.ZV))||-1!==d.uK.concat(d.ZM).indexOf(e.text))}))])}:(console.error(new Error("getTagKeysProvider: invalid tagKeys!")),{values:[]})}var r;return console.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{values:[]}},Ga=function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){Wa(i,a,r,s,o,"next",e)}function o(e){Wa(i,a,r,s,o,"throw",e)}s(void 0)}))},Ga.apply(this,arguments)}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(qa,"Component",(({model:e})=>{const t=function(e){return o.jh.getAncestor(e,Qa)}(e),n=(0,ja.useNavigate)(),{controls:a}=t.useState(),i=(0,u.useStyles2)(Ha),s=(0,u.useTheme2)(),l=cr(t),c=lr(t);return r().createElement("div",{className:i.container},r().createElement("div",{className:i.header},r().createElement("div",{className:i.headerTitleContainer},s.isDark?r().createElement(xa,null):r().createElement(Sa,null),r().createElement("h2",{className:i.title},"Start your traces exploration!")),r().createElement("div",null,r().createElement("p",null,"Drilldown and visualize your trace data without writing a query."),r().createElement("div",{className:i.headerActions},r().createElement(u.Button,{variant:"primary",onClick:()=>{te(ne.home,ae.home.explore_traces_clicked),n(d.D5)}},"Let’s start",r().createElement(u.Icon,{name:"arrow-right",size:"lg"})),r().createElement(u.LinkButton,{icon:"external-link-alt",fill:"text",size:"md",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces",className:i.documentationLink,onClick:()=>te(ne.home,ae.home.read_documentation_clicked)},"Read documentation")))),r().createElement(Fa,null),r().createElement("div",{className:i.subHeader},r().createElement("h4",null,"Or quick-start into your tracing data")),r().createElement(u.Stack,{gap:2},r().createElement("div",{className:i.variablesAndControls},r().createElement("div",{className:i.variables},l&&r().createElement(u.Stack,{gap:1,alignItems:"center"},r().createElement("div",{className:i.label},"Data source"),r().createElement(l.Component,{model:l})),c&&r().createElement(u.Stack,{gap:1,alignItems:"center"},r().createElement("div",{className:i.label},"Filter"),r().createElement(c.Component,{model:c}))),r().createElement("div",{className:i.controls},null==a?void 0:a.map((e=>r().createElement(e.Component,{key:e.state.key,model:e})))))))}));function Ka(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Qa extends o.Bs{_onActivate(){const e=o.jh.getTimeRange(this),t=lr(this);t.setState({getTagKeysProvider:Ua}),cr(this).subscribeToState((e=>{e.value&&localStorage.setItem(d.cd,e.value.toString())})),lr(this).subscribeToState(((t,n)=>{if(t.filters!==n.filters){this.buildPanels(e,t.filters),localStorage.setItem(d.$U,JSON.stringify(t.filters));const a=t.filters.filter((e=>!n.filters.find((t=>t.key===e.key))));a.length>0&&te(ne.home,ae.home.filter_changed,{key:a[0].key})}})),e.subscribeToState(((n,a)=>{n.value.from===a.value.from&&n.value.to===a.value.to||this.buildPanels(e,t.state.filters)})),this.buildPanels(e,t.state.filters)}buildPanels(e,t){const n=e.state.value.from.unix(),a=e.state.value.to.unix(),r=`${(0,Jt.duration)(a-n,"s").asSeconds()}s`,i=function(e){const t=e.filter((e=>e.key&&e.operator&&e.value)).map((e=>(e=>{if(!e)return"";let t=e.value;return null==t||""===t?"":(mr.test(t)||["kind"].includes(e.key)||"string"!=typeof t||t.startsWith('"')||t.endsWith('"')||(t=`"${t}"`),`${e.key}${e.operator}${t}`)})(e))).join(d.x5);return t.length?`&& ${t}`:""}(t);this.setState({body:new o.gF({children:[new o.gF({autoRows:"min-content",columnGap:2,rowGap:2,children:[new o.xK({body:new ha({query:{query:`{nestedSetParent < 0 && status = error ${i}} | count_over_time() by (resource.service.name)`,step:r},title:"Errored services",type:"errored-services"})}),new o.xK({body:new ha({query:{query:`{nestedSetParent < 0 ${i}} | quantile_over_time(duration, 0.9) by (resource.service.name)`,step:r},title:"Slow services",type:"slowest-services"})}),new o.xK({body:new ha({query:{query:`{nestedSetParent<0 ${i}} | histogram_over_time(duration)`},title:"Slow traces",type:"slowest-traces",filter:i})})]})]})})}constructor(e){var t,n,a,r,i;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){Ka(e,t,n[t])}))}return e}({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new o.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:(r=e.initialFilters,i=e.initialDS,new o.Pj({variables:[new o.mI({name:d.EY,label:"Data source",value:i,pluginId:"tempo"}),new o.H9({name:d.zM,datasource:d.Vl,layout:"combobox",filters:r,allowCustomValue:!0})]})),controls:null!==(a=e.controls)&&void 0!==a?a:[new o.KE({}),new o.WM({})]},e)),this.addActivationHandler(this._onActivate.bind(this))}}function Za(e){return{container:(0,l.css)({margin:`${e.spacing(4)} auto`,width:"75%","@media (max-width: 900px)":{width:"95%"}})}}function Ja(e){return o.jh.getAncestor(e,Xn)}function Xa(e){return o.jh.getAncestor(e,kn)}function Ya(e){var t,n,a;return null!==(a=null==e||null===(n=e.data)||void 0===n||null===(t=n.error)||void 0===t?void 0:t.message)&&void 0!==a?a:"There are no Tempo data sources"}function er(e){return o.jh.interpolate(e,d.gR)}function tr(e){return e.map((e=>({label:e,value:e})))}function nr(e,t){var n;const a=null===(n=e.fields.find((e=>"number"===e.type)))||void 0===n?void 0:n.labels;if(!a)return"No labels";const r=Object.keys(a).filter((e=>"p"!==e));return 0===r.length?"No labels":a[t||r[0]].replace(/"/g,"")}function ar(e){const t=o.jh.lookupVariable(d.z,e);if(!(t instanceof o.yP))throw new Error("Group by variable not found");return t}function rr(e){const t=o.jh.lookupVariable(d.gP,e);if(!(t instanceof o.yP))throw new Error("Span list columns variable not found");return t}function ir(e){const t=o.jh.lookupVariable(d.PU,e);if(!(t instanceof o.yP))throw new Error("Metric variable not found");return t}function sr(e){const t=o.jh.lookupVariable(d.Ao,e);if(!(t instanceof o.H9))throw new Error("Filters variable not found");return t}function or(e){const t=o.jh.lookupVariable(d.CE,e);if(!(t instanceof qn))throw new Error("Primary signal variable not found");return t}function lr(e){const t=o.jh.lookupVariable(d.zM,e);if(!(t instanceof o.H9))throw new Error("Home filter variable not found");return t}function cr(e){const t=o.jh.lookupVariable(d.EY,e);if(!(t instanceof o.mI))throw new Error("Datasource variable not found");return t}function ur(e){return"comparison"===e||"traceList"===e}function dr(e){var t,n,a;return null!==(a=null==e||null===(n=e.data)||void 0===n||null===(t=n.series[0].fields)||void 0===t?void 0:t.some((e=>e.values.every((e=>void 0===e)))))&&void 0!==a&&a}Ka(Qa,"Component",(({model:e})=>{const{body:t}=e.useState(),n=(0,u.useStyles2)(Za);return r().createElement("div",{className:n.container},r().createElement(qa.Component,{model:e}),t&&r().createElement(t.Component,{model:t}))}));const mr=/^-?\d+\.?\d*$/,pr=e=>mr.test(e)||"string"!=typeof e||e.startsWith('"')||e.endsWith('"')?e:`"${e}"`,fr=e=>{var t;return(null==e||null===(t=e[0])||void 0===t?void 0:t.toUpperCase())+(null==e?void 0:e.slice(1))||""},vr=e=>(t,n)=>{e.publishEvent(new d.vR({traceId:t,spanId:n}),!0)},gr=()=>{var e;const t=null!==(e=null===c.useSidecar_EXPERIMENTAL||void 0===c.useSidecar_EXPERIMENTAL?void 0:(0,c.useSidecar_EXPERIMENTAL)())&&void 0!==e?e:{},n=localStorage.getItem(d.cd)||"",[i]=(0,a.useState)((s=n,l=function(e){const t=yr.safeParse(e);if(t.success)return t.data.filters}(t.initialContext),new Xn({initialDS:s,initialFilters:null!=l?l:[],$timeRange:new o.JZ({from:"now-30m",to:"now"})})));var s,l;return r().createElement(hr,{exploration:i})};function hr({exploration:e}){const[t,n]=r().useState(!1);return(0,a.useEffect)((()=>{t||(n(!0),te(ne.common,ae.common.app_initialized))}),[e,t]),t?r().createElement(o.$L,{scene:e,updateUrlOnInit:!0,createBrowserHistorySteps:!0},r().createElement(e.Component,{model:e})):null}const br=i.Ay.object({key:i.Ay.string(),operator:i.Ay.string(),value:i.Ay.string()}),yr=i.Ay.object({filters:i.Ay.array(br)})}}]);
//# sourceMappingURL=389.js.map