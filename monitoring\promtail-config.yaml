server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # CTFd application logs
  - job_name: ctfd
    static_configs:
      - targets:
          - localhost
        labels:
          job: ctfd
          __path__: /var/log/CTFd/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+) (?P<level>\w+) (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: '2006-01-02T15:04:05.000000'
      - labels:
          level:

  # Security monitor logs
  - job_name: security
    static_configs:
      - targets:
          - localhost
        labels:
          job: security
          __path__: /var/log/CTFd/security.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            event_type: event_type
            ip: ip
      - timestamp:
          source: timestamp
          format: RFC3339
      - labels:
          event_type:
          ip:

  # Nginx access logs
  - job_name: nginx_access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx_access
          __path__: /var/log/nginx/access.log
    pipeline_stages:
      - regex:
          expression: '^(?P<ip>\S+) .* "(?P<method>\S+) (?P<path>\S+) (?P<protocol>\S+)" (?P<status>\d+) (?P<size>\d+)'
      - labels:
          method:
          status:

  # Nginx error logs
  - job_name: nginx_error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx_error
          __path__: /var/log/nginx/error.log

