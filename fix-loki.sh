#!/bin/bash
# Fix Loki WAL permission issue

echo "🔧 Fixing Loki WAL permissions..."

# Create the loki data directory with proper permissions
mkdir -p .data/loki
chmod -R 777 .data/loki

# Stop Loki
docker compose stop loki

# Remove any existing loki data that might have wrong permissions
docker compose rm -f loki

# Start <PERSON> again
docker compose up -d loki

echo "✅ Loki permissions fixed!"
echo ""
echo "Checking Loki status..."
sleep 5
docker compose logs loki --tail 20
