#!/bin/bash
# Create necessary directories for CTFd Security Monitoring

echo "Creating necessary directories..."

# Create log directories
mkdir -p .data/CTFd/logs
mkdir -p .data/nginx/logs
mkdir -p .data/prometheus
mkdir -p .data/grafana
mkdir -p .data/loki

# Set proper permissions
chmod -R 755 .data/

echo "Directories created successfully!"

# Stop all containers first
echo "Stopping containers..."
docker compose down

# Start containers
echo "Starting containers..."
docker compose up -d

# Wait a moment
sleep 5

# Check status
echo ""
echo "Container status:"
docker compose ps

echo ""
echo "Checking if Security Monitor plugin loaded:"
docker compose logs ctfd 2>&1 | grep -i "security_monitor" || echo "Plugin not loaded yet, checking again..."

sleep 5
docker compose logs ctfd 2>&1 | grep "Loaded module" | tail -10

echo ""
echo "Access points:"
echo "- CTFd: http://localhost:8000"
echo "- Grafana: http://localhost:3000 (admin/admin)"
echo "- Prometheus: http://localhost:9090"
echo ""
echo "Check admin panel for 'Security Monitor' menu item!"
