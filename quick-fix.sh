#!/bin/bash
# Quick fix and restart script for CTFd Security Monitoring

echo "🚀 Quick Security Monitoring Fix & Restart"

# Create directories
mkdir -p .data/{CTFd/logs,nginx/logs,prometheus,grafana,loki} logs 2>/dev/null

# Fix permissions (especially for <PERSON>)
chmod -R 777 .data/loki 2>/dev/null
chmod -R 755 .data/CTFd .data/nginx .data/prometheus .data/grafana logs 2>/dev/null

# Create empty log files
touch .data/CTFd/logs/security.log .data/nginx/logs/{access,error}.log 2>/dev/null

# Remove version from docker-compose.yml
sed -i '/^version:/d' docker-compose.yml 2>/dev/null || true

# Fix paths in docker-compose.yml
sed -i 's|\.data/|./.data/|g' docker-compose.yml 2>/dev/null || true

# Detect docker-compose command
DC="docker compose"
$DC version >/dev/null 2>&1 || DC="docker-compose"

# Restart everything
echo "🔄 Restarting services..."
$DC down
$DC up -d

# Wait and check
echo "⏳ Waiting for services to start..."
sleep 10

# Show status
echo ""
echo "📊 Status:"
$DC ps | grep -E "(ctfd|grafana|prometheus)" || echo "Services starting..."

echo ""
echo "🔍 Checking Security Monitor plugin:"
$DC logs ctfd 2>&1 | grep -i "security_monitor" | tail -5 || echo "Plugin loading..."

echo ""
echo "✅ Done! Access:"
echo "   Admin Panel: http://localhost:8000/admin"
echo "   Security Monitor: http://localhost:8000/admin/plugins/security_monitor/dashboard"
echo "   Grafana: http://localhost:3000 (admin/admin)"
