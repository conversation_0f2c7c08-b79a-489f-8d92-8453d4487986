{"version": 3, "file": "919.js?_cache=20d467191de04bdd8fc1", "mappings": "wOAUA,MAAMA,EAAc,IAClB,IAAIC,EAAAA,GAAS,CACXC,MAAO,EAACC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OACzBC,eAAgB,CACdC,2BAA2B,EAC3BC,iBAAiB,KA8BvB,QA1BA,WACE,MAAOC,EAAeC,GAAoBC,IAAAA,UAAe,IAEzDC,EAAAA,EAAAA,MAEA,MAAMC,GAAQC,EAAAA,EAAAA,IAAYb,IAE1Bc,EAAAA,EAAAA,YAAU,KACHN,GACHC,GAAiB,EACnB,GACC,CAACG,EAAOJ,IAEX,MAAMO,EAAkBC,EAAAA,OAAOC,SAASC,KAAKC,YAE7C,OADkBJ,aAAAA,EAAAA,EAAkB,mCAAmCA,aAAAA,EAAAA,EAAkB,wBAKpFP,EAIE,kBAACI,EAAMQ,UAAS,CAACC,MAAOT,IAHtB,KAJA,kBAACU,EAAAA,SAAQA,CAACC,GAAG,IAAIC,SAAAA,GAQ5B,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/LogExplorationPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\n\nimport { Navigate } from 'react-router-dom';\n\nimport { config } from '@grafana/runtime';\nimport { SceneApp, useSceneApp } from '@grafana/scenes';\n\nimport { initializeMetadataService } from '../services/metadata';\nimport { makeIndexPage, makeRedirectPage } from './Pages';\n\nconst getSceneApp = () =>\n  new SceneApp({\n    pages: [makeIndexPage(), makeRedirectPage()],\n    urlSyncOptions: {\n      createBrowserHistorySteps: true,\n      updateUrlOnInit: true,\n    },\n  });\n\nfunction LogExplorationView() {\n  const [isInitialized, setIsInitialized] = React.useState(false);\n\n  initializeMetadataService();\n\n  const scene = useSceneApp(getSceneApp);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      setIsInitialized(true);\n    }\n  }, [scene, isInitialized]);\n\n  const userPermissions = config.bootData.user.permissions;\n  const canUseApp = userPermissions?.['grafana-lokiexplore-app:read'] || userPermissions?.['datasources:explore'];\n  if (!canUseApp) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return <scene.Component model={scene} />;\n}\n\nexport default LogExplorationView;\n"], "names": ["getSceneApp", "SceneApp", "pages", "makeIndexPage", "makeRedirectPage", "urlSyncOptions", "createBrowserHistorySteps", "updateUrlOnInit", "isInitialized", "setIsInitialized", "React", "initializeMetadataService", "scene", "useSceneApp", "useEffect", "userPermissions", "config", "bootData", "user", "permissions", "Component", "model", "Navigate", "to", "replace"], "sourceRoot": ""}