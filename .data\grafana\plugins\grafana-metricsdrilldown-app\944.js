"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[944],{2011:(e,t,n)=>{e.exports=n.p+"5e493d758066ba82f810.wasm"},6944:(e,t,n)=>{let r;n.r(t),n.d(t,{LoadedOutlierDetector:()=>x,OutlierDetector:()=>O,custom_init:()=>m,default:()=>R,initLogging:()=>h,initSync:()=>S});let _=0,o=null;function i(){return null!==o&&0!==o.byteLength||(o=new Uint8Array(r.memory.buffer)),o}const c="undefined"!=typeof TextEncoder?new TextEncoder("utf-8"):{encode:()=>{throw Error("TextEncoder not available")}},s="function"==typeof c.encodeInto?function(e,t){return c.encodeInto(e,t)}:function(e,t){const n=c.encode(e);return t.set(n),{read:e.length,written:n.length}};function b(e,t,n){if(void 0===n){const n=c.encode(e),r=t(n.length,1)>>>0;return i().subarray(r,r+n.length).set(n),_=n.length,r}let r=e.length,o=t(r,1)>>>0;const b=i();let u=0;for(;u<r;u++){const t=e.charCodeAt(u);if(t>127)break;b[o+u]=t}if(u!==r){0!==u&&(e=e.slice(u)),o=n(o,r,r=u+3*e.length,1)>>>0;const t=i().subarray(o+u,o+r);u+=s(e,t).written,o=n(o,r,u,1)>>>0}return _=u,o}let u=null;function a(){return(null===u||!0===u.buffer.detached||void 0===u.buffer.detached&&u.buffer!==r.memory.buffer)&&(u=new DataView(r.memory.buffer)),u}function f(e){const t=r.__externref_table_alloc();return r.__wbindgen_export_4.set(t,e),t}function g(e,t){try{return e.apply(this,t)}catch(e){const t=f(e);r.__wbindgen_exn_store(t)}}const w="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};function d(e,t){return e>>>=0,w.decode(i().subarray(e,e+t))}function l(e){const t=typeof e;if("number"==t||"boolean"==t||null==e)return`${e}`;if("string"==t)return`"${e}"`;if("symbol"==t){const t=e.description;return null==t?"Symbol":`Symbol(${t})`}if("function"==t){const t=e.name;return"string"==typeof t&&t.length>0?`Function(${t})`:"Function"}if(Array.isArray(e)){const t=e.length;let n="[";t>0&&(n+=l(e[0]));for(let r=1;r<t;r++)n+=", "+l(e[r]);return n+="]",n}const n=/\[object ([^\]]+)\]/.exec(toString.call(e));let r;if(!(n&&n.length>1))return toString.call(e);if(r=n[1],"Object"==r)try{return"Object("+JSON.stringify(e)+")"}catch(e){return"Object"}return e instanceof Error?`${e.name}: ${e.message}\n${e.stack}`:r}function y(e){return null==e}function p(e){const t=r.__wbindgen_export_4.get(e);return r.__externref_table_dealloc(e),t}function h(e){const t=r.initLogging(y(e)?0:f(e));if(t[1])throw p(t[0])}function m(){r.custom_init()}"undefined"!=typeof TextDecoder&&w.decode();const A="undefined"==typeof FinalizationRegistry?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry((e=>r.__wbg_loadedoutlierdetector_free(e>>>0,1)));class x{static __wrap(e){e>>>=0;const t=Object.create(x.prototype);return t.__wbg_ptr=e,A.register(t,t.__wbg_ptr,t),t}__destroy_into_raw(){const e=this.__wbg_ptr;return this.__wbg_ptr=0,A.unregister(this),e}free(){const e=this.__destroy_into_raw();r.__wbg_loadedoutlierdetector_free(e,0)}detect(){const e=r.loadedoutlierdetector_detect(this.__wbg_ptr);if(e[2])throw p(e[1]);return p(e[0])}updateDetector(e){const t=r.loadedoutlierdetector_updateDetector(this.__wbg_ptr,e);if(t[1])throw p(t[0])}}const v="undefined"==typeof FinalizationRegistry?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry((e=>r.__wbg_outlierdetector_free(e>>>0,1)));class O{static __wrap(e){e>>>=0;const t=Object.create(O.prototype);return t.__wbg_ptr=e,v.register(t,t.__wbg_ptr,t),t}__destroy_into_raw(){const e=this.__wbg_ptr;return this.__wbg_ptr=0,v.unregister(this),e}free(){const e=this.__destroy_into_raw();r.__wbg_outlierdetector_free(e,0)}constructor(e,t){const n=r.outlierdetector_new(e,t);if(n[2])throw p(n[1]);return this.__wbg_ptr=n[0]>>>0,v.register(this,this.__wbg_ptr,this),this}static dbscan(e){const t=r.outlierdetector_dbscan(e);if(t[2])throw p(t[1]);return O.__wrap(t[0])}static mad(e){const t=r.outlierdetector_mad(e);if(t[2])throw p(t[1]);return O.__wrap(t[0])}detect(e){const t=r.outlierdetector_detect(this.__wbg_ptr,e);if(t[2])throw p(t[1]);return p(t[0])}preprocess(e){const t=r.outlierdetector_preprocess(this.__wbg_ptr,e);if(t[2])throw p(t[1]);return x.__wrap(t[0])}}function j(){const e={wbg:{}};return e.wbg.__wbg_String_8f0eb39a4a4c2f66=function(e,t){const n=b(String(t),r.__wbindgen_malloc,r.__wbindgen_realloc),o=_;a().setInt32(e+4,o,!0),a().setInt32(e+0,n,!0)},e.wbg.__wbg_buffer_609cc3eee51ed158=function(e){return e.buffer},e.wbg.__wbg_call_672a4d21634d4a24=function(){return g((function(e,t){return e.call(t)}),arguments)},e.wbg.__wbg_done_769e5ede4b31c67b=function(e){return e.done},e.wbg.__wbg_entries_3265d4158b33e5dc=function(e){return Object.entries(e)},e.wbg.__wbg_error_7534b8e9a36f1ab4=function(e,t){let n,_;try{n=e,_=t,console.error(d(e,t))}finally{r.__wbindgen_free(n,_,1)}},e.wbg.__wbg_get_67b2ba62fc30de12=function(){return g((function(e,t){return Reflect.get(e,t)}),arguments)},e.wbg.__wbg_get_b9b93047fe3cf45b=function(e,t){return e[t>>>0]},e.wbg.__wbg_getwithrefkey_1dc361bd10053bfe=function(e,t){return e[t]},e.wbg.__wbg_instanceof_ArrayBuffer_e14585432e3737fc=function(e){let t;try{t=e instanceof ArrayBuffer}catch(e){t=!1}return t},e.wbg.__wbg_instanceof_Uint8Array_17156bcf118086a9=function(e){let t;try{t=e instanceof Uint8Array}catch(e){t=!1}return t},e.wbg.__wbg_isArray_a1eab7e0d067391b=function(e){return Array.isArray(e)},e.wbg.__wbg_iterator_9a24c88df860dc65=function(){return Symbol.iterator},e.wbg.__wbg_length_a446193dc22c12f8=function(e){return e.length},e.wbg.__wbg_length_e2d2a49132c1b256=function(e){return e.length},e.wbg.__wbg_log_c6ef241383c92365=function(e,t,n,_,o,i,c,s){let b,u;try{b=e,u=t}finally{r.__wbindgen_free(b,u,1)}},e.wbg.__wbg_log_e84c5ecfbbae3597=function(e,t){let n,_;try{n=e,_=t}finally{r.__wbindgen_free(n,_,1)}},e.wbg.__wbg_mark_d518bb94ec985f57=function(e,t){performance.mark(d(e,t))},e.wbg.__wbg_measure_1dd89292debb9055=function(){return g((function(e,t,n,_){let o,i,c,s;try{o=e,i=t,c=n,s=_,performance.measure(d(e,t),d(n,_))}finally{r.__wbindgen_free(o,i,1),r.__wbindgen_free(c,s,1)}}),arguments)},e.wbg.__wbg_new_405e22f390576ce2=function(){return new Object},e.wbg.__wbg_new_78feb108b6472713=function(){return new Array},e.wbg.__wbg_new_8a6f238a6ece86ea=function(){return new Error},e.wbg.__wbg_new_a12002a7f91c75be=function(e){return new Uint8Array(e)},e.wbg.__wbg_next_25feadfc0913fea9=function(e){return e.next},e.wbg.__wbg_next_6574e1a8a62d1055=function(){return g((function(e){return e.next()}),arguments)},e.wbg.__wbg_set_37837023f3d740e8=function(e,t,n){e[t>>>0]=n},e.wbg.__wbg_set_3f1d0b984ed272ed=function(e,t,n){e[t]=n},e.wbg.__wbg_set_65595bdd868b3009=function(e,t,n){e.set(t,n>>>0)},e.wbg.__wbg_stack_0ed75d68575b0f3c=function(e,t){const n=b(t.stack,r.__wbindgen_malloc,r.__wbindgen_realloc),o=_;a().setInt32(e+4,o,!0),a().setInt32(e+0,n,!0)},e.wbg.__wbg_value_cd1ffa7b1ab794f1=function(e){return e.value},e.wbg.__wbindgen_bigint_from_u64=function(e){return BigInt.asUintN(64,e)},e.wbg.__wbindgen_boolean_get=function(e){return"boolean"==typeof e?e?1:0:2},e.wbg.__wbindgen_debug_string=function(e,t){const n=b(l(t),r.__wbindgen_malloc,r.__wbindgen_realloc),o=_;a().setInt32(e+4,o,!0),a().setInt32(e+0,n,!0)},e.wbg.__wbindgen_error_new=function(e,t){return new Error(d(e,t))},e.wbg.__wbindgen_in=function(e,t){return e in t},e.wbg.__wbindgen_init_externref_table=function(){const e=r.__wbindgen_export_4,t=e.grow(4);e.set(0,void 0),e.set(t+0,void 0),e.set(t+1,null),e.set(t+2,!0),e.set(t+3,!1)},e.wbg.__wbindgen_is_function=function(e){return"function"==typeof e},e.wbg.__wbindgen_is_object=function(e){return"object"==typeof e&&null!==e},e.wbg.__wbindgen_is_string=function(e){return"string"==typeof e},e.wbg.__wbindgen_is_undefined=function(e){return void 0===e},e.wbg.__wbindgen_jsval_loose_eq=function(e,t){return e==t},e.wbg.__wbindgen_memory=function(){return r.memory},e.wbg.__wbindgen_number_get=function(e,t){const n="number"==typeof t?t:void 0;a().setFloat64(e+8,y(n)?0:n,!0),a().setInt32(e+0,!y(n),!0)},e.wbg.__wbindgen_number_new=function(e){return e},e.wbg.__wbindgen_string_get=function(e,t){const n="string"==typeof t?t:void 0;var o=y(n)?0:b(n,r.__wbindgen_malloc,r.__wbindgen_realloc),i=_;a().setInt32(e+4,i,!0),a().setInt32(e+0,o,!0)},e.wbg.__wbindgen_string_new=function(e,t){return d(e,t)},e.wbg.__wbindgen_throw=function(e,t){throw new Error(d(e,t))},e}function I(e,t){return r=e.exports,k.__wbindgen_wasm_module=t,u=null,o=null,r.__wbindgen_start(),r}function S(e){if(void 0!==r)return r;void 0!==e&&(Object.getPrototypeOf(e)===Object.prototype?({module:e}=e):console.warn("using deprecated parameters for `initSync()`; pass a single object instead"));const t=j();e instanceof WebAssembly.Module||(e=new WebAssembly.Module(e));return I(new WebAssembly.Instance(e,t),e)}async function k(e){if(void 0!==r)return r;void 0!==e&&(Object.getPrototypeOf(e)===Object.prototype?({module_or_path:e}=e):console.warn("using deprecated parameters for the initialization function; pass a single object instead")),void 0===e&&(e=new URL(n(2011),n.b));const t=j();("string"==typeof e||"function"==typeof Request&&e instanceof Request||"function"==typeof URL&&e instanceof URL)&&(e=fetch(e));const{instance:_,module:o}=await async function(e,t){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,t)}catch(t){if("application/wasm"==e.headers.get("Content-Type"))throw t;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve Wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t)}const n=await e.arrayBuffer();return await WebAssembly.instantiate(n,t)}{const n=await WebAssembly.instantiate(e,t);return n instanceof WebAssembly.Instance?{instance:n,module:e}:n}}(await e,t);return I(_,o)}const R=k}}]);
//# sourceMappingURL=944.js.map?_cache=641a2488269c99381577