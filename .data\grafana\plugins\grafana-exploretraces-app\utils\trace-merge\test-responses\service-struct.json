{"traces": [{"traceID": "2a6bee861db8a7dff9561ae4344c226", "rootServiceName": "grafana", "rootTraceName": "alert rule execution", "startTimeUnixNano": "1712759380123867720", "durationMs": 71, "spanSet": {"spans": [{"spanID": "7963efe433d64f94", "name": "HTTP POST", "startTimeUnixNano": "1712759380156810000", "durationNanos": "1788000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "19"}}, {"key": "nestedSetParent", "value": {"intValue": "18"}}, {"key": "nestedSetRight", "value": {"intValue": "32"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "05e3a76c84b5331a", "name": "querysharding", "startTimeUnixNano": "1712759380158130000", "durationNanos": "33000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "23"}}, {"key": "nestedSetLeft", "value": {"intValue": "24"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "nestedSetRight", "value": {"intValue": "25"}}]}, {"spanID": "11ffe9e1c881ce3a", "name": "cardinality_estimation", "startTimeUnixNano": "1712759380157829000", "durationNanos": "340000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "nestedSetLeft", "value": {"intValue": "23"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "22"}}, {"key": "nestedSetRight", "value": {"intValue": "26"}}]}], "matched": 3}, "spanSets": [{"spans": [{"spanID": "7963efe433d64f94", "name": "HTTP POST", "startTimeUnixNano": "1712759380156810000", "durationNanos": "1788000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "19"}}, {"key": "nestedSetParent", "value": {"intValue": "18"}}, {"key": "nestedSetRight", "value": {"intValue": "32"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "05e3a76c84b5331a", "name": "querysharding", "startTimeUnixNano": "1712759380158130000", "durationNanos": "33000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "23"}}, {"key": "nestedSetLeft", "value": {"intValue": "24"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "nestedSetRight", "value": {"intValue": "25"}}]}, {"spanID": "11ffe9e1c881ce3a", "name": "cardinality_estimation", "startTimeUnixNano": "1712759380157829000", "durationNanos": "340000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "nestedSetLeft", "value": {"intValue": "23"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "22"}}, {"key": "nestedSetRight", "value": {"intValue": "26"}}]}], "matched": 3}]}, {"traceID": "7782bcb2a2d73b2b49f7e784b5cb4f58", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759379462970605", "durationMs": 71, "spanSet": {"spans": [{"spanID": "6352c2265a424661", "name": "HTTP GET", "startTimeUnixNano": "1712759379499389000", "durationNanos": "18395000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "6352c2265a424661", "name": "HTTP GET", "startTimeUnixNano": "1712759379499389000", "durationNanos": "18395000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "fd9edeea6cc040fb0cffe14af0a87130", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759378983959480", "durationMs": 38, "spanSet": {"spans": [{"spanID": "420cb4b3eee1866d", "name": "HTTP GET", "startTimeUnixNano": "1712759378986520000", "durationNanos": "34902000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "420cb4b3eee1866d", "name": "HTTP GET", "startTimeUnixNano": "1712759378986520000", "durationNanos": "34902000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}]}, {"traceID": "1e050274d4bb24d1ddda55b994fc8d7a", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759378809131929", "durationMs": 24, "spanSet": {"spans": [{"spanID": "5d95c0f430aead90", "name": "HTTP GET", "startTimeUnixNano": "1712759378815125000", "durationNanos": "16676000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "5d95c0f430aead90", "name": "HTTP GET", "startTimeUnixNano": "1712759378815125000", "durationNanos": "16676000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "84cf55b22179f9a59a873e52c9c59f32", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759378216597482", "durationMs": 35, "spanSet": {"spans": [{"spanID": "665d311024bfb375", "name": "HTTP GET", "startTimeUnixNano": "1712759378234595000", "durationNanos": "14660000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "665d311024bfb375", "name": "HTTP GET", "startTimeUnixNano": "1712759378234595000", "durationNanos": "14660000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "3584ba5931d83278d567ebe373a39b92", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759377918086929", "durationMs": 66, "spanSet": {"spans": [{"spanID": "65252e596b4f53b7", "name": "HTTP GET", "startTimeUnixNano": "1712759377931348000", "durationNanos": "52638000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "65252e596b4f53b7", "name": "HTTP GET", "startTimeUnixNano": "1712759377931348000", "durationNanos": "52638000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "280ebfaf208a73e8a855471292741cb", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759377314054104", "durationMs": 28, "spanSet": {"spans": [{"spanID": "482cb074dc232827", "name": "HTTP GET", "startTimeUnixNano": "1712759377320133000", "durationNanos": "20387000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "482cb074dc232827", "name": "HTTP GET", "startTimeUnixNano": "1712759377320133000", "durationNanos": "20387000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}]}], "matched": 1}]}, {"traceID": "490e44841a3f51c5", "rootServiceName": "grafana", "rootTraceName": "HTTP POST /api/ds/query", "startTimeUnixNano": "1712759377181826000", "durationMs": 27, "spanSet": {"spans": [{"spanID": "0d2b2e1d07791d9a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377193801000", "durationNanos": "2073000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "38"}}, {"key": "nestedSetParent", "value": {"intValue": "37"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "51"}}]}, {"spanID": "4ef7b873988a7a61", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194208000", "durationNanos": "2691000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1240"}}, {"key": "nestedSetParent", "value": {"intValue": "1239"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1253"}}]}, {"spanID": "5a250bcdc202a9eb", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194595000", "durationNanos": "2380000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1045"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1046"}}, {"key": "nestedSetRight", "value": {"intValue": "1059"}}]}, {"spanID": "13d65c2e31b6a6db", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194667000", "durationNanos": "2673000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1575"}}, {"key": "nestedSetParent", "value": {"intValue": "1561"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1562"}}]}, {"spanID": "1b586fa702cf6c6d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194264000", "durationNanos": "3409000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1833"}}, {"key": "nestedSetParent", "value": {"intValue": "1819"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1820"}}]}, {"spanID": "7a9c3a4293590f30", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194647000", "durationNanos": "5868000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2228"}}, {"key": "nestedSetRight", "value": {"intValue": "2241"}}, {"key": "nestedSetParent", "value": {"intValue": "2227"}}]}, {"spanID": "6d8f14a5c9cca044", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377196442000", "durationNanos": "5059000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2521"}}, {"key": "nestedSetParent", "value": {"intValue": "2507"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2508"}}]}, {"spanID": "00ced5a122b578df", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377196393000", "durationNanos": "5237000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2331"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2318"}}, {"key": "nestedSetParent", "value": {"intValue": "2317"}}]}, {"spanID": "40aaedb872f86544", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377197963000", "durationNanos": "3687000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2422"}}, {"key": "nestedSetRight", "value": {"intValue": "2435"}}, {"key": "nestedSetParent", "value": {"intValue": "2421"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "664bcf56b549fc1c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377197105000", "durationNanos": "4636000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2594"}}, {"key": "nestedSetParent", "value": {"intValue": "2593"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2607"}}]}, {"spanID": "5f58e9b57da00578", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377202095000", "durationNanos": "3154000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2675"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2662"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "2661"}}]}, {"spanID": "77cbc9fae95ad4d6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194035000", "durationNanos": "2526000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "620"}}, {"key": "nestedSetParent", "value": {"intValue": "619"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "633"}}]}, {"spanID": "72d19cbceb37fed9", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194659000", "durationNanos": "2032000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "719"}}, {"key": "nestedSetRight", "value": {"intValue": "733"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "720"}}]}, {"spanID": "052ae974edfa5c36", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377193821000", "durationNanos": "2125000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "220"}}, {"key": "nestedSetParent", "value": {"intValue": "219"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "233"}}]}, {"spanID": "443f567d7ac37e0c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194658000", "durationNanos": "1811000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "534"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "547"}}, {"key": "nestedSetParent", "value": {"intValue": "533"}}]}, {"spanID": "1c66848a3d6eec71", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377195056000", "durationNanos": "2038000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1321"}}, {"key": "nestedSetParent", "value": {"intValue": "1307"}}, {"key": "nestedSetLeft", "value": {"intValue": "1308"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "5adecb4129f83878", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194834000", "durationNanos": "2787000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1751"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1738"}}, {"key": "nestedSetParent", "value": {"intValue": "1737"}}]}, {"spanID": "3befff2ce11cc751", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194480000", "durationNanos": "1758000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "375"}}, {"key": "nestedSetParent", "value": {"intValue": "361"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "362"}}]}, {"spanID": "2978e97f22dc7aa9", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194392000", "durationNanos": "3079000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1652"}}, {"key": "nestedSetRight", "value": {"intValue": "1665"}}, {"key": "nestedSetParent", "value": {"intValue": "1651"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "29b19dda3494f102", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194216000", "durationNanos": "2574000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "791"}}, {"key": "nestedSetLeft", "value": {"intValue": "792"}}, {"key": "nestedSetRight", "value": {"intValue": "805"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}], "matched": 28}, "spanSets": [{"spans": [{"spanID": "0d2b2e1d07791d9a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377193801000", "durationNanos": "2073000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "38"}}, {"key": "nestedSetParent", "value": {"intValue": "37"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "51"}}]}, {"spanID": "4ef7b873988a7a61", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194208000", "durationNanos": "2691000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1240"}}, {"key": "nestedSetParent", "value": {"intValue": "1239"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1253"}}]}, {"spanID": "5a250bcdc202a9eb", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194595000", "durationNanos": "2380000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1045"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1046"}}, {"key": "nestedSetRight", "value": {"intValue": "1059"}}]}, {"spanID": "13d65c2e31b6a6db", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194667000", "durationNanos": "2673000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1575"}}, {"key": "nestedSetParent", "value": {"intValue": "1561"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1562"}}]}, {"spanID": "1b586fa702cf6c6d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194264000", "durationNanos": "3409000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1833"}}, {"key": "nestedSetParent", "value": {"intValue": "1819"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1820"}}]}, {"spanID": "7a9c3a4293590f30", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194647000", "durationNanos": "5868000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2228"}}, {"key": "nestedSetRight", "value": {"intValue": "2241"}}, {"key": "nestedSetParent", "value": {"intValue": "2227"}}]}, {"spanID": "6d8f14a5c9cca044", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377196442000", "durationNanos": "5059000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2521"}}, {"key": "nestedSetParent", "value": {"intValue": "2507"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2508"}}]}, {"spanID": "00ced5a122b578df", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377196393000", "durationNanos": "5237000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2331"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2318"}}, {"key": "nestedSetParent", "value": {"intValue": "2317"}}]}, {"spanID": "40aaedb872f86544", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377197963000", "durationNanos": "3687000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2422"}}, {"key": "nestedSetRight", "value": {"intValue": "2435"}}, {"key": "nestedSetParent", "value": {"intValue": "2421"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "664bcf56b549fc1c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377197105000", "durationNanos": "4636000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2594"}}, {"key": "nestedSetParent", "value": {"intValue": "2593"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2607"}}]}, {"spanID": "5f58e9b57da00578", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377202095000", "durationNanos": "3154000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2675"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2662"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "2661"}}]}, {"spanID": "77cbc9fae95ad4d6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194035000", "durationNanos": "2526000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "620"}}, {"key": "nestedSetParent", "value": {"intValue": "619"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "633"}}]}, {"spanID": "72d19cbceb37fed9", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194659000", "durationNanos": "2032000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "719"}}, {"key": "nestedSetRight", "value": {"intValue": "733"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "720"}}]}, {"spanID": "052ae974edfa5c36", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377193821000", "durationNanos": "2125000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "220"}}, {"key": "nestedSetParent", "value": {"intValue": "219"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "233"}}]}, {"spanID": "443f567d7ac37e0c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194658000", "durationNanos": "1811000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "534"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "547"}}, {"key": "nestedSetParent", "value": {"intValue": "533"}}]}, {"spanID": "1c66848a3d6eec71", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377195056000", "durationNanos": "2038000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1321"}}, {"key": "nestedSetParent", "value": {"intValue": "1307"}}, {"key": "nestedSetLeft", "value": {"intValue": "1308"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "5adecb4129f83878", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194834000", "durationNanos": "2787000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1751"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1738"}}, {"key": "nestedSetParent", "value": {"intValue": "1737"}}]}, {"spanID": "3befff2ce11cc751", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194480000", "durationNanos": "1758000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "375"}}, {"key": "nestedSetParent", "value": {"intValue": "361"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "362"}}]}, {"spanID": "2978e97f22dc7aa9", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194392000", "durationNanos": "3079000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1652"}}, {"key": "nestedSetRight", "value": {"intValue": "1665"}}, {"key": "nestedSetParent", "value": {"intValue": "1651"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "29b19dda3494f102", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377194216000", "durationNanos": "2574000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "791"}}, {"key": "nestedSetLeft", "value": {"intValue": "792"}}, {"key": "nestedSetRight", "value": {"intValue": "805"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}], "matched": 28}]}, {"traceID": "1ada799efc587775", "rootServiceName": "grafana", "rootTraceName": "HTTP POST /api/ds/query", "startTimeUnixNano": "1712759377131083000", "durationMs": 21, "spanSet": {"spans": [{"spanID": "0efc2d081401d499", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377141905000", "durationNanos": "1212000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "151"}}, {"key": "nestedSetParent", "value": {"intValue": "137"}}, {"key": "nestedSetLeft", "value": {"intValue": "138"}}]}, {"spanID": "52bb5a97c202733e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377141913000", "durationNanos": "1209000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "121"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "122"}}, {"key": "nestedSetRight", "value": {"intValue": "135"}}]}, {"spanID": "783d97da80e57c17", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142270000", "durationNanos": "872000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "38"}}, {"key": "nestedSetRight", "value": {"intValue": "51"}}, {"key": "nestedSetParent", "value": {"intValue": "37"}}]}, {"spanID": "05660edc82014134", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142260000", "durationNanos": "893000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "67"}}, {"key": "nestedSetParent", "value": {"intValue": "53"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "54"}}]}, {"spanID": "0d515cb4b36668c1", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142350000", "durationNanos": "3082000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1886"}}, {"key": "nestedSetRight", "value": {"intValue": "1899"}}, {"key": "nestedSetParent", "value": {"intValue": "1885"}}]}, {"spanID": "479cccbc309e8e0d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142203000", "durationNanos": "3241000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1800"}}, {"key": "nestedSetRight", "value": {"intValue": "1813"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1799"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "081fee77af87bd16", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142590000", "durationNanos": "1005000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "704"}}, {"key": "nestedSetRight", "value": {"intValue": "721"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "703"}}]}, {"spanID": "0e58e79dbf16d4af", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142745000", "durationNanos": "851000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "620"}}, {"key": "nestedSetParent", "value": {"intValue": "619"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "633"}}]}, {"spanID": "71c1d5e9b20ad25b", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142712000", "durationNanos": "891000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "636"}}, {"key": "nestedSetParent", "value": {"intValue": "635"}}, {"key": "nestedSetRight", "value": {"intValue": "649"}}]}, {"spanID": "2801d5df7488fd82", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142794000", "durationNanos": "1007000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "1043"}}, {"key": "nestedSetLeft", "value": {"intValue": "1044"}}, {"key": "nestedSetRight", "value": {"intValue": "1057"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "5d75d1c5dd24cc59", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142792000", "durationNanos": "1025000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1028"}}, {"key": "nestedSetParent", "value": {"intValue": "1027"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1041"}}]}, {"spanID": "176f0aa7efbcb635", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142469000", "durationNanos": "2002000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1413"}}, {"key": "nestedSetLeft", "value": {"intValue": "1400"}}, {"key": "nestedSetParent", "value": {"intValue": "1399"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "1e08e769997a024c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146800000", "durationNanos": "1055000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2408"}}, {"key": "nestedSetRight", "value": {"intValue": "2421"}}, {"key": "nestedSetParent", "value": {"intValue": "2407"}}]}, {"spanID": "146a8e5106c7b8a4", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146826000", "durationNanos": "1030000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2392"}}, {"key": "nestedSetRight", "value": {"intValue": "2405"}}, {"key": "nestedSetParent", "value": {"intValue": "2391"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "4d5f5ab6705434fa", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146912000", "durationNanos": "952000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "2339"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2340"}}, {"key": "nestedSetRight", "value": {"intValue": "2353"}}]}, {"spanID": "0280003a861c3012", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146901000", "durationNanos": "973000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2324"}}, {"key": "nestedSetRight", "value": {"intValue": "2337"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "2323"}}]}, {"spanID": "43806ab2a735ce7a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146835000", "durationNanos": "2105000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2523"}}, {"key": "nestedSetParent", "value": {"intValue": "2509"}}, {"key": "nestedSetLeft", "value": {"intValue": "2510"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "51713d0f0c9e0039", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142142000", "durationNanos": "995000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "221"}}, {"key": "nestedSetRight", "value": {"intValue": "235"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "222"}}]}, {"spanID": "1375fe68cff6c834", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142065000", "durationNanos": "1082000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "206"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "219"}}, {"key": "nestedSetParent", "value": {"intValue": "205"}}]}, {"spanID": "1d897137cc9b10c6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142410000", "durationNanos": "981000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "303"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "290"}}, {"key": "nestedSetParent", "value": {"intValue": "289"}}]}], "matched": 44}, "spanSets": [{"spans": [{"spanID": "0efc2d081401d499", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377141905000", "durationNanos": "1212000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "151"}}, {"key": "nestedSetParent", "value": {"intValue": "137"}}, {"key": "nestedSetLeft", "value": {"intValue": "138"}}]}, {"spanID": "52bb5a97c202733e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377141913000", "durationNanos": "1209000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "121"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "122"}}, {"key": "nestedSetRight", "value": {"intValue": "135"}}]}, {"spanID": "783d97da80e57c17", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142270000", "durationNanos": "872000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "38"}}, {"key": "nestedSetRight", "value": {"intValue": "51"}}, {"key": "nestedSetParent", "value": {"intValue": "37"}}]}, {"spanID": "05660edc82014134", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142260000", "durationNanos": "893000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "67"}}, {"key": "nestedSetParent", "value": {"intValue": "53"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "54"}}]}, {"spanID": "0d515cb4b36668c1", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142350000", "durationNanos": "3082000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1886"}}, {"key": "nestedSetRight", "value": {"intValue": "1899"}}, {"key": "nestedSetParent", "value": {"intValue": "1885"}}]}, {"spanID": "479cccbc309e8e0d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142203000", "durationNanos": "3241000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1800"}}, {"key": "nestedSetRight", "value": {"intValue": "1813"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1799"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "081fee77af87bd16", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142590000", "durationNanos": "1005000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "704"}}, {"key": "nestedSetRight", "value": {"intValue": "721"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "703"}}]}, {"spanID": "0e58e79dbf16d4af", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142745000", "durationNanos": "851000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "620"}}, {"key": "nestedSetParent", "value": {"intValue": "619"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "633"}}]}, {"spanID": "71c1d5e9b20ad25b", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142712000", "durationNanos": "891000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "636"}}, {"key": "nestedSetParent", "value": {"intValue": "635"}}, {"key": "nestedSetRight", "value": {"intValue": "649"}}]}, {"spanID": "2801d5df7488fd82", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142794000", "durationNanos": "1007000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "1043"}}, {"key": "nestedSetLeft", "value": {"intValue": "1044"}}, {"key": "nestedSetRight", "value": {"intValue": "1057"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "5d75d1c5dd24cc59", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142792000", "durationNanos": "1025000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1028"}}, {"key": "nestedSetParent", "value": {"intValue": "1027"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1041"}}]}, {"spanID": "176f0aa7efbcb635", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142469000", "durationNanos": "2002000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1413"}}, {"key": "nestedSetLeft", "value": {"intValue": "1400"}}, {"key": "nestedSetParent", "value": {"intValue": "1399"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "1e08e769997a024c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146800000", "durationNanos": "1055000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2408"}}, {"key": "nestedSetRight", "value": {"intValue": "2421"}}, {"key": "nestedSetParent", "value": {"intValue": "2407"}}]}, {"spanID": "146a8e5106c7b8a4", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146826000", "durationNanos": "1030000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2392"}}, {"key": "nestedSetRight", "value": {"intValue": "2405"}}, {"key": "nestedSetParent", "value": {"intValue": "2391"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "4d5f5ab6705434fa", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146912000", "durationNanos": "952000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "2339"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2340"}}, {"key": "nestedSetRight", "value": {"intValue": "2353"}}]}, {"spanID": "0280003a861c3012", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146901000", "durationNanos": "973000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2324"}}, {"key": "nestedSetRight", "value": {"intValue": "2337"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "2323"}}]}, {"spanID": "43806ab2a735ce7a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377146835000", "durationNanos": "2105000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2523"}}, {"key": "nestedSetParent", "value": {"intValue": "2509"}}, {"key": "nestedSetLeft", "value": {"intValue": "2510"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "51713d0f0c9e0039", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142142000", "durationNanos": "995000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "221"}}, {"key": "nestedSetRight", "value": {"intValue": "235"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "222"}}]}, {"spanID": "1375fe68cff6c834", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142065000", "durationNanos": "1082000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "206"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "219"}}, {"key": "nestedSetParent", "value": {"intValue": "205"}}]}, {"spanID": "1d897137cc9b10c6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759377142410000", "durationNanos": "981000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "303"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "290"}}, {"key": "nestedSetParent", "value": {"intValue": "289"}}]}], "matched": 44}]}, {"traceID": "61cf6e75bdf18b42", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759376719503000", "durationMs": 31, "spanSet": {"spans": [{"spanID": "407a3f94ccad6a21", "name": "HTTP GET", "startTimeUnixNano": "1712759376727646000", "durationNanos": "21441000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "407a3f94ccad6a21", "name": "HTTP GET", "startTimeUnixNano": "1712759376727646000", "durationNanos": "21441000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}]}], "matched": 1}]}, {"traceID": "53ac97e0be99fcbad83c4d59182332ff", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759376614993559", "durationMs": 28, "spanSet": {"spans": [{"spanID": "2533097d1504a8fa", "name": "HTTP GET", "startTimeUnixNano": "1712759376617516000", "durationNanos": "25090000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "2533097d1504a8fa", "name": "HTTP GET", "startTimeUnixNano": "1712759376617516000", "durationNanos": "25090000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "9b5de8ea7c12f038db21715ce8da782c", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759376519533639", "durationMs": 30, "spanSet": {"spans": [{"spanID": "651e822529af7c6e", "name": "HTTP GET", "startTimeUnixNano": "1712759376522176000", "durationNanos": "21489000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "651e822529af7c6e", "name": "HTTP GET", "startTimeUnixNano": "1712759376522176000", "durationNanos": "21489000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}]}, {"traceID": "a65f9fb932b371c14149f4f1b03438b9", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759376170500320", "durationMs": 20, "spanSet": {"spans": [{"spanID": "172e1e453dc5432a", "name": "HTTP GET", "startTimeUnixNano": "1712759376173520000", "durationNanos": "16434000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "172e1e453dc5432a", "name": "HTTP GET", "startTimeUnixNano": "1712759376173520000", "durationNanos": "16434000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "676a860c08b6298a", "rootServiceName": "grafana", "rootTraceName": "HTTP POST /api/ds/query", "startTimeUnixNano": "1712759376142815000", "durationMs": 42, "spanSet": {"spans": [{"spanID": "6bcef17d8464a55e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152482000", "durationNanos": "11609000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "629"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "616"}}, {"key": "nestedSetParent", "value": {"intValue": "615"}}]}, {"spanID": "5625b83279055f66", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153035000", "durationNanos": "16170000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1698"}}, {"key": "nestedSetRight", "value": {"intValue": "1711"}}, {"key": "nestedSetParent", "value": {"intValue": "1697"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "130137705beaf793", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153265000", "durationNanos": "12714000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1233"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1234"}}, {"key": "nestedSetRight", "value": {"intValue": "1247"}}]}, {"spanID": "0147ab44907395d6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152728000", "durationNanos": "14767000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1521"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1522"}}, {"key": "nestedSetRight", "value": {"intValue": "1535"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "21438451bf5f5733", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153046000", "durationNanos": "10671000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "531"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "532"}}, {"key": "nestedSetRight", "value": {"intValue": "545"}}]}, {"spanID": "21c25c2aabb483f2", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152760000", "durationNanos": "13178000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "1133"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1134"}}, {"key": "nestedSetRight", "value": {"intValue": "1147"}}]}, {"spanID": "1e83a449f0ef9faf", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152217000", "durationNanos": "13175000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "890"}}, {"key": "nestedSetParent", "value": {"intValue": "889"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "891"}}]}, {"spanID": "5ce2a1e80ee1ff31", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152567000", "durationNanos": "14441000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1467"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1454"}}, {"key": "nestedSetParent", "value": {"intValue": "1453"}}]}, {"spanID": "5a064c422793982b", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152361000", "durationNanos": "12147000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "803"}}, {"key": "nestedSetParent", "value": {"intValue": "789"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "790"}}]}, {"spanID": "44d6303b6971fd3e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152505000", "durationNanos": "12996000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "959"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "946"}}, {"key": "nestedSetParent", "value": {"intValue": "945"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "02fcb49697456c61", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153144000", "durationNanos": "27604000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2142"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "2143"}}, {"key": "nestedSetParent", "value": {"intValue": "2141"}}]}, {"spanID": "362aabfa3645c909", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152326000", "durationNanos": "2870000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "217"}}, {"key": "nestedSetParent", "value": {"intValue": "203"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "204"}}]}, {"spanID": "5c00165e2dc59be6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152138000", "durationNanos": "1815000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "43"}}, {"key": "nestedSetLeft", "value": {"intValue": "44"}}, {"key": "nestedSetRight", "value": {"intValue": "57"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "2eca2a53c4fd9c3f", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152061000", "durationNanos": "13799000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1065"}}, {"key": "nestedSetParent", "value": {"intValue": "1051"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1052"}}]}, {"spanID": "48867bdfaf0abba5", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152984000", "durationNanos": "13896000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1363"}}, {"key": "nestedSetParent", "value": {"intValue": "1349"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1350"}}]}, {"spanID": "4027351a662e4bda", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152466000", "durationNanos": "16877000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "1785"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1786"}}, {"key": "nestedSetRight", "value": {"intValue": "1799"}}]}, {"spanID": "0d3e7d4a89d36222", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153265000", "durationNanos": "1757000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "149"}}, {"key": "nestedSetParent", "value": {"intValue": "135"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "136"}}]}, {"spanID": "00a4245c2eea2bb0", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152878000", "durationNanos": "11796000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "717"}}, {"key": "nestedSetParent", "value": {"intValue": "703"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "704"}}]}, {"spanID": "065f45b4e47a671e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152358000", "durationNanos": "18044000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1867"}}, {"key": "nestedSetParent", "value": {"intValue": "1853"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1854"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "5a1f44e9ae1b5a99", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153044000", "durationNanos": "17470000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2068"}}, {"key": "nestedSetRight", "value": {"intValue": "2069"}}, {"key": "nestedSetParent", "value": {"intValue": "2067"}}]}], "matched": 21}, "spanSets": [{"spans": [{"spanID": "6bcef17d8464a55e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152482000", "durationNanos": "11609000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "629"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "616"}}, {"key": "nestedSetParent", "value": {"intValue": "615"}}]}, {"spanID": "5625b83279055f66", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153035000", "durationNanos": "16170000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1698"}}, {"key": "nestedSetRight", "value": {"intValue": "1711"}}, {"key": "nestedSetParent", "value": {"intValue": "1697"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "130137705beaf793", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153265000", "durationNanos": "12714000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1233"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1234"}}, {"key": "nestedSetRight", "value": {"intValue": "1247"}}]}, {"spanID": "0147ab44907395d6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152728000", "durationNanos": "14767000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1521"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1522"}}, {"key": "nestedSetRight", "value": {"intValue": "1535"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "21438451bf5f5733", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153046000", "durationNanos": "10671000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "531"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "532"}}, {"key": "nestedSetRight", "value": {"intValue": "545"}}]}, {"spanID": "21c25c2aabb483f2", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152760000", "durationNanos": "13178000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "1133"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1134"}}, {"key": "nestedSetRight", "value": {"intValue": "1147"}}]}, {"spanID": "1e83a449f0ef9faf", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152217000", "durationNanos": "13175000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "890"}}, {"key": "nestedSetParent", "value": {"intValue": "889"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "891"}}]}, {"spanID": "5ce2a1e80ee1ff31", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152567000", "durationNanos": "14441000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1467"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1454"}}, {"key": "nestedSetParent", "value": {"intValue": "1453"}}]}, {"spanID": "5a064c422793982b", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152361000", "durationNanos": "12147000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "803"}}, {"key": "nestedSetParent", "value": {"intValue": "789"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "790"}}]}, {"spanID": "44d6303b6971fd3e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152505000", "durationNanos": "12996000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "959"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "946"}}, {"key": "nestedSetParent", "value": {"intValue": "945"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "02fcb49697456c61", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153144000", "durationNanos": "27604000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2142"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "2143"}}, {"key": "nestedSetParent", "value": {"intValue": "2141"}}]}, {"spanID": "362aabfa3645c909", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152326000", "durationNanos": "2870000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "217"}}, {"key": "nestedSetParent", "value": {"intValue": "203"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "204"}}]}, {"spanID": "5c00165e2dc59be6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152138000", "durationNanos": "1815000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "43"}}, {"key": "nestedSetLeft", "value": {"intValue": "44"}}, {"key": "nestedSetRight", "value": {"intValue": "57"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "2eca2a53c4fd9c3f", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152061000", "durationNanos": "13799000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1065"}}, {"key": "nestedSetParent", "value": {"intValue": "1051"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1052"}}]}, {"spanID": "48867bdfaf0abba5", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152984000", "durationNanos": "13896000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1363"}}, {"key": "nestedSetParent", "value": {"intValue": "1349"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1350"}}]}, {"spanID": "4027351a662e4bda", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152466000", "durationNanos": "16877000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "1785"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1786"}}, {"key": "nestedSetRight", "value": {"intValue": "1799"}}]}, {"spanID": "0d3e7d4a89d36222", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153265000", "durationNanos": "1757000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "149"}}, {"key": "nestedSetParent", "value": {"intValue": "135"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "136"}}]}, {"spanID": "00a4245c2eea2bb0", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152878000", "durationNanos": "11796000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "717"}}, {"key": "nestedSetParent", "value": {"intValue": "703"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "704"}}]}, {"spanID": "065f45b4e47a671e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376152358000", "durationNanos": "18044000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1867"}}, {"key": "nestedSetParent", "value": {"intValue": "1853"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1854"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "5a1f44e9ae1b5a99", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376153044000", "durationNanos": "17470000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2068"}}, {"key": "nestedSetRight", "value": {"intValue": "2069"}}, {"key": "nestedSetParent", "value": {"intValue": "2067"}}]}], "matched": 21}]}, {"traceID": "5a0997178c8df800", "rootServiceName": "grafana", "rootTraceName": "HTTP POST /api/ds/query", "startTimeUnixNano": "1712759376051664000", "durationMs": 72, "spanSet": {"spans": [{"spanID": "2257c5659569e654", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064544000", "durationNanos": "25323000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "2013"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2000"}}, {"key": "nestedSetParent", "value": {"intValue": "1999"}}]}, {"spanID": "78b87d5553f4438e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065100000", "durationNanos": "1679000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "141"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "142"}}, {"key": "nestedSetRight", "value": {"intValue": "155"}}]}, {"spanID": "63927588bc3f81ae", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064929000", "durationNanos": "15667000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "636"}}, {"key": "nestedSetRight", "value": {"intValue": "649"}}, {"key": "nestedSetParent", "value": {"intValue": "635"}}]}, {"spanID": "39ba6c663313a2bf", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064440000", "durationNanos": "16482000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "980"}}, {"key": "nestedSetRight", "value": {"intValue": "993"}}, {"key": "nestedSetParent", "value": {"intValue": "979"}}]}, {"spanID": "24e3895be669529c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064242000", "durationNanos": "19638000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1391"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1392"}}, {"key": "nestedSetRight", "value": {"intValue": "1405"}}]}, {"spanID": "609edc41c0760356", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064468000", "durationNanos": "19771000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1478"}}, {"key": "nestedSetRight", "value": {"intValue": "1491"}}, {"key": "nestedSetParent", "value": {"intValue": "1477"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "3d91cbccc5a73219", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064596000", "durationNanos": "22922000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1681"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1668"}}, {"key": "nestedSetParent", "value": {"intValue": "1667"}}]}, {"spanID": "0ef033ecfb934df8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064607000", "durationNanos": "27459000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2240"}}, {"key": "nestedSetRight", "value": {"intValue": "2253"}}, {"key": "nestedSetParent", "value": {"intValue": "2239"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "65b28b12d728fd3d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064782000", "durationNanos": "36133000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2325"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2326"}}, {"key": "nestedSetRight", "value": {"intValue": "2339"}}]}, {"spanID": "3a4a771d63346bfe", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065153000", "durationNanos": "52154000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2682"}}, {"key": "nestedSetParent", "value": {"intValue": "2681"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2695"}}]}, {"spanID": "42d19a90180bd16a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065242000", "durationNanos": "21402000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1582"}}, {"key": "nestedSetRight", "value": {"intValue": "1595"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1581"}}]}, {"spanID": "5fe7401296832b0e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065312000", "durationNanos": "26265000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2167"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2154"}}, {"key": "nestedSetParent", "value": {"intValue": "2153"}}]}, {"spanID": "708b598c402f9a0c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065848000", "durationNanos": "35528000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2510"}}, {"key": "nestedSetParent", "value": {"intValue": "2509"}}, {"key": "nestedSetRight", "value": {"intValue": "2523"}}]}, {"spanID": "75a248818ba30799", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064447000", "durationNanos": "16219000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "728"}}, {"key": "nestedSetRight", "value": {"intValue": "741"}}, {"key": "nestedSetParent", "value": {"intValue": "727"}}]}, {"spanID": "6998733eff497aef", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064695000", "durationNanos": "16018000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "823"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "810"}}, {"key": "nestedSetParent", "value": {"intValue": "809"}}]}, {"spanID": "68009a381c91d8b6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064437000", "durationNanos": "18287000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1140"}}, {"key": "nestedSetRight", "value": {"intValue": "1153"}}, {"key": "nestedSetParent", "value": {"intValue": "1139"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "6d6c680de45162ab", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064495000", "durationNanos": "17745000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1053"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1054"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1067"}}]}, {"spanID": "720284a05cb48021", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065231000", "durationNanos": "18232000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1330"}}, {"key": "nestedSetParent", "value": {"intValue": "1329"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1343"}}]}, {"spanID": "504c1c1f66a62f90", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064770000", "durationNanos": "3178000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "225"}}, {"key": "nestedSetParent", "value": {"intValue": "211"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "212"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "4d959b4115dad37a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064614000", "durationNanos": "4442000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "415"}}, {"key": "nestedSetLeft", "value": {"intValue": "402"}}, {"key": "nestedSetParent", "value": {"intValue": "401"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 29}, "spanSets": [{"spans": [{"spanID": "2257c5659569e654", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064544000", "durationNanos": "25323000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "2013"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2000"}}, {"key": "nestedSetParent", "value": {"intValue": "1999"}}]}, {"spanID": "78b87d5553f4438e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065100000", "durationNanos": "1679000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "141"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "142"}}, {"key": "nestedSetRight", "value": {"intValue": "155"}}]}, {"spanID": "63927588bc3f81ae", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064929000", "durationNanos": "15667000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "636"}}, {"key": "nestedSetRight", "value": {"intValue": "649"}}, {"key": "nestedSetParent", "value": {"intValue": "635"}}]}, {"spanID": "39ba6c663313a2bf", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064440000", "durationNanos": "16482000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "980"}}, {"key": "nestedSetRight", "value": {"intValue": "993"}}, {"key": "nestedSetParent", "value": {"intValue": "979"}}]}, {"spanID": "24e3895be669529c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064242000", "durationNanos": "19638000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1391"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1392"}}, {"key": "nestedSetRight", "value": {"intValue": "1405"}}]}, {"spanID": "609edc41c0760356", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064468000", "durationNanos": "19771000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1478"}}, {"key": "nestedSetRight", "value": {"intValue": "1491"}}, {"key": "nestedSetParent", "value": {"intValue": "1477"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "3d91cbccc5a73219", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064596000", "durationNanos": "22922000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "1681"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1668"}}, {"key": "nestedSetParent", "value": {"intValue": "1667"}}]}, {"spanID": "0ef033ecfb934df8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064607000", "durationNanos": "27459000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2240"}}, {"key": "nestedSetRight", "value": {"intValue": "2253"}}, {"key": "nestedSetParent", "value": {"intValue": "2239"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "65b28b12d728fd3d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064782000", "durationNanos": "36133000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2325"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2326"}}, {"key": "nestedSetRight", "value": {"intValue": "2339"}}]}, {"spanID": "3a4a771d63346bfe", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065153000", "durationNanos": "52154000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2682"}}, {"key": "nestedSetParent", "value": {"intValue": "2681"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2695"}}]}, {"spanID": "42d19a90180bd16a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065242000", "durationNanos": "21402000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "1582"}}, {"key": "nestedSetRight", "value": {"intValue": "1595"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "1581"}}]}, {"spanID": "5fe7401296832b0e", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065312000", "durationNanos": "26265000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2167"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2154"}}, {"key": "nestedSetParent", "value": {"intValue": "2153"}}]}, {"spanID": "708b598c402f9a0c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065848000", "durationNanos": "35528000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2510"}}, {"key": "nestedSetParent", "value": {"intValue": "2509"}}, {"key": "nestedSetRight", "value": {"intValue": "2523"}}]}, {"spanID": "75a248818ba30799", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064447000", "durationNanos": "16219000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "728"}}, {"key": "nestedSetRight", "value": {"intValue": "741"}}, {"key": "nestedSetParent", "value": {"intValue": "727"}}]}, {"spanID": "6998733eff497aef", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064695000", "durationNanos": "16018000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "823"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "810"}}, {"key": "nestedSetParent", "value": {"intValue": "809"}}]}, {"spanID": "68009a381c91d8b6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064437000", "durationNanos": "18287000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1140"}}, {"key": "nestedSetRight", "value": {"intValue": "1153"}}, {"key": "nestedSetParent", "value": {"intValue": "1139"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "6d6c680de45162ab", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064495000", "durationNanos": "17745000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1053"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1054"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1067"}}]}, {"spanID": "720284a05cb48021", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376065231000", "durationNanos": "18232000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1330"}}, {"key": "nestedSetParent", "value": {"intValue": "1329"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "1343"}}]}, {"spanID": "504c1c1f66a62f90", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064770000", "durationNanos": "3178000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "225"}}, {"key": "nestedSetParent", "value": {"intValue": "211"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "212"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "4d959b4115dad37a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376064614000", "durationNanos": "4442000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "415"}}, {"key": "nestedSetLeft", "value": {"intValue": "402"}}, {"key": "nestedSetParent", "value": {"intValue": "401"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 29}]}, {"traceID": "55945c92dbc91444", "rootServiceName": "grafana", "rootTraceName": "HTTP POST /api/ds/query", "startTimeUnixNano": "1712759375992840000", "durationMs": 103, "spanSet": {"spans": [{"spanID": "1d7346a984098768", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017553000", "durationNanos": "2683000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1736"}}, {"key": "nestedSetRight", "value": {"intValue": "1749"}}, {"key": "nestedSetParent", "value": {"intValue": "1735"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "3225b97bb730daa7", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017947000", "durationNanos": "61916000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2584"}}, {"key": "nestedSetRight", "value": {"intValue": "2597"}}, {"key": "nestedSetParent", "value": {"intValue": "2583"}}]}, {"spanID": "77e0fadf20ac3cf8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017792000", "durationNanos": "69426000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "724"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "737"}}, {"key": "nestedSetParent", "value": {"intValue": "723"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "11d466843f01bcad", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018116000", "durationNanos": "2500000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1500"}}, {"key": "nestedSetParent", "value": {"intValue": "1499"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1513"}}]}, {"spanID": "7033f6d203c395e0", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017905000", "durationNanos": "48912000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "2265"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2252"}}, {"key": "nestedSetParent", "value": {"intValue": "2251"}}]}, {"spanID": "5f2dfffd1eb62ca6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018680000", "durationNanos": "62279000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "479"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "466"}}, {"key": "nestedSetParent", "value": {"intValue": "465"}}]}, {"spanID": "7ed2902966f9c444", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018047000", "durationNanos": "50643000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2411"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2412"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2425"}}]}, {"spanID": "0aef16d59ff2686d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018710000", "durationNanos": "61250000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "380"}}, {"key": "nestedSetRight", "value": {"intValue": "393"}}, {"key": "nestedSetParent", "value": {"intValue": "379"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "427eb4789c9545b3", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018082000", "durationNanos": "64714000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "637"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "638"}}, {"key": "nestedSetRight", "value": {"intValue": "651"}}]}, {"spanID": "6f1473c4f650fdc4", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017476000", "durationNanos": "2394000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1156"}}, {"key": "nestedSetRight", "value": {"intValue": "1169"}}, {"key": "nestedSetParent", "value": {"intValue": "1155"}}]}, {"spanID": "73f38ae2cd90a65a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018007000", "durationNanos": "2166000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1259"}}, {"key": "nestedSetLeft", "value": {"intValue": "1260"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1273"}}]}, {"spanID": "1bde6949acd3442d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017305000", "durationNanos": "3034000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1328"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1341"}}, {"key": "nestedSetParent", "value": {"intValue": "1327"}}]}, {"spanID": "3ca426d03913fd32", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017345000", "durationNanos": "62622000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2670"}}, {"key": "nestedSetRight", "value": {"intValue": "2683"}}, {"key": "nestedSetParent", "value": {"intValue": "2669"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "43f0a9dcc7b14750", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017963000", "durationNanos": "62813000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "565"}}, {"key": "nestedSetParent", "value": {"intValue": "551"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "552"}}]}, {"spanID": "38c28f5d2955403c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017668000", "durationNanos": "69673000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "810"}}, {"key": "nestedSetParent", "value": {"intValue": "809"}}, {"key": "nestedSetRight", "value": {"intValue": "823"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "5dd8ee905b2d1125", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018167000", "durationNanos": "4907000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2007"}}, {"key": "nestedSetParent", "value": {"intValue": "1993"}}, {"key": "nestedSetLeft", "value": {"intValue": "1994"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "6a0e3fab169607b8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017911000", "durationNanos": "59565000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2515"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2516"}}, {"key": "nestedSetRight", "value": {"intValue": "2529"}}]}, {"spanID": "49b56617800b2ce5", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018103000", "durationNanos": "70522000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "896"}}, {"key": "nestedSetRight", "value": {"intValue": "909"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "895"}}]}, {"spanID": "1daff974a0839074", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017807000", "durationNanos": "43080000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2079"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2080"}}, {"key": "nestedSetRight", "value": {"intValue": "2093"}}]}, {"spanID": "7281e50bca43a2a2", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017732000", "durationNanos": "62455000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "298"}}, {"key": "nestedSetRight", "value": {"intValue": "311"}}, {"key": "nestedSetParent", "value": {"intValue": "297"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 28}, "spanSets": [{"spans": [{"spanID": "1d7346a984098768", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017553000", "durationNanos": "2683000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1736"}}, {"key": "nestedSetRight", "value": {"intValue": "1749"}}, {"key": "nestedSetParent", "value": {"intValue": "1735"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "3225b97bb730daa7", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017947000", "durationNanos": "61916000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2584"}}, {"key": "nestedSetRight", "value": {"intValue": "2597"}}, {"key": "nestedSetParent", "value": {"intValue": "2583"}}]}, {"spanID": "77e0fadf20ac3cf8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017792000", "durationNanos": "69426000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "724"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "737"}}, {"key": "nestedSetParent", "value": {"intValue": "723"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}]}, {"spanID": "11d466843f01bcad", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018116000", "durationNanos": "2500000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1500"}}, {"key": "nestedSetParent", "value": {"intValue": "1499"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1513"}}]}, {"spanID": "7033f6d203c395e0", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017905000", "durationNanos": "48912000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "2265"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "2252"}}, {"key": "nestedSetParent", "value": {"intValue": "2251"}}]}, {"spanID": "5f2dfffd1eb62ca6", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018680000", "durationNanos": "62279000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "479"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "466"}}, {"key": "nestedSetParent", "value": {"intValue": "465"}}]}, {"spanID": "7ed2902966f9c444", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018047000", "durationNanos": "50643000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2411"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2412"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetRight", "value": {"intValue": "2425"}}]}, {"spanID": "0aef16d59ff2686d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018710000", "durationNanos": "61250000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "380"}}, {"key": "nestedSetRight", "value": {"intValue": "393"}}, {"key": "nestedSetParent", "value": {"intValue": "379"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "427eb4789c9545b3", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018082000", "durationNanos": "64714000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "637"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "638"}}, {"key": "nestedSetRight", "value": {"intValue": "651"}}]}, {"spanID": "6f1473c4f650fdc4", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017476000", "durationNanos": "2394000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "1156"}}, {"key": "nestedSetRight", "value": {"intValue": "1169"}}, {"key": "nestedSetParent", "value": {"intValue": "1155"}}]}, {"spanID": "73f38ae2cd90a65a", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018007000", "durationNanos": "2166000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "1259"}}, {"key": "nestedSetLeft", "value": {"intValue": "1260"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1273"}}]}, {"spanID": "1bde6949acd3442d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017305000", "durationNanos": "3034000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "1328"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "1341"}}, {"key": "nestedSetParent", "value": {"intValue": "1327"}}]}, {"spanID": "3ca426d03913fd32", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017345000", "durationNanos": "62622000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "2670"}}, {"key": "nestedSetRight", "value": {"intValue": "2683"}}, {"key": "nestedSetParent", "value": {"intValue": "2669"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "43f0a9dcc7b14750", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017963000", "durationNanos": "62813000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "565"}}, {"key": "nestedSetParent", "value": {"intValue": "551"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "552"}}]}, {"spanID": "38c28f5d2955403c", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017668000", "durationNanos": "69673000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "810"}}, {"key": "nestedSetParent", "value": {"intValue": "809"}}, {"key": "nestedSetRight", "value": {"intValue": "823"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "5dd8ee905b2d1125", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018167000", "durationNanos": "4907000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "2007"}}, {"key": "nestedSetParent", "value": {"intValue": "1993"}}, {"key": "nestedSetLeft", "value": {"intValue": "1994"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "6a0e3fab169607b8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017911000", "durationNanos": "59565000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2515"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2516"}}, {"key": "nestedSetRight", "value": {"intValue": "2529"}}]}, {"spanID": "49b56617800b2ce5", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376018103000", "durationNanos": "70522000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "896"}}, {"key": "nestedSetRight", "value": {"intValue": "909"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "895"}}]}, {"spanID": "1daff974a0839074", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017807000", "durationNanos": "43080000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "2079"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "2080"}}, {"key": "nestedSetRight", "value": {"intValue": "2093"}}]}, {"spanID": "7281e50bca43a2a2", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759376017732000", "durationNanos": "62455000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "298"}}, {"key": "nestedSetRight", "value": {"intValue": "311"}}, {"key": "nestedSetParent", "value": {"intValue": "297"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 28}]}, {"traceID": "15073180ec1d1c0d37fc093a2be6b32", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759375087097861", "durationMs": 72, "spanSet": {"spans": [{"spanID": "145652ad9c81f8b5", "name": "HTTP GET", "startTimeUnixNano": "1712759375103611000", "durationNanos": "55179000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "145652ad9c81f8b5", "name": "HTTP GET", "startTimeUnixNano": "1712759375103611000", "durationNanos": "55179000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "f3c41fadf42deba2045f0d52ce7fa0de", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759374852975729", "durationMs": 33, "spanSet": {"spans": [{"spanID": "3cfda31cf745a920", "name": "HTTP GET", "startTimeUnixNano": "1712759374866039000", "durationNanos": "19112000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "3cfda31cf745a920", "name": "HTTP GET", "startTimeUnixNano": "1712759374866039000", "durationNanos": "19112000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "343d960afa1abb239a386abd11e90a37", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759374690004948", "durationMs": 49, "spanSet": {"spans": [{"spanID": "4232afe8db70f800", "name": "HTTP GET", "startTimeUnixNano": "1712759374711699000", "durationNanos": "26736000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "4232afe8db70f800", "name": "HTTP GET", "startTimeUnixNano": "1712759374711699000", "durationNanos": "26736000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}]}], "matched": 1}]}, {"traceID": "aa166cd275464e4d496873c1d16afa60", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759374647888698", "durationMs": 22, "spanSet": {"spans": [{"spanID": "10eed83e85302e33", "name": "HTTP GET", "startTimeUnixNano": "1712759374650353000", "durationNanos": "18072000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "10eed83e85302e33", "name": "HTTP GET", "startTimeUnixNano": "1712759374650353000", "durationNanos": "18072000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}]}, {"traceID": "415dc2d01c7771609b52d640c589338", "rootServiceName": "grafana", "rootTraceName": "alert rule execution", "startTimeUnixNano": "1712759374187510020", "durationMs": 2646, "spanSet": {"spans": [{"spanID": "625d0f7cb5fb85e8", "name": "HTTP POST", "startTimeUnixNano": "1712759375853684000", "durationNanos": "1376000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "219"}}, {"key": "nestedSetRight", "value": {"intValue": "224"}}, {"key": "nestedSetParent", "value": {"intValue": "218"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "436d467f908f69bf", "name": "HTTP POST", "startTimeUnixNano": "1712759376314689000", "durationNanos": "2371000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "nestedSetRight", "value": {"intValue": "160"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "155"}}, {"key": "nestedSetParent", "value": {"intValue": "154"}}]}, {"spanID": "3f6449952381b8e8", "name": "HTTP POST", "startTimeUnixNano": "1712759376435378000", "durationNanos": "3389000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "136"}}, {"key": "nestedSetParent", "value": {"intValue": "130"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "131"}}]}, {"spanID": "5b226ad6d79a73a9", "name": "HTTP POST", "startTimeUnixNano": "1712759376555113000", "durationNanos": "1748000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "106"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "107"}}, {"key": "nestedSetRight", "value": {"intValue": "112"}}]}, {"spanID": "2968d099eda5febc", "name": "HTTP POST", "startTimeUnixNano": "1712759376193110000", "durationNanos": "4574000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "nestedSetLeft", "value": {"intValue": "183"}}, {"key": "nestedSetRight", "value": {"intValue": "188"}}, {"key": "nestedSetParent", "value": {"intValue": "182"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "12013b521749211b", "name": "HTTP POST", "startTimeUnixNano": "1712759376632201000", "durationNanos": "1368000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "35"}}, {"key": "nestedSetRight", "value": {"intValue": "40"}}, {"key": "nestedSetParent", "value": {"intValue": "34"}}]}, {"spanID": "331d1245d8751357", "name": "HTTP POST", "startTimeUnixNano": "1712759376616252000", "durationNanos": "5866000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "83"}}, {"key": "nestedSetRight", "value": {"intValue": "88"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "82"}}]}, {"spanID": "4ebf8330a9dfe483", "name": "HTTP POST", "startTimeUnixNano": "1712759376626252000", "durationNanos": "1515000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "59"}}, {"key": "nestedSetRight", "value": {"intValue": "64"}}, {"key": "nestedSetParent", "value": {"intValue": "58"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}]}, {"spanID": "49d48ac8ea0a6347", "name": "HTTP POST", "startTimeUnixNano": "1712759376731048000", "durationNanos": "1849000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "nestedSetLeft", "value": {"intValue": "11"}}, {"key": "nestedSetRight", "value": {"intValue": "16"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "10"}}]}], "matched": 9}, "spanSets": [{"spans": [{"spanID": "625d0f7cb5fb85e8", "name": "HTTP POST", "startTimeUnixNano": "1712759375853684000", "durationNanos": "1376000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "219"}}, {"key": "nestedSetRight", "value": {"intValue": "224"}}, {"key": "nestedSetParent", "value": {"intValue": "218"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "436d467f908f69bf", "name": "HTTP POST", "startTimeUnixNano": "1712759376314689000", "durationNanos": "2371000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "nestedSetRight", "value": {"intValue": "160"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "155"}}, {"key": "nestedSetParent", "value": {"intValue": "154"}}]}, {"spanID": "3f6449952381b8e8", "name": "HTTP POST", "startTimeUnixNano": "1712759376435378000", "durationNanos": "3389000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "136"}}, {"key": "nestedSetParent", "value": {"intValue": "130"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "131"}}]}, {"spanID": "5b226ad6d79a73a9", "name": "HTTP POST", "startTimeUnixNano": "1712759376555113000", "durationNanos": "1748000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "106"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "107"}}, {"key": "nestedSetRight", "value": {"intValue": "112"}}]}, {"spanID": "2968d099eda5febc", "name": "HTTP POST", "startTimeUnixNano": "1712759376193110000", "durationNanos": "4574000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "nestedSetLeft", "value": {"intValue": "183"}}, {"key": "nestedSetRight", "value": {"intValue": "188"}}, {"key": "nestedSetParent", "value": {"intValue": "182"}}, {"key": "status", "value": {"stringValue": "error"}}]}, {"spanID": "12013b521749211b", "name": "HTTP POST", "startTimeUnixNano": "1712759376632201000", "durationNanos": "1368000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "35"}}, {"key": "nestedSetRight", "value": {"intValue": "40"}}, {"key": "nestedSetParent", "value": {"intValue": "34"}}]}, {"spanID": "331d1245d8751357", "name": "HTTP POST", "startTimeUnixNano": "1712759376616252000", "durationNanos": "5866000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "83"}}, {"key": "nestedSetRight", "value": {"intValue": "88"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "82"}}]}, {"spanID": "4ebf8330a9dfe483", "name": "HTTP POST", "startTimeUnixNano": "1712759376626252000", "durationNanos": "1515000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "59"}}, {"key": "nestedSetRight", "value": {"intValue": "64"}}, {"key": "nestedSetParent", "value": {"intValue": "58"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}]}, {"spanID": "49d48ac8ea0a6347", "name": "HTTP POST", "startTimeUnixNano": "1712759376731048000", "durationNanos": "1849000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-A"}}, {"key": "nestedSetLeft", "value": {"intValue": "11"}}, {"key": "nestedSetRight", "value": {"intValue": "16"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "10"}}]}], "matched": 9}]}, {"traceID": "3fb80663a4b48ff022a9b541e00beac8", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759374155147185", "durationMs": 23, "spanSet": {"spans": [{"spanID": "69c477e188d55ac5", "name": "HTTP GET", "startTimeUnixNano": "1712759374158107000", "durationNanos": "19408000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "69c477e188d55ac5", "name": "HTTP GET", "startTimeUnixNano": "1712759374158107000", "durationNanos": "19408000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "2f89dd964e54035ce9afd0d5fba07f98", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759373918000047", "durationMs": 23, "spanSet": {"spans": [{"spanID": "439f81f8796b81c6", "name": "HTTP GET", "startTimeUnixNano": "1712759373924085000", "durationNanos": "16174000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "439f81f8796b81c6", "name": "HTTP GET", "startTimeUnixNano": "1712759373924085000", "durationNanos": "16174000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "a29226c47bbfe5a9df13d9433ef88123", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759373916695473", "durationMs": 18, "spanSet": {"spans": [{"spanID": "31db9a0f08cbdd65", "name": "HTTP GET", "startTimeUnixNano": "1712759373919131000", "durationNanos": "14639000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "31db9a0f08cbdd65", "name": "HTTP GET", "startTimeUnixNano": "1712759373919131000", "durationNanos": "14639000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}]}], "matched": 1}]}, {"traceID": "634f48390f517b497ad33d5c426c897", "rootServiceName": "grafana", "rootTraceName": "alert rule execution", "startTimeUnixNano": "1712759373843405146", "durationMs": 59, "spanSet": {"spans": [{"spanID": "3d1ff0e6c63665e8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373879010000", "durationNanos": "1800000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "83"}}, {"key": "nestedSetRight", "value": {"intValue": "96"}}, {"key": "nestedSetParent", "value": {"intValue": "82"}}]}, {"spanID": "30b8311853206bd3", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373885425000", "durationNanos": "1605000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "182"}}, {"key": "nestedSetParent", "value": {"intValue": "168"}}, {"key": "nestedSetLeft", "value": {"intValue": "169"}}]}, {"spanID": "55c109116b6fd189", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373880018000", "durationNanos": "1641000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "316"}}, {"key": "nestedSetParent", "value": {"intValue": "302"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "303"}}]}, {"spanID": "62f07ac872318fdf", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373858770000", "durationNanos": "1888000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "572"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "559"}}, {"key": "nestedSetParent", "value": {"intValue": "558"}}]}, {"spanID": "5105d1fd0c0ef632", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373885521000", "durationNanos": "1654000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "399"}}, {"key": "nestedSetRight", "value": {"intValue": "412"}}, {"key": "nestedSetParent", "value": {"intValue": "398"}}]}, {"spanID": "7795508fd680e40d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373859163000", "durationNanos": "2119000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "656"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "657"}}, {"key": "nestedSetRight", "value": {"intValue": "670"}}]}], "matched": 6}, "spanSets": [{"spans": [{"spanID": "3d1ff0e6c63665e8", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373879010000", "durationNanos": "1800000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "83"}}, {"key": "nestedSetRight", "value": {"intValue": "96"}}, {"key": "nestedSetParent", "value": {"intValue": "82"}}]}, {"spanID": "30b8311853206bd3", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373885425000", "durationNanos": "1605000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "182"}}, {"key": "nestedSetParent", "value": {"intValue": "168"}}, {"key": "nestedSetLeft", "value": {"intValue": "169"}}]}, {"spanID": "55c109116b6fd189", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373880018000", "durationNanos": "1641000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "316"}}, {"key": "nestedSetParent", "value": {"intValue": "302"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "303"}}]}, {"spanID": "62f07ac872318fdf", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373858770000", "durationNanos": "1888000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "572"}}, {"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetLeft", "value": {"intValue": "559"}}, {"key": "nestedSetParent", "value": {"intValue": "558"}}]}, {"spanID": "5105d1fd0c0ef632", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373885521000", "durationNanos": "1654000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "399"}}, {"key": "nestedSetRight", "value": {"intValue": "412"}}, {"key": "nestedSetParent", "value": {"intValue": "398"}}]}, {"spanID": "7795508fd680e40d", "name": "Span-name-PQ<PERSON>", "startTimeUnixNano": "1712759373859163000", "durationNanos": "2119000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-D"}}, {"key": "nestedSetParent", "value": {"intValue": "656"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "657"}}, {"key": "nestedSetRight", "value": {"intValue": "670"}}]}], "matched": 6}]}, {"traceID": "53a68a16242c1708427027ccd98ec94", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759373555268028", "durationMs": 25, "spanSet": {"spans": [{"spanID": "4d7749c23cc606d5", "name": "HTTP GET", "startTimeUnixNano": "1712759373559440000", "durationNanos": "20125000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "4d7749c23cc606d5", "name": "HTTP GET", "startTimeUnixNano": "1712759373559440000", "durationNanos": "20125000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}]}], "matched": 1}]}, {"traceID": "da84d78489320e48bca277bb04323d3", "rootServiceName": "grafana", "rootTraceName": "recoded_queries.scheduled_execution", "startTimeUnixNano": "1712759373294207023", "durationMs": 3135, "spanSet": {"spans": [{"spanID": "853f76ee744f4448", "name": "Span-name-XYZ", "startTimeUnixNano": "1712759373295185329", "durationNanos": "3133752758", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "5"}}, {"key": "nestedSetParent", "value": {"intValue": "4"}}, {"key": "nestedSetRight", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-E"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "853f76ee744f4448", "name": "Span-name-XYZ", "startTimeUnixNano": "1712759373295185329", "durationNanos": "3133752758", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "5"}}, {"key": "nestedSetParent", "value": {"intValue": "4"}}, {"key": "nestedSetRight", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-E"}}]}], "matched": 1}]}, {"traceID": "290c25a3365f973349b3040e388ff3de", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759373228386817", "durationMs": 35, "spanSet": {"spans": [{"spanID": "2340eea597c814ed", "name": "HTTP GET", "startTimeUnixNano": "1712759373235292000", "durationNanos": "27609000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "2340eea597c814ed", "name": "HTTP GET", "startTimeUnixNano": "1712759373235292000", "durationNanos": "27609000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "f9b765ccd3a65f5d58b53327827674d4", "rootServiceName": "grafana", "rootTraceName": "recoded_queries.scheduled_execution", "startTimeUnixNano": "1712759373185400940", "durationMs": 217, "spanSet": {"spans": [{"spanID": "1ca3f38840b3de03", "name": "HTTP Outgoing Request", "startTimeUnixNano": "1712759373278792819", "durationNanos": "123362826", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-F"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "7"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "1ca3f38840b3de03", "name": "HTTP Outgoing Request", "startTimeUnixNano": "1712759373278792819", "durationNanos": "123362826", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-F"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "7"}}]}], "matched": 1}]}, {"traceID": "dd2cbafb9246e62c87446a9159461901", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759373114435631", "durationMs": 24, "spanSet": {"spans": [{"spanID": "2e7c6c2540a7e34a", "name": "HTTP GET", "startTimeUnixNano": "1712759373117400000", "durationNanos": "20280000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "2e7c6c2540a7e34a", "name": "HTTP GET", "startTimeUnixNano": "1712759373117400000", "durationNanos": "20280000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}]}, {"traceID": "d6ebed3e278e2f899b7bb1eb4cb0070d", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759372776359876", "durationMs": 37, "spanSet": {"spans": [{"spanID": "4ea22f0a26d3ea3b", "name": "HTTP GET", "startTimeUnixNano": "1712759372784550000", "durationNanos": "28207000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "4ea22f0a26d3ea3b", "name": "HTTP GET", "startTimeUnixNano": "1712759372784550000", "durationNanos": "28207000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}]}], "matched": 1}]}, {"traceID": "dfc54d3ec936161ec96e1f2641432be7", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759371452368945", "durationMs": 44, "spanSet": {"spans": [{"spanID": "0e60e2b7af39bb03", "name": "HTTP GET", "startTimeUnixNano": "1712759371472134000", "durationNanos": "22848000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "0e60e2b7af39bb03", "name": "HTTP GET", "startTimeUnixNano": "1712759371472134000", "durationNanos": "22848000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "801f39803c0ae5e56952a53b876ed76b", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370993056000", "durationMs": 19, "spanSet": {"spans": [{"spanID": "766bc600905e938b", "name": "HTTP GET", "startTimeUnixNano": "1712759370994587000", "durationNanos": "10841000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "766bc600905e938b", "name": "HTTP GET", "startTimeUnixNano": "1712759370994587000", "durationNanos": "10841000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "f267525cd57ba11c13b40eab0402f3c5", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370873962759", "durationMs": 16, "spanSet": {"spans": [{"spanID": "03bbb1efb2612bb2", "name": "HTTP GET", "startTimeUnixNano": "1712759370875416000", "durationNanos": "13951000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "03bbb1efb2612bb2", "name": "HTTP GET", "startTimeUnixNano": "1712759370875416000", "durationNanos": "13951000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "6eaf92d1c44a11d0072f70d21b75f591", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370681207770", "durationMs": 46, "spanSet": {"spans": [{"spanID": "148da0580566e02b", "name": "HTTP GET", "startTimeUnixNano": "1712759370695006000", "durationNanos": "13931000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "148da0580566e02b", "name": "HTTP GET", "startTimeUnixNano": "1712759370695006000", "durationNanos": "13931000", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "84bf001c491bd7dfa93e56aae63a132b", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370367609501", "durationMs": 72, "spanSet": {"spans": [{"spanID": "0eba7c72eeebf5ab", "name": "HTTP GET", "startTimeUnixNano": "1712759370385293000", "durationNanos": "36508000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "0eba7c72eeebf5ab", "name": "HTTP GET", "startTimeUnixNano": "1712759370385293000", "durationNanos": "36508000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "516b97390a8fcaf38cbae836c2839e06", "rootServiceName": "grafana", "rootTraceName": "recoded_queries.scheduled_execution", "startTimeUnixNano": "1712759370329923220", "durationMs": 130, "spanSet": {"spans": [{"spanID": "76a3bee3645cc3c7", "name": "Span-name-XYZ", "startTimeUnixNano": "1712759370330606261", "durationNanos": "129837515", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-E"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "5"}}, {"key": "nestedSetParent", "value": {"intValue": "4"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "76a3bee3645cc3c7", "name": "Span-name-XYZ", "startTimeUnixNano": "1712759370330606261", "durationNanos": "129837515", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-E"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "5"}}, {"key": "nestedSetParent", "value": {"intValue": "4"}}]}], "matched": 1}]}, {"traceID": "37d09d86b2229f62c89ce22aecc485ec", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370291513303", "durationMs": 20, "spanSet": {"spans": [{"spanID": "5fe32fec891a462e", "name": "HTTP GET", "startTimeUnixNano": "1712759370295959000", "durationNanos": "15162000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "5fe32fec891a462e", "name": "HTTP GET", "startTimeUnixNano": "1712759370295959000", "durationNanos": "15162000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "77f99c054dc3014f9290b746c1ca69a1", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370260395416", "durationMs": 22, "spanSet": {"spans": [{"spanID": "02012f12542dbc1e", "name": "HTTP GET", "startTimeUnixNano": "1712759370267828000", "durationNanos": "14165000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "02012f12542dbc1e", "name": "HTTP GET", "startTimeUnixNano": "1712759370267828000", "durationNanos": "14165000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}]}], "matched": 1}]}, {"traceID": "884e343cc842d2cc930c145fcbe5284a", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370257652949", "durationMs": 26, "spanSet": {"spans": [{"spanID": "47cda8edcdea9053", "name": "HTTP GET", "startTimeUnixNano": "1712759370264289000", "durationNanos": "18661000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "47cda8edcdea9053", "name": "HTTP GET", "startTimeUnixNano": "1712759370264289000", "durationNanos": "18661000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "bc29201b882b064aaa932cef123c8371", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370180774945", "durationMs": 32, "spanSet": {"spans": [{"spanID": "6f8322540e990f6a", "name": "HTTP GET", "startTimeUnixNano": "1712759370188210000", "durationNanos": "22897000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "6f8322540e990f6a", "name": "HTTP GET", "startTimeUnixNano": "1712759370188210000", "durationNanos": "22897000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}]}], "matched": 1}]}, {"traceID": "ed7182f7c5da01e93a194b675cbdc8ca", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759370105945264", "durationMs": 63, "spanSet": {"spans": [{"spanID": "533c359058eea16e", "name": "HTTP GET", "startTimeUnixNano": "1712759370107990000", "durationNanos": "60777000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "533c359058eea16e", "name": "HTTP GET", "startTimeUnixNano": "1712759370107990000", "durationNanos": "60777000", "attributes": [{"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "6f46c25b180a43dba24e6d74982d5d13", "rootServiceName": "grafana", "rootTraceName": "recoded_queries.scheduled_execution", "startTimeUnixNano": "1712759369944611214", "durationMs": 129, "spanSet": {"spans": [{"spanID": "006784d4270aaed3", "name": "Span-name-XYZ", "startTimeUnixNano": "1712759369945273725", "durationNanos": "128392840", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "6"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "4"}}, {"key": "nestedSetLeft", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-E"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "006784d4270aaed3", "name": "Span-name-XYZ", "startTimeUnixNano": "1712759369945273725", "durationNanos": "128392840", "attributes": [{"key": "nestedSetRight", "value": {"intValue": "6"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "4"}}, {"key": "nestedSetLeft", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-E"}}]}], "matched": 1}]}, {"traceID": "1bb4ec63e2efbdf7f7d044e4806fb64a", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759369810648098", "durationMs": 156, "spanSet": {"spans": [{"spanID": "34787502df8ff0dd", "name": "HTTP GET", "startTimeUnixNano": "1712759369880130000", "durationNanos": "26232000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "34787502df8ff0dd", "name": "HTTP GET", "startTimeUnixNano": "1712759369880130000", "durationNanos": "26232000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "8446ed496500630830177a7c64f708a6", "rootServiceName": "grafana", "rootTraceName": "alert rule execution", "startTimeUnixNano": "1712759369754873623", "durationMs": 9, "spanSet": {"spans": [{"spanID": "7407b222c755d391", "name": "split_by_interval_and_results_cache", "startTimeUnixNano": "1712759369759107000", "durationNanos": "35000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "16"}}, {"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "nestedSetParent", "value": {"intValue": "14"}}, {"key": "nestedSetLeft", "value": {"intValue": "15"}}]}, {"spanID": "29cf41a1c90a6c02", "name": "step_align", "startTimeUnixNano": "1712759369759100000", "durationNanos": "56000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "14"}}, {"key": "nestedSetRight", "value": {"intValue": "17"}}, {"key": "nestedSetParent", "value": {"intValue": "13"}}]}, {"spanID": "030fafd4fd575675", "name": "HTTP POST", "startTimeUnixNano": "1712759369757776000", "durationNanos": "1736000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "10"}}, {"key": "nestedSetLeft", "value": {"intValue": "11"}}, {"key": "nestedSetRight", "value": {"intValue": "22"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}]}], "matched": 3}, "spanSets": [{"spans": [{"spanID": "7407b222c755d391", "name": "split_by_interval_and_results_cache", "startTimeUnixNano": "1712759369759107000", "durationNanos": "35000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "16"}}, {"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "nestedSetParent", "value": {"intValue": "14"}}, {"key": "nestedSetLeft", "value": {"intValue": "15"}}]}, {"spanID": "29cf41a1c90a6c02", "name": "step_align", "startTimeUnixNano": "1712759369759100000", "durationNanos": "56000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-B"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "14"}}, {"key": "nestedSetRight", "value": {"intValue": "17"}}, {"key": "nestedSetParent", "value": {"intValue": "13"}}]}, {"spanID": "030fafd4fd575675", "name": "HTTP POST", "startTimeUnixNano": "1712759369757776000", "durationNanos": "1736000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "10"}}, {"key": "nestedSetLeft", "value": {"intValue": "11"}}, {"key": "nestedSetRight", "value": {"intValue": "22"}}, {"key": "service.name", "value": {"stringValue": "Service-A"}}]}], "matched": 3}]}, {"traceID": "92fbd585b47905dc9b2d8d4e5eed2f5f", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759369464023522", "durationMs": 27, "spanSet": {"spans": [{"spanID": "0b3d18ecfeb91b5d", "name": "HTTP GET", "startTimeUnixNano": "1712759369467217000", "durationNanos": "23085000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "0b3d18ecfeb91b5d", "name": "HTTP GET", "startTimeUnixNano": "1712759369467217000", "durationNanos": "23085000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}]}], "matched": 1}]}, {"traceID": "2847b7231fdc4712474b947cc4370ad3", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759368594339970", "durationMs": 38, "spanSet": {"spans": [{"spanID": "457725122d5940ac", "name": "HTTP GET", "startTimeUnixNano": "1712759368601700000", "durationNanos": "30157000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "457725122d5940ac", "name": "HTTP GET", "startTimeUnixNano": "1712759368601700000", "durationNanos": "30157000", "attributes": [{"key": "nestedSetParent", "value": {"intValue": "9"}}, {"key": "nestedSetLeft", "value": {"intValue": "10"}}, {"key": "nestedSetRight", "value": {"intValue": "13"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}, {"traceID": "af1b3658be2f998b3caf8aed6a8fb00f", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759368583302600", "durationMs": 28, "spanSet": {"spans": [{"spanID": "14275f456d8986d2", "name": "HTTP GET", "startTimeUnixNano": "1712759368586185000", "durationNanos": "24053000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "14275f456d8986d2", "name": "HTTP GET", "startTimeUnixNano": "1712759368586185000", "durationNanos": "24053000", "attributes": [{"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}]}], "matched": 1}]}, {"traceID": "f0a701f1885a5dca64f5417357bd3c9d", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759368582993566", "durationMs": 21, "spanSet": {"spans": [{"spanID": "5f1239475444908b", "name": "HTTP GET", "startTimeUnixNano": "1712759368585370000", "durationNanos": "17122000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "5f1239475444908b", "name": "HTTP GET", "startTimeUnixNano": "1712759368585370000", "durationNanos": "17122000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "status", "value": {"stringValue": "error"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}]}], "matched": 1}]}, {"traceID": "b3d8dae0fa3af9f40685e06566940c62", "rootServiceName": "grafana", "rootTraceName": "datasource reverse proxy", "startTimeUnixNano": "1712759368528525523", "durationMs": 24, "spanSet": {"spans": [{"spanID": "2ba1093e815b898a", "name": "HTTP GET", "startTimeUnixNano": "1712759368532718000", "durationNanos": "18805000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}, "spanSets": [{"spans": [{"spanID": "2ba1093e815b898a", "name": "HTTP GET", "startTimeUnixNano": "1712759368532718000", "durationNanos": "18805000", "attributes": [{"key": "service.name", "value": {"stringValue": "Service-C"}}, {"key": "nestedSetLeft", "value": {"intValue": "6"}}, {"key": "nestedSetRight", "value": {"intValue": "9"}}, {"key": "nestedSetParent", "value": {"intValue": "5"}}, {"key": "status", "value": {"stringValue": "error"}}]}], "matched": 1}]}], "metrics": {"inspectedBytes": 15905329490, "completedJobs": 1, "totalJobs": 1}}