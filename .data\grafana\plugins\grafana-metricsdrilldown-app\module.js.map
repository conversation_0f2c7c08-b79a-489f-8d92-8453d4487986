{"version": 3, "file": "module.js", "mappings": ";6LACIA,EADAC,ECAAC,EACAC,E,+BCDG,IAAIC,E,oCACX,SAAWA,GACPA,EAAgB,MAAI,QACpBA,EAAgB,MAAI,QACpBA,EAAe,KAAI,OACnBA,EAAc,IAAI,MAClBA,EAAe,KAAI,OACnBA,EAAgB,MAAI,OACvB,CAPD,CAOGA,IAAaA,EAAW,CAAC,IACrB,MAAMC,EAAkBD,EAASE,IAC3BC,EAAe,CACxBH,EAASI,MACTJ,EAASK,MACTL,EAASM,KACTN,EAASE,IACTF,EAASO,KACTP,EAASQ,M,gFCdN,MAAMC,EAAYC,EAAAA,GACZC,EAAkB,MAAMD,EAAAA,KAExBE,EAAS,CACpBC,KAAM,GACNC,MAAO,QACPC,UAAW,Y,8BCRN,IAAIC,E,6BACX,SAAWA,GACPA,EAA6B,UAAI,YACjCA,EAAuB,IAAI,MAC3BA,EAA+B,YAAI,cACnCA,EAAyB,MAAI,QAC7BA,EAAyB,MAAI,OAChC,CAND,CAMGA,IAAsBA,EAAoB,CAAC,IACvC,MAAMC,EAA6B,CACtC,CAACD,EAAkBE,WAAY,aAC/B,CAACF,EAAkBd,KAAM,OACzB,CAACc,EAAkBG,aAAc,eACjC,CAACH,EAAkBZ,OAAQ,SAC3B,CAACY,EAAkBI,OAAQ,UCbxB,SAASC,EAASC,EAAOC,GAC5B,cAAcD,IAAUC,CAC5B,CACO,SAASC,EAAWF,EAAOC,GAC9B,OAAOE,OAAOC,UAAUC,SAASC,KAAKN,KAAW,WAAWC,IAChE,CACO,SAASM,EAAaP,EAAOQ,GAChC,IACI,OAAOR,aAAiBQ,CAC5B,CACA,MAAOC,GACH,OAAO,CACX,CACJ,CACO,MACMC,EAAWV,GAAUD,EAASC,EAAO,QACrCW,EAAaX,GAAUD,EAASC,EAAO,UACvCY,EAAaZ,GAAWD,EAASC,EAAO,YAAca,MAAMb,IAAWD,EAASC,EAAO,UAIvFc,EAAad,IAAWU,EAAOV,IAAUD,EAASC,EAAO,UACzDe,EAAef,GAAUD,EAASC,EAAO,YACzCgB,EAAYhB,GAAUE,EAAWF,EAAO,SAIxCiB,EAAkC,oBAAVC,MAExBC,EAAkC,oBAAVC,MACxBC,EAAYrB,GAAUmB,GAAkBZ,EAAaP,EAAOoB,OAYlE,SAASE,EAAQtB,GACpB,OAAa,MAATA,IAGAgB,EAAQhB,IAAUW,EAASX,GACH,IAAjBA,EAAMuB,SAEbT,EAASd,IAC4B,IAA9BG,OAAOqB,KAAKxB,GAAOuB,OAGlC,CCxCO,SAASE,EAAsBC,EAAO,CAAC,GAC1C,OAAOC,KAAKC,UAAUF,QAAmCA,EAAO,CAAC,EAb9D,WACH,MAAMG,EAAY,IAAIC,QACtB,OAAO,SAAUC,EAAM/B,GACnB,GAAIc,EAASd,IAAoB,OAAVA,EAAgB,CACnC,GAAI6B,EAAUG,IAAIhC,GACd,OAAO,KAEX6B,EAAUI,IAAIjC,EAClB,CACA,OAAOA,CACX,CACJ,CAEwEkC,GACxE,CACO,SAASC,EAAsBC,EAAM,CAAC,GACzC,MAAMC,EAAI,CAAC,EACX,IAAK,MAAOC,EAAKtC,KAAUG,OAAOoC,QAAQH,GACtCC,EAAEC,GAAOxB,EAASd,IAAoB,OAAVA,EAAiByB,EAAsBzB,GAASwC,OAAOxC,GAEvF,OAAOqC,CACX,CCtBO,SAASI,IACZ,OAAOC,KAAKC,KAChB,CACO,SAASC,IACZ,OAAO,IAAIF,MAAOG,aACtB,CACO,SAASC,EAAqB9C,GACjC,OAAO,IAAI0C,KAAK1C,GAAO6C,aAC3B,CCLO,SAASE,EAAUC,EAAGC,GACzB,GAAID,IAAMC,EACN,OAAO,EAGX,GAAIlD,EAASiD,EAAG,WAAanC,MAAMmC,GAC/B,OAAOjD,EAASkD,EAAG,WAAapC,MAAMoC,GAE1C,MAAMC,EAAWlC,EAAQgC,GACnBG,EAAWnC,EAAQiC,GACzB,GAAIC,IAAaC,EACb,OAAO,EAEX,GAAID,GAAYC,EAAU,CACtB,MAAM5B,EAASyB,EAAEzB,OACjB,GAAIA,IAAW0B,EAAE1B,OACb,OAAO,EAEX,IAAK,IAAI6B,EAAM7B,EAAkB,GAAV6B,KACnB,IAAKL,EAAUC,EAAEI,GAAMH,EAAEG,IACrB,OAAO,EAGf,OAAO,CACX,CACA,MAAMC,EAAYvC,EAASkC,GACrBM,EAAYxC,EAASmC,GAC3B,GAAII,IAAcC,EACd,OAAO,EAEX,GAAIN,GAAKC,GAAKI,GAAaC,EAAW,CAClC,MAAMC,EAAQpD,OAAOqB,KAAKwB,GACpBQ,EAAQrD,OAAOqB,KAAKyB,GAG1B,GAFgBM,EAAMhC,SACNiC,EAAMjC,OAElB,OAAO,EAEX,IAAK,IAAIkC,KAAQF,EACb,IAAKC,EAAME,SAASD,GAChB,OAAO,EAGf,IAAK,IAAIA,KAAQF,EACb,IAAKR,EAAUC,EAAES,GAAOR,EAAEQ,IACtB,OAAO,EAGf,OAAO,CACX,CACA,OAAO,CACX,CCrDO,MAAME,EAAuB,QACvBC,EAA8BC,GAChCA,EACFC,KAAKC,GACFjD,EAASiD,GACFtC,EAAsBsC,GAE1BvB,OAAOuB,KAEbC,KAAK,KCLd,IAAIC,EACG,SAASC,EAAwBC,EAAmBC,EAAgBC,EAAQC,EAAOC,EAAYC,GAClG,IAAIC,EACJL,EAAeM,MAAM,+BACrB,IAAIC,EAAc,KAClBV,EAAqD,QAAjCQ,EAAKJ,EAAOO,uBAAoC,IAAPH,EAAgBA,EAAKR,EAClF,MAAMY,EAA0BC,IAC5BV,EAAeM,MAAM,8BACrBT,EAAmBa,QAAiEA,EAAsBb,CAAgB,GAGxH,aAAEc,EAAe,IAAOV,EA0C9B,OADAQ,EAAuBR,EAAOO,iBACvB,CACHC,yBACAG,oBA7CwB,IAAMf,EA8C9BgB,UA5Cc,CAACC,GAASC,aAAYC,cAAanF,OAAMoF,UAASC,cAAaC,wBAAyB,CAAC,KACvG,GA6DR,SAAwBR,EAAcG,GAClC,MAAM,QAAEM,EAAO,KAAEC,EAAI,MAAEC,GAAUR,EACjC,OChF8BS,EDgFLZ,EChFea,EDgFDJ,EAAU,IAAMC,EAAO,IAAMC,EC/E7DC,EAASE,MAAMC,GACXnF,EAASmF,GAAWF,EAAIlC,SAASoC,KAAaF,EAAIG,MAAMD,KAFhE,IAA2BH,EAAUC,CDiF5C,CAhEYI,CAAejB,EAAcG,GAC7B,OAEJ,MAAMe,EAAO,CACTC,KAAM5B,EAAMtE,MACZmG,QAAS,CACLlG,KAAMA,GAAQiF,EAAMO,MAAQ9B,EAC5B3D,MAAOkF,EAAMM,QACbY,UAAWb,EAAuBzC,EAAqByC,GAAwB3C,IAC/EyD,MAAOf,EACD,CACEgB,SAAUhB,EAAYiB,QACtBC,QAASlB,EAAYmB,QAEvBjC,EAAUkC,kBAChBrB,QAASlD,EAAsBhC,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGC,EAAW1B,IAAUG,QAAyCA,EAAU,CAAC,KAE5IpF,KAAMP,EAAkBE,YAGxBwF,OADJA,EAAcA,QAAiDA,EAAeF,EAAMQ,MAAQzB,aAA2D,EAASA,EAAiBiB,GAAO2B,YAASC,QAC5I,EAAS1B,EAAY7D,UACtE0E,EAAKE,QAAQY,WAAa,CACtBF,OAAQzB,IAGhB,MAAM4B,EAAiB,CACnB/G,KAAMgG,EAAKE,QAAQlG,KACnBD,MAAOiG,EAAKE,QAAQnG,MACpBiH,WAAYhB,EAAKE,QAAQY,WACzB1B,QAASY,EAAKE,QAAQd,SAErBF,IAAcd,EAAO6C,QAAWxG,EAAOiE,KAAgB5B,EAAUiE,EAAgBrC,IAItFA,EAAcqC,EACd5C,EAAeM,MAAM,sBAAuBuB,GAC5C1B,EAAW4C,QAAQlB,IALf7B,EAAeM,MAAM,+DAAgEuB,EAAKE,QAKtE,EAQhC,CACA,SAASS,EAAW1B,GAChB,IAAIkC,EAAQlC,EAAMkC,MAYlB,OAXI/F,EAAQ+F,GACRA,EAAQlC,EAAMkC,MAAM/G,WAIL,OAAV+G,IAAmBtG,EAASoE,EAAMkC,QAAUpG,EAAQkE,EAAMkC,QAC/DA,EAAQ3F,EAAsByD,EAAMkC,OAEtB,MAATA,IACLA,EAAQlC,EAAMkC,MAAM/G,YAER,MAAT+G,EAAgB,CAAC,EAAI,CAAEA,QAClC,C,cE9EO,MAAMC,EAA4BxD,GAASA,EAC7CC,KAAKC,IACN,IACI,OAAOvB,OAAOuB,EAClB,CACA,MAAOtD,GACH,MAAO,EACX,KAECuD,KAAK,KCHH,SAASsD,EAAcC,EAAkBnD,EAAgBC,EAAQC,EAAOC,GAC3EH,EAAeM,MAAM,oBACrB,MAAMF,ECPH,SAA6BL,EAAmBC,EAAgBoD,EAASlD,EAAOC,GAEnF,IAAIkD,EAiCJ,OAlCArD,EAAeM,MAAM,2BAkCd,CACHgD,QAHY,IAAMD,EAIlBf,gBA3BoB,KACpB,MAAMiB,EAAMF,aAAmC,EAASA,EAAKpB,MAAMuB,eAAeH,EAAKpC,QAAQwC,UAC/F,OAAQF,EAEF,CACErB,SAAUqB,EAAIpB,QACdC,QAASmB,EAAIlB,aAHfK,CAID,EAqBLgB,SAnCa,CAACzB,EAAOhB,KACrBjB,EAAeM,MAAM,8BACrB+C,EAAO,CACHpB,QACAhB,UACH,EA+BD0C,kBALsB,MAAQN,EAM9BO,WArBgB7B,IAChB,IACI,MAAMF,EAAO,CACThG,KAAMP,EAAkBZ,MACxBqH,UACAD,KAAM5B,EAAMtE,OAEhBoE,EAAeM,MAAM,kBAAmBuB,GACxC1B,EAAW4C,QAAQlB,EACvB,CACA,MAAOxF,GACH2D,EAAec,MAAM,wBAAyBzE,EAClD,GAWR,CDnCsBwH,CAAoBV,EAAkBnD,EAAgBC,EAAQC,EAAOC,GACvF,OAAOpE,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGnC,GAAYN,EAAwBqD,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYC,IER7L,SAA2BL,EAAmBC,EAAgBoD,EAASlD,GAE1E,IAAI4D,EACAC,EACAC,EACAC,EAJJjE,EAAeM,MAAM,yBAKrB,MAAM4D,EAAWC,IACTJ,GACA7D,EAAMkE,OAAOL,GAEjBA,EAAW,CACPI,QAEJjE,EAAMrC,IAAIkG,EAAS,EAEjBM,EAAa,CAACC,EAASC,KACzB,IAAIlE,EACJ,MAAMmE,EAAeD,aAAyC,EAASA,EAAQE,UACzEA,EAAYD,EACZ,CACEC,UAAW1I,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAA4F,QAAxFlC,EAAKyD,aAAiD,EAASA,EAAYQ,eAA4B,IAAPjE,OAAgB,EAASA,EAAGoE,WAAYD,IAEtL,CAAC,EACHV,GACA5D,EAAMkE,OAAON,GAEjBA,EAAc,CACVQ,QAASvI,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAIrF,EAAQoH,QAAW5B,EAAY4B,GAAWG,IAExFvE,EAAMrC,IAAIiG,EAAY,EAEpBY,EAAa,IAAMxE,EAAMtE,MAAM0I,QA+B/BK,EAAU,IAAMzE,EAAMtE,MAAMgJ,KAClC,MAAO,CACHV,UACAW,UAAWX,EACXG,aACAS,aAAcT,EACdK,aACAK,QArCY,CAACC,EAAMT,KACnB,IAAIlE,EAIJ,IAHIkE,aAAyC,EAASA,EAAQE,YAC1DJ,EAAWK,IAAc,CAAED,UAAWF,EAAQE,aAEgC,QAA5EpE,EAAK2D,aAA2C,EAASA,EAASgB,YAAyB,IAAP3E,OAAgB,EAASA,EAAGgB,SAAW2D,aAAmC,EAASA,EAAK3D,MAC9K,OAEJ,MAAM4D,EAAejB,EACrBA,EAAW,CACPgB,QAEJ9E,EAAMrC,IAAImG,GACNiB,GACA/E,EAAMkE,OAAOa,EACjB,EAuBAC,QArBY,IAAMhF,EAAMtE,MAAMoJ,KAsB9BG,QArBaP,IACb,IAAIvE,EACJ,MAAM+E,EAAW7I,EAASqI,GACpB7I,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAiF,QAA5ElC,EAAK4D,aAA2C,EAASA,EAASW,YAAyB,IAAPvE,EAAgBA,EAAKsE,KAAa,CAAEU,GAAIT,IAAUA,EAC1KX,GACA/D,EAAMkE,OAAOH,GAEjBA,EAAW,CACPW,KAAMQ,GAEVlF,EAAMrC,IAAIoG,EAAS,EAYnBU,UAER,CFlEiNW,CAAkBnC,EAAkBnD,EAAgBC,EAAQC,IGLtQ,SAA2BH,EAAmBC,EAAgBC,EAAQC,EAAOC,EAAYC,GAC5F,IAAIC,EACJL,EAAeM,MAAM,yBACrB,IAAIC,EAAc,KAClB,MAAMgF,EAAwD,QAAnClF,EAAKJ,EAAOsF,yBAAsC,IAAPlF,EAAgBA,EAAK4C,EAoC3F,MAAO,CACHuC,QApCY,CAAC/F,GAAQwB,UAASwE,QAAO1E,aAAYG,cAAaC,wBAAyB,CAAC,KACxF,IACI,MAAMU,EAAO,CACThG,KAAMP,EAAkBd,IACxBuH,QAAS,CACLX,QAASmE,EAAkB9F,GAC3BgG,MAAOA,QAAqCA,EAAQ,KACpDxE,QAASlD,EAAsBkD,GAC/Be,UAAWb,EAAuBzC,EAAqByC,GAAwB3C,IAC/EyD,MAAOf,EACD,CACEgB,SAAUhB,EAAYiB,QACtBC,QAASlB,EAAYmB,QAEvBjC,EAAUkC,mBAEpBR,KAAM5B,EAAMtE,OAEVgH,EAAiB,CACnBxB,QAASS,EAAKE,QAAQX,QACtBqE,MAAO5D,EAAKE,QAAQ0D,MACpBxE,QAASY,EAAKE,QAAQd,SAE1B,IAAKF,GAAcd,EAAO6C,SAAWxG,EAAOiE,IAAgB5B,EAAUiE,EAAgBrC,GAElF,YADAP,EAAeM,MAAM,6DAA8DuB,EAAKE,SAG5FxB,EAAcqC,EACd5C,EAAeM,MAAM,gBAAiBuB,GACtC1B,EAAW4C,QAAQlB,EACvB,CACA,MAAOxF,GACH2D,EAAec,MAAM,sBAAuBzE,EAChD,GAKR,CHtCkSqJ,CAAkBvC,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYC,IIN1W,SAAmCL,EAAmBC,EAAgBC,EAAQC,EAAOC,EAAYC,GACpGJ,EAAeM,MAAM,iCACrB,IAAIC,EAAc,KA8BlB,MAAO,CACHoF,gBA9BoB,CAAC5D,GAAWhB,aAAYE,UAASC,cAAaC,wBAAyB,CAAC,KAC5F,IACI,MAAMU,EAAO,CACThG,KAAMP,EAAkBG,YACxBsG,QAAShG,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGR,GAAU,CAAEE,MAAOf,EAClD,CACEgB,SAAUhB,EAAYiB,QACtBC,QAASlB,EAAYmB,QAEvBjC,EAAUkC,kBAAmBN,UAAWb,EAAuBzC,EAAqByC,GAAwB3C,IAAuByC,QAASlD,EAAsBkD,KAC5Ka,KAAM5B,EAAMtE,OAEVgH,EAAiB,CACnB/G,KAAMgG,EAAKE,QAAQlG,KACnB+J,OAAQ/D,EAAKE,QAAQ6D,OACrB3E,QAASY,EAAKE,QAAQd,SAE1B,IAAKF,GAAcd,EAAO6C,SAAWxG,EAAOiE,IAAgB5B,EAAUiE,EAAgBrC,GAElF,YADAP,EAAeM,MAAM,qEAAsEuB,EAAKE,SAGpGxB,EAAcqC,EACd5C,EAAeM,MAAM,wBAAyBuB,GAC9C1B,EAAW4C,QAAQlB,EACvB,CACA,MAAOxF,GACH2D,EAAec,MAAM,8BAA+BzE,EACxD,GAKR,CJ7B8XwJ,CAA0B1C,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYC,IKN9c,SAA6BL,EAAmBC,EAAgBC,EAAQC,EAAOC,EAAYC,GAC9F,IAAIG,EAAc,KAoClB,MAAO,CACHuF,UApCc,CAACzE,EAAM0E,EAAYC,GAAUjF,aAAYG,cAAaC,wBAAyB,CAAC,KAC9F,IACI,MAAMU,EAAO,CACTC,KAAM5B,EAAMtE,MACZmG,QAAS,CACLV,OACA2E,OAAQA,QAAuCA,EAAS/F,EAAOgG,YAC/DF,WAAYhI,EAAsBgI,GAClC/D,UAAWb,EAAuBzC,EAAqByC,GAAwB3C,IAC/EyD,MAAOf,EACD,CACEgB,SAAUhB,EAAYiB,QACtBC,QAASlB,EAAYmB,QAEvBjC,EAAUkC,mBAEpBzG,KAAMP,EAAkBI,OAEtBkH,EAAiB,CACnBvB,KAAMQ,EAAKE,QAAQV,KACnB0E,WAAYlE,EAAKE,QAAQgE,WACzBC,OAAQnE,EAAKE,QAAQiE,QAEzB,IAAKjF,GAAcd,EAAO6C,SAAWxG,EAAOiE,IAAgB5B,EAAUiE,EAAgBrC,GAElF,YADAP,EAAeM,MAAM,+DAAgEuB,EAAKE,SAG9FxB,EAAcqC,EACd5C,EAAeM,MAAM,kBAAmBuB,GACxC1B,EAAW4C,QAAQlB,EACvB,CACA,MAAOxF,GACH2D,EAAec,MAAM,sBAAuBzE,EAChD,GAKR,CLlCke6J,CAAoB/C,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYC,GACnjB,CMVO,SAAS+F,IAAS,CCClB,IAAIC,GACX,SAAWA,GACPA,EAAoBA,EAAyB,IAAI,GAAK,MACtDA,EAAoBA,EAA2B,MAAI,GAAK,QACxDA,EAAoBA,EAA0B,KAAI,GAAK,OACvDA,EAAoBA,EAA0B,KAAI,GAAK,OACvDA,EAAoBA,EAA6B,QAAI,GAAK,SAC7D,CAND,CAMGA,IAAwBA,EAAsB,CAAC,IAC3C,MACMC,EAAwB,CACjC/F,MAAO6F,EACPrF,MAAOqF,EACPG,KAAMH,EACNI,OALuC,OAMvCC,KAAML,GAEGM,EAA6BL,EAAoBtL,MCjBjD4L,EAA0B3K,OAAOwG,OAAO,CAAC,EAAGoE,SCGlD,SAASC,EAAqBzD,EAAmBuD,EAAyBG,EAAsBJ,GACnG,MAAMzG,EAAiBqG,EA2BvB,OA1BIQ,EAAsBT,EAAoBU,MAC1C9G,EAAec,MACX+F,GAAuBT,EAAoBtL,MACrC,YAAa2E,GACX0D,EAAiBrC,MAAM,GAAGd,EAAeuG,cAAe9G,EAC5D,EACE0G,EACVnG,EAAewG,KACXK,GAAuBT,EAAoBvL,KACrC,YAAa4E,GACX0D,EAAiBqD,KAAK,GAAGxG,EAAeuG,cAAe9G,EAC3D,EACE0G,EACVnG,EAAesG,KACXO,GAAuBT,EAAoBxL,KACrC,YAAa6E,GACX0D,EAAiBmD,KAAK,GAAGtG,EAAeuG,cAAe9G,EAC3D,EACE0G,EACVnG,EAAeM,MACXuG,GAAuBT,EAAoBW,QACrC,YAAatH,GACX0D,EAAiB7C,MAAM,GAAGN,EAAeuG,cAAe9G,EAC5D,EACE0G,GAEPnG,CACX,CC9BO,IAAIA,EAAiBqG,EACrB,SAASW,EAAyB7D,EAAkBlD,GAEvD,OADAD,EAAiB4G,EAAqBzD,EAAkBlD,EAAO4G,qBACxD7G,CACX,CCLO,MAAM,EAAsC,oBAAfiH,WAC9BA,gBACkB,IAAX,EAAAC,EACH,EAAAA,EACgB,oBAATC,KACHA,UACAzE,ECNP,MAAM0E,EAAU,SCDhB,MAAM,EAA0B,gBCEhC,IAAIC,EAAO,CAAC,EACZ,SAASC,EAAanE,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYoH,EAAKC,GAe3F,OAdAxH,EAAeM,MAAM,qBACrB+G,EAAO,CACHE,MACAtH,SACAuH,mBACAxH,iBACAE,QACAuH,MAAOtH,EAAWsH,MAClBtH,aACAgD,mBACAuE,QAASvH,EAAWuH,SCTrB,SAAuCL,GACrCA,EAAKpH,OAAO0H,QAUbN,EAAKrH,eAAeM,MAAM,iEAT1B+G,EAAKrH,eAAeM,MAAM,uDAC1BvE,OAAO6L,eAAe,EAAc,EAAyB,CACzDC,cAAc,EACdC,YAAY,EACZC,UAAU,EACVnM,MAAOyL,IAMnB,CDFIW,CAA8BX,GEf3B,SAA+BA,GAClC,GAAKA,EAAKpH,OAAOgI,sBAabZ,EAAKrH,eAAeM,MAAM,qEAbU,CAEpC,GADA+G,EAAKrH,eAAeM,MAAM,gEAAgE+G,EAAKpH,OAAOiI,wBAClGb,EAAKpH,OAAOiI,mBAAmB,EAE/B,YADAb,EAAKrH,eAAewG,KAAK,4CAA4Ca,EAAKpH,OAAOiI,gIAGrFnM,OAAO6L,eAAe,EAAcP,EAAKpH,OAAOiI,gBAAiB,CAC7DL,cAAc,EACdE,UAAU,EACVnM,MAAOyL,GAEf,CAIJ,CFAIc,CAAsBd,GACfA,CACX,CGjBO,MAAMe,EACT,WAAAC,CAAYC,EAAQ/D,GAChB,IAAIlE,EAAIkI,EACRC,KAAKC,aAAe,GACpBD,KAAKE,UAA2F,QAA9ErI,EAAKkE,aAAyC,EAASA,EAAQmE,iBAA8B,IAAPrI,EAAgBA,EAL/F,GAMzBmI,KAAKG,YAA+F,QAAhFJ,EAAKhE,aAAyC,EAASA,EAAQoE,mBAAgC,IAAPJ,EAAgBA,EAPpG,IAQxBC,KAAKI,QAAUrE,aAAyC,EAASA,EAAQqE,UAAW,EACpFJ,KAAKF,OAASA,EACdE,KAAKK,eAAiB,EACjBL,KAAKI,QACNJ,KAAKM,QAITC,SAASC,iBAAiB,oBAAoB,KACT,WAA7BD,SAASE,iBACTT,KAAKU,OACT,GAER,CACA,OAAAC,CAAQtH,GACA2G,KAAKI,SAGTJ,KAAKC,aAAaW,KAAKvH,GACnB2G,KAAKC,aAAatL,QAAUqL,KAAKE,WACjCF,KAAKU,QAEb,CACA,KAAAJ,GACIN,KAAKI,QAAS,EACVJ,KAAKG,YAAc,IACnBH,KAAKK,cAAgBQ,OAAOC,aAAY,IAAMd,KAAKU,SAASV,KAAKG,aAEzE,CACA,KAAAlB,GACIe,KAAKI,QAAS,EACdW,cAAcf,KAAKK,cACvB,CACA,UAAAW,CAAWC,GACP,MAAMC,EAAU,IAAIC,IAYpB,OAXAF,EAAMG,SAAS/H,IACX,MAAMgI,EAAUtM,KAAKC,UAAUqE,EAAKC,MACpC,IAAIgI,EAAeJ,EAAQK,IAAIF,GAE3BC,OADiBpH,IAAjBoH,EACe,CAACjI,GAGD,IAAIiI,EAAcjI,GAErC6H,EAAQM,IAAIH,EAASC,EAAa,IAE/BG,MAAMC,KAAKR,EAAQ9D,SAC9B,CACA,KAAAsD,GACI,GAAIV,KAAKI,QAAuC,IAA7BJ,KAAKC,aAAatL,OACjC,OAEeqL,KAAKgB,WAAWhB,KAAKC,cAC7BmB,QAAQpB,KAAKF,QACxBE,KAAKC,aAAe,EACxB,EC9DG,SAAS0B,EAAqBhH,EAAkBnD,EAAgBC,EAAQC,GAC3E,IAAIG,EACJL,EAAeM,MAAM,2BACrB,MAAMH,EAAa,GACnB,IAAIyI,EAAS3I,EAAO2I,OAChBwB,EAAkB,GACtB,MAwBMC,EAAwBZ,IAC1B,IAAIa,EAAgBb,EACpB,IAAK,MAAMc,KAAQH,EAAiB,CAChC,MAAMI,EAAWF,EAAc5K,IAAI6K,GAAME,OAAOC,SAChD,GAAwB,IAApBF,EAASrN,OACT,MAAO,GAEXmN,EAAgBE,CACpB,CACA,OAAOF,CAAa,EAElBK,EAAelB,IACjB,MAAMa,EAAgBD,EAAqBZ,GAC3C,GAA6B,IAAzBa,EAAcnN,OAGlB,IAAK,MAAMyN,KAAazK,EACpBH,EAAeM,MAAM,2BAA2BsK,EAAUvJ,SAAUiJ,GAChEM,EAAUC,aACVD,EAAUE,KAAKR,EAEvB,EAsBJ,IAAIS,GAC2B,QAA1B1K,EAAKJ,EAAO+K,gBAA6B,IAAP3K,OAAgB,EAASA,EAAG4K,WAC/DF,EAAgB,IAAI3C,EAAcuC,EAAa,CAC3ChC,YAAa1I,EAAO+K,SAASrC,YAC7BD,UAAWzI,EAAO+K,SAAStC,UAC3BE,YA+CR,MAAO,CACH/K,IAxHQ,IAAIqN,KACZlL,EAAeM,MAAM,qBACrB4K,EAActB,SAASuB,IACnBnL,EAAeM,MAAM,WAAW6K,EAAa9J,mBAC9BlB,EAAWsB,MAAM2J,GAAsBA,IAAsBD,IAExEnL,EAAewG,KAAK,aAAa2E,EAAa9J,0BAGlD8J,EAAahI,iBAAmBA,EAChCgI,EAAanL,eAAiBA,EAC9BmL,EAAalL,OAASA,EACtBkL,EAAajL,MAAQA,EACrBC,EAAWiJ,KAAK+B,GAAa,GAC/B,EA2GFE,mBAzGuB,IAAIC,KAC3BtL,EAAeM,MAAM,2BAA4B8J,GACjDkB,EAAmB1B,SAAS2B,IACpBA,GACAnB,EAAgBhB,KAAKmC,EACzB,GACF,EAoGFC,mBA9BuB,IAAM,IAAIpB,GA+BjCrH,QAzCalB,IACb,IAAIxB,EACAuI,KAG2B,QAA1BvI,EAAKJ,EAAO+K,gBAA6B,IAAP3K,OAAgB,EAASA,EAAG4K,WAC/DF,SAA8DA,EAAc5B,QAAQtH,IAzCxE,CAACA,IACjB,IAAIxB,EAAIkI,EAER,IAAgC,QAA1BlI,EAAKJ,EAAO+K,gBAA6B,IAAP3K,OAAgB,EAASA,EAAG4K,UAAY9K,EAAWsL,OAAOb,GAAcA,EAAUC,cACtH,OAEJ,MAAOa,GAAgBrB,EAAqB,CAACxI,IAC7C,QAAqBa,IAAjBgJ,EAGJ,IAAK,MAAMd,KAAazK,EACpBH,EAAeM,MAAM,2BAA2BsK,EAAUvJ,SAAUqK,GAC/Dd,EAAUC,aAGuB,QAA1BtC,EAAKtI,EAAO+K,gBAA6B,IAAPzC,OAAgB,EAASA,EAAG0C,UACtEL,EAAUE,KAAK,CAACY,IAHhBd,EAAUE,KAAKY,EAKvB,EAyBAC,CAAY9J,GAAK,EAkCjB+J,SA/Ba,IAAMhD,EAgCnBnB,MA/BU,KACVzH,EAAeM,MAAM,sBACrByK,SAA8DA,EAActD,QAC5EmB,GAAS,CAAI,EA6BbxE,OA3BW,IAAIyH,KACf7L,EAAeM,MAAM,uBACrBuL,EAAmBjC,SAASkC,IACxB9L,EAAeM,MAAM,aAAawL,EAAkBzK,mBACpD,MAAM0K,EAAyB5L,EAAW6L,QAAQF,IAClB,IAA5BC,EAIJ5L,EAAW8L,OAAOF,EAAwB,GAHtC/L,EAAewG,KAAK,cAAcsF,EAAkBzK,qBAGZ,GAC9C,EAkBF6K,sBAhB0B,IAAIC,KAC9B/B,EAAgBK,QAAQc,IAAoBY,EAAwB7M,SAASiM,IAAgB,EAgB7F,cAAIpL,GACA,MAAO,IAAIA,EACf,EACAuH,QAjBY,KACZ1H,EAAeM,MAAM,wBACrByK,SAA8DA,EAAcjC,QAC5EF,GAAS,CAAK,EAgBtB,CC3IO,IAAIzF,EAAmBuD,EACvB,SAAS0F,EAA2BnM,GACvC,IAAII,EAEJ,OADA8C,EAAsD,QAAlC9C,EAAKJ,EAAOkD,wBAAqC,IAAP9C,EAAgBA,EAAK8C,EAC5EA,CACX,CCCO,SAASkJ,EAAepM,GAC3B,MAAMkD,EAAmBiJ,EAA2BnM,GAC9CD,EAAiBgH,EAAyB7D,EAAkBlD,GAClE,GLUO,KAA2B,IKVKA,EAAO0H,QAE1C,YADA3H,EAAec,MAAM,sIAGzBd,EAAeM,MAAM,gBAErB,MAAMJ,ECfH,SAAyBH,EAAmBC,GAC/C,IAAIyJ,EAAQ,GACR6C,EAAY,GAChB,MAAMC,EAAW,IAAM9C,EAAM+C,QAAO,CAACC,EAAK5K,IAAS9F,OAAOwG,OAAOkK,EAAK9P,EAAWkF,GAAQA,IAASA,IAAO,CAAC,GACpG6K,EAAkB,KACpB,GAAIJ,EAAUnP,OAAQ,CAClB,MAAMvB,EAAQ2Q,IACdD,EAAU1C,SAAS+C,GAAaA,EAAS/Q,IAC7C,GAoBJ,MAAO,CACHiC,IAnBQ,IAAI+O,KACZ5M,EAAeM,MAAM,iBAAkBsM,GACvCnD,EAAML,QAAQwD,GACdF,GAAiB,EAiBjBtI,OAfW,IAAIyI,KACf7M,EAAeM,MAAM,mBAAoBuM,GACzCpD,EAAQA,EAAMgB,QAAQqC,IAAiBD,EAAcvN,SAASwN,KAC9DJ,GAAiB,EAajBK,YAXiBJ,IACjB3M,EAAeM,MAAM,0BAA2BqM,GAChDL,EAAUlD,KAAKuD,EAAS,EAUxBK,eARoBL,IACpB3M,EAAeM,MAAM,4BAA6BqM,GAClDL,EAAYA,EAAU7B,QAAQwC,GAAoBA,IAAoBN,GAAS,EAO/E,SAAI/Q,GACA,OAAO2Q,GACX,EAER,CDtBkBW,CAAgB/J,EAAkBnD,GAC1CG,EAAagK,EAAqBhH,EAAkBnD,EAAgBC,EAAQC,GAC5EqH,EAAMrE,EAAcC,EAAkBnD,EAAgBC,EAAQC,EAAOC,GACrEqH,EEnBH,SAAoCrE,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYoH,GACpGvH,EAAeM,MAAM,iCACrB,MAAMkH,EAAmB,GAuCzB,MAAO,CACH3J,IAvCQ,IAAIsP,KACZnN,EAAeM,MAAM,2BACrB6M,EAAoBvD,SAASwD,IACzBpN,EAAeM,MAAM,WAAW8M,EAAmB/L,yBACpCmG,EAAiB/F,MAAM4L,GAA4BA,EAAwBhM,OAAS+L,EAAmB/L,OAElHrB,EAAewG,KAAK,mBAAmB4G,EAAmB/L,0BAG9D+L,EAAmBjK,iBAAmBA,EACtCiK,EAAmBpN,eAAiBA,EACpCoN,EAAmBnN,OAASA,EAC5BmN,EAAmBlN,MAAQA,EAC3BkN,EAAmBjN,WAAaA,EAChCiN,EAAmB7F,IAAMA,EACzBC,EAAiB4B,KAAKgE,GACtBA,EAAmBE,aAAY,GACjC,EAuBF,oBAAI9F,GACA,MAAO,IAAIA,EACf,EACApD,OAxBW,IAAImJ,KACfvN,EAAeM,MAAM,6BACrBiN,EAAyB3D,SAAS4D,IAC9B,IAAInN,EAAIkI,EACRvI,EAAeM,MAAM,aAAakN,EAAwBnM,yBAC1D,MAAMoM,EAA+BjG,EAAiBgF,QAAO,CAACC,EAAKY,EAAyBtB,IAC5E,OAARU,GAAgBY,EAAwBhM,OAASmM,EAAwBnM,KAClE0K,EAEJ,MACR,MACE0B,GAIoE,QAAxElF,GAAMlI,EAAKmH,EAAiBiG,IAA+BC,eAA4B,IAAPnF,GAAyBA,EAAGrM,KAAKmE,GAClHmH,EAAiByE,OAAOwB,EAA8B,IAJlDzN,EAAewG,KAAK,oBAAoBgH,EAAwBnM,qBAIZ,GAC1D,EASV,CF7B6BsM,CAA2BxK,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYoH,GAC3GF,EAAOC,EAAanE,EAAkBnD,EAAgBC,EAAQC,EAAOC,EAAYoH,EAAKC,GAK5F,OGvBG,SAA8BH,GACjC,IAAIhH,EAAIkI,EACR,MAAMqF,EAAU,CACZC,IAAK,CACDC,QAAS1G,GAEb2G,IAAK,CACDC,SAAU3G,EAAKpH,OAAO8N,IAAI1M,OCRV4M,EDQ8B5G,EAAKpH,OAAO8N,IAAI1M,KCP/D,aAAmD,EAAS,EAAa,kBAAkB4M,QAD/F,IAAqBA,EDWxB,MAAM3J,EAAiD,QAAtCjE,EAAKgH,EAAKpH,OAAOiO,uBAAoC,IAAP7N,OAAgB,EAASA,EAAGiE,QACvFA,GACA+C,EAAKE,IAAIlD,WAAWC,GAEpB+C,EAAKpH,OAAO8N,MACZH,EAAQG,IAAMhS,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG8E,EAAKpH,OAAO8N,KAAMH,EAAQG,MAExE1G,EAAKpH,OAAOkE,OACZyJ,EAAQzJ,KAAOkD,EAAKpH,OAAOkE,MAE3BkD,EAAKpH,OAAO+E,OACZ4I,EAAQ5I,KAAOqC,EAAKpH,OAAO+E,MAE/BqC,EAAKnH,MAAMrC,IAAI+P,KAA0C,QAA5BrF,EAAKlB,EAAKpH,OAAOC,aAA0B,IAAPqI,EAAgBA,EAAK,GAC1F,CHJI4F,CAAqB9G,GKtBlB,SAAmCA,GACtCA,EAAKlH,WAAWtC,OAAOwJ,EAAKpH,OAAOE,YACnCkH,EAAKlH,WAAWkL,mBAAmBhE,EAAKpH,OAAOmO,WACnD,CLoBIC,CAA0BhH,GMvBvB,SAAyCA,GAC5CA,EAAKG,iBAAiB3J,OAAOwJ,EAAKpH,OAAOuH,iBAC7C,CNsBI8G,CAAgCjH,GACzBA,CACX,CO1BO,MAAMkH,EAAyB,OACzBC,EAAwB,CACjCvD,SAAS,EACTtC,YAAa,IACbD,UAAW,ICJF+F,EAAqB,UCArBC,EAAgB,KAChBC,GAAa,OACbC,GAAsB,IACtBC,GAAW,IACXC,GAAkB,sLAClBC,GAAkB,gCAClBC,GAAmB,OACnBC,GAAwB,cACxBC,GAA8BD,GAAsB9R,OACpDgS,GAAmB,qOACnBC,GAAmB,gDACnBC,GAAoB,UACpBC,GAAwB,mBACxBC,GAA2B,uBAC3BC,GAAqB,8BCb3B,SAASC,GAAgBC,EAAUC,EAAMC,EAAQC,GACpD,MAAMC,EAAa,CACfJ,SAAUA,GAAY3G,SAASgH,SAASC,KACxCC,SAAUN,GAAQf,IAQtB,YANelM,IAAXkN,IACAE,EAAWF,OAASA,QAEVlN,IAAVmN,IACAC,EAAWD,MAAQA,GAEhBC,CACX,CCZO,SAASI,GAA4BP,EAAMD,GAC9C,MAAMS,EAAoBR,aAAmC,EAASA,EAAKrQ,SAASgQ,IAC9Ec,GAAwBD,IAAsBR,aAAmC,EAASA,EAAKrQ,SAASiQ,KAC9G,OAAKY,GAAsBC,EAGpB,EACFT,aAAmC,EAASA,EAAKrQ,SAASuP,KAAac,EAAKU,MAAMxB,IAAU,GAAKc,EAClGQ,EAAoB,GAAGb,MAAyBI,IAAa,GAAGH,MAA4BG,KAJrF,CAACC,EAAMD,EAMtB,CCPO,SAASY,GAAwBxP,GACpC,IAAIyP,EAAQ,GACRzP,EAAM6B,WACN4N,EAAQzP,EAAM6B,WAAW0N,MAAM3B,GAAejE,QAAO,CAAC+F,EAAOxR,IAAQA,EAAM,GAAM,IAE5E8B,EAAMQ,QACXiP,EAAQzP,EAAMQ,MAAM+O,MAAM3B,IAE9B,MAAM1N,EAAcuP,EAAM/D,QAAO,CAACC,EAAKgE,EAAMzR,KACzC,IAAI0R,EACAf,EACAD,EACAE,EACAC,EACJ,GAAKa,EAAQ5B,GAAgB6B,KAAKF,GAAQ,CAKtC,GAJAd,EAAOe,EAAM,GACbhB,EAAWgB,EAAM,GACjBd,EAASc,EAAM,GACfb,EAAQa,EAAM,GACVhB,aAA2C,EAASA,EAASkB,WAAW5B,IAAmB,CAC3F,MAAM6B,EAAW9B,GAAgB4B,KAAKjB,GAClCmB,IACAnB,EAAWmB,EAAS,GACpBjB,EAASiB,EAAS,GAClBhB,EAAQgB,EAAS,GAEzB,CACAnB,GAAYA,aAA2C,EAASA,EAASkB,WAAW3B,KAC9ES,EAASoB,UAAU5B,IACnBQ,GACLC,EAAMD,GAAYQ,GAA4BP,EAAMD,EACzD,MACK,GAAKgB,EAAQvB,GAAiBwB,KAAKF,GAAQ,CAK5C,GAJAd,EAAOe,EAAM,GACbhB,EAAWgB,EAAM,GACjBd,EAASc,EAAM,GACfb,EAAQa,EAAM,GACRhB,GAAYA,EAASpQ,SAAS+P,IAAoB,CACpD,MAAMwB,EAAWzB,GAAiBuB,KAAKjB,GACnCmB,IACAlB,EAAOA,GAAQhB,GACfe,EAAWmB,EAAS,GACpBjB,EAASiB,EAAS,GAE1B,MACiB,IAAR7R,IAAc6Q,GAASrT,EAASsE,EAAMiQ,gBAC3ClB,EAAQzR,OAAO0C,EAAMiQ,aAAe,KAEvCpB,EAAMD,GAAYQ,GAA4BP,EAAMD,EACzD,CAIA,OAHIA,GAAYC,IACZlD,EAAIrD,KAAKqG,GAAgBC,EAAUC,EAAMC,EAASoB,OAAOpB,QAAUlN,EAAWmN,EAAQmB,OAAOnB,QAASnN,IAEnG+J,CAAG,GACX,IACH,OAAI+C,GAAmByB,KAAKnQ,EAAMM,SACvBJ,EAAYkQ,MAAM,GAEtBlQ,CACX,CC9DO,SAASR,GAAgBM,GAC5B,MAAO,CACH2B,OAAQ6N,GAAwBxP,GAExC,CCLO,MAAMqQ,GAAc,2BAEdC,GAA0B,IAO1BC,GAA+B,CACxCpG,SAAS,EACTqG,YAAY,EACZC,0BAJwCH,I,eCRrC,MAAMI,GAAgB,UCEhBC,GAAc,KACvB,MAAMC,EAAS,IAAI,GAAAC,UACb,KAAEtQ,EAAI,QAAEyM,GAAY4D,EAAOE,cACzBvQ,KAAMwQ,EAAQ/D,QAASgE,GAAcJ,EAAOK,QAC9CC,EAAYN,EAAOO,QACnBC,EAAWC,UAAUD,SACrBE,EAASD,UAAUH,UAAU1S,SAAS,QACtC+S,EAcN,WACI,IAAKhR,IAASyM,EACV,OAEJ,GAAI,kBAAmBqE,WAAaA,UAAUG,cAE1C,OAAOH,UAAUG,cAAcD,OAEnC,MACJ,CAvBeE,GACf,MAAO,CACHC,QAAS,CACLnR,KAAMA,QAAmCA,EAAOmQ,GAChD1D,QAASA,QAAyCA,EAAU0D,GAC5DiB,GAAI,GAAGZ,QAAuCA,EAASL,MAAiBM,QAA6CA,EAAYN,KACjIQ,UAAWA,QAA6CA,EAAYR,GACpEU,SAAUA,QAA2CA,EAAWV,GAChEY,SACAC,OAAQA,QAAuCA,EAASb,GACxDkB,cAAe,GAAGrJ,OAAOsJ,aACzBC,eAAgB,GAAGvJ,OAAOwJ,eAYlC,EChCSC,GAAS,KAClB,MAAMC,EAAe1J,OAAO2J,GAC5B,MAAO,CACHA,GAAIjX,OAAOwG,OAAO,CAEd0Q,aAAa,IAAUF,aAAmD,EAASA,EAAaG,YAAc,CAAEA,UAAWH,aAAmD,EAASA,EAAaG,YAC3M,ECLL,IAAIC,GACAC,GACG,SAASC,IAAe,eAAEC,EAAc,gBAAEC,GAAoB,CAAC,GAWlE,MAViB,KACb,MAAMC,EAAezD,SAASC,KAK9B,OAJIrT,EAAW2W,IAAmBH,KAAgBK,IAC9CL,GAAcK,EACdJ,GAASE,EAAevD,WAErB,CACHnL,KAAM7I,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAEkR,IAAKD,GAAiBJ,GAAS,CAAE/N,GAAI+N,IAAW,CAAC,GAAKG,GAC7F,CAGT,CCbO,MAAMG,GACT,WAAArL,GACIG,KAAKrF,iBAAmBuD,EACxB8B,KAAKxI,eAAiBqG,EACtBmC,KAAKvI,OAAS,CAAC,EACfuI,KAAKtI,MAAQ,CAAC,CAClB,CACA,QAAAyT,IAAYlU,GACR+I,KAAKxI,eAAeM,MAAM,GAAGkI,KAAKnH,YAAa5B,EACnD,CACA,OAAAmU,IAAWnU,GACP+I,KAAKxI,eAAesG,KAAK,GAAGkC,KAAKnH,YAAa5B,EAClD,CACA,OAAAoU,IAAWpU,GACP+I,KAAKxI,eAAewG,KAAK,GAAGgC,KAAKnH,YAAa5B,EAClD,CACA,QAAAqU,IAAYrU,GACR+I,KAAKxI,eAAec,MAAM,GAAG0H,KAAKnH,YAAa5B,EACnD,ECnBG,MAAMsU,WAAsBL,GAC/B,SAAA7I,GACI,OAAO,CACX,CACA,aAAAmJ,GACI,MAAO,EACX,ECNG,SAASC,GAAmBC,EAAQC,GACvC,IAAI9T,EAAIkI,EACR,QAAsB7F,IAAlByR,EACA,OAAOD,EAEX,QAAexR,IAAXwR,EACA,MAAO,CACHC,iBAGR,MAAMC,EAAkD,QAA/B/T,EAAK6T,EAAOC,qBAAkC,IAAP9T,OAAgB,EAASA,EAAG,GAC5F,QAAwBqC,IAApB0R,EACA,OAAOF,EAEX,MAAMG,GAAgBD,aAAyD,EAASA,EAAgBE,aAAe,GACjHC,GAAsG,QAAzFhM,EAAK4L,aAAqD,EAASA,EAAc,UAAuB,IAAP5L,OAAgB,EAASA,EAAG+L,aAAe,GAC/J,OAAOvY,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG2R,GAAS,CAAEC,cAAe,CACzDpY,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG6R,GAAkB,CAAEE,WAAY,IAAID,KAAiBE,OAEjG,CCjBO,SAASC,GAASC,EAAUC,GAC/B,IACIC,EADAlN,GAAQ,EAEZ,MAAMmN,EAAkB,KACD,MAAfD,GAIJF,KAAYE,GACZA,EAAc,KACdE,WAAWD,EAAiBF,IALxBjN,GAAQ,CAKsB,EAEtC,MAAO,IAAIhI,KACHgI,EACAkN,EAAclV,GAGlBgV,KAAYhV,GACZgI,GAAQ,EACRoN,WAAWD,EAAiBF,GAAM,CAE1C,CCvBO,MAAMI,GACA,iBADAA,GAEF,eAOJ,SAASC,GAAsBlZ,GAClC,IAAIwE,EACJ,IACI,IAAI2U,EACJA,EAAU3L,OAAOxN,GACjB,MAAMoZ,EAAW,wBAGjB,OAFAD,EAAQE,QAAQD,EAAUA,GAC1BD,EAAQG,WAAWF,IACZ,CACX,CACA,MAAOnU,GAGH,OAD+B,QAA9BT,EAAKgH,EAAKrH,sBAAmC,IAAPK,GAAyBA,EAAGiG,KAAK,uBAAuBzK,+BAAkCiF,MAC1H,CACX,CACJ,CAMO,SAASsU,GAAQlX,EAAKmX,GACzB,OAAIC,GAA0BD,GACnBhM,OAAOgM,GAAqBD,QAAQlX,GAExC,IACX,CAOO,SAASgX,GAAQhX,EAAKtC,EAAOyZ,GAChC,GAAIC,GAA0BD,GAC1B,IACIhM,OAAOgM,GAAqBH,QAAQhX,EAAKtC,EAC7C,CACA,MAAOkF,GAEP,CAER,CAMO,SAASqU,GAAWjX,EAAKmX,GACxBC,GAA0BD,IAC1BhM,OAAOgM,GAAqBF,WAAWjX,EAE/C,CACO,MAAMqX,GAA0BR,GAAsBD,IAChDU,GAA4BT,GAAsBD,IAC/D,SAASQ,GAA0BD,GAC/B,OAAIA,IAAwBP,GACjBS,GAEPF,IAAwBP,IACjBU,EAGf,CCzEA,MAAMC,GAAW,8DACV,SAASC,GAAWvY,EAAS,IAChC,OAAO8M,MAAMC,KAAKD,MAAM9M,IACnBuC,KAAI,IAAM+V,GAASE,KAAKC,MAAsBH,GAAhBE,KAAKE,aACnCjW,KAAK,GACd,CCFO,MAcMkW,GAAsB,gBACtBC,GAAuB,iBAEvBC,GAA+B,wBCnBrC,SAASC,KACZ,IAAI5V,EAAIkI,EAAI2N,EACZ,MACMhI,EAAkB7G,EAAKpH,OAAOiO,gBACpC,IAAIiI,EAA6V,QAA7UD,EAA4M,QAAtM3N,EAA0G,QAApGlI,EAAK6N,aAAyD,EAASA,EAAgBkI,eAA4B,IAAP/V,OAAgB,EAASA,EAAGnE,KAAKgS,EAAiB,CAAEhO,MAAOmH,EAAKnH,MAAMtE,eAA6B,IAAP2M,EAAgBA,EAAK2F,aAAyD,EAASA,EAAgBiI,oBAAiC,IAAPD,EAAgBA,EAFlW,EAGvB,GAA4B,iBAAjBC,EAA2B,CAElCA,EADsB,CAE1B,CACA,OAAOR,KAAKE,SAAWM,CAC3B,CCPO,SAASE,IAAwB,UAAEC,EAAS,QAAEC,EAAO,aAAEC,EAAY,UAAEP,GAAY,GAAU,CAAC,GAC/F,IAAI5V,EAAIkI,EACR,MAAMhK,EAAMF,IACNoY,EAA0G,QAArFlO,EAA4B,QAAtBlI,EAAKgH,EAAKpH,cAA2B,IAAPI,OAAgB,EAASA,EAAG6N,uBAAoC,IAAP3F,OAAgB,EAASA,EAAGkO,kBAIpJ,OAHiB,MAAbH,IACAA,EAAyC,mBAAtBG,EAAmCA,IAAsBf,MAEzE,CACHY,YACAE,aAAcA,QAAmDA,EAAejY,EAChFgY,QAASA,QAAyCA,EAAUhY,EAC5D0X,UAAWA,EAEnB,CACO,SAASS,GAAmBpS,GAC/B,GAAe,MAAXA,EACA,OAAO,EAEX,MAAM/F,EAAMF,IAEZ,KADsBE,EAAM+F,EAAQiS,QbtBD,OawB/B,OAAO,EAGX,OAD8BhY,EAAM+F,EAAQkS,aAAepF,EAE/D,CACO,SAASuF,IAAsB,iBAAEC,EAAgB,iBAAEC,IACtD,OAAO,UAAuB,mBAAEC,GAAuB,CAAEA,oBAAoB,IACzE,IAAIzW,EAAIkI,EAAI2N,EACZ,IAAKU,IAAqBC,EACtB,OAEJ,MAAME,EAAwB1P,EAAKpH,OAAOiO,gBACpC8I,EAAuBD,aAAqE,EAASA,EAAsBzF,WACjI,GAAK0F,IAAyBzB,KAA8ByB,IAAyBxB,GACjF,OAEJ,MAAMyB,EAAqBL,IAC3B,IAA2B,IAAvBE,GAAgCJ,GAAmBO,GACnDJ,EAAiB9a,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG0U,GAAqB,CAAET,aAAcnY,WAErF,CACD,IAAI6Y,EAAaC,GAAgCd,GAAwB,CAAEJ,UAAWA,OAAgBgB,GACtGJ,EAAiBK,GACG,QAAnB7W,EAAKgH,EAAKE,WAAwB,IAAPlH,GAAyBA,EAAGgE,WAAW6S,EAAWE,aACiD,QAA9H7O,EAAKwO,aAAqE,EAASA,EAAsBM,uBAAoC,IAAP9O,GAAyBA,EAAGrM,KAAK6a,EAAyI,QAAjHb,EAAKe,aAA+D,EAASA,EAAmBG,mBAAgC,IAAPlB,EAAgBA,EAAK,KAAMgB,EAAWE,YACnW,CACJ,CACJ,CACO,SAASD,GAAgCD,EAAYI,GACxD,IAAIjX,EAAIkI,EAAI2N,EAAIqB,EAAIC,EAAIC,EAAIC,EAC5B,MAAMC,EAAkB5b,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG2U,GAAa,CAAEE,YAAa,CAC5E/R,GAAI6R,EAAWZ,UACfvQ,WAAYhK,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAiG,QAA7FgG,EAA4C,QAAtClI,EAAKgH,EAAKpH,OAAOiO,uBAAoC,IAAP7N,OAAgB,EAASA,EAAGiE,eAA4B,IAAPiE,OAAgB,EAASA,EAAGxC,YAA4G,QAA7FwR,EAAyC,QAAnCrB,EAAK7O,EAAKnH,MAAMtE,MAAM0I,eAA4B,IAAP4R,OAAgB,EAASA,EAAGnQ,kBAA+B,IAAPwR,EAAgBA,EAAK,CAAC,GAAK,CAAEtB,UAAWiB,EAAWjB,UAAUha,gBAE/WwI,EAAyG,QAA5FgT,EAAyC,QAAnCD,EAAKnQ,EAAKnH,MAAMtE,MAAM0I,eAA4B,IAAPkT,OAAgB,EAASA,EAAG/S,iBAA8B,IAAPgT,EAAgBA,EAA8G,QAAxGC,EAAKJ,aAAyD,EAASA,EAAgBF,mBAAgC,IAAPM,OAAgB,EAASA,EAAGjT,UACpRvH,EAAQuH,KACTkT,EAAgBP,YAAY3S,UAAYA,GAE5C,MAAMmT,EAAoBN,aAAyD,EAASA,EAAgBhB,UAI5G,OAHyB,MAArBsB,IACAD,EAAgBP,YAAYrR,WAA4B,gBAAI6R,GAEzDD,CACX,CACO,SAASE,IAA4B,iBAAEjB,EAAgB,iBAAEC,IAC5D,OAAO,SAAwC/U,GAC3C,MAAMwC,EAAUxC,EAAKwC,QACfwT,EAA4BlB,IAClC,IAAIN,EAAYhS,aAAyC,EAASA,EAAQe,GAC1E,MAAM0S,EAAoBzT,aAAyC,EAASA,EAAQyB,WAC9EiS,EAAmB1T,aAAyC,EAASA,EAAQG,UAC7EwT,EAAoBH,aAA6E,EAASA,EAA0BV,YACpIc,EAA6BD,aAA6D,EAASA,EAAkBxT,UACrH0T,IAA+BH,IAAqBrZ,EAAUqZ,EAAkBE,GAChFE,IAAyBL,IAAsBpZ,EAAUoZ,EAAmBE,aAA6D,EAASA,EAAkBlS,YAE1K,KAD8BzB,GAAWgS,KAAewB,aAA6E,EAASA,EAA0BxB,YAC7I8B,GAAwBD,EAA4B,CAC3E,MAAME,EAAclB,GAAgCd,GAAwB,CAAEC,YAAWL,UAAWA,OAAgB6B,GACpHjB,EAAiBwB,GAM7B,SAA2BF,EAA4BH,EAAmB,CAAC,EAAGM,EAAyB,CAAC,GACpG,IAAIjY,EAAIkI,EAAI2N,EACZ,IAAKiC,EACD,OAEJ,MAAMI,EAAcP,EAAiBO,YAC/BC,EAAqL,QAA9JtC,EAAmD,QAA7C7V,EAAKiY,EAAuBC,mBAAgC,IAAPlY,EAAgBA,EAAqC,QAA/BkI,EAAKlB,EAAKnH,MAAMtE,MAAMmS,WAAwB,IAAPxF,OAAgB,EAASA,EAAGlH,YAAyB,IAAP6U,EAAgBA,EAAK,GACpNqC,GAAeA,IAAgBC,GAC/BnR,EAAKE,IAAIzB,UAAUkQ,GAA8B,CAC7CuC,cACAC,uBAGZ,CAlBYC,CAAkBN,EAA4BH,EAAkBE,GAChE7Q,EAAKE,IAAIlD,WAAWgU,EAAYjB,YACpC,CACJ,CACJ,CCnFO,MAAMsB,GACT,WAAArQ,GACIG,KAAKmQ,cAAgBnE,IAAS,IAAMhM,KAAKoQ,qBdJb,KcK5BpQ,KAAKoQ,kBAAoBjC,GAAsB,CAC3CC,iBAAkB8B,GAA0B9B,iBAC5CC,iBAAkB6B,GAA0B7B,mBAEhDrO,KAAKqQ,MACT,CACA,wBAAOC,GACH3D,GAAWhE,GAAauH,GAA0BK,iBACtD,CACA,uBAAOlC,CAAiBvS,GACpB4Q,GAAQ/D,GAAa9T,EAAsBiH,GAAUoU,GAA0BK,iBACnF,CACA,uBAAOnC,GACH,MAAMoC,EAAgB5D,GAAQjE,GAAauH,GAA0BK,kBACrE,OAAIC,EACOzb,KAAK0b,MAAMD,GAEf,IACX,CACA,IAAAH,GACI9P,SAASC,iBAAiB,oBAAoB,KACT,YAA7BD,SAASE,iBACTT,KAAKmQ,eACT,IAGJtR,EAAKnH,MAAM6M,YAAY8K,GAA4B,CAC/CjB,iBAAkB8B,GAA0B9B,iBAC5CC,iBAAkB6B,GAA0B7B,mBAEpD,EAEJ6B,GAA0BK,iBAAmBjE,GCnCtC,MAAMoE,GACT,WAAA7Q,GACIG,KAAKmQ,cAAgBnE,IAAS,IAAMhM,KAAKoQ,qBfJb,KeK5BpQ,KAAKoQ,kBAAoBjC,GAAsB,CAC3CC,iBAAkBsC,GAAwBtC,iBAC1CC,iBAAkBqC,GAAwBrC,mBAE9CrO,KAAKqQ,MACT,CACA,wBAAOC,GACH3D,GAAWhE,GAAa+H,GAAwBC,mBACpD,CACA,uBAAOtC,CAAiBvS,GACpB4Q,GAAQ/D,GAAa9T,EAAsBiH,GAAU4U,GAAwBC,mBACjF,CACA,uBAAOvC,GACH,MAAMoC,EAAgB5D,GAAQjE,GAAa+H,GAAwBC,oBACnE,OAAIH,EACOzb,KAAK0b,MAAMD,GAEf,IACX,CACA,IAAAH,GACI9P,SAASC,iBAAiB,oBAAoB,KACT,YAA7BD,SAASE,iBACTT,KAAKmQ,eACT,IAGJtR,EAAKnH,MAAM6M,YAAY8K,GAA4B,CAC/CjB,iBAAkBsC,GAAwBtC,iBAC1CC,iBAAkBqC,GAAwBrC,mBAElD,ECpCG,SAASuC,GAA0BrC,GACtC,OAAQA,aAAqE,EAASA,EAAsBzF,YAAcoH,GAA4BQ,EAC1J,CDoCAA,GAAwBC,mBAAqBrE,GExC7C,IAAIuE,GAAwC,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUje,GAAS,IAAMke,EAAKL,EAAUM,KAAKne,GAAS,CAAE,MAAOoe,GAAKJ,EAAOI,EAAI,CAAE,CAC1F,SAASC,EAASre,GAAS,IAAMke,EAAKL,EAAiB,MAAE7d,GAAS,CAAE,MAAOoe,GAAKJ,EAAOI,EAAI,CAAE,CAC7F,SAASF,EAAKI,GAJlB,IAAete,EAIase,EAAOC,KAAOR,EAAQO,EAAOte,QAJ1CA,EAIyDse,EAAOte,MAJhDA,aAAiB4d,EAAI5d,EAAQ,IAAI4d,GAAE,SAAUG,GAAWA,EAAQ/d,EAAQ,KAIjBwe,KAAKP,EAAWI,EAAW,CAC7GH,GAAML,EAAYA,EAAUY,MAAMf,EAASC,GAAc,KAAKQ,OAClE,GACJ,EACIO,GAAkC,SAAUC,EAAGP,GAC/C,IAAIQ,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOxe,OAAOC,UAAU0e,eAAexe,KAAKqe,EAAGE,IAAMT,EAAEhO,QAAQyO,GAAK,IAC9ED,EAAEC,GAAKF,EAAEE,IACb,GAAS,MAALF,GAAqD,mBAAjCxe,OAAO4e,sBACtB,KAAIC,EAAI,EAAb,IAAgBH,EAAI1e,OAAO4e,sBAAsBJ,GAAIK,EAAIH,EAAEtd,OAAQyd,IAC3DZ,EAAEhO,QAAQyO,EAAEG,IAAM,GAAK7e,OAAOC,UAAU6e,qBAAqB3e,KAAKqe,EAAGE,EAAEG,MACvEJ,EAAEC,EAAEG,IAAML,EAAEE,EAAEG,IAF4B,CAItD,OAAOJ,CACX,EAUO,MAAMM,WAAuB/G,GAChC,WAAA1L,CAAY9D,GACR,IAAIlE,EAAIkI,EAAI2N,EAAIqB,EAChBwD,QACAvS,KAAKjE,QAAUA,EACfiE,KAAKnH,KAAO,wCACZmH,KAAKsF,QAAU1G,EACfoB,KAAKwS,cAAgB,IAAI1c,KACzBkK,KAAKyS,mBAAkE,QAA5C5a,EAAKkE,EAAQ2W,iCAA8C,IAAP7a,EAAgBA,EAZjE,IAa9BmI,KAAK2S,OAAmC,QAAzB5S,EAAKhE,EAAQ4W,cAA2B,IAAP5S,EAAgBA,EAAK,IAAOjK,KAAKC,MACjFiK,KAAK4S,cCvCN,SAA6B7W,GAChC,MAAM,KAAE8W,EAAI,YAAEC,GAAgB/W,EACxBgX,EAAS,GACf,IAAInhB,EAAa,EACjB,MAAMohB,EAAO,KAGT,GAAIphB,EAAakhB,GAAeC,EAAOpe,OAAQ,CAC3C,MAAM,SAAEse,EAAQ,QAAE9B,EAAO,OAAEC,GAAW2B,EAAOG,QAC7CthB,IACAqhB,IAAWrB,MAAMF,IACb9f,IACAohB,IACA7B,EAAQO,EAAO,IACfyB,IACAvhB,IACAohB,IACA5B,EAAO+B,EAAO,GAEtB,GAeJ,MAAO,CACH9d,IAdS+d,IACT,GAAIL,EAAOpe,OAAS/C,GAAcihB,EAC9B,MAAM,IAAIre,MAAM,oBAEpB,OAAO,IAAI0c,SAAQ,CAACC,EAASC,KACzB2B,EAAOnS,KAAK,CACRqS,SAAUG,EACVjC,UACAC,WAEJ4B,GAAM,GACR,EAKV,CDE6BK,CAAoB,CACrCR,KAAoC,QAA7BnF,EAAK3R,EAAQuX,kBAA+B,IAAP5F,EAAgBA,EAjB5C,GAkBhBoF,YAA4C,QAA9B/D,EAAKhT,EAAQ+W,mBAAgC,IAAP/D,EAAgBA,EAjBpD,GAmBxB,CACA,IAAAzM,CAAKrB,GACD,OAAO4P,GAAU7Q,UAAM,OAAQ,GAAQ,YACnC,IACI,GAAIA,KAAKwS,cAAgB,IAAI1c,KAAKkK,KAAK2S,UAEnC,OADA3S,KAAKqL,QAAQ,mEAAmErL,KAAKwS,iBAC9EtB,QAAQC,gBAEbnR,KAAK4S,cAAcvd,KAAI,KACzB,MAAMke,EAAOxe,KAAKC,UV/B/B,SAA0BqE,GAC7B,IAAIka,EAAO,CACPja,KAAM,CAAC,GAoBX,YAlBgBY,IAAZb,EAAK,KACLka,EAAKja,KAAOD,EAAK,GAAGC,MAExBD,EAAK+H,SAASkD,IACV,OAAQA,EAAYjR,MAChB,KAAKP,EAAkBd,IACvB,KAAKc,EAAkBI,MACvB,KAAKJ,EAAkBE,UACvB,KAAKF,EAAkBG,YACnB,MAAMugB,EAAKzgB,EAA2BuR,EAAYjR,MAC5CogB,EAAUF,EAAKC,GACrBD,EAAOhgB,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGwZ,GAAO,CAAE,CAACC,QAAiBtZ,IAAZuZ,EAAwB,CAACnP,EAAY/K,SAAW,IAAIka,EAASnP,EAAY/K,WAC/H,MACJ,KAAKzG,EAAkBZ,MACnBqhB,EAAOhgB,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGwZ,GAAO,CAAE7H,OAAQD,GAAmB8H,EAAK7H,OAAQpH,EAAY/K,QAAQoS,iBAEpH,IAEG4H,CACX,CUQgDG,CAAiBzS,KACvC,IAAEgK,EAAG,eAAE0I,EAAc,OAAEC,GAAW5T,KAAKjE,QACvClE,EAAK8b,QAAuDA,EAAiB,CAAC,GAAG,QAAEE,GAAYhc,EAAIic,EAAuBhC,GAAOja,EAAI,CAAC,YAC5I,IAAIiW,EACJ,MAAMc,EAAc5O,KAAKtI,MAAMtE,MAAM0I,QAIrC,OAHmB,MAAf8S,IACAd,EAAYc,EAAY/R,IAErBkX,MAAM9I,EAAK1X,OAAOwG,OAAO,CAAEia,OAAQ,OAAQH,QAAStgB,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAE,eAAgB,oBAAuB8Z,QAAyCA,EAAU,CAAC,GAAMD,EAAS,CAAE,YAAaA,GAAW,CAAC,GAAM9F,EAAY,CAAE,oBAAqBA,GAAc,CAAC,GAAKyF,OAAMU,UAAWV,EAAK5e,QAlCpT,KAkCyVmf,QAAmEA,EAAuB,CAAC,IAC1blC,MAAMsC,GAAarD,GAAU7Q,UAAM,OAAQ,GAAQ,YACpD,GAlCP,MAkCWkU,EAASC,OAAqB,CAC2C,YAAlDD,EAASL,QAAQtS,IAAI,0BAExCvB,KAAKoU,kBAAkBpU,KAAKvI,OAAQuI,KAAKmL,SAEjD,CAOA,OA/CE,MAyCE+I,EAASC,SACTnU,KAAKwS,cAAgBxS,KAAKqU,kBAAkBH,GAC5ClU,KAAKqL,QAAQ,wCAAwCrL,KAAKwS,kBAG9D0B,EAASI,OAAOC,MAAM5W,GACfuW,CACX,MACKK,OAAO1gB,IACRmM,KAAKsL,SAAS,2CAA4CvW,KAAK0b,MAAM8C,GAAO1f,EAAI,GAClF,GAEV,CACA,MAAOA,GACHmM,KAAKsL,SAASzX,EAClB,CACJ,GACJ,CACA,aAAA2X,GACI,IAAI3T,EACJ,MAAO,CAACmI,KAAKjE,QAAQkP,KAAKuJ,OAAyC,QAAjC3c,EAAKmI,KAAKvI,OAAOgd,kBAA+B,IAAP5c,EAAgBA,EAAK,GACpG,CACA,SAAAwK,GACI,OAAO,CACX,CACA,iBAAAgS,CAAkBH,GACd,MAAMne,EAAMiK,KAAK2S,SACX+B,EAAmBR,EAASL,QAAQtS,IAAI,eAC9C,GAAImT,EAAkB,CAClB,MAAMxI,EAAQ1D,OAAOkM,GACrB,IAAKzgB,MAAMiY,GACP,OAAO,IAAIpW,KAAa,IAARoW,EAAenW,GAEnC,MAAM4e,EAAO7e,KAAK2a,MAAMiE,GACxB,IAAKzgB,MAAM0gB,GACP,OAAO,IAAI7e,KAAK6e,EAExB,CACA,OAAO,IAAI7e,KAAKC,EAAMiK,KAAKyS,mBAC/B,CACA,iBAAA2B,CAAkB3c,EAAQ0T,GACtB,MAAMyJ,EAAuB,kBACvBrG,EAAwB9W,EAAOiO,gBACrC,GAAI6I,aAAqE,EAASA,EAAsB9L,QAAS,CAC7G,MAAM,iBAAE2L,EAAgB,iBAAEC,GAAqBuC,GAA0BrC,GACzEJ,GAAsB,CAAEC,mBAAkBC,oBAA1CF,CAA8D,CAAEG,oBAAoB,IACpFnD,EAAS,GAAGyJ,yBAChB,MAEIzJ,EAAS,GAAGyJ,KAEpB,EEtHG,MAAMC,WAA4B3J,GACrC,WAAArL,GACI0S,SAASuC,WACT9U,KAAKjB,IAAM,CAAC,EACZiB,KAAKrI,WAAa,CAAC,CACvB,ECNG,MAMMod,GAAiB,2GCFvB,SAASC,GAAgBC,GAC5B,IAAI7hB,EACAC,EAEA6hB,EACAC,EAFA3c,EAAc,GAGlB,G/DqBwB,CAAEpF,GAAUE,EAAWF,EAAO,c+DrBlDgiB,CAAaH,IAAQA,EAAI3c,MACzBlF,EAAQ6hB,EAAI3c,MAAMM,QAClBvF,EAAO4hB,EAAI3c,MAAMO,KACjBL,EAAcsP,GAAwBmN,EAAI3c,YAEzC,IAAK4c,E/DiBY,CAAE9hB,GAAUE,EAAWF,EAAO,Y+DjB1BiiB,CAAWJ,K/DkBX,CAAE7hB,GAAUE,EAAWF,EAAO,gB+DlBVkiB,CAAeL,GAAM,CAC/D,MAAM,KAAEpc,EAAI,QAAED,GAAYqc,EAC1B5hB,EAAOwF,QAAmCA,EAAQqc,EDf9B,WACI,eCexB9hB,EAAQwF,EAAU,GAAGvF,MAASuF,IAAYvF,CAC9C,MACSoB,EAAQwgB,IACb7hB,EAAQ6hB,EAAIrc,QACZJ,EAAcsP,GAAwBmN,KAEjC/gB,EAAS+gB,KAASE,E/DIR,CAAE/hB,GAAUiB,GAAkBV,EAAaP,EAAOkB,O+DJ7BihB,CAAQN,OAC5C5hB,EAAO8hB,EAAaF,EAAIpV,YAAYhH,UAAOqB,EAC3C9G,EAAQ,2CAAuBG,OAAOqB,KAAKqgB,MAE/C,MAAO,CAAC7hB,EAAOC,EAAMmF,EACzB,CACO,SAASgd,GAAwBve,GACpC,MAAOge,EAAKQ,EAAQrO,EAAQC,EAAO/O,GAASrB,EAC5C,IAAI7D,EACAC,EACAmF,EAAc,GAClB,MAAMkd,EAAgB3hB,EAASkhB,GACzBU,EAAoB1O,GAAgBwO,ED/BX,IC+BwCrO,EAAQC,GAW/E,OAVI/O,IAAUod,IACTtiB,EAAOC,EAAMmF,GAAewc,GAAiB1c,QAAqCA,EAAQ2c,GAChE,IAAvBzc,EAAY7D,SACZ6D,EAAc,CAACmd,KAGdD,KACJtiB,EAAOC,GC1CT,SAAoCuF,GACvC,IAAIf,EAAIkI,EACR,MAAM6V,EAAShd,EAAQO,MAAM4b,IACvB1hB,EAA4E,QAApEwE,EAAK+d,aAAuC,EAASA,EAAO,UAAuB,IAAP/d,EAAgBA,EAAKd,EAE/G,MAAO,CAD4E,QAApEgJ,EAAK6V,aAAuC,EAASA,EAAO,UAAuB,IAAP7V,EAAgBA,EAAKnH,EACjGvF,EACnB,CDoCwBwiB,CAA2BZ,GAC3Czc,EAAc,CAACmd,IAEZ,CAAEviB,QAAOC,OAAMmF,cAC1B,CACO,SAASsd,GAA+B7e,EAAM8e,GACjD,OAAIthB,EAAQwC,EAAK,IACNue,GAAwBve,GAGxB,CAAE7D,MAAO2iB,EAAW9e,GAEnC,CErDO,SAAS+e,GAA6BjX,GACzC8B,OAAOL,iBAAiB,sBAAuByU,IAC3C,IAAIpd,EAAIkI,EACR,IAOI3M,EACAC,EARAiF,EAAQ2c,EACR3c,EAAM6a,OACN7a,EAAQ2c,EAAI9B,QAEe,QAArBtb,EAAKod,EAAIgB,cAA2B,IAAPpe,OAAgB,EAASA,EAAGsb,UAC/D7a,EAA8B,QAArByH,EAAKkV,EAAIgB,cAA2B,IAAPlW,OAAgB,EAASA,EAAGoT,QAItE,IAAI3a,EAAc,GjEWC,CAAEpF,IAAWc,EAASd,KAAWe,EAAWf,GiEV3D8iB,CAAY5d,IACZlF,EAAQ,oDAA8BwC,OAAO0C,KAC7CjF,EHjB0B,uBGoBzBD,EAAOC,EAAMmF,GAAewc,GAAgB1c,GAE7ClF,GACA2L,EAAI1G,UAAU,IAAI7D,MAAMpB,GAAQ,CAAEC,OAAMmF,eAC5C,GAER,CCxBO,MAAM2d,WAA8BtB,GACvC,WAAAhV,GACI0S,SAASuC,WACT9U,KAAKnH,KAAO,+CACZmH,KAAKsF,QAAU1G,CACnB,CACA,UAAAkG,GACI9E,KAAKmL,SAAS,gBCTf,SAAyBpM,GAC5B,MAAMqX,EAAavV,OAAOwV,QAC1BxV,OAAOwV,QAAU,IAAIpf,KACjB,IACI,MAAM,MAAE7D,EAAK,KAAEC,EAAI,YAAEmF,GAAgBgd,GAAwBve,GACzD7D,GACA2L,EAAI1G,UAAU,IAAI7D,MAAMpB,GAAQ,CAAEC,OAAMmF,eAEhD,CACA,QACI4d,SAAwDA,EAAWvE,MAAMhR,OAAQ5J,EACrF,EAER,CDHQqf,CAAgBtW,KAAKjB,KACrBiX,GAA6BhW,KAAKjB,IACtC,EEbJ,IAAIyS,GAAE+E,GAAEvE,GAAEwE,GAAEpE,GAAE3c,IAAG,EAAEW,GAAE,SAASob,GAAGhR,iBAAiB,YAAW,SAAU+V,GAAGA,EAAEE,YAAYhhB,GAAE8gB,EAAEG,UAAUlF,EAAE+E,GAAI,IAAE,EAAG,EAAEI,GAAE,WAAW,IAAInF,EAAE7S,KAAKiY,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,GAAG,GAAGrF,GAAGA,EAAEsF,cAAc,GAAGtF,EAAEsF,cAAcF,YAAY7gB,MAAM,OAAOyb,CAAC,EAAEuF,GAAE,WAAW,IAAIvF,EAAEmF,KAAI,OAAOnF,GAAGA,EAAEwF,iBAAiB,CAAC,EAAEC,GAAE,SAASzF,EAAE+E,GAAG,IAAIvE,EAAE2E,KAAIH,EAAE,WAA8J,OAAnJ/gB,IAAG,EAAE+gB,EAAE,qBAAqBxE,IAAIzR,SAAS2W,cAAcH,KAAI,EAAEP,EAAE,YAAYjW,SAAS4W,aAAaX,EAAE,UAAUxE,EAAE3e,OAAOmjB,EAAExE,EAAE3e,KAAK+jB,QAAQ,KAAK,OAAa,CAACve,KAAK2Y,EAAEpe,WAAM,IAASmjB,GAAG,EAAEA,EAAEc,OAAO,OAAOC,MAAM,EAAE3hB,QAAQ,GAAGkH,GAAG,MAAM2X,OAAO1e,KAAKC,MAAM,KAAKye,OAAOrH,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMkK,eAAef,EAAE,EAAEzE,GAAE,SAASP,EAAE+E,EAAEvE,GAAG,IAAI,GAAGwF,oBAAoBC,oBAAoB3gB,SAAS0a,GAAG,CAAC,IAAIgF,EAAE,IAAIgB,qBAAoB,SAAUhG,GAAGN,QAAQC,UAAUS,MAAK,WAAY2E,EAAE/E,EAAEkG,aAAc,GAAG,IAAG,OAAOlB,EAAEmB,QAAQpkB,OAAOwG,OAAO,CAAC1G,KAAKme,EAAEoG,UAAS,GAAI5F,GAAG,CAAC,IAAIwE,CAAC,CAAC,CAAC,MAAMhF,GAAG,CAAC,EAAEqG,GAAE,SAASrG,EAAE+E,EAAEvE,EAAEwE,GAAG,IAAIpE,EAAE3c,EAAE,OAAO,SAASW,GAAGmgB,EAAEnjB,OAAO,IAAIgD,GAAGogB,MAAM/gB,EAAE8gB,EAAEnjB,OAAOgf,GAAG,UAAK,IAASA,KAAKA,EAAEmE,EAAEnjB,MAAMmjB,EAAEe,MAAM7hB,EAAE8gB,EAAEc,OAAO,SAAS7F,EAAE+E,GAAG,OAAO/E,EAAE+E,EAAE,GAAG,OAAO/E,EAAE+E,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEnjB,MAAM4e,GAAGR,EAAE+E,GAAG,CAAC,EAAEuB,GAAE,SAAStG,GAAGuG,uBAAsB,WAAY,OAAOA,uBAAsB,WAAY,OAAOvG,GAAI,GAAG,GAAE,EAAES,GAAE,SAAST,GAAGjR,SAASC,iBAAiB,oBAAmB,WAAY,WAAWD,SAASE,iBAAiB+Q,GAAI,GAAE,EAAEwG,GAAE,SAASxG,GAAG,IAAI+E,GAAE,EAAG,OAAO,WAAWA,IAAI/E,IAAI+E,GAAE,EAAG,CAAC,EAAE0B,IAAG,EAAEC,GAAE,WAAW,MAAM,WAAW3X,SAASE,iBAAiBF,SAAS2W,aAAa,IAAI,CAAC,EAAExY,GAAE,SAAS8S,GAAG,WAAWjR,SAASE,iBAAiBwX,IAAG,IAAIA,GAAE,qBAAqBzG,EAAEne,KAAKme,EAAEkF,UAAU,EAAEyB,KAAI,EAAEC,GAAE,WAAW5X,iBAAiB,mBAAmB9B,IAAE,GAAI8B,iBAAiB,qBAAqB9B,IAAE,EAAG,EAAEyZ,GAAE,WAAWE,oBAAoB,mBAAmB3Z,IAAE,GAAI2Z,oBAAoB,qBAAqB3Z,IAAE,EAAG,EAAE4Z,GAAE,WAAW,OAAOL,GAAE,IAAIA,GAAEC,KAAIE,KAAIhiB,IAAE,WAAYiW,YAAW,WAAY4L,GAAEC,KAAIE,IAAI,GAAE,EAAG,KAAI,CAAC,mBAAIG,GAAkB,OAAON,EAAC,EAAE,EAAEO,GAAE,SAAShH,GAAGjR,SAAS2W,aAAa1W,iBAAiB,sBAAqB,WAAY,OAAOgR,GAAI,IAAE,GAAIA,GAAG,EAAEnb,GAAE,CAAC,KAAK,KAAKoiB,GAAE,SAASjH,EAAE+E,GAAGA,EAAEA,GAAG,CAAC,EAAEiC,IAAE,WAAY,IAAIxG,EAAEwE,EAAE8B,KAAIlG,EAAE6E,GAAE,OAAOxhB,EAAEsc,GAAE,SAAQ,SAAUP,GAAGA,EAAEpQ,SAAQ,SAAUoQ,GAAG,2BAA2BA,EAAE3Y,OAAOpD,EAAEijB,aAAalH,EAAEmH,UAAUnC,EAAE+B,kBAAkBnG,EAAEhf,MAAM+Z,KAAKyL,IAAIpH,EAAEmH,UAAU5B,KAAI,GAAG3E,EAAEzc,QAAQiL,KAAK4Q,GAAGQ,GAAE,IAAM,GAAG,IAAGvc,IAAIuc,EAAE6F,GAAErG,EAAEY,EAAE/b,GAAEkgB,EAAEsC,kBAAkBziB,IAAE,SAAUogB,GAAGpE,EAAE6E,GAAE,OAAOjF,EAAE6F,GAAErG,EAAEY,EAAE/b,GAAEkgB,EAAEsC,kBAAkBf,IAAE,WAAY1F,EAAEhf,MAAMwjB,YAAY7gB,MAAMygB,EAAEE,UAAU1E,GAAE,EAAI,GAAG,IAAI,GAAE,EAAE8G,GAAE,CAAC,GAAG,KAAogBC,GAAE,EAAEC,GAAE,IAAIhI,GAAE,EAAEiI,GAAE,SAASzH,GAAGA,EAAEpQ,SAAQ,SAAUoQ,GAAGA,EAAE0H,gBAAgBF,GAAE7L,KAAKgM,IAAIH,GAAExH,EAAE0H,eAAelI,GAAE7D,KAAKyL,IAAI5H,GAAEQ,EAAE0H,eAAeH,GAAE/H,IAAGA,GAAEgI,IAAG,EAAE,EAAE,EAAG,GAAE,EAAEI,GAAE,WAAW,OAAO5H,GAAEuH,GAAEnC,YAAYyC,kBAAkB,CAAC,EAAEC,GAAE,WAAW,qBAAqB1C,aAAapF,KAAIA,GAAEO,GAAE,QAAQkH,GAAE,CAAC5lB,KAAK,QAAQukB,UAAS,EAAG2B,kBAAkB,IAAI,EAAEC,GAAE,GAAGC,GAAE,IAAItY,IAAIuY,GAAE,EAA8EC,GAAE,GAAGC,GAAE,SAASpI,GAAG,GAAGmI,GAAEvY,SAAQ,SAAUmV,GAAG,OAAOA,EAAE/E,EAAG,IAAGA,EAAE0H,eAAe,gBAAgB1H,EAAEqI,UAAU,CAAC,IAAItD,EAAEiD,GAAEA,GAAE7kB,OAAO,GAAGqd,EAAEyH,GAAElY,IAAIiQ,EAAE0H,eAAe,GAAGlH,GAAGwH,GAAE7kB,OAAO,IAAI6c,EAAEsI,SAASvD,EAAEwD,QAAQ,CAAC,GAAG/H,EAAER,EAAEsI,SAAS9H,EAAE+H,SAAS/H,EAAErc,QAAQ,CAAC6b,GAAGQ,EAAE+H,QAAQvI,EAAEsI,UAAUtI,EAAEsI,WAAW9H,EAAE+H,SAASvI,EAAEmH,YAAY3G,EAAErc,QAAQ,GAAGgjB,WAAW3G,EAAErc,QAAQiL,KAAK4Q,OAAO,CAAC,IAAIgF,EAAE,CAAC3Z,GAAG2U,EAAE0H,cAAca,QAAQvI,EAAEsI,SAASnkB,QAAQ,CAAC6b,IAAIiI,GAAEjY,IAAIgV,EAAE3Z,GAAG2Z,GAAGgD,GAAE5Y,KAAK4V,EAAE,CAACgD,GAAEQ,MAAK,SAAUxI,EAAE+E,GAAG,OAAOA,EAAEwD,QAAQvI,EAAEuI,OAAQ,IAAGP,GAAE7kB,OAAO,IAAI6kB,GAAE/V,OAAO,IAAIrC,SAAQ,SAAUoQ,GAAG,OAAOiI,GAAEQ,OAAOzI,EAAE3U,GAAI,GAAE,CAAC,CAAC,EAAEqd,GAAE,SAAS1I,GAAG,IAAI+E,EAAE5X,KAAKwb,qBAAqBxb,KAAK0N,WAAW2F,GAAG,EAAE,OAAOR,EAAEwG,GAAExG,GAAG,WAAWjR,SAASE,gBAAgB+Q,KAAKQ,EAAEuE,EAAE/E,GAAGS,GAAET,IAAIQ,CAAC,EAAEoI,GAAE,CAAC,IAAI,KAA6jBC,GAAE,CAAC,KAAK,KAAKC,GAAE,CAAC,EAA0nBC,GAAE,CAAC,IAAI,MAAMC,GAAE,SAAShJ,EAAE+E,GAAGhW,SAAS2W,aAAasB,IAAE,WAAY,OAAOhH,EAAE+E,EAAG,IAAG,aAAahW,SAASka,WAAWja,iBAAiB,QAAO,WAAY,OAAOgR,EAAE+E,EAAG,IAAE,GAAIlK,WAAWkK,EAAE,EAAE,EAAwOmE,GAAE,CAACC,SAAQ,EAAGC,SAAQ,GAAIC,GAAE,IAAI/kB,KAAKglB,GAAE,SAAStJ,EAAEY,GAAGmE,KAAIA,GAAEnE,EAAEJ,GAAER,EAAEgF,GAAE,IAAI1gB,KAAKilB,GAAE1C,qBAAqB2C,KAAI,EAAEA,GAAE,WAAW,GAAGhJ,IAAG,GAAGA,GAAEwE,GAAEqE,GAAE,CAAC,IAAIrJ,EAAE,CAACqI,UAAU,cAAchhB,KAAK0d,GAAEljB,KAAK4nB,OAAO1E,GAAE0E,OAAOC,WAAW3E,GAAE2E,WAAWvC,UAAUpC,GAAEG,UAAUyE,gBAAgB5E,GAAEG,UAAU1E,IAAGI,GAAEhR,SAAQ,SAAUmV,GAAGA,EAAE/E,EAAG,IAAGY,GAAE,EAAE,CAAC,EAAEgJ,GAAE,SAAS5J,GAAG,GAAGA,EAAE0J,WAAW,CAAC,IAAI3E,GAAG/E,EAAEkF,UAAU,KAAK,IAAI5gB,KAAK8gB,YAAY7gB,OAAOyb,EAAEkF,UAAU,eAAelF,EAAEne,KAAK,SAASme,EAAE+E,GAAG,IAAIvE,EAAE,WAAW8I,GAAEtJ,EAAE+E,GAAGnE,GAAG,EAAEoE,EAAE,WAAWpE,GAAG,EAAEA,EAAE,WAAWiG,oBAAoB,YAAYrG,EAAE0I,IAAGrC,oBAAoB,gBAAgB7B,EAAEkE,GAAE,EAAEla,iBAAiB,YAAYwR,EAAE0I,IAAGla,iBAAiB,gBAAgBgW,EAAEkE,GAAE,CAAhO,CAAkOnE,EAAE/E,GAAGsJ,GAAEvE,EAAE/E,EAAE,CAAC,EAAEuJ,GAAE,SAASvJ,GAAG,CAAC,YAAY,UAAU,aAAa,eAAepQ,SAAQ,SAAUmV,GAAG,OAAO/E,EAAE+E,EAAE6E,GAAEV,GAAG,GAAE,EAAEW,GAAE,CAAC,IAAI,KCCl7M,MAAMC,GACT,WAAAzb,CAAY1C,EAAiBoe,GACzBvb,KAAK7C,gBAAkBA,EACvB6C,KAAKub,eAAiBA,CAC1B,CACA,UAAAzW,GACIvR,OAAOoC,QAAQ2lB,GAAeE,SAASpa,SAAQ,EAAEqa,EAAWC,MACxD,IAAI7jB,EACJ6jB,GAAUC,IACN3b,KAAK7C,gBAAgB,CACjB9J,KAAM,aACN+J,OAAQ,CACJ,CAACqe,GAAYE,EAAOvoB,QAE1B,GACH,CAAEylB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAAmB,GAErH,EAEJyC,GAAeE,QAAU,CACrBI,IDrBk/E,SAASpK,EAAE+E,GAAGA,EAAEA,GAAG,CAAC,EAAEkC,GAAET,IAAE,WAAY,IAAIhG,EAAEwE,EAAES,GAAE,MAAM,GAAG7E,EAAE,EAAE3c,EAAE,GAAGkhB,EAAE,SAASnF,GAAGA,EAAEpQ,SAAQ,SAAUoQ,GAAG,IAAIA,EAAEqK,eAAe,CAAC,IAAItF,EAAE9gB,EAAE,GAAGuc,EAAEvc,EAAEA,EAAEd,OAAO,GAAGyd,GAAGZ,EAAEmH,UAAU3G,EAAE2G,UAAU,KAAKnH,EAAEmH,UAAUpC,EAAEoC,UAAU,KAAKvG,GAAGZ,EAAEpe,MAAMqC,EAAEmL,KAAK4Q,KAAKY,EAAEZ,EAAEpe,MAAMqC,EAAE,CAAC+b,GAAG,CAAE,IAAGY,EAAEoE,EAAEpjB,QAAQojB,EAAEpjB,MAAMgf,EAAEoE,EAAE7gB,QAAQF,EAAEuc,IAAI,EAAE+E,EAAEhF,GAAE,eAAe4E,GAAGI,IAAI/E,EAAE6F,GAAErG,EAAEgF,EAAEsC,GAAEvC,EAAEsC,kBAAkB5G,IAAE,WAAY0E,EAAEI,EAAE+E,eAAe9J,GAAE,EAAI,IAAG5b,IAAE,WAAYgc,EAAE,EAAEoE,EAAES,GAAE,MAAM,GAAGjF,EAAE6F,GAAErG,EAAEgF,EAAEsC,GAAEvC,EAAEsC,kBAAkBf,IAAE,WAAY,OAAO9F,GAAI,GAAG,IAAG3F,WAAW2F,EAAE,GAAI,IAAG,ECsB7+F+J,IAAK,GACLC,IDvB67M,SAASxK,EAAEgF,GAAGA,EAAEA,GAAG,CAAC,EAAEgC,IAAE,WAAY,IAAI/iB,EAAEkhB,EAAE2B,KAAIvB,EAAEE,GAAE,OAAOa,EAAE,SAAStG,GAAGA,EAAEmH,UAAUhC,EAAE4B,kBAAkBxB,EAAE3jB,MAAMoe,EAAE2J,gBAAgB3J,EAAEmH,UAAU5B,EAAEphB,QAAQiL,KAAK4Q,GAAG/b,GAAE,GAAI,EAAEwiB,EAAE,SAASzG,GAAGA,EAAEpQ,QAAQ0W,EAAE,EAAEI,EAAEnG,GAAE,cAAckG,GAAGxiB,EAAEoiB,GAAErG,EAAEuF,EAAEsE,GAAE7E,EAAEqC,kBAAkBX,IAAIjG,GAAE+F,IAAE,WAAYC,EAAEC,EAAE4D,eAAe5D,EAAEQ,YAAa,KAAItiB,IAAE,WAAY,IAAIA,EAAE2gB,EAAEE,GAAE,OAAOxhB,EAAEoiB,GAAErG,EAAEuF,EAAEsE,GAAE7E,EAAEqC,kBAAkBzG,GAAE,GAAGJ,IAAG,EAAEuE,GAAE,KAAKwE,GAAEva,kBAAkBpK,EAAE0hB,EAAE1F,GAAExR,KAAKxK,GAAG4kB,IAAI,IAAI,GAAE,ECwBz2NiB,IDxB6kI,SAASzK,EAAE+E,GAAG,2BAA2B5X,MAAM,kBAAkBud,uBAAuB1oB,YAAY+iB,EAAEA,GAAG,CAAC,EAAEiC,IAAE,WAAY,IAAIxG,EAAEsH,KAAI,IAAI9C,EAAEpE,EAAE6E,GAAE,OAAOxhB,EAAE,SAAS+b,GAAG0I,IAAE,WAAY1I,EAAEpQ,QAAQwY,IAAG,IAAIrD,EAAz8B,WAAW,IAAI/E,EAAErE,KAAKgM,IAAIK,GAAE7kB,OAAO,EAAEwY,KAAKC,OAAOgM,KAAIM,IAAG,KAAK,OAAOF,GAAEhI,EAAE,CAAm4B2K,GAAI5F,GAAGA,EAAEwD,UAAU3H,EAAEhf,QAAQgf,EAAEhf,MAAMmjB,EAAEwD,QAAQ3H,EAAEzc,QAAQ4gB,EAAE5gB,QAAQ6gB,IAAK,GAAE,EAAEG,EAAE5E,GAAE,QAAQtc,EAAE,CAAC8jB,kBAAkB,QAAQvH,EAAEuE,EAAEgD,yBAAoB,IAASvH,EAAEA,EAAE,KAAKwE,EAAEqB,GAAErG,EAAEY,EAAEgI,GAAE7D,EAAEsC,kBAAkBlC,IAAIA,EAAEgB,QAAQ,CAACtkB,KAAK,cAAcukB,UAAS,IAAK3F,IAAE,WAAYxc,EAAEkhB,EAAEmF,eAAetF,GAAE,EAAI,IAAGpgB,IAAE,WAAYsjB,GAAEN,KAAII,GAAE7kB,OAAO,EAAE8kB,GAAE2C,QAAQhK,EAAE6E,GAAE,OAAOT,EAAEqB,GAAErG,EAAEY,EAAEgI,GAAE7D,EAAEsC,iBAAkB,IAAI,IAAG,ECyBjoJwD,IDzBupJ,SAAS7K,EAAE+E,GAAGA,EAAEA,GAAG,CAAC,EAAEiC,IAAE,WAAY,IAAIxG,EAAEwE,EAAE8B,KAAIlG,EAAE6E,GAAE,OAAOxhB,EAAE,SAAS+b,GAAG+E,EAAEsC,mBAAmBrH,EAAEA,EAAE9I,OAAO,IAAI8I,EAAEpQ,SAAQ,SAAUoQ,GAAGA,EAAEmH,UAAUnC,EAAE+B,kBAAkBnG,EAAEhf,MAAM+Z,KAAKyL,IAAIpH,EAAEmH,UAAU5B,KAAI,GAAG3E,EAAEzc,QAAQ,CAAC6b,GAAGQ,IAAK,GAAE,EAAE2E,EAAE5E,GAAE,2BAA2Btc,GAAG,GAAGkhB,EAAE,CAAC3E,EAAE6F,GAAErG,EAAEY,EAAEiI,GAAE9D,EAAEsC,kBAAkB,IAAIZ,EAAED,IAAE,WAAYsC,GAAElI,EAAEvV,MAAMpH,EAAEkhB,EAAEmF,eAAenF,EAAE+B,aAAa4B,GAAElI,EAAEvV,KAAI,EAAGmV,GAAE,GAAK,IAAG,CAAC,UAAU,SAAS5Q,SAAQ,SAAUoQ,GAAGhR,iBAAiBgR,GAAE,WAAY,OAAO0I,GAAEjC,EAAG,GAAE,CAACqE,MAAK,EAAG1B,SAAQ,GAAK,IAAG3I,GAAEgG,GAAG7hB,IAAE,SAAUogB,GAAGpE,EAAE6E,GAAE,OAAOjF,EAAE6F,GAAErG,EAAEY,EAAEiI,GAAE9D,EAAEsC,kBAAkBf,IAAE,WAAY1F,EAAEhf,MAAMwjB,YAAY7gB,MAAMygB,EAAEE,UAAU4D,GAAElI,EAAEvV,KAAI,EAAGmV,GAAE,EAAI,GAAG,GAAE,CAAE,GAAE,EC0B3wKuK,KD1Bu8K,SAAS/K,EAAE+E,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAIvE,EAAEiF,GAAE,QAAQT,EAAEqB,GAAErG,EAAEQ,EAAEuI,GAAEhE,EAAEsC,kBAAkB2B,IAAE,WAAY,IAAIpI,EAAEuE,KAAIvE,IAAIJ,EAAE5e,MAAM+Z,KAAKyL,IAAIxG,EAAE0E,cAAcC,KAAI,GAAG/E,EAAErc,QAAQ,CAACyc,GAAGoE,GAAE,GAAIpgB,IAAE,WAAY4b,EAAEiF,GAAE,OAAO,IAAIT,EAAEqB,GAAErG,EAAEQ,EAAEuI,GAAEhE,EAAEsC,oBAAmB,EAAI,IAAI,GAAE,GEA7qL,IAAI,GAAE,GAAE,GAAE,WAAW,IAAI7G,EAAErT,KAAKiY,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,GAAG,GAAG7E,GAAGA,EAAE8E,cAAc,GAAG9E,EAAE8E,cAAcF,YAAY7gB,MAAM,OAAOic,CAAC,EAAE,GAAE,SAASA,GAAG,GAAG,YAAYzR,SAASka,WAAW,MAAM,UAAU,IAAIjJ,EAAE,KAAI,GAAGA,EAAE,CAAC,GAAGQ,EAAER,EAAEgL,eAAe,MAAM,UAAU,GAAG,IAAIhL,EAAEiL,4BAA4BzK,EAAER,EAAEiL,2BAA2B,MAAM,kBAAkB,GAAG,IAAIjL,EAAEkL,aAAa1K,EAAER,EAAEkL,YAAY,MAAM,oBAAoB,CAAC,MAAM,UAAU,EAAE,GAAE,SAAS1K,GAAG,IAAIR,EAAEQ,EAAE2K,SAAS,OAAO,IAAI3K,EAAE4K,SAASpL,EAAEqL,cAAcrL,EAAEsL,cAAc1F,QAAQ,KAAK,GAAG,EAAE,GAAE,SAASpF,EAAER,GAAG,IAAI+E,EAAE,GAAG,IAAI,KAAKvE,GAAG,IAAIA,EAAE4K,UAAU,CAAC,IAAIpG,EAAExE,EAAE5b,EAAEogB,EAAE3Z,GAAG,IAAI2Z,EAAE3Z,GAAG,GAAE2Z,IAAIA,EAAEuG,WAAWvG,EAAEuG,UAAU3pB,OAAOojB,EAAEuG,UAAU3pB,MAAM4pB,QAAQxG,EAAEuG,UAAU3pB,MAAM4pB,OAAOroB,OAAO,IAAI6hB,EAAEuG,UAAU3pB,MAAM4pB,OAAO5F,QAAQ,OAAO,KAAK,IAAI,GAAGb,EAAE5hB,OAAOyB,EAAEzB,QAAQ6c,GAAG,KAAK,EAAE,OAAO+E,GAAGngB,EAAE,GAAGmgB,EAAEA,EAAEngB,EAAE,IAAImgB,EAAEngB,EAAEogB,EAAE3Z,GAAG,MAAMmV,EAAEwE,EAAEyG,UAAU,CAAC,CAAC,MAAMjL,GAAG,CAAC,OAAOuE,CAAC,EAAE,IAAG,EAAE,GAAE,WAAW,OAAO,EAAC,EAAE,GAAE,SAASvE,GAAGxR,iBAAiB,YAAW,SAAUgR,GAAGA,EAAEiF,YAAY,GAAEjF,EAAEkF,UAAU1E,EAAER,GAAI,IAAE,EAAG,EAAE,GAAE,WAAW,IAAIQ,EAAE,KAAI,OAAOA,GAAGA,EAAEgF,iBAAiB,CAAC,EAAE,GAAE,SAAShF,EAAER,GAAG,IAAIgF,EAAE,KAAIpE,EAAE,WAAgK,OAArJ,MAAK,EAAEA,EAAE,qBAAqBoE,IAAIjW,SAAS2W,cAAc,KAAI,EAAE9E,EAAE,YAAY7R,SAAS4W,aAAa/E,EAAE,UAAUoE,EAAEnjB,OAAO+e,EAAEoE,EAAEnjB,KAAK+jB,QAAQ,KAAK,OAAa,CAACve,KAAKmZ,EAAE5e,WAAM,IAASoe,GAAG,EAAEA,EAAE6F,OAAO,OAAOC,MAAM,EAAE3hB,QAAQ,GAAGkH,GAAG,MAAM2X,OAAO1e,KAAKC,MAAM,KAAKye,OAAOrH,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMkK,eAAenF,EAAE,EAAE,GAAE,SAASJ,EAAER,EAAE+E,GAAG,IAAI,GAAGiB,oBAAoBC,oBAAoB3gB,SAASkb,GAAG,CAAC,IAAIwE,EAAE,IAAIgB,qBAAoB,SAAUxF,GAAGd,QAAQC,UAAUS,MAAK,WAAYJ,EAAEQ,EAAE0F,aAAc,GAAG,IAAG,OAAOlB,EAAEmB,QAAQpkB,OAAOwG,OAAO,CAAC1G,KAAK2e,EAAE4F,UAAS,GAAIrB,GAAG,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMxE,GAAG,CAAC,EAAE,GAAE,SAASA,EAAER,EAAE+E,EAAEC,GAAG,IAAIpE,EAAEhc,EAAE,OAAO,SAASX,GAAG+b,EAAEpe,OAAO,IAAIqC,GAAG+gB,MAAMpgB,EAAEob,EAAEpe,OAAOgf,GAAG,UAAK,IAASA,KAAKA,EAAEZ,EAAEpe,MAAMoe,EAAE8F,MAAMlhB,EAAEob,EAAE6F,OAAO,SAASrF,EAAER,GAAG,OAAOQ,EAAER,EAAE,GAAG,OAAOQ,EAAER,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEpe,MAAMmjB,GAAGvE,EAAER,GAAG,CAAC,EAAE,GAAE,SAASQ,GAAG+F,uBAAsB,WAAY,OAAOA,uBAAsB,WAAY,OAAO/F,GAAI,GAAG,GAAE,EAAE,GAAE,SAASA,GAAGzR,SAASC,iBAAiB,oBAAmB,WAAY,WAAWD,SAASE,iBAAiBuR,GAAI,GAAE,EAAE,GAAE,SAASA,GAAG,IAAIR,GAAE,EAAG,OAAO,WAAWA,IAAIQ,IAAIR,GAAE,EAAG,CAAC,EAAE,IAAG,EAAE,GAAE,WAAW,MAAM,WAAWjR,SAASE,iBAAiBF,SAAS2W,aAAa,IAAI,CAAC,EAAE,GAAE,SAASlF,GAAG,WAAWzR,SAASE,iBAAiB,IAAG,IAAI,GAAE,qBAAqBuR,EAAE3e,KAAK2e,EAAE0E,UAAU,EAAE,KAAI,EAAE,GAAE,WAAWlW,iBAAiB,mBAAmB,IAAE,GAAIA,iBAAiB,qBAAqB,IAAE,EAAG,EAAE,GAAE,WAAW6X,oBAAoB,mBAAmB,IAAE,GAAIA,oBAAoB,qBAAqB,IAAE,EAAG,EAAE,GAAE,WAAW,OAAO,GAAE,IAAI,GAAE,KAAI,KAAI,IAAE,WAAYhM,YAAW,WAAY,GAAE,KAAI,IAAI,GAAE,EAAG,KAAI,CAAC,mBAAIkM,GAAkB,OAAO,EAAC,EAAE,EAAE,GAAE,SAASvG,GAAGzR,SAAS2W,aAAa1W,iBAAiB,sBAAqB,WAAY,OAAOwR,GAAI,IAAE,GAAIA,GAAG,EAAE,GAAE,CAAC,KAAK,KAAK,GAAE,SAASA,EAAER,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAE,WAAY,IAAI+E,EAAEC,EAAE,KAAIpE,EAAE,GAAE,OAAOhc,EAAE,GAAE,SAAQ,SAAU4b,GAAGA,EAAE5Q,SAAQ,SAAU4Q,GAAG,2BAA2BA,EAAEnZ,OAAOzC,EAAEsiB,aAAa1G,EAAE2G,UAAUnC,EAAE+B,kBAAkBnG,EAAEhf,MAAM+Z,KAAKyL,IAAI5G,EAAE2G,UAAU,KAAI,GAAGvG,EAAEzc,QAAQiL,KAAKoR,GAAGuE,GAAE,IAAM,GAAG,IAAGngB,IAAImgB,EAAE,GAAEvE,EAAEI,EAAE,GAAEZ,EAAEqH,kBAAkB,IAAE,SAAUrC,GAAGpE,EAAE,GAAE,OAAOmE,EAAE,GAAEvE,EAAEI,EAAE,GAAEZ,EAAEqH,kBAAkB,IAAE,WAAYzG,EAAEhf,MAAMwjB,YAAY7gB,MAAMygB,EAAEE,UAAUH,GAAE,EAAI,GAAG,IAAI,GAAE,EAAE,GAAE,CAAC,GAAG,KAAg4C,GAAE,EAAE,GAAE,IAAI,GAAE,EAAE,GAAE,SAASvE,GAAGA,EAAE5Q,SAAQ,SAAU4Q,GAAGA,EAAEkH,gBAAgB,GAAE/L,KAAKgM,IAAI,GAAEnH,EAAEkH,eAAe,GAAE/L,KAAKyL,IAAI,GAAE5G,EAAEkH,eAAe,GAAE,IAAG,GAAE,IAAG,EAAE,EAAE,EAAG,GAAE,EAAE,GAAE,WAAW,OAAO,GAAE,GAAEtC,YAAYyC,kBAAkB,CAAC,EAAqH,GAAE,GAAG,GAAE,IAAIlY,IAAI,GAAE,EAA8E,GAAE,GAAG,GAAE,SAAS6Q,GAAG,GAAG,GAAE5Q,SAAQ,SAAUoQ,GAAG,OAAOA,EAAEQ,EAAG,IAAGA,EAAEkH,eAAe,gBAAgBlH,EAAE6H,UAAU,CAAC,IAAIrI,EAAE,GAAE,GAAE7c,OAAO,GAAG4hB,EAAE,GAAEhV,IAAIyQ,EAAEkH,eAAe,GAAG3C,GAAG,GAAE5hB,OAAO,IAAIqd,EAAE8H,SAAStI,EAAEuI,QAAQ,CAAC,GAAGxD,EAAEvE,EAAE8H,SAASvD,EAAEwD,SAASxD,EAAE5gB,QAAQ,CAACqc,GAAGuE,EAAEwD,QAAQ/H,EAAE8H,UAAU9H,EAAE8H,WAAWvD,EAAEwD,SAAS/H,EAAE2G,YAAYpC,EAAE5gB,QAAQ,GAAGgjB,WAAWpC,EAAE5gB,QAAQiL,KAAKoR,OAAO,CAAC,IAAIwE,EAAE,CAAC3Z,GAAGmV,EAAEkH,cAAca,QAAQ/H,EAAE8H,SAASnkB,QAAQ,CAACqc,IAAI,GAAExQ,IAAIgV,EAAE3Z,GAAG2Z,GAAG,GAAE5V,KAAK4V,EAAE,CAAC,GAAEwD,MAAK,SAAUhI,EAAER,GAAG,OAAOA,EAAEuI,QAAQ/H,EAAE+H,OAAQ,IAAG,GAAEplB,OAAO,IAAI,GAAE8O,OAAO,IAAIrC,SAAQ,SAAU4Q,GAAG,OAAO,GAAEiI,OAAOjI,EAAEnV,GAAI,GAAE,CAAC,CAAC,EAAE,GAAE,SAASmV,GAAG,IAAIR,EAAE7S,KAAKwb,qBAAqBxb,KAAK0N,WAAWkK,GAAG,EAAE,OAAOvE,EAAE,GAAEA,GAAG,WAAWzR,SAASE,gBAAgBuR,KAAKuE,EAAE/E,EAAEQ,GAAG,GAAEA,IAAIuE,CAAC,EAAE,GAAE,CAAC,IAAI,KAAK,GAAE,SAASvE,EAAER,GAAG,2BAA2B7S,MAAM,kBAAkBud,uBAAuB1oB,YAAYge,EAAEA,GAAG,CAAC,EAAE,IAAE,WAAY,IAAI+E,EAAhgC,qBAAqBK,aAAa,KAAI,GAAE,GAAE,QAAQ,GAAE,CAACvjB,KAAK,QAAQukB,UAAS,EAAG2B,kBAAkB,KAAs6B,IAAI/C,EAAEpE,EAAE,GAAE,OAAOhc,EAAE,SAAS4b,GAAG,IAAE,WAAYA,EAAE5Q,QAAQ,IAAG,IAAIoQ,EAAz8B,WAAW,IAAIQ,EAAE7E,KAAKgM,IAAI,GAAExkB,OAAO,EAAEwY,KAAKC,OAAO,KAAI,IAAG,KAAK,OAAO,GAAE4E,EAAE,CAAm4B,GAAIR,GAAGA,EAAEuI,UAAU3H,EAAEhf,QAAQgf,EAAEhf,MAAMoe,EAAEuI,QAAQ3H,EAAEzc,QAAQ6b,EAAE7b,QAAQ6gB,IAAK,GAAE,EAAE/gB,EAAE,GAAE,QAAQW,EAAE,CAACmjB,kBAAkB,QAAQhD,EAAE/E,EAAE+H,yBAAoB,IAAShD,EAAEA,EAAE,KAAKC,EAAE,GAAExE,EAAEI,EAAE,GAAEZ,EAAEqH,kBAAkBpjB,IAAIA,EAAEkiB,QAAQ,CAACtkB,KAAK,cAAcukB,UAAS,IAAK,IAAE,WAAYxhB,EAAEX,EAAEqmB,eAAetF,GAAE,EAAI,IAAG,IAAE,WAAY,GAAE,KAAI,GAAE7hB,OAAO,EAAE,GAAEynB,QAAQhK,EAAE,GAAE,OAAOoE,EAAE,GAAExE,EAAEI,EAAE,GAAEZ,EAAEqH,iBAAkB,IAAI,IAAG,EAAE,GAAE,GAAG,GAAE,GAAG,GAAE,EAAE,GAAE,IAAIqE,QAAQ,GAAE,IAAI/b,IAAI,IAAG,EAAE,GAAE,SAAS6Q,GAAG,GAAE,GAAEwC,OAAOxC,GAAG,IAAG,EAAE,GAAE,WAAW,GAAE,IAAI,GAAE,GAAE,IAAG,EAAE,GAAE,WAAW,GAAEa,KAAK,IAAI,GAAEzR,SAAQ,SAAU4Q,EAAER,GAAG,GAAEpc,IAAIoc,IAAI,GAAEyI,OAAOzI,EAAG,IAAG,IAAIQ,EAAE,GAAE9a,KAAI,SAAU8a,GAAG,OAAO,GAAEzQ,IAAIyQ,EAAErc,QAAQ,GAAI,IAAG6b,EAAE,GAAE7c,OAAO,GAAG,GAAE,GAAEsN,QAAO,SAAUsU,EAAEC,GAAG,OAAOA,GAAGhF,GAAGQ,EAAElb,SAASyf,EAAG,IAAG,IAAI,IAAIA,EAAE,IAAI4G,IAAI3G,EAAE,EAAEA,EAAE,GAAE7hB,OAAO6hB,IAAI,CAAC,IAAIpE,EAAE,GAAEoE,GAAG4G,GAAGhL,EAAEuG,UAAUvG,EAAEiL,eAAejc,SAAQ,SAAU4Q,GAAGuE,EAAElhB,IAAI2c,EAAG,GAAE,CAAC,IAAI5b,EAAE,GAAEzB,OAAO,EAAE,GAAG,GAAE,GAAEsN,QAAO,SAAU+P,EAAER,GAAG,OAAOQ,EAAE2G,UAAU,IAAGnH,EAAEpb,GAAGmgB,EAAEnhB,IAAI4c,EAAG,IAAG,IAAG,CAAC,EAAE,GAAEpR,MAAK,SAAUoR,GAAGA,EAAEkH,eAAelH,EAAEiJ,SAAS,GAAE7lB,IAAI4c,EAAEkH,gBAAgB,GAAE1X,IAAIwQ,EAAEkH,cAAclH,EAAEiJ,OAAQ,IAAE,SAAUjJ,GAAG,IAAIR,EAAE+E,EAAEvE,EAAE2G,UAAU3G,EAAE8H,SAAS,GAAE3M,KAAKyL,IAAI,GAAE5G,EAAEqL,eAAe,IAAI,IAAI7G,EAAE,GAAE7hB,OAAO,EAAE6hB,GAAG,EAAEA,IAAI,CAAC,IAAIpE,EAAE,GAAEoE,GAAG,GAAGrJ,KAAKmQ,IAAI/G,EAAEnE,EAAEmL,aAAa,EAAE,EAAE/L,EAAEY,GAAGuG,UAAUxL,KAAKgM,IAAInH,EAAE2G,UAAUnH,EAAEmH,WAAWnH,EAAE2J,gBAAgBhO,KAAKgM,IAAInH,EAAEmJ,gBAAgB3J,EAAE2J,iBAAiB3J,EAAE6L,cAAclQ,KAAKyL,IAAI5G,EAAEqL,cAAc7L,EAAE6L,eAAe7L,EAAE7b,QAAQiL,KAAKoR,GAAG,KAAK,CAAC,CAACR,IAAIA,EAAE,CAACmH,UAAU3G,EAAE2G,UAAUwC,gBAAgBnJ,EAAEmJ,gBAAgBkC,cAAcrL,EAAEqL,cAAcE,WAAWhH,EAAE5gB,QAAQ,CAACqc,IAAI,GAAEpR,KAAK4Q,KAAKQ,EAAEkH,eAAe,gBAAgBlH,EAAE6H,YAAY,GAAErY,IAAIwQ,EAAER,GAAG,IAAI,IAAG,IAAI,GAAE,GAAEgM,GAAGC,GAAGL,GAAG,SAASpL,EAAER,GAAG,IAAI,IAAI+E,EAAEC,EAAE,GAAGpE,EAAE,EAAEmE,EAAE,GAAEnE,GAAGA,IAAI,KAAKmE,EAAEoC,UAAUpC,EAAEuD,SAAS9H,GAAG,CAAC,GAAGuE,EAAEoC,UAAUnH,EAAE,MAAMgF,EAAE5V,KAAK2V,EAAE,CAAC,OAAOC,CAAC,EAA4zBkH,GAAG,CAAC,KAAK,KAAKC,GAAG,CAAC,EAAq1CC,GAAG,CAAC,IAAI,MAAMC,GAAG,SAAS7L,EAAER,GAAGjR,SAAS2W,aAAa,IAAE,WAAY,OAAOlF,EAAER,EAAG,IAAG,aAAajR,SAASka,WAAWja,iBAAiB,QAAO,WAAY,OAAOwR,EAAER,EAAG,IAAE,GAAInF,WAAWmF,EAAE,EAAE,EAAEsM,GAAG,SAAS9L,EAAER,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAIgF,EAAE,GAAE,QAAQpE,EAAE,GAAEJ,EAAEwE,EAAEoH,GAAGpM,EAAEqH,kBAAkBgF,IAAG,WAAY,IAAIznB,EAAE,KAAIA,IAAIogB,EAAEpjB,MAAM+Z,KAAKyL,IAAIxiB,EAAE0gB,cAAc,KAAI,GAAGN,EAAE7gB,QAAQ,CAACS,GAAGgc,GAAE,GAAI,IAAE,WAAYoE,EAAE,GAAE,OAAO,IAAIpE,EAAE,GAAEJ,EAAEwE,EAAEoH,GAAGpM,EAAEqH,oBAAmB,EAAI,IAAI,GAAE,EAAmhBkF,GAAG,CAACpD,SAAQ,EAAGC,SAAQ,GAAIoD,GAAG,IAAIloB,KAAKmoB,GAAG,SAASjM,EAAER,GAAG,KAAI,GAAEA,EAAE,GAAEQ,EAAEwL,GAAG,IAAI1nB,KAAKooB,GAAG7F,qBAAqB8F,KAAK,EAAEA,GAAG,WAAW,GAAG,IAAG,GAAG,GAAEX,GAAGQ,GAAG,CAAC,IAAIhM,EAAE,CAAC6H,UAAU,cAAchhB,KAAK,GAAExF,KAAK4nB,OAAO,GAAEA,OAAOC,WAAW,GAAEA,WAAWvC,UAAU,GAAEjC,UAAUyE,gBAAgB,GAAEzE,UAAU,IAAG+G,GAAGrc,SAAQ,SAAUoQ,GAAGA,EAAEQ,EAAG,IAAGyL,GAAG,EAAE,CAAC,EAAEW,GAAG,SAASpM,GAAG,GAAGA,EAAEkJ,WAAW,CAAC,IAAI1J,GAAGQ,EAAE0E,UAAU,KAAK,IAAI5gB,KAAK8gB,YAAY7gB,OAAOic,EAAE0E,UAAU,eAAe1E,EAAE3e,KAAK,SAAS2e,EAAER,GAAG,IAAI+E,EAAE,WAAW0H,GAAGjM,EAAER,GAAGY,GAAG,EAAEoE,EAAE,WAAWpE,GAAG,EAAEA,EAAE,WAAWiG,oBAAoB,YAAY9B,EAAEwH,IAAI1F,oBAAoB,gBAAgB7B,EAAEuH,GAAG,EAAEvd,iBAAiB,YAAY+V,EAAEwH,IAAIvd,iBAAiB,gBAAgBgW,EAAEuH,GAAG,CAArO,CAAuOvM,EAAEQ,GAAGiM,GAAGzM,EAAEQ,EAAE,CAAC,EAAEkM,GAAG,SAASlM,GAAG,CAAC,YAAY,UAAU,aAAa,eAAe5Q,SAAQ,SAAUoQ,GAAG,OAAOQ,EAAER,EAAE4M,GAAGL,GAAI,GAAE,EAAEM,GAAG,CAAC,IAAI,KAA4bC,GAAG,SAAStM,EAAER,IAAlc,SAASQ,EAAER,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAE,WAAY,IAAI+E,EAAEC,EAAE,KAAIpE,EAAE,GAAE,OAAOhc,EAAE,SAAS4b,GAAGA,EAAE2G,UAAUnC,EAAE+B,kBAAkBnG,EAAEhf,MAAM4e,EAAEmJ,gBAAgBnJ,EAAE2G,UAAUvG,EAAEzc,QAAQiL,KAAKoR,GAAGuE,GAAE,GAAI,EAAE9gB,EAAE,SAASuc,GAAGA,EAAE5Q,QAAQhL,EAAE,EAAEugB,EAAE,GAAE,cAAclhB,GAAG8gB,EAAE,GAAEvE,EAAEI,EAAEiM,GAAG7M,EAAEqH,kBAAkBlC,IAAI,GAAE,IAAE,WAAYlhB,EAAEkhB,EAAEmF,eAAenF,EAAE+B,YAAa,KAAI,IAAE,WAAY,IAAIlC,EAAEpE,EAAE,GAAE,OAAOmE,EAAE,GAAEvE,EAAEI,EAAEiM,GAAG7M,EAAEqH,kBAAkB4E,GAAG,GAAG,IAAG,EAAE,GAAE,KAAKS,GAAG1d,kBAAkBgW,EAAEpgB,EAAEqnB,GAAG7c,KAAK4V,GAAG2H,IAAK,IAAI,GAAE,CAAmBI,EAAG,SAAU/M,GAAG,IAAI+E,EAAE,SAASvE,GAAG,IAAIR,EAAEQ,EAAErc,QAAQ,GAAG4gB,EAAE,CAACiI,YAAY,GAAEhN,EAAEyJ,QAAQwD,UAAUjN,EAAE3Y,KAAK6lB,UAAUlN,EAAEmH,UAAUgG,WAAWnN,EAAEoN,UAAU,GAAEpN,EAAEmH,YAAY,OAAOplB,OAAOwG,OAAOiY,EAAE,CAAC6M,YAAYtI,GAAG,CAA/K,CAAiL/E,GAAGQ,EAAEuE,EAAG,GAAE/E,EAAE,ECA5gY,MAAMsN,GAA4B,oCCMnCC,GAAe,aACfC,GAAqB,qBACpB,MAAMC,GACT,WAAApf,CAAYqf,EAAqB3D,GAC7Bvb,KAAKkf,oBAAsBA,EAC3Blf,KAAKub,eAAiBA,CAC1B,CACA,UAAAzW,GACI9E,KAAKmf,aACLnf,KAAKof,aACLpf,KAAKqf,aACLrf,KAAKsf,aACLtf,KAAKuf,aACLvf,KAAKwf,aACT,CACA,UAAAL,GACI,IAAItnB,GFtBytG,SAASma,EAAER,IAAI,SAASQ,EAAER,GAAGA,EAAEA,GAAG,CAAC,EAAE,GAAE,IAAE,WAAY,IAAI+E,EAAEC,EAAE,GAAE,MAAM,GAAGpE,EAAE,EAAEhc,EAAE,GAAGX,EAAE,SAASuc,GAAGA,EAAE5Q,SAAQ,SAAU4Q,GAAG,IAAIA,EAAE6J,eAAe,CAAC,IAAIrK,EAAEpb,EAAE,GAAGmgB,EAAEngB,EAAEA,EAAEzB,OAAO,GAAGyd,GAAGJ,EAAE2G,UAAUpC,EAAEoC,UAAU,KAAK3G,EAAE2G,UAAUnH,EAAEmH,UAAU,KAAKvG,GAAGJ,EAAE5e,MAAMgD,EAAEwK,KAAKoR,KAAKI,EAAEJ,EAAE5e,MAAMgD,EAAE,CAAC4b,GAAG,CAAE,IAAGI,EAAEoE,EAAEpjB,QAAQojB,EAAEpjB,MAAMgf,EAAEoE,EAAE7gB,QAAQS,EAAEmgB,IAAI,EAAEI,EAAE,GAAE,eAAelhB,GAAGkhB,IAAIJ,EAAE,GAAEvE,EAAEwE,EAAE,GAAEhF,EAAEqH,kBAAkB,IAAE,WAAYpjB,EAAEkhB,EAAEmF,eAAevF,GAAE,EAAI,IAAG,IAAE,WAAYnE,EAAE,EAAEoE,EAAE,GAAE,MAAM,GAAGD,EAAE,GAAEvE,EAAEwE,EAAE,GAAEhF,EAAEqH,kBAAkB,IAAE,WAAY,OAAOtC,GAAI,GAAG,IAAGlK,WAAWkK,EAAE,GAAI,IAAG,CAA3f,EAA6f,SAAU/E,GAAG,IAAI+E,EAAE,SAASvE,GAAG,IAAIR,EAAE+E,EAAE,CAAC,EAAE,GAAGvE,EAAErc,QAAQhB,OAAO,CAAC,IAAIyd,EAAEJ,EAAErc,QAAQqO,QAAO,SAAUgO,EAAER,GAAG,OAAOQ,GAAGA,EAAE5e,MAAMoe,EAAEpe,MAAM4e,EAAER,CAAE,IAAG,GAAGY,GAAGA,EAAEqN,SAASrN,EAAEqN,QAAQ9qB,OAAO,CAAC,IAAIc,GAAG+b,EAAEY,EAAEqN,SAASC,MAAK,SAAU1N,GAAG,OAAOA,EAAE2N,MAAM,IAAI3N,EAAE2N,KAAK/C,QAAS,KAAIpL,EAAE,GAAG/b,IAAI8gB,EAAE,CAACqJ,mBAAmB,GAAEnqB,EAAEkqB,MAAME,iBAAiBzN,EAAEuG,UAAUmH,kBAAkB1N,EAAEhf,MAAM2sB,mBAAmBtqB,EAAEuqB,kBAAkB5N,EAAEwM,UAAU,GAAExM,EAAEuG,YAAY,CAAC,CAAC,OAAOplB,OAAOwG,OAAOiY,EAAE,CAAC6M,YAAYtI,GAAG,CAA/a,CAAib/E,GAAGQ,EAAEuE,EAAG,GAAE/E,EAAE,CEuBzrI,EAAOmK,IACH,MAAM,UAAEiD,EAAS,kBAAEkB,EAAiB,iBAAED,EAAgB,mBAAED,GAAuBjE,EAAOkD,YAChFzhB,EAAS4C,KAAKigB,mBAAmBtE,GACvC3b,KAAKkgB,aAAa9iB,EAAQ,sBAAuB0iB,GACjD9f,KAAKkgB,aAAa9iB,EAAQ,qBAAsByiB,GAChD,MAAMpnB,EAAUuH,KAAKmgB,oBAAoBxE,GACzC3b,KAAKkgB,aAAaznB,EAASsmB,GAAcH,GACzC5e,KAAKkgB,aAAaznB,EAAS,uBAAwBmnB,GACnD5f,KAAK7C,gBAAgBC,EAAQ3E,EAAQ,GACtC,CAAEogB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAC9F,CACA,UAAAuG,GACI,IAAIvnB,GFnCyrI,SAASma,EAAER,GAAG,IAAE,SAAUA,GAAG,IAAIY,EAAE,SAASJ,GAAG,IAAIR,EAAE,CAAC4O,gBAAgB,EAAEC,eAAerO,EAAE5e,MAAMwrB,UAAU,GAAE,OAAM,GAAG5M,EAAErc,QAAQhB,OAAO,CAAC,IAAIyd,EAAE,KAAIhc,EAAE4b,EAAErc,QAAQqc,EAAErc,QAAQhB,OAAO,GAAG,GAAGyd,EAAE,CAAC,IAAI3c,EAAE2c,EAAE4E,iBAAiB,EAAED,EAAE5J,KAAKyL,IAAI,EAAExG,EAAE0E,cAAcrhB,GAAG+b,EAAE,CAAC4O,gBAAgBrJ,EAAEsJ,eAAerO,EAAE5e,MAAM2jB,EAAE6H,UAAU,GAAE5M,EAAErc,QAAQ,GAAGgjB,WAAW2H,gBAAgBlO,EAAEmO,SAASnqB,EAAE,CAAC,CAAC,OAAO7C,OAAOwG,OAAOiY,EAAE,CAAC6M,YAAYrN,GAAG,CAAtW,CAAwWA,GAAGQ,EAAEI,EAAG,GAAEZ,EAAE,CEoCplJ,EAAOmK,IACH,MAAM,eAAE0E,EAAc,gBAAED,EAAe,UAAExB,GAAcjD,EAAOkD,YACxDzhB,EAAS4C,KAAKigB,mBAAmBtE,GACvC3b,KAAKkgB,aAAa9iB,EAAQ,oBAAqBijB,GAC/CrgB,KAAKkgB,aAAa9iB,EAAQ4hB,GAAoBoB,GAC9C,MAAM3nB,EAAUuH,KAAKmgB,oBAAoBxE,GACzC3b,KAAKkgB,aAAaznB,EAASsmB,GAAcH,GACzC5e,KAAK7C,gBAAgBC,EAAQ3E,EAAQ,GACtC,CAAEogB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAC9F,CACA,UAAAwG,GACI,IAAIxnB,EACJ,IAAO8jB,IACH,MAAM,UAAE+C,EAAS,YAAEF,EAAW,UAAEC,EAAS,UAAEG,GAAcjD,EAAOkD,YAC1DzhB,EAAS4C,KAAKigB,mBAAmBtE,GACvC3b,KAAKkgB,aAAa9iB,EAAQ,aAAcshB,GACxC,MAAMjmB,EAAUuH,KAAKmgB,oBAAoBxE,GACzC3b,KAAKkgB,aAAaznB,EAAS,eAAgB+lB,GAC3Cxe,KAAKkgB,aAAaznB,EAAS,aAAcgmB,GACzCze,KAAKkgB,aAAaznB,EAASsmB,GAAcH,GACzC5e,KAAK7C,gBAAgBC,EAAQ3E,EAAQ,GACtC,CAAEogB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAC9F,CACA,UAAAyG,GACI,IAAIznB,GF5Do/O,SAASma,EAAEuE,GAAG,KAAI,GAAE,GAAE,uBAAuB,KAAI,IAAE,SAAU/E,GAAG,IAAI+E,EAAE,SAASvE,GAAG,IAAIR,EAAEQ,EAAErc,QAAQ,GAAG4gB,EAAE,GAAEhV,IAAIiQ,GAAGY,EAAEZ,EAAE2J,gBAAgB1lB,EAAE8gB,EAAE8G,cAAc1G,EAAEJ,EAAE5gB,QAAQqkB,MAAK,SAAUhI,EAAER,GAAG,OAAOQ,EAAEmJ,gBAAgB3J,EAAE2J,eAAgB,IAAGpE,EAAEqG,GAAG5L,EAAEmH,UAAUljB,GAAGsc,EAAEC,EAAErc,QAAQ+pB,MAAK,SAAU1N,GAAG,OAAOA,EAAEiJ,MAAO,IAAGhE,EAAElF,GAAGA,EAAEkJ,QAAQ,GAAE1Z,IAAIiQ,EAAE0H,eAAerB,EAAE,CAACrG,EAAEmH,UAAUnH,EAAEsI,SAASrkB,GAAG+e,OAAOuC,EAAE7f,KAAI,SAAU8a,GAAG,OAAOA,EAAE2G,UAAU3G,EAAE8H,QAAS,KAAIhC,EAAE3K,KAAKyL,IAAI/G,MAAM1E,KAAK0K,GAAGI,EAAE,CAACuI,kBAAkB,GAAEvJ,GAAGwJ,yBAAyBxJ,EAAEyJ,gBAAgBlP,EAAE3Y,KAAKuP,WAAW,OAAO,WAAW,UAAUuY,gBAAgBnP,EAAEmH,UAAUiI,cAAc9I,EAAE+I,sBAAsBlK,EAAEmK,0BAA0B/J,EAAEgK,WAAW3O,EAAEZ,EAAEmH,UAAUqI,mBAAmBvrB,EAAE2c,EAAE6O,kBAAkB9T,KAAKyL,IAAId,EAAEriB,EAAE,GAAGmpB,UAAU,GAAEpN,EAAEmH,YAAY,OAAOplB,OAAOwG,OAAOiY,EAAE,CAAC6M,YAAY5G,GAAG,CAAjuB,CAAmuBzG,GAAGQ,EAAEuE,EAAG,GAAEA,EAAE,CE6D7yQ,EAAOoF,IACH,MAAM,gBAAEgF,EAAe,kBAAEM,EAAiB,WAAEF,EAAU,mBAAEC,EAAkB,cAAEJ,EAAa,UAAEhC,EAAS,kBAAE4B,EAAiB,gBAAEE,GAAqB/E,EAAOkD,YAC/IzhB,EAAS4C,KAAKigB,mBAAmBtE,GACvC3b,KAAKkgB,aAAa9iB,EAAQ,mBAAoBujB,GAC9C3gB,KAAKkgB,aAAa9iB,EAAQ,qBAAsB6jB,GAChDjhB,KAAKkgB,aAAa9iB,EAAQ,cAAe2jB,GACzC/gB,KAAKkgB,aAAa9iB,EAAQ,sBAAuB4jB,GACjDhhB,KAAKkgB,aAAa9iB,EAAQ,kBAAmBwjB,GAC7C,MAAMnoB,EAAUuH,KAAKmgB,oBAAoBxE,GACzC3b,KAAKkgB,aAAaznB,EAASsmB,GAAcH,GACzC5e,KAAKkgB,aAAaznB,EAAS,qBAAsB+nB,GACjDxgB,KAAKkgB,aAAaznB,EAAS,mBAAoBioB,GAC/C1gB,KAAK7C,gBAAgBC,EAAQ3E,EAAQ,GACtC,CAAEogB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAC9F,CACA,UAAA0G,GACI,IAAI1nB,GF7Ek0Q,SAASma,EAAER,IAAI,SAASQ,EAAER,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAE,WAAY,IAAI+E,EAAEC,EAAE,KAAIpE,EAAE,GAAE,OAAOhc,EAAE,SAAS4b,GAAGR,EAAEqH,mBAAmB7G,EAAEA,EAAEtJ,OAAO,IAAIsJ,EAAE5Q,SAAQ,SAAU4Q,GAAGA,EAAE2G,UAAUnC,EAAE+B,kBAAkBnG,EAAEhf,MAAM+Z,KAAKyL,IAAI5G,EAAE2G,UAAU,KAAI,GAAGvG,EAAEzc,QAAQ,CAACqc,GAAGuE,IAAK,GAAE,EAAE9gB,EAAE,GAAE,2BAA2BW,GAAG,GAAGX,EAAE,CAAC8gB,EAAE,GAAEvE,EAAEI,EAAEsL,GAAGlM,EAAEqH,kBAAkB,IAAIlC,EAAE,IAAE,WAAYgH,GAAGvL,EAAEvV,MAAMzG,EAAEX,EAAEqmB,eAAermB,EAAEijB,aAAaiF,GAAGvL,EAAEvV,KAAI,EAAG0Z,GAAE,GAAK,IAAG,CAAC,UAAU,SAASnV,SAAQ,SAAU4Q,GAAGxR,iBAAiBwR,GAAE,WAAY,OAAO,GAAE2E,EAAG,GAAE,CAAC2F,MAAK,EAAG1B,SAAQ,GAAK,IAAG,GAAEjE,GAAG,IAAE,SAAUH,GAAGpE,EAAE,GAAE,OAAOmE,EAAE,GAAEvE,EAAEI,EAAEsL,GAAGlM,EAAEqH,kBAAkB,IAAE,WAAYzG,EAAEhf,MAAMwjB,YAAY7gB,MAAMygB,EAAEE,UAAUiH,GAAGvL,EAAEvV,KAAI,EAAG0Z,GAAE,EAAI,GAAG,GAAE,CAAE,GAAE,CAAznB,EAA2nB,SAAU/E,GAAG,IAAIgF,EAAE,SAASxE,GAAG,IAAIR,EAAE,CAAC4O,gBAAgB,EAAEc,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmBpP,EAAE5e,OAAO,GAAG4e,EAAErc,QAAQhB,OAAO,CAAC,IAAI6hB,EAAE,KAAI,GAAGA,EAAE,CAAC,IAAIpE,EAAEoE,EAAEQ,iBAAiB,EAAEvhB,EAAEuc,EAAErc,QAAQqc,EAAErc,QAAQhB,OAAO,GAAGgiB,EAAElhB,EAAEwV,KAAK2L,YAAYC,iBAAiB,YAAY5U,QAAO,SAAU+P,GAAG,OAAOA,EAAEnZ,OAAOpD,EAAEwV,GAAI,IAAG,GAAG8L,EAAE5J,KAAKyL,IAAI,EAAEpC,EAAEM,cAAc1E,GAAGL,EAAE5E,KAAKyL,IAAI7B,EAAEJ,GAAGA,EAAE0K,cAAc1K,EAAEgC,WAAWvG,EAAE,GAAG6E,EAAE9J,KAAKyL,IAAI7G,EAAE4E,EAAEA,EAAE2K,YAAYlP,EAAE,GAAGyF,EAAE1K,KAAKyL,IAAI3B,EAAExhB,EAAEkjB,UAAUvG,GAAGZ,EAAE,CAAC+P,QAAQ,GAAE9rB,EAAE8rB,SAASnB,gBAAgBrJ,EAAEmK,kBAAkBnP,EAAEgF,EAAEoK,qBAAqBlK,EAAElF,EAAEqP,mBAAmBvJ,EAAEZ,EAAEqJ,gBAAgB9J,EAAEgL,SAAS/rB,GAAGA,EAAEwV,MAAMuG,EAAEvG,IAAIxV,EAAEwV,KAAK0L,IAAInF,EAAEiQ,iBAAiB9K,EAAE,CAAC,CAAC,OAAOpjB,OAAOwG,OAAOiY,EAAE,CAAC6M,YAAYrN,GAAG,CAAnqB,CAAqqBA,GAAGQ,EAAEwE,EAAG,GAAEhF,EAAE,CE8EppT,EAAOmK,IACH,MAAM,mBAAEyF,EAAkB,kBAAEF,EAAiB,qBAAEC,EAAoB,gBAAEf,EAAe,QAAEmB,GAAY5F,EAAOkD,YACnGzhB,EAAS4C,KAAKigB,mBAAmBtE,GACvC3b,KAAKkgB,aAAa9iB,EAAQ,uBAAwBgkB,GAClDphB,KAAKkgB,aAAa9iB,EAAQ,sBAAuB8jB,GACjDlhB,KAAKkgB,aAAa9iB,EAAQ,yBAA0B+jB,GACpDnhB,KAAKkgB,aAAa9iB,EAAQ4hB,GAAoBoB,GAC9C,MAAM3nB,EAAUuH,KAAKmgB,oBAAoBxE,GACzC3b,KAAKkgB,aAAaznB,EAAS,UAAW8oB,GACtCvhB,KAAK7C,gBAAgBC,EAAQ3E,EAAQ,GACtC,CAAEogB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAC9F,CACA,WAAA2G,GACI,IAAI3nB,GF3FyjU,SAASma,EAAER,GAAGsM,IAAG,SAAUtM,GAAG,IAAI+E,EAAE,SAASvE,GAAG,IAAIR,EAAE,CAACkQ,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,gBAAgB,GAAG,GAAG9P,EAAErc,QAAQhB,OAAO,CAAC,IAAI4hB,EAAEvE,EAAErc,QAAQ,GAAG6gB,EAAED,EAAES,iBAAiB,EAAE5E,EAAEjF,KAAKyL,KAAKrC,EAAEwL,aAAaxL,EAAEyL,YAAYxL,EAAE,GAAGpgB,EAAE+W,KAAKyL,IAAIrC,EAAE0L,kBAAkBzL,EAAE,GAAG/gB,EAAE0X,KAAKyL,IAAIrC,EAAE2L,aAAa1L,EAAE,GAAGG,EAAExJ,KAAKyL,IAAIrC,EAAE4L,WAAW3L,EAAE,GAAGhF,EAAE,CAACkQ,gBAAgBtP,EAAEuP,cAAcvrB,EAAEgc,EAAEwP,YAAYnsB,EAAEW,EAAEyrB,mBAAmBlL,EAAElhB,EAAEqsB,gBAAgB9P,EAAE5e,MAAMujB,EAAE2J,gBAAgB/J,EAAE,CAAC,OAAOhjB,OAAOwG,OAAOiY,EAAE,CAAC6M,YAAYrN,GAAG,CAA1d,CAA4dA,GAAGQ,EAAEuE,EAAG,GAAE/E,EAAE,CE4FzkV,EAAQmK,IACJ,MAAM,YAAEiG,EAAW,mBAAEC,EAAkB,gBAAEC,EAAe,gBAAEJ,EAAe,cAAEC,GAAkBhG,EAAOkD,YAC9FzhB,EAAS4C,KAAKigB,mBAAmBtE,GACvC3b,KAAKkgB,aAAa9iB,EAAQ,eAAgBwkB,GAC1C5hB,KAAKkgB,aAAa9iB,EAAQ,sBAAuBykB,GACjD7hB,KAAKkgB,aAAa9iB,EAAQ,mBAAoB0kB,GAC9C9hB,KAAKkgB,aAAa9iB,EAAQ,mBAAoBskB,GAC9C1hB,KAAKkgB,aAAa9iB,EAAQ,iBAAkBukB,GAC5C,MAAMlpB,EAAUuH,KAAKmgB,oBAAoBxE,GACzC3b,KAAK7C,gBAAgBC,EAAQ3E,EAAQ,GACtC,CAAEogB,iBAAiD,QAA9BhhB,EAAKmI,KAAKub,sBAAmC,IAAP1jB,OAAgB,EAASA,EAAGghB,kBAC9F,CACA,kBAAAoH,CAAmBtE,GACf,MAAMF,EAAYE,EAAO9iB,KAAKgkB,cAC9B,MAAO,CACH,CAACpB,GAAYE,EAAOvoB,MACpBkkB,MAAOqE,EAAOrE,MAEtB,CACA,mBAAA6I,CAAoBxE,GAChB,IAAI9jB,EACJ,MAAMuqB,EAA0F,QAArEvqB,EAAK+U,GAAQkS,GAA2BxS,WAA4C,IAAPzU,EAAgBA,EAAKmR,GAC7H,MAAO,CACHnM,GAAI8e,EAAO9e,GACXwa,OAAQsE,EAAOtE,OACfgL,gBAAiB1G,EAAOpE,eACxB+K,oBAAqBF,EAE7B,CACA,eAAAjlB,CAAgBC,EAAQ3E,GAEpBuH,KAAKkf,oBAAoB,CAAE7rB,KADd,aACoB+J,UAAU,CAAE3E,WACjD,CACA,YAAAynB,CAAazK,EAAQ/f,EAAKimB,GAClBA,IACAlG,EAAO/f,GAAOimB,EAEtB,EC9HG,MAAM4G,WAAiC1N,GAC1C,WAAAhV,GACI0S,SAASuC,WACT9U,KAAKnH,KAAO,mDACZmH,KAAKsF,QAAU1G,CACnB,CACA,UAAAkG,GACI9E,KAAKmL,SAAS,gBACInL,KAAKwiB,oCACb1d,YACd,CACA,iCAAA0d,GACI,IAAI3qB,EACJ,OAA2B,QAAtBA,EAAKmI,KAAKvI,cAA2B,IAAPI,OAAgB,EAASA,EAAG4qB,2BACpD,IAAIxD,GAAyBjf,KAAKjB,IAAI5B,gBAAiB6C,KAAKvI,OAAOirB,0BAEvE,IAAIpH,GAAetb,KAAKjB,IAAI5B,gBAAiB6C,KAAKvI,OAAOirB,yBACpE,ECfG,MAAMC,WAA+B9N,GACxC,WAAAhV,GACI0S,SAASuC,WACT9U,KAAKnH,KAAO,gDACZmH,KAAKsF,QAAU1G,CACnB,CACA,qBAAAgkB,CAAsBtpB,GAClB,IAAIzB,EAAIkI,EACR,MAAMjE,EAAUxC,EAAKwC,QACrB,GAAIA,GAAWA,EAAQe,MAAwC,QAA/BhF,EAAKmI,KAAK6iB,uBAAoC,IAAPhrB,OAAgB,EAASA,EAAGgF,IAAK,CACpG,GAAImD,KAAK6iB,iBAAmB7iB,KAAK6iB,gBAAgBhmB,MAAsC,QAA7BkD,EAAKjE,EAAQyB,kBAA+B,IAAPwC,OAAgB,EAASA,EAAoB,iBAGxI,OAFAC,KAAKjB,IAAIzB,UrBGW,iBqBHqB,CAAC,OAAGpD,EAAW,CAAE3B,YAAY,SACtEyH,KAAK6iB,gBAAkB/mB,GAG3BkE,KAAK6iB,gBAAkB/mB,EAGvBkE,KAAKjB,IAAIzB,UAAUgQ,GAAqB,CAAC,OAAGpT,EAAW,CAAE3B,YAAY,GACzE,CACJ,CACA,oBAAAuqB,CAAqBC,EAAgBC,GACjC,IAAInrB,EAAIkI,EAAI2N,EAAIqB,EAAIC,EAAIC,EACxB,IASIgU,EACAC,EAVAC,EAAoBJ,EAAe3U,mBACvC,GAAI4U,EAAela,YAAcka,EAAeja,2BAA6Boa,EAAmB,CAC5F,MAAMptB,EAAMF,IACyBstB,EAAkBnV,aAAejY,EAAMitB,EAAeja,4BAEvFmH,GAA0BI,oBAC1B6S,EAAoB,KAE5B,CAGA,GAAIjV,GAAmBiV,GAAoB,CACvC,MAAMrV,EAAYqV,aAA6D,EAASA,EAAkBrV,UAC1GoV,EAAiBrV,GAAwB,CACrCC,YACAL,UAAW0V,EAAkB1V,YAAa,EAC1CM,QAASoV,aAA6D,EAASA,EAAkBpV,UAErG,MAAMqV,EAAwBD,aAA6D,EAASA,EAAkBvU,YAGhH3S,EAAY1I,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAqC,QAAjClC,EAAKmrB,EAAelnB,eAA4B,IAAPjE,OAAgB,EAASA,EAAGoE,WAAYmnB,aAAqE,EAASA,EAAsBnnB,WACxOinB,EAAetU,YAAcrb,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGipB,EAAelnB,SAAU,CAAEe,GAAIiR,EAAWvQ,WAAYhK,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAqC,QAAjCgG,EAAKijB,EAAelnB,eAA4B,IAAPiE,OAAgB,EAASA,EAAGxC,YAAa6lB,aAAqE,EAASA,EAAsB7lB,YAAa,CAE7VkQ,UAAWyV,EAAezV,UAAUha,aAAewI,cAC3DgnB,EAAgB1V,EACpB,KACK,CACD,MAAMO,EAAgG,QAAnFiB,EAAuC,QAAjCrB,EAAKsV,EAAelnB,eAA4B,IAAP4R,OAAgB,EAASA,EAAG7Q,UAAuB,IAAPkS,EAAgBA,ECvDnI,SAAuBxR,GAC1B,IAAI1F,EAAIkI,EAAI2N,EAAIqB,EAChB,MAAO,CACHlS,GAAmN,QAA9MkS,EAA4J,QAAtJrB,EAA2F,QAArF3N,EAA4B,QAAtBlI,EAAKgH,EAAKpH,cAA2B,IAAPI,OAAgB,EAASA,EAAG6N,uBAAoC,IAAP3F,OAAgB,EAASA,EAAGkO,yBAAsC,IAAPP,OAAgB,EAASA,EAAGha,KAAKqM,UAAwB,IAAPgP,EAAgBA,EAAK7B,KAChP3P,aAER,CDiD+I8lB,GAAgBxmB,GACnJqmB,EAAiBrV,GAAwB,CACrCC,YACAL,UAAWA,OAEf,MAAMxR,EAA8C,QAAjC+S,EAAKgU,EAAelnB,eAA4B,IAAPkT,OAAgB,EAASA,EAAG/S,UACxFinB,EAAetU,YAAcrb,OAAOwG,OAAO,CAAE8C,GAAIiR,EAAWvQ,WAAYhK,OAAOwG,OAAO,CAAE0T,UAAWyV,EAAezV,UAAUha,YAAgD,QAAjCwb,EAAK+T,EAAelnB,eAA4B,IAAPmT,OAAgB,EAASA,EAAG1R,aAAgBtB,EAAY,CAAEA,aAAc,CAAC,GAC7PgnB,EAAgB3V,EACpB,CACA,MAAO,CAAE4V,iBAAgBD,gBAC7B,CACA,sBAAAK,CAAuBP,GACnB,IAAIlrB,EACJ,MAAM,cAAEsY,GAAkB,IAAI4S,EACH,QAA1BlrB,EAAKmI,KAAKrI,kBAA+B,IAAPE,GAAyBA,EAAGgL,oBAAoBxJ,IAC/E,IAAIxB,EAAIkI,EAAI2N,EACZyC,IACA,MAAM5S,EAA0C,QAA5B1F,EAAKwB,EAAKC,KAAKwC,eAA4B,IAAPjE,OAAgB,EAASA,EAAG0F,WACpF,GAAIA,GAAoG,UAArFA,aAA+C,EAASA,EAAsB,WAAe,CAC5G,IAAIgmB,EAAUxuB,KAAK0b,MAAM1b,KAAKC,UAAUqE,IACxC,MAAMmqB,EAAgD,QAA/BzjB,EAAKwjB,EAAQjqB,KAAKwC,eAA4B,IAAPiE,OAAgB,EAASA,EAAGxC,WAK1F,OAJAimB,gBAAmEA,EAAyB,UACQ,IAAhGjwB,OAAOqB,KAAK4uB,QAAqDA,EAAgB,CAAC,GAAG7uB,SACrD,QAA/B+Y,EAAK6V,EAAQjqB,KAAKwC,eAA4B,IAAP4R,UAA8BA,EAAGnQ,YAEtEgmB,CACX,CACA,OAAO,IAAI,GAEnB,CACA,UAAAze,GACI9E,KAAKmL,SAAS,gCACd,MAAMoD,EAAwBvO,KAAKvI,OAAOiO,gBAC1C,GAAI6I,aAAqE,EAASA,EAAsB9L,QAAS,CAC7G,MAAMsgB,EAAiBnS,GAA0BrC,GACjDvO,KAAKsjB,uBAAuBP,GAC5B,MAAM,eAAEG,EAAc,cAAED,GAAkBjjB,KAAK8iB,qBAAqBC,EAAgBxU,GACpFwU,EAAe1U,iBAAiB6U,GAChC,MAAMO,EAAqBP,EAAetU,YAC1C5O,KAAK6iB,gBAAkBY,EACvBzjB,KAAKjB,IAAIlD,WAAW4nB,GAChBR,IAAkB3V,IAClBtN,KAAKjB,IAAIzB,UAAUgQ,GAAqB,CAAC,OAAGpT,EAAW,CAAE3B,YAAY,IAErE0qB,IAAkB1V,IAClBvN,KAAKjB,IAAIzB,UAAUiQ,GAAsB,CAAC,OAAGrT,EAAW,CAAE3B,YAAY,GAE9E,CACAyH,KAAKtI,MAAM6M,YAAYvE,KAAK4iB,sBAAsBc,KAAK1jB,MAC3D,EEvGG,MAAM2jB,WAA4B9O,GACrC,WAAAhV,GACI0S,SAASuC,WACT9U,KAAKnH,KAAO,6CACZmH,KAAKsF,QAAU1G,CACnB,CACA,oBAAAglB,CAAqBtqB,GACjB,IAAIzB,EAAIkI,EAAI2N,EAAIqB,EAChB,MAAMvS,EAAOlD,EAAKkD,KACdA,GAAQA,EAAK3D,QAAuC,QAA5BhB,EAAKmI,KAAK6jB,oBAAiC,IAAPhsB,OAAgB,EAASA,EAAGgB,QACxFmH,KAAKjB,IAAIzB,UvBIa,euBJiB,CACnCwmB,SAA2F,QAAhFpW,EAAkC,QAA5B3N,EAAKC,KAAK6jB,oBAAiC,IAAP9jB,OAAgB,EAASA,EAAGlH,YAAyB,IAAP6U,EAAgBA,EAAK1E,GACxH+a,OAA6B,QAApBhV,EAAKvS,EAAK3D,YAAyB,IAAPkW,EAAgBA,EAAK/F,SAC3D9O,EAAW,CAAE3B,YAAY,IAC5ByH,KAAK6jB,aAAernB,EAE5B,CACA,UAAAsI,GACI9E,KAAKtI,MAAM6M,YAAYvE,KAAK4jB,qBAAqBF,KAAK1jB,MAC1D,ECrBG,MCCDgkB,GAAuB,4CAEtB,SAASC,GAA+BC,EAAgB,IAC3D,IAAK,MAAMC,KAAeD,EACtB,GAAyB,gBAArBC,EAAYtrB,KAAwB,CACpC,IAAKmrB,GAAqBvb,KAAK0b,EAAYC,aACvC,SAEJ,MAAO,CAAEzqB,EAASE,GAAUsqB,EAAYC,YAAYvc,MAAM,KAC1D,GAAe,MAAXlO,GAA6B,MAAVE,EACnB,MAAO,CAAEF,UAASE,UAEtB,KACJ,CAGR,CAIO,SAASwqB,GAAkBC,EAAc,GAAIC,GAChD,OAAOD,EAAYrrB,MAAMgS,GAAQA,GAA+B,MAAxBsZ,EAAUprB,MAAM8R,IAC5D,CAeO,SAASuZ,GAAwBC,EAAsBC,EAAa,CAAC,GACxE,IAAK,MAAOC,EAAcC,KAAmBrxB,OAAOoC,QAAQ+uB,GAAa,CACrE,MAAMG,EAAmBJ,EAAqBE,GAC9C,OAAwB,MAApBE,IAGAzwB,EAAQwwB,GACDA,EAAe9tB,SAAS+tB,GAE5BA,IAAqBD,EAChC,CAEA,OAAO,CACX,CACO,SAASE,GAAyBC,GACrC,MAAM,WAAE5C,EAAU,aAAED,EAAY,gBAAE8C,EAAe,gBAAEC,EAAe,kBAAEhD,EAAiB,SAAEnI,EAAQ,gBAAEoL,EAAe,WAAElD,EAAU,cAAEmD,EAAa,KAAEtsB,EAAI,gBAAEusB,EAAe,YAAEC,EAAW,cAAEC,EAEjLC,qBAAsBC,EAAG,aAAEnE,EAAY,YAAEC,EAAW,cAAExK,EAAa,eAEnE2O,EAAc,sBAAEC,EAAqB,aAAEC,EAAY,YAAE5D,GAAiBgD,EACtE,MAAO,CACHlsB,KAAMA,EACNihB,SAAU8L,GAA8B9L,GACxC+L,iBAAkBD,GAA8BzD,EAAaD,GAC7D4D,cAAeF,GAA8BX,EAAkBhD,GAC/D8D,mBAAoBH,GAA8BvE,EAAeqE,GACjED,eAAgBG,GAA8BH,GAC9CO,aAAcJ,GAA8BP,EAAcC,GAC1DW,YAAaL,GAA8B9O,EAAgBuK,GAC3D6E,aAAcN,GAA8BtE,EAAcxK,GAC1DqP,UAAWP,GAA8BtE,EAAcU,GACvDoE,kBAAmBR,GAA8B5D,EAAaD,GAC9DiD,gBAAiBY,GAA8BZ,GAC/CE,gBAAiBU,GAA8BV,GAC/CmB,eASJ,WACI,IAAIC,EAAY,WACK,IAAjBX,EACIX,EAAkB,IAClBsB,EAAY,SAIM,MAAlBb,EACuB,MAAnBA,IACAa,EAAY,oBAGXpB,EAAkB,GAAKS,EAAeT,IAC3CoB,EAAY,oBAGpB,OAAOA,CACX,CA3BoBC,GAChBhB,qBAAsBK,GAA8BJ,GACpDgB,SAAUpB,EACVD,cAAeA,EACf1kB,gBAAiBF,SAASE,gBAC1B8b,KAAMqJ,GAA8B9O,EAAgBuK,GAuB5D,CACO,SAASoF,GAA2BC,GACvC,MAAM,gBAAE1P,EAAe,YAAE0F,EAAW,yBAAEiK,EAAwB,2BAAElK,EAA0B,eAAED,EAAc,WAAEwF,EAAU,aAAE4E,EAAY,eAAEC,EAAc,cAAE/P,EAAa,KAAEzjB,GAAUqzB,EACzKI,EAOV,WACI,IAAIjvB,EACJ,GAAsF,OAAnD,QAA7BA,EAAK+e,YAAYmQ,cAA2B,IAAPlvB,OAAgB,EAASA,EAAGmvB,YAInE,OAAOpQ,YAAYmQ,OAAOC,WAAapQ,YAAYqQ,WAEvD,OAAO,IACX,CAhBwBC,GACpB,OAAO3zB,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG+qB,GAAyB4B,IAAsB,CAAES,aAAcvB,GAA8BlJ,EAAcsF,GAAaoF,oBAAqBxB,GAA8BkB,EAActK,EAAiBsK,EAAc,MAAOO,kBAAmBzB,GAA8BlJ,EAAcF,GAAiB8K,0BAA2B1B,GAA8Be,EAA2BlK,GAA6B8K,WAAY3B,GAA8BgB,EAAeC,GAI5ftK,KAAMqJ,GAA8BzY,KAAKyL,IAAI9B,GAAiBE,QAAyDA,EAAkB,GAAI,IAAK3jB,KAAMA,GAChK,CAWA,SAASuyB,GAA8B5N,GACnC,OAAS,MAALA,EACOhP,GAEM,iBAANgP,EACA7K,KAAKqa,MAAMxP,GAAGvkB,WAElBukB,EAAEvkB,UACb,CC7HA,MAAMg0B,GAA0B,CAAEtC,cAAe,CAAC,iBAAkB,UCHpE,IAAI,GAAwC,SAAUrU,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUje,GAAS,IAAMke,EAAKL,EAAUM,KAAKne,GAAS,CAAE,MAAOoe,GAAKJ,EAAOI,EAAI,CAAE,CAC1F,SAASC,EAASre,GAAS,IAAMke,EAAKL,EAAiB,MAAE7d,GAAS,CAAE,MAAOoe,GAAKJ,EAAOI,EAAI,CAAE,CAC7F,SAASF,EAAKI,GAJlB,IAAete,EAIase,EAAOC,KAAOR,EAAQO,EAAOte,QAJ1CA,EAIyDse,EAAOte,MAJhDA,aAAiB4d,EAAI5d,EAAQ,IAAI4d,GAAE,SAAUG,GAAWA,EAAQ/d,EAAQ,KAIjBwe,KAAKP,EAAWI,EAAW,CAC7GH,GAAML,EAAYA,EAAUY,MAAMf,EAASC,GAAc,KAAKQ,OAClE,GACJ,EAKO,MAAMmW,WAAmC7S,GAC5C,WAAAhV,GACI0S,SAASuC,WACT9U,KAAKnH,KAAO,oDACZmH,KAAKsF,QAAU1G,CACnB,CACA,UAAAkG,GFAO,wBAAyBjE,OAK7B,SAAyB8mB,GAC5B,GAA4B,aAAxBpnB,SAASka,WACTkN,QAEC,CACD,MAAMC,EAA4B,KACF,aAAxBrnB,SAASka,aACTkN,IACApnB,SAAS8X,oBAAoB,mBAAoBuP,GACrD,EAEJrnB,SAASC,iBAAiB,mBAAoBonB,EAClD,CACJ,CEbQC,EAAgB,IAAM,GAAU7nB,UAAM,OAAQ,GAAQ,YAClD,MAAM1C,EAAY0C,KAAKjB,IAAIzB,UACrBgnB,EAActkB,KAAKwL,iBACnB,iBAAEsc,SCtBb,SAA8BxqB,EAAWgnB,GAC5C,IAAIyD,EACJ,MAAMC,EAA6B,IAAI9W,SAASC,IAC5C4W,EAA6B5W,CAAO,IAuBxC,OArBiB,IAAIqG,qBAAqByQ,IACtC,IAAIpwB,EACJ,MAAO6uB,GAAsBuB,EAAgBvQ,aAC7C,GAA0B,MAAtBgP,GAA8BrC,GAAkBC,EAAaoC,EAAmB7tB,MAChF,OAEJ,MAAMqvB,EAAexB,EAAmByB,SACxC,IAAIzvB,EAAcurB,GAA+BiE,aAAmD,EAASA,EAAaE,cAC1H,MAAMC,EAAiG,QAArExwB,EAAK+U,GAAQkS,GAA2BxS,WAA4C,IAAPzU,EAAgBA,EAAKmR,GAC9Hsf,EAAsB/0B,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG0sB,GAA2ByB,IAAgB,CAAEJ,iBAAkB5a,KAAcmb,6BACzI3b,GAAQoS,GAA2BwJ,EAAoBR,iBAAkBxb,IACzEhP,EAAU,8BAA+BgrB,OAAqBpuB,EAAW,CACrExB,cACAC,qBAAsBie,YAAYqQ,WAAaiB,EAAavP,YAEhEoP,EAA2BO,EAAoB,IAE1C3Q,QAAQ,CACbtkB,KJ5BwB,aI6BxBukB,UAAU,IAEPoQ,CACX,CDL+CO,CAAqBjrB,EAAWgnB,GAC3C,MAApBwD,GDxBT,SAAgCA,EAAkBxqB,EAAWgnB,GAChE,MAAMkE,EAAiB3pB,EAAKpH,OAAO+wB,eAClB,IAAIhR,qBAAqByQ,IACtC,MAAMtyB,EAAUsyB,EAAgBvQ,aAChC,IAAK,MAAMqN,KAAoBpvB,EAAS,CACpC,GAAI0uB,GAAkBC,EAAaS,EAAiBlsB,MAChD,OAEJ,MAAM4vB,EAAoB1D,EAAiBoD,SAC3C,IAAIzvB,EAAcurB,GAA+BwE,aAA6D,EAASA,EAAkBL,cACzI,GAAuB,MAAlBI,GAA0BhE,GAAwBiE,EAAmBhB,KACtEe,EAAgB,CAChB,MAAME,EAAoBn1B,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG+qB,GAAyB2D,IAAqB,CAAEX,mBAAkBa,eAAgBzb,OAC5I5P,EAAU,4BAA6BorB,OAAmBxuB,EAAW,CACjExB,cACAC,qBAAsBie,YAAYqQ,WAAawB,EAAkB9P,WAEzE,CACJ,KAEKhB,QAAQ,CACbtkB,KFxBsB,WEyBtBukB,UAAU,GAElB,CCCgBgR,CAAuBd,EAAkBxqB,EAAWgnB,EAE5D,MAVItkB,KAAKmL,SAAS,2EAWtB,CACA,aAAAK,GACI,IAAI3T,EACJ,OAA6C,QAArCA,EAAKmI,KAAKrI,WAAWA,kBAA+B,IAAPE,OAAgB,EAASA,EAAGgxB,SAASzmB,GAAcA,EAAUoJ,iBACtH,EElCG,MAAMsd,WAA+BjU,GACxC,WAAAhV,CAAY9D,EAAU,CAAC,GACnBwW,QACAvS,KAAKjE,QAAUA,EACfiE,KAAKnH,KAAO,gDACZmH,KAAKsF,QAAU1G,EACfoB,KAAK+oB,gBAAkBtuB,CAC3B,CACA,UAAAqK,GACI,IAAIjN,EAAIkI,EAAI2N,EAAIqB,EAChB/O,KAAKjE,QAAUxI,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGiG,KAAKjE,SAAUiE,KAAKvI,OAAOuxB,wBAC1E,MAAMC,GAA2C,QAAvBpxB,EAAKmI,KAAKjE,eAA4B,IAAPlE,OAAgB,EAASA,EAAGoxB,qBAA+C,QAAvBlpB,EAAKC,KAAKjE,eAA4B,IAAPgE,OAAgB,EAASA,EAAGgpB,iBACxK/oB,KAAK+oB,gBAAkBE,EACuE,QAAtFla,EAA6B,QAAvBrB,EAAK1N,KAAKjE,eAA4B,IAAP2R,OAAgB,EAASA,EAAGqb,uBAAoC,IAAPha,EAAgBA,EAAK/X,EACrHyD,EACN,KACKwH,QAAQhF,IAAY,IAAIpF,EAAIkI,EAAI,QAA+F,QAArFA,EAA6B,QAAvBlI,EAAKmI,KAAKjE,eAA4B,IAAPlE,OAAgB,EAASA,EAAGqxB,sBAAmC,IAAPnpB,EAAgBA,EAAK+oB,GAAuBK,uBAAuBryB,SAASmG,EAAM,IACzNmE,SAASnE,IAEVkB,QAAQlB,GAAS,IAAIhG,KACjB,IAAIY,EAAIkI,EACR,IACI,GAAI9C,IAAU,KAAS3K,QAAmC,QAAvBuF,EAAKmI,KAAKjE,eAA4B,IAAPlE,OAAgB,EAASA,EAAGuxB,mBAQzF,GAAInsB,IAAU,KAAS3K,QAAkC,QAAvByN,EAAKC,KAAKjE,eAA4B,IAAPgE,OAAgB,EAASA,EAAGqpB,mBAAoB,CAClH,MAAM,MAAEh2B,EAAK,KAAEC,EAAI,YAAEmF,GAAgBsd,GAA+B7e,EAAM+I,KAAK+oB,iBAC/E/oB,KAAKjB,IAAI/B,QAAQ5J,EAAQ,CAAC01B,GAAuBO,mBAAqBj2B,GAAS6D,EAAM,CACjFgG,QACAxE,QAAS,CACLrF,MAAOA,QAAqCA,EAAQ,GACpDC,KAAMA,QAAmCA,EAAO,GAChDmF,aAAcA,aAAiD,EAASA,EAAY7D,QAAUqC,EAA2BwB,GAAe,KAGpJ,MAEIwH,KAAKjB,IAAI/B,QAAQ/F,EAAM,CAAEgG,cApBqF,CAC9G,MAAM,MAAE7J,EAAK,KAAEC,EAAI,YAAEmF,GAAgBsd,GAA+B7e,EAAM+I,KAAK+oB,iBAC/E,GAAI31B,IAAUC,IAASmF,EAEnB,YADAwH,KAAKjB,IAAI1G,UAAU,IAAI7D,MAAMs0B,GAAuBO,mBAAqBj2B,IAG7E4M,KAAKjB,IAAI1G,UAAU,IAAI7D,MAAMs0B,GAAuBO,mBAAqBj2B,GAAQ,CAAEC,OAAMmF,eAC7F,CAeJ,CACA,MAAO3E,GACHmM,KAAKsL,SAASzX,EAClB,CACA,QACImM,KAAKrF,iBAAiBsC,MAAUhG,EACpC,EACH,GAET,ECtDG,SAASqyB,GAAuBvtB,EAAU,CAAC,GAC9C,MAAMiD,EAAmB,CACrB,IAAImX,GACJ,IAAIoM,GACJ,IAAII,GACJ,IAAIgB,IAWR,OATiD,IAA7C5nB,EAAQwtB,kCAERvqB,EAAiBwqB,QAAQ,IAAI9B,KAEF,IAA3B3rB,EAAQ0tB,gBACRzqB,EAAiB4B,KAAK,IAAIkoB,GAAuB,CAC7CI,eAAgBntB,EAAQ2tB,gCAGzB1qB,CACX,CC0CA,SAAS2qB,GAAmBC,GACxB,IAAI/xB,EAAIkI,EACR,MAAM,KAAE3D,EAAI,eAAE0O,GAAuH,QAAnGjT,EAAK+xB,aAAqD,EAASA,EAAcC,oBAAiC,IAAPhyB,EAAgBA,EAAK,CAAC,EAC7JiyB,EAAe,CACjB7gB,GACA4B,GAAe,CAAEC,iBAAgBC,gBAAiB3O,OACf,QAA9B2D,EAAK6pB,EAAclyB,aAA0B,IAAPqI,EAAgBA,EAAK,IAGpE,OAD2B7L,EAAS2M,OAAO2J,IAEhC,IAAIsf,EAAcxf,IAEtBwf,CACX,CACA,SAASC,IAAiB,oBAAEC,EAAmB,gBAAEtkB,IAC7C,IAAI7N,EACJ,MAAMoE,EAAY,CAAC,EAInB,OAH+G,OAA1G+tB,aAAiE,EAASA,EAAoBvnB,WAC/FxG,EAAUguB,2BAA6BD,EAAoBvnB,SAE3D/N,EAAQuH,GACD,CAAC,EAEL,CACHH,QAASvI,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAyG,QAApGlC,EAAK6N,aAAyD,EAASA,EAAgB5J,eAA4B,IAAPjE,EAAgBA,EAAK,CAAC,GAAK,CAAEoE,cAE5L,CCpFO,SAAS,GAAexE,GAC3B,MAAMyyB,EDMH,SAAwBN,GAC3B,IAAI/xB,EACJ,MAAMF,EAAa,GACbH,EAAiB4G,EAAqBwrB,EAAcjvB,iBAAkBivB,EAAcvrB,qBACtFurB,EAAcjyB,aACViyB,EAAc3e,KAAO2e,EAAchW,SACnCpc,EAAec,MAAM,wEAEzBX,EAAWiJ,QAAQgpB,EAAcjyB,aAE5BiyB,EAAc3e,IACnBtT,EAAWiJ,KAAK,IAAI0R,GAAe,CAC/BrH,IAAK2e,EAAc3e,IACnB2I,OAAQgW,EAAchW,UAI1Bpc,EAAec,MAAM,gDAEzB,MAAM,IAAEiN,EAAG,SAAE/C,EAAQ,WAAEoD,EAAU,uBAAEojB,EAAsB,aAAE7wB,EAAY,gBAAEuN,EAAe,eAAE8iB,EAAc,0BAAE/F,EAAyB,KAAE9mB,EAAI,KAAEa,EAAI,oBAAEwtB,EAAmB,OAEpK1vB,GAAS,EAAI,YAAEmD,EAAcwI,EAAkB,gBAAEvG,EAAkBqG,EAAsB,iBAAE/G,EAAmBsqB,KAAwB,oBAAEjrB,EAAsBJ,EAA0B,QAAEkB,GAAU,EAAK,kBAAEpC,EAAoBtC,EAAwB,MAAE/C,EAAQiyB,GAAmBC,GAAc,OAAExpB,GAAS,EAAK,sBAAEX,GAAwB,EAAK,iBAAE9E,EAAmBuD,EAAuB,yBAAEwkB,GAA8BkH,EAC7Z,MAAO,CACHrkB,MACA/C,SAAUjP,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAGiM,GAAwBxD,GAClElI,OAAQA,EACRoF,kBACAV,mBACAX,sBACAc,UACApC,oBACArF,QACAM,gBAAe,GACfoI,SACAX,wBACA9H,aACAgD,mBACAiL,aACAnI,cACAtF,eAEAsc,YAAiD,QAAnC5c,EAAK+xB,EAAcnV,kBAA+B,IAAP5c,EAAgBA,EAAK,IAAI2c,OAAO,CAAC,2BAC1F9O,gBAAiBnS,OAAOwG,OAAOxG,OAAOwG,OAAOxG,OAAOwG,OAAO,CAAC,EAAG8O,IAA+BnD,GAAkBqkB,GAAiB,CAAEC,sBAAqBtkB,qBACxJ/J,OACAa,OACAgsB,iBACA/F,4BACAuG,yBACAtG,2BAER,CCxDuByH,CAAe1yB,GAClC,GAAKyyB,EAGL,OAAO,EAASA,EACpB,CHiDApB,GAAuBK,sBAAwB,CAAC,KAASh3B,MAAO,KAASD,MAAO,KAASF,KACzF82B,GAAuBO,mBAAqB,kB,0BIvDrC,MAAMe,GAAoB,IAAIjpB,IAAkC,CAWrE,CACE,MACA,CACEkpB,YAAa,MACb5kB,QAAS,mCACT6kB,QAAS,mGAGb,CACE,MACA,CACED,YAAa,MACb5kB,QAAS,mCACT6kB,QAAS,mGAGb,CACE,OACA,CACED,YAAa,OACb5kB,QAAS,oCACT6kB,QAAS,qG,0BC5Bf,IAAIzrB,GAAoB,KAEjB,MAAM0rB,GAAU,IAAM1rB,GAChB2rB,GAAWC,GAA2B5rB,GAAO4rB,EAEnD,SAASC,KACd,GAAIH,KACF,OAGF,MAAMI,ECZD,WACL,MAAMN,GAAcO,EAAAA,GAAAA,KAEpB,GAAKP,GAAgBD,GAAkBh1B,IAAIi1B,GAI3C,OAAOD,GAAkB7oB,IAAI8oB,EAC/B,CDI0BQ,GACxB,IAAKF,EACH,OAGF,MAAM,YAAEN,EAAW,QAAEC,EAAO,QAAE7kB,GAAYklB,GACpC,KAAEG,EAAI,SAAEC,GAAatzB,GAAAA,OACrBuzB,EAAaF,EAAKv4B,GAAAA,IAAW+S,QAC7B2lB,EAAYF,EAASpvB,KAAKuvB,MAEhCV,GACE3mB,GAAe,CACboH,IAAKqf,EACL/kB,IAAK,CACH1M,KAAM4M,EACN0lB,QAASH,EACT1lB,QAAS8lB,GAAAA,EACTf,eAEF1uB,KAAM,CACJuvB,MAAOD,GAETjsB,iBAAkB,IACbsqB,GAAuB,CACxBG,gBAAgB,KAGpBtqB,SAAS,EACTyG,WAAaylB,I,IACNA,EAAAA,EAAL,OAAyB,QAApBA,EAAe,QAAfA,EAAAA,EAAM/xB,KAAK8C,YAAXivB,IAAAA,OAAAA,EAAAA,EAAiBpgB,WAAjBogB,IAAAA,EAAAA,EAAwB,IAAIv0B,SAASrE,GAAAA,IACjC44B,EAGF,IAAI,IAInB,C,+CEpDA,MAAMC,EAAgE,CACpE,CACEC,OAAQ,YACRlB,YAAa,SAEf,CACEkB,OAAQ,mBACRlB,YAAa,OAEf,CACEkB,OAAQ,mBACRlB,YAAa,OAEf,CACEkB,OAAQ,eACRlB,YAAa,SAIV,SAASO,I,IACT/pB,EAAAA,EAAL,KAAKA,QAAAA,EAAAA,cAAAA,IAAAA,GAAgB,QAAhBA,EAAAA,EAAQ0G,gBAAR1G,IAAAA,OAAAA,EAAAA,EAAkB2qB,MACrB,OAAO,KAGT,MAAMC,EAAQH,EAAS5L,MAAK,EAAG6L,YAAaA,EAAO9iB,KAAK5H,OAAO0G,SAASikB,QAExE,OAAOC,EAAQA,EAAMpB,YAAc,IACrC,C,kmBCTE,cAMA,yBAAaqB,EAAmEz0B,G,MAEpD,S,oDAAtB+I,K,IAAK,E,SACP7B,QAAQutB,MAAez0B,EAE3B,CAwDK,MAAM00B,EAAS,IApEf,MAcLlyB,KAAAA,G,IAGE8wB,EAFA,EAAAvqB,KAAK,UAALA,KAAkB,QAAS,IAEpBuqB,QAAPA,GAAAA,EAAAA,EAAAA,aAAAA,IAAAA,GAAAA,EAAWxrB,IAAI/B,QAAQ,GAAI,CACzBC,MAAOnL,EAAAA,GAASI,OAEpB,CAEA4F,KAAAA,IAASb,G,IAGPszB,EAFA,EAAAvqB,KAAK,UAALA,KAAkB,QAAS/I,GAEpBszB,QAAPA,GAAAA,EAAAA,EAAAA,aAAAA,IAAAA,GAAAA,EAAWxrB,IAAI/B,QAAQ/F,EAAM,CAC3BgG,MAAOnL,EAAAA,GAASK,OAEpB,CAEA2L,IAAAA,IAAQ7G,G,IAGNszB,EAFA,EAAAvqB,KAAK,UAALA,KAAkB,OAAQ/I,GAEnBszB,QAAPA,GAAAA,EAAAA,EAAAA,aAAAA,IAAAA,GAAAA,EAAWxrB,IAAI/B,QAAQ/F,EAAM,CAC3BgG,MAAOnL,EAAAA,GAASM,MAEpB,CAEAw5B,GAAAA,IAAO30B,G,IAGLszB,EAFA,EAAAvqB,KAAK,UAALA,KAAkB,MAAO/I,GAElBszB,QAAPA,GAAAA,EAAAA,EAAAA,aAAAA,IAAAA,GAAAA,EAAWxrB,IAAI/B,QAAQ/F,EAAM,CAC3BgG,MAAOnL,EAAAA,GAASE,KAEpB,CAEAgM,IAAAA,IAAQ/G,G,IAGNszB,EAFA,EAAAvqB,KAAK,UAALA,KAAkB,OAAQ/I,GAEnBszB,QAAPA,GAAAA,EAAAA,EAAAA,aAAAA,IAAAA,GAAAA,EAAWxrB,IAAI/B,QAAQ/F,EAAM,CAC3BgG,MAAOnL,EAAAA,GAASO,MAEpB,CAEAiG,KAAAA,CAAMA,EAAcG,G,IAQlB8xB,EAPA,EAAAvqB,KAAK,UAALA,KAAkB,QAAS,CAAC1H,IAExBG,GACF,EAAAuH,KAAK,UAALA,KAAkB,QAAS,CAAC,gBAAiBvH,IAIxC8xB,QAAPA,GAAAA,EAAAA,EAAAA,aAAAA,IAAAA,GAAAA,EAAWxrB,IAAI1G,UAAUC,EAAO,CAC9BG,WAEJ,CA9DAoH,WAAAA,G,YAIA,K,EAAA,G,2CANA,S,uBAAA,I,OAGO,GAAe+qB,EAAAA,EAAAA,KACtB,E,+CCxBK,MAAMQ,EAAa,0C,uBCA1B,OAUA,SAAWvqB,EAAQ3G,GAEf,aAOA,IAGI2xB,EAAc,WACdC,EAAc,YACdC,EAAc,SACdC,EAAc,SACdC,EAAc,QACdC,EAAc,QACdC,EAAc,OACdC,EAAc,OACdC,EAAc,SACdztB,EAAc,UACd0tB,EAAc,eACdC,EAAc,UACdC,EAAc,SACdC,EAAc,SACdC,EAAc,UACdC,EAAc,WACdC,EAAc,WAGdC,EAAU,SACVC,EAAU,QACVC,EAAU,OACVC,EAAa,aACbC,EAAU,UACVC,EAAU,SAEVC,EAAU,UACVC,EAAU,SACVC,EAAU,SACVC,EAAU,KACVC,EAAY,YACZC,EAAY,WACZC,EAAU,QACVC,EAAU,UACVC,EAAU,QACVC,EAAU,OACVC,EAAU,SACVC,EAAU,QACVC,EAAc,WACdC,EAAc,cACdC,EAAU,SACVC,EAAiB,WAiBjBC,EAAY,SAAUC,GAElB,IADA,IAAIC,EAAQ,CAAC,EACJjc,EAAE,EAAGA,EAAEgc,EAAIz5B,OAAQyd,IACxBic,EAAMD,EAAIhc,GAAG0K,eAAiBsR,EAAIhc,GAEtC,OAAOic,CACX,EACAj5B,EAAM,SAAUk5B,EAAMC,GAClB,cAAcD,IAAStC,IAAuD,IAA5CwC,EAASD,GAAM/qB,QAAQgrB,EAASF,GACtE,EACAE,EAAW,SAAUC,GACjB,OAAOA,EAAI5R,aACf,EAIAG,EAAO,SAAUyR,EAAKC,GAClB,UAAU,IAAU1C,EAEhB,OADAyC,EAAMA,EAAIrX,QAAQ,SA9EZ,WA+EO,IAAU0U,EAAa2C,EAAMA,EAAInmB,UAAU,EA5DhD,IA8DpB,EAMIqmB,EAAY,SAAUC,EAAIC,GAKtB,IAHA,IAAWC,EAAG1V,EAAGnH,EAAG2H,EAAGmV,EAAS51B,EAA5BiZ,EAAI,EAGDA,EAAIyc,EAAOl6B,SAAWo6B,GAAS,CAElC,IAAIC,EAAQH,EAAOzc,GACf6c,EAAQJ,EAAOzc,EAAI,GAIvB,IAHA0c,EAAI1V,EAAI,EAGD0V,EAAIE,EAAMr6B,SAAWo6B,GAEnBC,EAAMF,IAGX,GAFAC,EAAUC,EAAMF,KAAK3mB,KAAKymB,GAGtB,IAAK3c,EAAI,EAAGA,EAAIgd,EAAMt6B,OAAQsd,IAC1B9Y,EAAQ41B,IAAU3V,UAClBQ,EAAIqV,EAAMhd,MAEO8Z,GAAYnS,EAAEjlB,OAAS,EACnB,IAAbilB,EAAEjlB,cACSilB,EAAE,IAAMiS,EAEf7rB,KAAK4Z,EAAE,IAAMA,EAAE,GAAGlmB,KAAKsM,KAAM7G,GAG7B6G,KAAK4Z,EAAE,IAAMA,EAAE,GAEC,IAAbA,EAAEjlB,cAEEilB,EAAE,KAAOiS,GAAejS,EAAE,GAAGzR,MAAQyR,EAAE,GAAGnR,KAKjDzI,KAAK4Z,EAAE,IAAMzgB,EAAQA,EAAMie,QAAQwC,EAAE,GAAIA,EAAE,IAAM1f,EAHjD8F,KAAK4Z,EAAE,IAAMzgB,EAAQygB,EAAE,GAAGlmB,KAAKsM,KAAM7G,EAAOygB,EAAE,IAAM1f,EAKpC,IAAb0f,EAAEjlB,SACLqL,KAAK4Z,EAAE,IAAMzgB,EAAQygB,EAAE,GAAGlmB,KAAKsM,KAAM7G,EAAMie,QAAQwC,EAAE,GAAIA,EAAE,KAAO1f,GAG1E8F,KAAK4Z,GAAKzgB,GAAgBe,EAK1CkY,GAAK,CACT,CACJ,EAEA8c,EAAY,SAAUT,EAAKv3B,GAEvB,IAAK,IAAIkb,KAAKlb,EAEV,UAAWA,EAAIkb,KAAO2Z,GAAY70B,EAAIkb,GAAGzd,OAAS,GAC9C,IAAK,IAAIm6B,EAAI,EAAGA,EAAI53B,EAAIkb,GAAGzd,OAAQm6B,IAC/B,GAAI15B,EAAI8B,EAAIkb,GAAG0c,GAAIL,GACf,MAlJN,MAkJcrc,EAAiBlY,EAAYkY,OAG1C,GAAIhd,EAAI8B,EAAIkb,GAAIqc,GACnB,MAtJE,MAsJMrc,EAAiBlY,EAAYkY,EAG7C,OAAOlb,EAAIgb,eAAe,KAAOhb,EAAI,KAAOu3B,CACpD,EAiBIU,EAAoB,CAChB,GAAc,OACd,UAAc,SACd,SAAc,QACd,IAAc,SACd,GAAc,CAAC,SAAU,UACzB,MAAc,SACd,EAAc,SACd,EAAc,SACd,IAAc,SACd,GAAc,CAAC,SAAU,WACzB,GAAc,OAOlBC,EAAU,CAEVplB,QAAU,CAAC,CAEP,gCACG,CAACpL,EAAS,CAACutB,EAAM,WAAY,CAChC,+BACG,CAACvtB,EAAS,CAACutB,EAAM,SAAU,CAG9B,4BACA,mDACA,2CACG,CAACA,EAAMvtB,GAAU,CACpB,yBACG,CAACA,EAAS,CAACutB,EAAMsB,EAAM,UAAW,CACrC,4BACG,CAAC7uB,EAAS,CAACutB,EAAMsB,EAAM,QAAS,CACnC,qBACG,CAAC7uB,EAAS,CAACutB,EAAMsB,IAAS,CAG7B,0DACG,CAAC7uB,EAAS,CAACutB,EAAM,UAAW,CAC/B,+CACG,CAACvtB,EAAS,CAACutB,EAAM,YAAa,CACjC,uBACA,uEAGA,4DACA,2BAGA,+NAEA,sCACA,uBACG,CAACA,EAAMvtB,GAAU,CACpB,6BACG,CAACA,EAAS,CAACutB,EAAM,UAAW,CAC/B,qBACG,CAACvtB,EAAS,CAACutB,EAAM,eAAgB,CACpC,qDACG,CAACvtB,EAAS,CAACutB,EAAM,KAAKc,IAAW,CACpC,+BACA,+BACA,8BACG,CAACruB,EAAS,CAACutB,EAAM,WAAY,CAChC,yBACG,CAACvtB,EAAS,CAACutB,EAAM,cAAe,CACnC,+CACG,CAACvtB,EAAS,CAACutB,EAAM,OAAQ,CAC5B,oCACG,CAACvtB,EAAS,CAACutB,EAAM,WAAY,CAChC,yBACG,CAACvtB,EAAS,CAACutB,EAAM,gBAAgBc,IAAW,CAC/C,2BACG,CAAC,CAACd,EAAM,OAAQ,aAAac,GAAUruB,GAAU,CACpD,uBACG,CAACA,EAAS,CAACutB,EAAMgB,EAAQ,WAAY,CACxC,qBACG,CAACvuB,EAAS,CAACutB,EAAMsB,EAAM,WAAY,CACtC,0BACG,CAAC7uB,EAAS,CAACutB,EAAM,YAAa,CACjC,sBACG,CAACvtB,EAAS,CAACutB,EAAM,YAAa,CACjC,qBACG,CAACvtB,EAAS,CAACutB,EAAMsB,EAAM,WAAY,CACtC,2BACG,CAAC7uB,EAAS,CAACutB,EAAM,OAAS+B,IAAkB,CAC/C,sBACG,CAACtvB,EAAS,CAACutB,EAAMgB,IAAW,CAC/B,+BACG,CAACvuB,EAAS,CAACutB,EAAM,QAAS,CAC7B,sBACG,CAAC,CAACA,EAAM,OAAQ,aAAcvtB,GAAU,CAC3C,yDACG,CAAC,CAACutB,EAAM,OAAQ,KAAO+B,GAAiBtvB,GAAU,CACrD,8BACG,CAACA,EAAS,CAACutB,EAAMuB,EAAU,cAAe,CAC7C,0BACG,CAAC9uB,EAAS,CAACutB,EAAM,mBAAoB,CACxC,4BACG,CAAC,CAACA,EAAM,gBAAiBvtB,GAAU,CACtC,gCACA,iDACA,sEACG,CAACutB,EAAMvtB,GAAU,CACpB,sBACA,sBACG,CAACutB,GAAO,CACX,kCACA,oCACG,CAACvtB,EAASutB,GAAO,CAGpB,+DACG,CAAC,CAACA,EAAM4B,GAAWnvB,GAAU,CAChC,uBACA,uCACA,kCACA,4BACA,4BACA,6BACA,qCACA,iDACG,CAACutB,EAAMvtB,GAAU,CACpB,gCACG,CAACA,EAAS,CAACutB,EAAM,QAAS,CAC7B,8CACG,CAACvtB,EAAS,CAACutB,EAAM,WAAY,CAEhC,oCACG,CAACvtB,EAAS,CAACutB,EAAMe,EAAO,cAAe,CAE1C,+BACG,CAAC,CAACf,EAAMe,EAAO,YAAatuB,GAAU,CAEzC,2DACG,CAACA,EAAS,CAACutB,EAAM,WAAWc,IAAW,CAE1C,+DACG,CAACd,EAAMvtB,GAAU,CAEpB,gDACG,CAACA,EAAS,CAACutB,EAAM,kBAAmB,CACvC,sDACG,CAACvtB,EAASutB,GAAO,CACpB,gDACG,CAACA,EAAM,CAACvtB,EAASswB,EA9JT,CACX,MAAU,KACV,IAAU,KACV,IAAU,KACV,MAAU,OACV,QAAU,OACV,QAAU,OACV,QAAU,OACV,IAAU,OAsJqC,CAE/C,8BACG,CAAC/C,EAAMvtB,GAAU,CAGpB,wCACG,CAAC,CAACutB,EAAM,YAAavtB,GAAU,CAClC,kCACG,CAACutB,EAAMvtB,GAAU,CACpB,uCACG,CAACA,EAAS,CAACutB,EAAMgB,EAAQ,aAAc,CAC1C,6BACA,cACA,8FAEA,+FAEA,wBACA,2CAGA,+GAEA,wBACG,CAAChB,EAAM,CAACvtB,EAAS,KAAM,MAAO,CAEjC,wBACG,CAACutB,EAAM,CAACvtB,EAAS,eAAgB,MAGxCywB,IAAM,CAAC,CAEH,iDACG,CAAC,CAAC/C,EAAc,UAAW,CAE9B,gBACG,CAAC,CAACA,EAAckC,IAAY,CAE/B,0BACG,CAAC,CAAClC,EAAc,SAAU,CAE7B,oCACG,CAAC,CAACA,EAAc,UAAW,CAE9B,mCACG,CAAC,CAACA,EAAc,UAAW,CAG9B,8BACG,CAAC,CAACA,EAAc,QAAS,CAE5B,0CACG,CAAC,CAACA,EAAc,OArXT,GAqXwBkC,IAAY,CAE9C,kBACG,CAAC,CAAClC,EAAc,UAAW,CAE9B,2HAEG,CAAC,CAACA,EAAckC,KAGvBc,OAAS,CAAC,CAON,mFACG,CAACpD,EAAO,CAACG,EAAQqB,GAAU,CAACtB,EAAMK,IAAU,CAC/C,iEACA,kCACA,iBACG,CAACP,EAAO,CAACG,EAAQqB,GAAU,CAACtB,EAAMI,IAAU,CAG/C,4CACG,CAACN,EAAO,CAACG,EAAQS,GAAQ,CAACV,EAAMI,IAAU,CAC7C,6BACA,oCACA,kCACG,CAACN,EAAO,CAACG,EAAQS,GAAQ,CAACV,EAAMK,IAAU,CAC7C,iBACG,CAACP,EAAO,CAACG,EAAQS,IAAS,CAG7B,iCACG,CAACZ,EAAO,CAACG,EAAQsB,GAAQ,CAACvB,EAAMI,IAAU,CAG7C,4BACG,CAACN,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMI,IAAU,CAG/C,+DACG,CAACN,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMK,IAAU,CAC9C,4BACA,sEACG,CAACP,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMI,IAAU,CAG9C,kDACA,yBACA,uCACA,iDACA,4DACA,6GACG,CAAC,CAACN,EAAO,KAAM,KAAM,CAACG,EAAQwB,GAAS,CAACzB,EAAMI,IAAU,CAC3D,+CACA,8CACE,CAAC,CAACN,EAAO,KAAM,KAAM,CAACG,EAAQwB,GAAS,CAACzB,EAAMK,IAAU,CAG1D,sBACA,mEACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMI,IAAU,CAC9C,wBACG,CAACN,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAG9C,yBACA,oCACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMI,IAAU,CAG9C,mCACG,CAACN,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMI,IAAU,CAGhD,iFACA,4BACA,sDACG,CAACN,EAAO,CAACG,EAAQmB,GAAW,CAACpB,EAAMI,IAAU,CAChD,qCACG,CAACN,EAAO,CAACG,EAAQmB,GAAW,CAACpB,EAAMK,IAAU,CAGhD,iEACG,CAACP,EAAO,CAACG,EAAQiB,GAAK,CAAClB,EAAMK,IAAU,CAC1C,sDACA,oDACA,wBACG,CAACP,EAAO,CAACG,EAAQiB,GAAK,CAAClB,EAAMI,IAAU,CAG1C,oBACA,qEACG,CAACN,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMK,IAAU,CAGhD,qCACA,0BACG,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQ,SAAU,CAACD,EAAMI,IAAU,CAG5D,gBACG,CAACN,EAAO,CAACG,EAAQe,GAAS,CAAChB,EAAMK,IAAU,CAC9C,6CACG,CAACP,EAAO,CAACG,EAAQe,GAAS,CAAChB,EAAMI,IAAU,CAG9C,2GACG,CAACN,EAAO,CAACG,EAAQuB,GAAO,CAACxB,EAAMI,IAAU,CAC5C,oBACA,iCACG,CAAC,CAACN,EAAO,iBAAkB,CAACG,EAAQuB,GAAO,CAACxB,EAAMK,IAAU,CAG/D,sCACA,0CACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMI,IAAU,CAGjD,eACA,yCACA,gCACG,CAACN,EAAO,CAACG,EAAQQ,GAAS,CAACT,EAAMK,IAAU,CAC9C,iDACG,CAAC,CAACP,EAAO,QAAS,iBAAkB,CAACG,EAAQQ,GAAS,CAACT,EAAMI,IAAU,CAG1E,gCACG,CAACN,EAAOG,EAAQ,CAACD,EAAMK,IAAU,CACpC,gCACA,kBACG,CAACP,EAAO,CAACG,EAAQW,GAAa,CAACZ,EAAMI,IAAU,CAGlD,qFACG,CAACN,EAAO,CAACG,EAAQU,GAAO,CAACX,EAAMK,IAAU,CAC5C,iDACG,CAACP,EAAO,CAACG,EAAQU,GAAO,CAACX,EAAMI,IAAU,CAG5C,cACG,CAACN,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,2CAGA,oCACA,iFACG,CAACJ,EAAQ,CAACH,EAAO,KAAM,KAAM,CAACE,EAAMI,IAAU,CAGjD,gHACG,CAACN,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAG7C,mBACG,CAAC,CAACJ,EAAQmC,GAAWtC,EAAO,CAACE,EAAM8C,EAAW,CAAE,OAAW,CAAC,UAAW,SAAU,IAAM,YAAc,CAGxG,uCACG,CAAChD,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAG9C,8BACA,qBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMI,IAAU,CAG/C,kDACG,CAACN,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMI,IAAU,CAGjD,8BACA,oCACG,CAACN,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMI,IAAU,CAGnD,gBACA,+CACG,CAACN,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMI,IAAU,CAG7C,0CACG,CAACN,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMI,IAAU,CAGnD,qCACG,CAACN,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMI,IAAU,CAGjD,+HAEA,uCACA,mBACA,iBACA,8BACA,0BACA,WACA,yBACG,CAACH,EAAQH,EAAO,CAACE,EAAMI,IAAU,CAEpC,mBACA,2BACA,wBACA,uCACA,uBACA,4BACA,iCACA,kCACA,8BACA,gCACA,mCACG,CAACH,EAAQH,EAAO,CAACE,EAAMK,IAAU,CAEpC,kBACG,CAACP,EAAO,CAACG,EAAQkB,GAAY,CAACnB,EAAMK,IAAU,CACjD,qCACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMI,IAAU,CACnD,aACG,CAACN,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMI,IAAU,CAC9C,gBACG,CAACN,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMI,IAAU,CACjD,iBACG,CAACN,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,0BACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,wBACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CACjD,+CACG,CAACP,EAAO,CAACG,EAAQ,kBAAmB,CAACD,EAAMK,IAAU,CACxD,qBACG,CAACP,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMK,IAAU,CAClD,cACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,mBACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMI,IAAU,CAC7C,wBACG,CAACN,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMI,IAAU,CAC/C,mBACG,CAACN,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAC/C,wBACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,mBACA,sCACG,CAAC,CAACJ,EAAQ,gBAAiBH,EAAO,CAACE,EAAMK,IAAU,CACtD,sBACG,CAACP,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMK,IAAU,CAClD,8BACG,CAACP,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMK,IAAU,CAClD,oDACG,CAAC,CAACJ,EAAQ,SAAUH,EAAO,CAACE,EAAMI,IAAU,CAC/C,2BACG,CAAC,CAACH,EAAQ,SAAUH,EAAO,CAACE,EAAMI,IAAU,CAC/C,cACG,CAACN,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMI,IAAU,CACnD,uCACG,CAACN,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CACjD,wBACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,kBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAC/C,qBACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMK,IAAU,CAChD,mBACG,CAACJ,EAAQH,EAAO,CAACE,EAAMI,IAAU,CACpC,sBACG,CAAC,CAACN,EAAO,MAAO,KAAM,CAACG,EAAQkB,GAAY,CAACnB,EAAMI,IAAU,CAC/D,yDACG,CAACN,EAAO,CAACG,EAAQyB,GAAQ,CAAC1B,EAAMK,IAAU,CAC7C,yCACG,CAACP,EAAO,CAACG,EAAQyB,GAAQ,CAAC1B,EAAMI,IAAU,CAM7C,wBACG,CAACH,EAAQ,CAACD,EAAMM,IAAW,CAC9B,uBACG,CAAC,CAACR,EAAO,IAAK,WAAY,CAACG,EAAQqB,GAAU,CAACtB,EAAMM,IAAW,CAClE,8DACG,CAAC,CAACL,EAAQiB,GAAK,CAAClB,EAAMM,IAAW,CACpC,gBACG,CAACL,EAAQ,CAACH,EAAOY,EAAM,OAAQ,CAACV,EAAMM,IAAW,CACpD,UACG,CAAC,CAACR,EAAOgB,EAAO,QAAS,CAACb,EAAQe,GAAS,CAAChB,EAAMM,IAAW,CAChE,6BACG,CAACR,EAAO,CAACG,EAAQQ,GAAS,CAACT,EAAMM,IAAW,CAC/C,uBACA,uBACG,CAACR,EAAO,CAACG,EAAQsB,GAAQ,CAACvB,EAAMM,IAAU,CAC7C,4BACG,CAACR,EAAO,CAACG,EAAQuB,GAAO,CAACxB,EAAMM,IAAW,CAC7C,qBACG,CAACR,EAAO,CAACG,EAAQwB,GAAS,CAACzB,EAAMM,IAAW,CAC/C,6BACG,CAACL,EAAQH,EAAO,CAACE,EAAMM,IAAW,CACrC,0CACA,6DACG,CAAC,CAACL,EAAQrP,GAAO,CAACkP,EAAOlP,GAAO,CAACoP,EAAMM,IAAW,CACrD,mDACG,CAAC,CAACN,EAAMM,IAAW,CAMtB,UACA,8BACG,CAACL,EAAQH,EAAO,CAACE,EAAMG,IAAW,CACrC,0BACG,CAACL,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMG,IAAW,CACjD,mCACG,CAACL,EAAO,CAACG,EAAQuB,GAAO,CAACxB,EAAMG,IAAW,CAC7C,sCACG,CAACL,EAAO,CAACG,EAAQkB,GAAY,CAACnB,EAAMG,IAAW,CAMlD,mCACG,CAACL,EAAO,CAACG,EAAQqB,GAAU,CAACtB,EAAMO,IAAY,CACjD,kBACG,CAACN,EAAQH,EAAO,CAACE,EAAMO,IAAY,CACtC,wCACG,CAACT,EAAO,CAACG,EAAQS,GAAQ,CAACV,EAAMO,IAAY,CAC/C,wBACG,CAACT,EAAO,CAACG,EAAQe,GAAS,CAAChB,EAAMO,IAAY,CAChD,6BACG,CAACT,EAAO,CAACG,EAAQyB,GAAQ,CAAC1B,EAAMO,IAAY,CAM/C,wBACG,CAACT,EAAO,CAACG,EAAQe,GAAS,CAAChB,EAAMO,IAAY,CAChD,kCACG,CAACN,EAAQH,EAAO,CAACE,EAAMO,IAAY,CACtC,yBACG,CAACT,EAAO,CAACG,EAAQ0B,GAAW,CAAC3B,EAAMO,IAAY,CAMlD,wCACG,CAACN,EAAQ,CAACD,EAAMQ,IAAY,CAC/B,cACG,CAACV,EAAO,CAACG,EAAQQ,GAAS,CAACT,EAAMQ,IAAY,CAMhD,kEACG,CAACV,EAAO,CAACE,EAAMI,IAAU,CAC5B,+DACG,CAACN,EAAO,CAACE,EAAMK,IAAU,CAC5B,gDACG,CAAC,CAACL,EAAMK,IAAU,CACrB,kEACG,CAAC,CAACL,EAAMI,IAAU,CACrB,kCACG,CAACN,EAAO,CAACG,EAAQ,aAGxBkD,OAAS,CAAC,CAEN,8BACG,CAAC3wB,EAAS,CAACutB,EAAMqD,aAAe,CAEnC,wBACG,CAACrD,EAAMvtB,GAAU,CAEpB,6CACG,CAACA,EAAS,CAACutB,EAAM,UAAW,CAE/B,uBACA,4EACA,0BACA,yCACA,8BACA,eACG,CAACA,EAAMvtB,GAAU,CAEpB,iCACG,CAACA,EAASutB,IAGjBliB,GAAK,CAAC,CAGF,mCACG,CAACkiB,EAAMvtB,GAAU,CACpB,yDACG,CAACutB,EAAM,CAACvtB,EAASswB,EAAWC,IAAqB,CACpD,0BACA,2CACA,wCACG,CAAC,CAACvwB,EAASswB,EAAWC,GAAoB,CAAChD,EAAM,YAAa,CAGjE,sDACA,4CACA,wBACG,CAAC,CAACvtB,EAAS,KAAM,KAAM,CAACutB,EAAM,QAAS,CAC1C,0BACA,yCACG,CAAC,CAACA,EAAM8B,GAAS,CAACrvB,EAAS,KAAM,MAAO,CAG3C,kDACG,CAACA,EAASutB,GAAO,CACpB,2FACA,8BACA,+BACA,kBACG,CAACA,EAAMvtB,GAAU,CACpB,cACG,CAACA,EAAS,CAACutB,EAAMa,IAAc,CAClC,6DACG,CAACpuB,EAAS,CAACutB,EAAM,YAAa,CACjC,mFACG,CAACvtB,EAAS,CAACutB,EAAMgB,EAAQ,QAAS,CACrC,kBACA,wCACG,CAACvuB,EAAS,CAACutB,EAAM,UAAW,CAC/B,wCACG,CAACvtB,EAAS,CAACutB,EAAM,YAAa,CAGjC,qBACG,CAACvtB,EAAS,CAACutB,EAAMe,EAAO,SAAU,CACrC,oCACG,CAAC,CAACf,EAAM6B,GAAcpvB,GAAS,CAGlC,qBACA,iBACA,2BAGA,mDACA,2BAGA,wCACA,yBACA,4BACA,8SAEA,2BACA,oBACA,6EACA,kBACG,CAACutB,EAAMvtB,GAAU,CACpB,yBACG,CAAC,CAACutB,EAAM,WAAYvtB,GAAU,CACjC,sCACA,kCACA,mEACA,sBACG,CAACutB,EAAMvtB,KAQduK,EAAW,SAAUylB,EAAIa,GAOzB,UALWb,IAAO7C,IACd0D,EAAab,EACbA,EAAK10B,KAGH8F,gBAAgBmJ,GAClB,OAAO,IAAIA,EAASylB,EAAIa,GAAYC,YAGxC,IAAIC,SAAqB9uB,IAAWirB,GAAcjrB,EAAO8I,UAAa9I,EAAO8I,UAAYzP,EACrF01B,EAAMhB,IAAQe,GAAcA,EAAWnmB,UAAammB,EAAWnmB,UA31BrD,IA41BVqmB,EAASF,GAAcA,EAAW7lB,cAAiB6lB,EAAW7lB,cAAgB5P,EAC9E41B,EAAUL,EA5yBL,SAAUL,EAASK,GACxB,IAAIM,EAAgB,CAAC,EACrB,IAAK,IAAI3d,KAAKgd,EACNK,EAAWrd,IAAMqd,EAAWrd,GAAGzd,OAAS,GAAM,EAC9Co7B,EAAc3d,GAAKqd,EAAWrd,GAAGoC,OAAO4a,EAAQhd,IAEhD2d,EAAc3d,GAAKgd,EAAQhd,GAGnC,OAAO2d,CACX,CAkyB2BC,CAAOZ,EAASK,GAAcL,EACrDa,EAAaN,GAAcA,EAAWnmB,WAAaomB,EAyEvD,OAvEA5vB,KAAKoJ,WAAa,WACd,IAxxBiB9D,EAwxBb4qB,EAAW,CAAC,EAShB,OARAA,EAAS/D,GAAQjyB,EACjBg2B,EAAStxB,GAAW1E,EACpBy0B,EAAUj7B,KAAKw8B,EAAUN,EAAKE,EAAQ9lB,SACtCkmB,EAASjE,UA5xBQ3mB,EA4xBU4qB,EAAStxB,MA3xBTotB,EAAW1mB,EAAQ8R,QAAQ,WA1E5C,IA0E+DvP,MAAM,KAAK,GAAK3N,EA6xBrF+1B,GAAcN,GAAcA,EAAWQ,cAAgBR,EAAWQ,MAAMC,SAAWvE,IACnFqE,EAAS/D,GAAQ,SAEd+D,CACX,EACAlwB,KAAKqwB,OAAS,WACV,IAAIC,EAAO,CAAC,EAGZ,OAFAA,EAAKhE,GAAgBpyB,EACrBy0B,EAAUj7B,KAAK48B,EAAMV,EAAKE,EAAQT,KAC3BiB,CACX,EACAtwB,KAAKuwB,UAAY,WACb,IAAIC,EAAU,CAAC,EAaf,OAZAA,EAAQnE,GAAUnyB,EAClBs2B,EAAQtE,GAAShyB,EACjBs2B,EAAQpE,GAAQlyB,EAChBy0B,EAAUj7B,KAAK88B,EAASZ,EAAKE,EAAQR,QACjCW,IAAeO,EAAQpE,IAASyD,GAASA,EAAMjmB,SAC/C4mB,EAAQpE,GAAQI,GAGhByD,GAAgC,aAAlBO,EAAQtE,IAAyByD,UAAqBA,EAAWc,aAAe3E,GAAc6D,EAAWe,gBAAkBf,EAAWe,eAAiB,IACrKF,EAAQtE,GAAS,OACjBsE,EAAQpE,GAAQK,GAEb+D,CACX,EACAxwB,KAAK2wB,UAAY,WACb,IAAIC,EAAU,CAAC,EAIf,OAHAA,EAAQzE,GAAQjyB,EAChB02B,EAAQhyB,GAAW1E,EACnBy0B,EAAUj7B,KAAKk9B,EAAShB,EAAKE,EAAQP,QAC9BqB,CACX,EACA5wB,KAAKuJ,MAAQ,WACT,IAAIsnB,EAAM,CAAC,EASX,OARAA,EAAI1E,GAAQjyB,EACZ22B,EAAIjyB,GAAW1E,EACfy0B,EAAUj7B,KAAKm9B,EAAKjB,EAAKE,EAAQ7lB,IAC7BgmB,IAAeY,EAAI1E,IAAS0D,GAASA,EAAMiB,UAA8B,WAAlBjB,EAAMiB,WAC7DD,EAAI1E,GAAQ0D,EAAMiB,SACG1Z,QAAQ,aAAc4W,GACtB5W,QAAQ,SAAU6W,IAEpC4C,CACX,EACA7wB,KAAK0vB,UAAY,WACb,MAAO,CACHd,GAAU5uB,KAAKyJ,QACfO,QAAUhK,KAAKoJ,aACfmmB,OAAUvvB,KAAK2wB,YACf1mB,GAAUjK,KAAKuJ,QACf+lB,OAAUtvB,KAAKuwB,YACflB,IAAUrvB,KAAKqwB,SAEvB,EACArwB,KAAKyJ,MAAQ,WACT,OAAOmmB,CACX,EACA5vB,KAAK+wB,MAAQ,SAAUnC,GAEnB,OADAgB,SAAchB,IAAO5C,GAAY4C,EAAGj6B,OAh5BxB,IAg5BkDqoB,EAAK4R,EAh5BvD,KAg5B4EA,EACjF5uB,IACX,EACAA,KAAK+wB,MAAMnB,GACJ5vB,IACX,EAEAmJ,EAASvK,QA36BS,SA46BlBuK,EAAS8jB,QAAWkB,EAAU,CAAChC,EAAMvtB,EAASqtB,IAC9C9iB,EAAS6nB,IAAM7C,EAAU,CAAC7B,IAC1BnjB,EAAS8nB,OAAS9C,EAAU,CAACjC,EAAOG,EAAQD,EAAMG,EAASC,EAAQE,EAASD,EAAQE,EAAUC,IAC9FzjB,EAAS+nB,OAAS/nB,EAASgoB,GAAKhD,EAAU,CAAChC,EAAMvtB,WAOvC,IAAcktB,GAEgBsF,EAAOC,UACvCA,EAAUD,EAAOC,QAAUloB,GAE/BkoB,EAAQloB,SAAWA,GAGiB,QAChC,aACI,OAAOA,CACV,2CACatI,IAAWirB,IAEzBjrB,EAAOsI,SAAWA,GAS1B,IAAIkS,UAAWxa,IAAWirB,IAAejrB,EAAOywB,QAAUzwB,EAAO0wB,OACjE,GAAIlW,KAAMA,GAAEuT,GAAI,CACZ,IAAI1lB,GAAS,IAAIC,EACjBkS,GAAEuT,GAAK1lB,GAAOwmB,YACdrU,GAAEuT,GAAGrtB,IAAM,WACP,OAAO2H,GAAOO,OAClB,EACA4R,GAAEuT,GAAGptB,IAAM,SAAUotB,GACjB1lB,GAAO6nB,MAAMnC,GACb,IAAIld,EAASxI,GAAOwmB,YACpB,IAAK,IAAI8B,KAAQ9f,EACb2J,GAAEuT,GAAG4C,GAAQ9f,EAAO8f,EAE5B,CACJ,CAEH,CAt+BD,CAs+BqB,iBAAX3wB,OAAsBA,OAASb,K,wBCh/BzCoxB,EAAOC,QAAUI,C,wBCAjBL,EAAOC,QAAUK,C,wBCAjBN,EAAOC,QAAUM,C,wBCAjBP,EAAOC,QAAUO,C,wBCAjBR,EAAOC,QAAUQ,C,wBCAjBT,EAAOC,QAAUS,C,wBCAjBV,EAAOC,QAAUU,C,wBCAjBX,EAAOC,QAAUW,C,wBCAjBZ,EAAOC,QAAUY,C,wBCAjBb,EAAOC,QAAUa,C,wBCAjBd,EAAOC,QAAUc,C,wBCAjBf,EAAOC,QAAUe,C,wFCCbC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBr4B,IAAjBs4B,EACH,OAAOA,EAAanB,QAGrB,IAAID,EAASiB,EAAyBE,GAAY,CACjD11B,GAAI01B,EACJE,QAAQ,EACRpB,QAAS,CAAC,GAUX,OANAqB,EAAoBH,GAAU7+B,KAAK09B,EAAOC,QAASD,EAAQA,EAAOC,QAASiB,GAG3ElB,EAAOqB,QAAS,EAGTrB,EAAOC,OACf,CAGAiB,EAAoBra,EAAIya,EC5BxBJ,EAAoBK,KAAO,CAAC,ECC5BL,EAAoB/b,EAAK6a,IACxB,IAAIwB,EAASxB,GAAUA,EAAOyB,WAC7B,IAAOzB,EAAiB,QACxB,IAAM,EAEP,OADAkB,EAAoBza,EAAE+a,EAAQ,CAAEx8B,EAAGw8B,IAC5BA,CAAM,EhHNVjhC,EAAW4B,OAAOu/B,eAAkBt9B,GAASjC,OAAOu/B,eAAet9B,GAASA,GAASA,EAAa,UAQtG88B,EAAoBtgB,EAAI,SAAS5e,EAAO2/B,GAEvC,GADU,EAAPA,IAAU3/B,EAAQ4M,KAAK5M,IAChB,EAAP2/B,EAAU,OAAO3/B,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP2/B,GAAa3/B,EAAMy/B,WAAY,OAAOz/B,EAC1C,GAAW,GAAP2/B,GAAoC,mBAAf3/B,EAAMwe,KAAqB,OAAOxe,CAC5D,CACA,IAAI4/B,EAAKz/B,OAAO0/B,OAAO,MACvBX,EAAoB9b,EAAEwc,GACtB,IAAIE,EAAM,CAAC,EACXxhC,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAIwhC,EAAiB,EAAPJ,GAAY3/B,EAAyB,iBAAX+/B,KAAyBzhC,EAAe8R,QAAQ2vB,GAAUA,EAAUxhC,EAASwhC,GACxH5/B,OAAO6/B,oBAAoBD,GAAS/xB,SAAS1L,GAASw9B,EAAIx9B,GAAO,IAAOtC,EAAMsC,KAI/E,OAFAw9B,EAAa,QAAI,IAAM,EACvBZ,EAAoBza,EAAEmb,EAAIE,GACnBF,CACR,EiHxBAV,EAAoBza,EAAI,CAACwZ,EAASgC,KACjC,IAAI,IAAI39B,KAAO29B,EACXf,EAAoB78B,EAAE49B,EAAY39B,KAAS48B,EAAoB78B,EAAE47B,EAAS37B,IAC5EnC,OAAO6L,eAAeiyB,EAAS37B,EAAK,CAAE4J,YAAY,EAAMiC,IAAK8xB,EAAW39B,IAE1E,ECND48B,EAAoBrb,EAAI,CAAC,EAGzBqb,EAAoB9gB,EAAK8hB,GACjBpiB,QAAQqiB,IAAIhgC,OAAOqB,KAAK09B,EAAoBrb,GAAGjT,QAAO,CAACwvB,EAAU99B,KACvE48B,EAAoBrb,EAAEvhB,GAAK49B,EAASE,GAC7BA,IACL,KCNJlB,EAAoBvb,EAAKuc,GAEZA,EAAU,cAAgB,CAAC,GAAK,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,wBAAwBA,GCHtNhB,EAAoB5zB,EAAI,WACvB,GAA0B,iBAAfD,WAAyB,OAAOA,WAC3C,IACC,OAAOuB,MAAQ,IAAIyzB,SAAS,cAAb,EAChB,CAAE,MAAOjiB,GACR,GAAsB,iBAAX3Q,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxByxB,EAAoB78B,EAAI,CAACD,EAAKg8B,IAAUj+B,OAAOC,UAAU0e,eAAexe,KAAK8B,EAAKg8B,GpHA9E5/B,EAAa,CAAC,EACdC,EAAoB,gCAExBygC,EAAoBxa,EAAI,CAAC7M,EAAK0G,EAAMjc,EAAK49B,KACxC,GAAG1hC,EAAWqZ,GAAQrZ,EAAWqZ,GAAKrK,KAAK+Q,OAA3C,CACA,IAAI+hB,EAAQC,EACZ,QAAWz5B,IAARxE,EAEF,IADA,IAAIk+B,EAAUrzB,SAASszB,qBAAqB,UACpCzhB,EAAI,EAAGA,EAAIwhB,EAAQj/B,OAAQyd,IAAK,CACvC,IAAIL,EAAI6hB,EAAQxhB,GAChB,GAAGL,EAAE+hB,aAAa,QAAU7oB,GAAO8G,EAAE+hB,aAAa,iBAAmBjiC,EAAoB6D,EAAK,CAAEg+B,EAAS3hB,EAAG,KAAO,CACpH,CAEG2hB,IACHC,GAAa,GACbD,EAASnzB,SAASwzB,cAAc,WAEzBC,QAAU,QACjBN,EAAOO,QAAU,IACb3B,EAAoB4B,IACvBR,EAAOS,aAAa,QAAS7B,EAAoB4B,IAElDR,EAAOS,aAAa,eAAgBtiC,EAAoB6D,GAExDg+B,EAAOU,IAAMnpB,EAC4C,IAArDyoB,EAAOU,IAAI5wB,QAAQ3C,OAAO0G,SAAS8sB,OAAS,OAC/CX,EAAOY,YAAc,aAEtBZ,EAAOa,UAAYjC,EAAoBkC,UAAUlB,GACjDI,EAAOY,YAAc,aAEtB1iC,EAAWqZ,GAAO,CAAC0G,GACnB,IAAI8iB,EAAmB,CAACC,EAAMrJ,KAE7BqI,EAAOrd,QAAUqd,EAAOiB,OAAS,KACjCC,aAAaX,GACb,IAAIY,EAAUjjC,EAAWqZ,GAIzB,UAHOrZ,EAAWqZ,GAClByoB,EAAOzW,YAAcyW,EAAOzW,WAAW6X,YAAYpB,GACnDmB,GAAWA,EAAQzzB,SAAS2zB,GAAQA,EAAG1J,KACpCqJ,EAAM,OAAOA,EAAKrJ,EAAM,EAExB4I,EAAU5nB,WAAWooB,EAAiB/Q,KAAK,UAAMxpB,EAAW,CAAE7G,KAAM,UAAW4nB,OAAQyY,IAAW,MACtGA,EAAOrd,QAAUoe,EAAiB/Q,KAAK,KAAMgQ,EAAOrd,SACpDqd,EAAOiB,OAASF,EAAiB/Q,KAAK,KAAMgQ,EAAOiB,QACnDhB,GAAcpzB,SAASy0B,KAAKC,YAAYvB,EAzCkB,CAyCX,EqH5ChDpB,EAAoB9b,EAAK6a,IACH,oBAAX6D,QAA0BA,OAAOC,aAC1C5hC,OAAO6L,eAAeiyB,EAAS6D,OAAOC,YAAa,CAAE/hC,MAAO,WAE7DG,OAAO6L,eAAeiyB,EAAS,aAAc,CAAEj+B,OAAO,GAAO,ECL9Dk/B,EAAoB8C,IAAOhE,IAC1BA,EAAOiE,MAAQ,GACVjE,EAAOkE,WAAUlE,EAAOkE,SAAW,IACjClE,GCHRkB,EAAoBrgB,EAAI,+CCCxBqgB,EAAoBkC,UAAY,CAAC,GAAK,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,uD,MCDlVlC,EAAoBj8B,EAAIkK,SAASg1B,SAAW52B,KAAK4I,SAASC,KAK1D,IAAIguB,EAAkB,CACrB,IAAK,GAGNlD,EAAoBrb,EAAE6X,EAAI,CAACwE,EAASE,KAElC,IAAIiC,EAAqBnD,EAAoB78B,EAAE+/B,EAAiBlC,GAAWkC,EAAgBlC,QAAWp5B,EACtG,GAA0B,IAAvBu7B,EAGF,GAAGA,EACFjC,EAAS5yB,KAAK60B,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIxkB,SAAQ,CAACC,EAASC,IAAYqkB,EAAqBD,EAAgBlC,GAAW,CAACniB,EAASC,KAC1GoiB,EAAS5yB,KAAK60B,EAAmB,GAAKC,GAGtC,IAAIzqB,EAAMqnB,EAAoBrgB,EAAIqgB,EAAoBvb,EAAEuc,GAEpDh7B,EAAQ,IAAI9D,MAgBhB89B,EAAoBxa,EAAE7M,GAfFogB,IACnB,GAAGiH,EAAoB78B,EAAE+/B,EAAiBlC,KAEf,KAD1BmC,EAAqBD,EAAgBlC,MACRkC,EAAgBlC,QAAWp5B,GACrDu7B,GAAoB,CACtB,IAAIE,EAAYtK,IAAyB,SAAfA,EAAMh4B,KAAkB,UAAYg4B,EAAMh4B,MAChEuiC,EAAUvK,GAASA,EAAMpQ,QAAUoQ,EAAMpQ,OAAOmZ,IACpD97B,EAAMM,QAAU,iBAAmB06B,EAAU,cAAgBqC,EAAY,KAAOC,EAAU,IAC1Ft9B,EAAMO,KAAO,iBACbP,EAAMjF,KAAOsiC,EACbr9B,EAAMu9B,QAAUD,EAChBH,EAAmB,GAAGn9B,EACvB,CACD,GAEwC,SAAWg7B,EAASA,EAE/D,CACD,EAcF,IAAIwC,EAAuB,CAACC,EAA4BC,KACvD,IAGIzD,EAAUe,GAHT2C,EAAUC,EAAaC,GAAWH,EAGhB5jB,EAAI,EAC3B,GAAG6jB,EAASh9B,MAAM4D,GAAgC,IAAxB24B,EAAgB34B,KAAa,CACtD,IAAI01B,KAAY2D,EACZ5D,EAAoB78B,EAAEygC,EAAa3D,KACrCD,EAAoBra,EAAEsa,GAAY2D,EAAY3D,IAGhD,GAAG4D,EAAsBA,EAAQ7D,EAClC,CAEA,IADGyD,GAA4BA,EAA2BC,GACrD5jB,EAAI6jB,EAASthC,OAAQyd,IACzBkhB,EAAU2C,EAAS7jB,GAChBkgB,EAAoB78B,EAAE+/B,EAAiBlC,IAAYkC,EAAgBlC,IACrEkC,EAAgBlC,GAAS,KAE1BkC,EAAgBlC,GAAW,CAC5B,EAIG8C,EAAqBz3B,KAA+C,yCAAIA,KAA+C,0CAAK,GAChIy3B,EAAmBh1B,QAAQ00B,EAAqBpS,KAAK,KAAM,IAC3D0S,EAAmBx1B,KAAOk1B,EAAqBpS,KAAK,KAAM0S,EAAmBx1B,KAAK8iB,KAAK0S,G,2FClFvF,IACE,KAAiB,QACb,QAAkB1tB,MAAM,EAAG,QAAkB2tB,YAAY,KAAO,GAChE,+C,+DCKN,MAAMC,EAAe,4BACfC,EAAQ,WAAWD,IACnBlS,EAAc,6BAA6BkS,SAIpCE,EAAgD,CAC3D,CACEC,QAAS,CAACC,EAAAA,sBAAsBC,mBAAoBD,EAAAA,sBAAsBE,sBAC1EL,QACAnS,cACAyS,KAPS,gBAQTC,SATa,oBAUbC,KAAMC,EAAatkC,EAAAA,GAAOG,WAC1BokC,UAAYx+B,IACV,QAAuB,IAAZA,EACT,OAGF,GAAI,aAAcA,GAAgC,eAArBA,EAAQy+B,SACnC,OAGF,MAAMC,EAAU,EAAyCV,QAAQx0B,OAAOm1B,GAExE,KAAKD,aAAAA,EAAAA,EAASxiC,QACZ,OAGF,MAAM,WAAE0iC,EAAU,KAAEC,GAASH,EAAQ,GAErC,IAAKG,GAA+B,gBAArBD,aAAAA,EAAAA,EAAYhkC,MACzB,OAGF,MAAMkkC,EAkFL,SAA6BA,GAClC,IAAI5b,EACJ,MAAM6b,EAAkC,GAClCC,EAAeF,EAAMva,OAGrB0a,EAAaD,EAAat+B,MAAM,kDAEtC,GAAIu+B,EAAY,CACd,MAAMC,EAAkBD,EAAW,GAC7BE,EAAgBF,EAAW,GAKS,MADzBD,EADKC,EAAW,GAAG/iC,SAINkjC,EAAcziC,IAAIuiC,KAC9Chc,EAASgc,QAIWz9B,IAAlB09B,GACFE,EAAYF,EAAeJ,EAE/B,CAGA,IAAK7b,EAAQ,CAEX,MAAMoc,EAAgB,iDACtB,IAAI5+B,EACJ,KAAsD,QAA9CA,EAAQ4+B,EAAc5vB,KAAKsvB,KAAyB,CAC1D,MAAME,EAAkBx+B,EAAM,GACxBy+B,EAAgBz+B,EAAM,GAE5B,IAAK0+B,EAAcziC,IAAIuiC,GAAkB,CACvChc,EAASgc,EACLC,GACFE,EAAYF,EAAeJ,GAE7B,KACF,CACF,CACF,CAEA,MAAO,CAAE7b,SAAQ6b,eAAcD,QACjC,CAjIoBS,CAAoBV,GAE5BW,EACJ,cAAex/B,GACc,iBAAtBA,EAAQw/B,WACO,OAAtBx/B,EAAQw/B,WACR,SAAUx/B,EAAQw/B,WAClB,OAAQx/B,EAAQw/B,UACXx/B,EAAQw/B,eACT/9B,EAEAg+B,EAkCL,SACLA,EACAC,GAEA,MAAMC,EAAe,IAAIC,gBAAgBF,aAAAA,EAAAA,EAAe1kC,YAQxD,OANAykC,EAAO92B,SAAQ,EAAE1L,EAAKtC,MAChBA,GACFglC,EAAaE,OAAO5iC,EAAKtC,EAC3B,IAGKglC,CACT,CA/CqBG,CAAoB,CACjC,CAACC,EAAcC,OAAQlB,EAAM5b,QAC7B,CAAC6c,EAAcE,cAAeT,aAAAA,EAAAA,EAAWv2B,MACzC,CAAC82B,EAAcG,YAAaV,aAAAA,EAAAA,EAAWW,IACvC,CAACJ,EAAcK,aAAcxB,EAAWyB,QACrCvB,EAAMC,aAAatgC,KACnB+f,GAAM,CAACuhB,EAAcO,QAAS,GAAG9hB,EAAE+hB,QAAQ/hB,EAAEgiB,KAAKhiB,EAAE7jB,aAMzD,MAAO,CACL2jC,KAHuBC,EAAatkC,EAAAA,GAAOG,UAAWqlC,GAIvD,IAKA,SAASlB,EAAakC,EAAeC,GAC1C,MAAO,GAAG1mC,EAAAA,MAAmBymC,IAAQC,EAAY,IAAIA,EAAU1lC,aAAe,IAChF,CAGO,MAAM+kC,EAAgB,CAC3BE,cAAe,OACfC,YAAa,KACbF,OAAQ,SACRI,aAAc,SACdE,QAAS,eAsBX,SAAS3B,EAAYG,GACnB,MAAO,SAAUA,CACnB,CAmEA,SAASO,EAAYF,EAAuBJ,GAC1C,MAAM4B,EAAaxB,EAAc/vB,MAAM,KACjCwxB,EAAa,oEAEnBD,EAAWh4B,SAASk4B,IAClB,GAAoB,KAAhBA,EAAKtc,OACP,OAEF,MAAM7jB,EAAQmgC,EAAKngC,MAAMkgC,GACzB,GAAIlgC,EAAO,CACT,MAAMogC,EAAiBpgC,EAAM,GAAGie,QAAQ,SAAU,MAClDogB,EAAa52B,KAAK,CAAEo4B,MAAO7/B,EAAM,GAAI8/B,GAAI9/B,EAAM,GAAI/F,MAAOmmC,GAC5D,MACE5N,EAAAA,EAAO3tB,KAAK,oDAAoDs7B,kBAAqB1B,IACvF,GAEJ,CAEA,MAAMC,EAAgB,IAAI1a,IAAI,CAC5B,OACA,WACA,MACA,MACA,QACA,MACA,MACA,SACA,SACA,OACA,UACA,WACA,qBACA,gBACA,aACA,SACA,SACA,OACA,YACA,QACA,OACA,eACA,cACA,gBACA,OACA,SACA,KACA,UACA,KACA,WACA,aACA,gB,4TC7NF,MAAMqc,GAAUC,EAAAA,EAAAA,MAAIA,GAAC,YACnB,MAAM,cAAEC,SAAwB,uDACxBC,QAASC,SAAsB,8BAOvC,OALIF,YACIE,KAID,8DACT,KAQaC,GAAS,IAAIC,EAAAA,WAAgBC,aAN7B9K,GACX,kBAAC+K,EAAAA,SAAQA,CAACC,SAAU,kBAACC,EAAAA,mBAAkBA,CAAC5lB,KAAK,MAC3C,kBAACklB,EAAYvK,MAMjB,IAAK,MAAMkL,KAAc3D,EACvBqD,EAAOO,QAAQD,E", "sources": ["webpack://grafana-metricsdrilldown-app/webpack/runtime/create fake namespace object", "webpack://grafana-metricsdrilldown-app/webpack/runtime/load script", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/logLevels.js", "webpack://grafana-metricsdrilldown-app/./constants.ts", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/transports/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/is.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/json.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/date.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/deepEqual.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/exceptions/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/exceptions/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/utils.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/logs/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/traces/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/meta/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/logs/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/measurements/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/api/events/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/noop.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/internalLogger/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/unpatchedConsole/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/internalLogger/createInternalLogger.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/internalLogger/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/globalObject/globalObject.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/version.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/sdk/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/sdk/registerFaro.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/sdk/internalFaroGlobalObject.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/sdk/faroGlobalObject.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/transports/batchExecutor.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/transports/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/unpatchedConsole/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/metas/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/instrumentations/initialize.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/metas/registerInitial.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/sourceMaps.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/transports/registerInitial.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/instrumentations/registerInitial.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/config/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/consts.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/stackFrames/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/stackFrames/buildStackFrame.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/stackFrames/getDataFromSafariExtensions.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/stackFrames/getStackFramesFromError.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/stackFrames/parseStacktrace.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/sessionManager/sessionConstants.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/consts.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/metas/browser/meta.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/metas/k6/meta.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/metas/page/meta.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/extensions/baseExtension.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/transports/base.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/transports/utils.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/utils/throttle.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/utils/webStorage.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/shortId.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/semantic.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/sessionManager/sampling.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/sessionManager/sessionManagerUtils.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/sessionManager/PersistentSessionsManager.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/sessionManager/VolatileSessionManager.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/sessionManager/getSessionManagerByConfig.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/transports/fetch/transport.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/utils/promiseBuffer.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-core/dist/esm/instrumentations/base.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/const.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/getErrorDetails.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/getValueAndTypeFromMessage.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/registerOnunhandledrejection.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/instrumentation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/errors/registerOnerror.js", "webpack://grafana-metricsdrilldown-app/../node_modules/web-vitals/dist/web-vitals.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/webVitals/webVitalsBasic.js", "webpack://grafana-metricsdrilldown-app/../node_modules/web-vitals/dist/web-vitals.attribution.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/instrumentationConstants.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/webVitals/webVitalsWithAttribution.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/webVitals/instrumentation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/session/instrumentation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/metas/session/createSession.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/view/instrumentation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/performance/performanceConstants.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/performance/performanceUtils.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/performance/resource.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/performance/instrumentation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/performance/navigation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/instrumentations/console/instrumentation.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/config/getWebInstrumentations.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/config/makeCoreConfig.js", "webpack://grafana-metricsdrilldown-app/../node_modules/@grafana/faro-web-sdk/dist/esm/initialize.js", "webpack://grafana-metricsdrilldown-app/./tracking/faro/faro-environments.ts", "webpack://grafana-metricsdrilldown-app/./tracking/faro/faro.ts", "webpack://grafana-metricsdrilldown-app/./tracking/faro/getFaroEnvironment.ts", "webpack://grafana-metricsdrilldown-app/./tracking/getEnvironment.ts", "webpack://grafana-metricsdrilldown-app/./tracking/logger/logger.ts", "webpack://grafana-metricsdrilldown-app/./version.ts", "webpack://grafana-metricsdrilldown-app/../node_modules/ua-parser-js/src/ua-parser.js", "webpack://grafana-metricsdrilldown-app/external amd \"@emotion/css\"", "webpack://grafana-metricsdrilldown-app/external amd \"@grafana/data\"", "webpack://grafana-metricsdrilldown-app/external amd \"@grafana/runtime\"", "webpack://grafana-metricsdrilldown-app/external amd \"@grafana/ui\"", "webpack://grafana-metricsdrilldown-app/external amd \"lodash\"", "webpack://grafana-metricsdrilldown-app/external amd \"module\"", "webpack://grafana-metricsdrilldown-app/external amd \"prismjs\"", "webpack://grafana-metricsdrilldown-app/external amd \"react\"", "webpack://grafana-metricsdrilldown-app/external amd \"react-dom\"", "webpack://grafana-metricsdrilldown-app/external amd \"react-router\"", "webpack://grafana-metricsdrilldown-app/external amd \"redux\"", "webpack://grafana-metricsdrilldown-app/external amd \"rxjs\"", "webpack://grafana-metricsdrilldown-app/webpack/bootstrap", "webpack://grafana-metricsdrilldown-app/webpack/runtime/amd options", "webpack://grafana-metricsdrilldown-app/webpack/runtime/compat get default export", "webpack://grafana-metricsdrilldown-app/webpack/runtime/define property getters", "webpack://grafana-metricsdrilldown-app/webpack/runtime/ensure chunk", "webpack://grafana-metricsdrilldown-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-metricsdrilldown-app/webpack/runtime/global", "webpack://grafana-metricsdrilldown-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-metricsdrilldown-app/webpack/runtime/make namespace object", "webpack://grafana-metricsdrilldown-app/webpack/runtime/node module decorator", "webpack://grafana-metricsdrilldown-app/webpack/runtime/publicPath", "webpack://grafana-metricsdrilldown-app/webpack/runtime/compat", "webpack://grafana-metricsdrilldown-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-metricsdrilldown-app/./node_modules/grafana-public-path.js", "webpack://grafana-metricsdrilldown-app/./extensions/links.ts", "webpack://grafana-metricsdrilldown-app/./module.tsx"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-metricsdrilldown-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t\tif (script.src.indexOf(window.location.origin + '/') !== 0) {\n\t\t\tscript.crossOrigin = \"anonymous\";\n\t\t}\n\t\tscript.integrity = __webpack_require__.sriHashes[chunkId];\n\t\tscript.crossOrigin = \"anonymous\";\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "export var LogLevel;\n(function (LogLevel) {\n    LogLevel[\"TRACE\"] = \"trace\";\n    LogLevel[\"DEBUG\"] = \"debug\";\n    LogLevel[\"INFO\"] = \"info\";\n    LogLevel[\"LOG\"] = \"log\";\n    LogLevel[\"WARN\"] = \"warn\";\n    LogLevel[\"ERROR\"] = \"error\";\n})(LogLevel || (LogLevel = {}));\nexport const defaultLogLevel = LogLevel.LOG;\nexport const allLogLevels = [\n    LogLevel.TRACE,\n    LogLevel.DEBUG,\n    LogLevel.INFO,\n    LogLevel.LOG,\n    LogLevel.WARN,\n    LogLevel.ERROR,\n];\n//# sourceMappingURL=logLevels.js.map", "import pluginJson from './plugin.json';\n\nexport const PLUGIN_ID = pluginJson.id;\nexport const PLUGIN_BASE_URL = `/a/${pluginJson.id}`;\n\nexport const ROUTES = {\n  Home: '',\n  Trail: 'trail',\n  Drilldown: 'drilldown',\n};\n\nexport const DATASOURCE_REF = {\n  uid: 'gdev-testdata',\n  type: 'testdata',\n};\n", "export var TransportItemType;\n(function (TransportItemType) {\n    TransportItemType[\"EXCEPTION\"] = \"exception\";\n    TransportItemType[\"LOG\"] = \"log\";\n    TransportItemType[\"MEASUREMENT\"] = \"measurement\";\n    TransportItemType[\"TRACE\"] = \"trace\";\n    TransportItemType[\"EVENT\"] = \"event\";\n})(TransportItemType || (TransportItemType = {}));\nexport const transportItemTypeToBodyKey = {\n    [TransportItemType.EXCEPTION]: 'exceptions',\n    [TransportItemType.LOG]: 'logs',\n    [TransportItemType.MEASUREMENT]: 'measurements',\n    [TransportItemType.TRACE]: 'traces',\n    [TransportItemType.EVENT]: 'events',\n};\n//# sourceMappingURL=const.js.map", "export function isTypeof(value, type) {\n    return typeof value === type;\n}\nexport function isToString(value, type) {\n    return Object.prototype.toString.call(value) === `[object ${type}]`;\n}\nexport function isInstanceOf(value, reference) {\n    try {\n        return value instanceof reference;\n    }\n    catch (err) {\n        return false;\n    }\n}\nexport const isUndefined = ((value) => isTypeof(value, 'undefined'));\nexport const isNull = ((value) => isTypeof(value, 'null'));\nexport const isString = ((value) => isTypeof(value, 'string'));\nexport const isNumber = ((value) => (isTypeof(value, 'number') && !isNaN(value)) || isTypeof(value, 'bigint'));\nexport const isInt = ((value) => isNumber(value) && Number.isInteger(value));\nexport const isBoolean = ((value) => isTypeof(value, 'boolean'));\nexport const isSymbol = ((value) => isTypeof(value, 'symbol'));\nexport const isObject = ((value) => !isNull(value) && isTypeof(value, 'object'));\nexport const isFunction = ((value) => isTypeof(value, 'function'));\nexport const isArray = ((value) => isToString(value, 'Array'));\nexport const isRegExp = ((value) => isToString(value, 'RegExp'));\nexport const isThenable = ((value) => isFunction(value === null || value === void 0 ? void 0 : value.then));\nexport const isPrimitive = ((value) => !isObject(value) && !isFunction(value));\nexport const isEventDefined = typeof Event !== 'undefined';\nexport const isEvent = ((value) => isEventDefined && isInstanceOf(value, Event));\nexport const isErrorDefined = typeof Error !== 'undefined';\nexport const isError = ((value) => isErrorDefined && isInstanceOf(value, Error));\nexport const isErrorEvent = ((value) => isToString(value, 'ErrorEvent'));\nexport const isDomError = ((value) => isToString(value, 'DOMError'));\nexport const isDomException = ((value) => isToString(value, 'DOMException'));\nexport const isElementDefined = typeof Element !== 'undefined';\nexport const isElement = ((value) => isElementDefined && isInstanceOf(value, Element));\nexport const isMapDefined = typeof Map !== 'undefined';\nexport const isMap = ((value) => isMapDefined && isInstanceOf(value, Map));\nexport const isSyntheticEvent = ((value) => isObject(value) &&\n    'nativeEvent' in value &&\n    'preventDefault' in value &&\n    'stopPropagation' in value);\nexport function isEmpty(value) {\n    if (value == null) {\n        return true;\n    }\n    if (isArray(value) || isString(value)) {\n        return value.length === 0;\n    }\n    if (isObject(value)) {\n        return Object.keys(value).length === 0;\n    }\n    return false;\n}\n//# sourceMappingURL=is.js.map", "import { isObject } from './is';\nexport function getCircularDependencyReplacer() {\n    const valueSeen = new WeakSet();\n    return function (_key, value) {\n        if (isObject(value) && value !== null) {\n            if (valueSeen.has(value)) {\n                return null;\n            }\n            valueSeen.add(value);\n        }\n        return value;\n    };\n}\nexport function stringifyExternalJson(json = {}) {\n    return JSON.stringify(json !== null && json !== void 0 ? json : {}, getCircularDependencyReplacer());\n}\nexport function stringifyObjectValues(obj = {}) {\n    const o = {};\n    for (const [key, value] of Object.entries(obj)) {\n        o[key] = isObject(value) && value !== null ? stringifyExternalJson(value) : String(value);\n    }\n    return o;\n}\n//# sourceMappingURL=json.js.map", "export function dateNow() {\n    return Date.now();\n}\nexport function getCurrentTimestamp() {\n    return new Date().toISOString();\n}\nexport function timestampToIsoString(value) {\n    return new Date(value).toISOString();\n}\n//# sourceMappingURL=date.js.map", "import { isArray, isObject, isTypeof } from './is';\n// This function was inspired by fast-deep-equal\n// fast-deep-equal has issues with Rollup and also it checks for some edge cases that we don't need\nexport function deepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    // Using isTypeOf instead of isNumber as isNumber also checks against NaN\n    if (isTypeof(a, 'number') && isNaN(a)) {\n        return isTypeof(b, 'number') && isNaN(b);\n    }\n    const aIsArray = isArray(a);\n    const bIsArray = isArray(b);\n    if (aIsArray !== bIsArray) {\n        return false;\n    }\n    if (aIsArray && bIsArray) {\n        const length = a.length;\n        if (length !== b.length) {\n            return false;\n        }\n        for (let idx = length; idx-- !== 0;) {\n            if (!deepEqual(a[idx], b[idx])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    const aIsObject = isObject(a);\n    const bIsObject = isObject(b);\n    if (aIsObject !== bIsObject) {\n        return false;\n    }\n    if (a && b && aIsObject && bIsObject) {\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        const aLength = aKeys.length;\n        const bLength = bKeys.length;\n        if (aLength !== bLength) {\n            return false;\n        }\n        for (let aKey of aKeys) {\n            if (!bKeys.includes(aKey)) {\n                return false;\n            }\n        }\n        for (let aKey of aKeys) {\n            if (!deepEqual(a[aKey], b[aKey])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return false;\n}\n//# sourceMappingURL=deepEqual.js.map", "import { isObject, stringifyExternalJson } from '../../utils';\nexport const defaultExceptionType = 'Error';\nexport const defaultErrorArgsSerializer = (args) => {\n    return args\n        .map((arg) => {\n        if (isObject(arg)) {\n            return stringifyExternalJson(arg);\n        }\n        return String(arg);\n    })\n        .join(' ');\n};\n//# sourceMappingURL=const.js.map", "import { TransportItemType } from '../../transports';\nimport { deepEqual, getCurrentTimestamp, isArray, isError, isNull, isObject, stringifyExternalJson, stringifyObjectValues, } from '../../utils';\nimport { timestampToIsoString } from '../../utils/date';\nimport { shouldIgnoreEvent } from '../utils';\nimport { defaultExceptionType } from './const';\nlet stacktraceParser;\nexport function initializeExceptionsAPI(_unpatchedConsole, internalLogger, config, metas, transports, tracesApi) {\n    var _a;\n    internalLogger.debug('Initializing exceptions API');\n    let lastPayload = null;\n    stacktraceParser = (_a = config.parseStacktrace) !== null && _a !== void 0 ? _a : stacktraceParser;\n    const changeStacktraceParser = (newStacktraceParser) => {\n        internalLogger.debug('Changing stacktrace parser');\n        stacktraceParser = newStacktraceParser !== null && newStacktraceParser !== void 0 ? newStacktraceParser : stacktraceParser;\n    };\n    const getStacktraceParser = () => stacktraceParser;\n    const { ignoreErrors = [] } = config;\n    const pushError = (error, { skipDedupe, stackFrames, type, context, spanContext, timestampOverwriteMs } = {}) => {\n        if (isErrorIgnored(ignoreErrors, error)) {\n            return;\n        }\n        const item = {\n            meta: metas.value,\n            payload: {\n                type: type || error.name || defaultExceptionType,\n                value: error.message,\n                timestamp: timestampOverwriteMs ? timestampToIsoString(timestampOverwriteMs) : getCurrentTimestamp(),\n                trace: spanContext\n                    ? {\n                        trace_id: spanContext.traceId,\n                        span_id: spanContext.spanId,\n                    }\n                    : tracesApi.getTraceContext(),\n                context: stringifyObjectValues(Object.assign(Object.assign({}, parseCause(error)), (context !== null && context !== void 0 ? context : {}))),\n            },\n            type: TransportItemType.EXCEPTION,\n        };\n        stackFrames = stackFrames !== null && stackFrames !== void 0 ? stackFrames : (error.stack ? stacktraceParser === null || stacktraceParser === void 0 ? void 0 : stacktraceParser(error).frames : undefined);\n        if (stackFrames === null || stackFrames === void 0 ? void 0 : stackFrames.length) {\n            item.payload.stacktrace = {\n                frames: stackFrames,\n            };\n        }\n        const testingPayload = {\n            type: item.payload.type,\n            value: item.payload.value,\n            stackTrace: item.payload.stacktrace,\n            context: item.payload.context,\n        };\n        if (!skipDedupe && config.dedupe && !isNull(lastPayload) && deepEqual(testingPayload, lastPayload)) {\n            internalLogger.debug('Skipping error push because it is the same as the last one\\n', item.payload);\n            return;\n        }\n        lastPayload = testingPayload;\n        internalLogger.debug('Pushing exception\\n', item);\n        transports.execute(item);\n    };\n    changeStacktraceParser(config.parseStacktrace);\n    return {\n        changeStacktraceParser,\n        getStacktraceParser,\n        pushError,\n    };\n}\nfunction parseCause(error) {\n    let cause = error.cause;\n    if (isError(cause)) {\n        cause = error.cause.toString();\n        // typeof operator on null returns \"object\". This is a well-known quirk in JavaScript and is considered a bug that cannot be fixed due to backward compatibility issues.\n        // MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/typeof#typeof_null\n    }\n    else if (cause !== null && (isObject(error.cause) || isArray(error.cause))) {\n        cause = stringifyExternalJson(error.cause);\n    }\n    else if (cause != null) {\n        cause = error.cause.toString();\n    }\n    return cause == null ? {} : { cause };\n}\nfunction isErrorIgnored(ignoreErrors, error) {\n    const { message, name, stack } = error;\n    return shouldIgnoreEvent(ignoreErrors, message + ' ' + name + ' ' + stack);\n}\n//# sourceMappingURL=initialize.js.map", "import { isString } from '../utils/is';\nexport function shouldIgnoreEvent(patterns, msg) {\n    return patterns.some((pattern) => {\n        return isString(pattern) ? msg.includes(pattern) : !!msg.match(pattern);\n    });\n}\n//# sourceMappingURL=utils.js.map", "export const defaultLogArgsSerializer = (args) => args\n    .map((arg) => {\n    try {\n        return String(arg);\n    }\n    catch (err) {\n        return '';\n    }\n})\n    .join(' ');\n//# sourceMappingURL=const.js.map", "import { initializeEventsAPI } from './events';\nimport { initializeExceptionsAPI } from './exceptions';\nimport { initializeLogsAPI } from './logs';\nimport { initializeMeasurementsAPI } from './measurements';\nimport { initializeMetaAPI } from './meta';\nimport { initializeTracesAPI } from './traces';\nexport function initializeAPI(unpatchedConsole, internalLogger, config, metas, transports) {\n    internalLogger.debug('Initializing API');\n    const tracesApi = initializeTracesAPI(unpatchedConsole, internalLogger, config, metas, transports);\n    return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, tracesApi), initializeExceptionsAPI(unpatchedConsole, internalLogger, config, metas, transports, tracesApi)), initializeMetaAPI(unpatchedConsole, internalLogger, config, metas, transports)), initializeLogsAPI(unpatchedConsole, internalLogger, config, metas, transports, tracesApi)), initializeMeasurementsAPI(unpatchedConsole, internalLogger, config, metas, transports, tracesApi)), initializeEventsAPI(unpatchedConsole, internalLogger, config, metas, transports, tracesApi));\n}\n//# sourceMappingURL=initialize.js.map", "import { TransportItemType } from '../../transports';\nexport function initializeTracesAPI(_unpatchedConsole, internalLogger, _config, metas, transports) {\n    internalLogger.debug('Initializing traces API');\n    let otel = undefined;\n    const initOTEL = (trace, context) => {\n        internalLogger.debug('Initializing OpenTelemetry');\n        otel = {\n            trace,\n            context,\n        };\n    };\n    const getTraceContext = () => {\n        const ctx = otel === null || otel === void 0 ? void 0 : otel.trace.getSpanContext(otel.context.active());\n        return !ctx\n            ? undefined\n            : {\n                trace_id: ctx.traceId,\n                span_id: ctx.spanId,\n            };\n    };\n    const pushTraces = (payload) => {\n        try {\n            const item = {\n                type: TransportItemType.TRACE,\n                payload,\n                meta: metas.value,\n            };\n            internalLogger.debug('Pushing trace\\n', item);\n            transports.execute(item);\n        }\n        catch (err) {\n            internalLogger.error('Error pushing trace\\n', err);\n        }\n    };\n    const getOTEL = () => otel;\n    const isOTELInitialized = () => !!otel;\n    return {\n        getOTEL,\n        getTraceContext,\n        initOTEL,\n        isOTELInitialized,\n        pushTraces,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "import { isEmpty, isString } from '../../utils/is';\nexport function initializeMetaAPI(_unpatchedConsole, internalLogger, _config, metas, _transports) {\n    internalLogger.debug('Initializing meta API');\n    let metaSession = undefined;\n    let metaUser = undefined;\n    let metaView = undefined;\n    let metaPage = undefined;\n    const setUser = (user) => {\n        if (metaUser) {\n            metas.remove(metaUser);\n        }\n        metaUser = {\n            user,\n        };\n        metas.add(metaUser);\n    };\n    const setSession = (session, options) => {\n        var _a;\n        const newOverrides = options === null || options === void 0 ? void 0 : options.overrides;\n        const overrides = newOverrides\n            ? {\n                overrides: Object.assign(Object.assign({}, (_a = metaSession === null || metaSession === void 0 ? void 0 : metaSession.session) === null || _a === void 0 ? void 0 : _a.overrides), newOverrides),\n            }\n            : {};\n        if (metaSession) {\n            metas.remove(metaSession);\n        }\n        metaSession = {\n            session: Object.assign(Object.assign({}, (isEmpty(session) ? undefined : session)), overrides),\n        };\n        metas.add(metaSession);\n    };\n    const getSession = () => metas.value.session;\n    const setView = (view, options) => {\n        var _a;\n        if (options === null || options === void 0 ? void 0 : options.overrides) {\n            setSession(getSession(), { overrides: options.overrides });\n        }\n        if (((_a = metaView === null || metaView === void 0 ? void 0 : metaView.view) === null || _a === void 0 ? void 0 : _a.name) === (view === null || view === void 0 ? void 0 : view.name)) {\n            return;\n        }\n        const previousView = metaView;\n        metaView = {\n            view,\n        };\n        metas.add(metaView);\n        if (previousView) {\n            metas.remove(previousView);\n        }\n    };\n    const getView = () => metas.value.view;\n    const setPage = (page) => {\n        var _a;\n        const pageMeta = isString(page)\n            ? Object.assign(Object.assign({}, ((_a = metaPage === null || metaPage === void 0 ? void 0 : metaPage.page) !== null && _a !== void 0 ? _a : getPage())), { id: page }) : page;\n        if (metaPage) {\n            metas.remove(metaPage);\n        }\n        metaPage = {\n            page: pageMeta,\n        };\n        metas.add(metaPage);\n    };\n    const getPage = () => metas.value.page;\n    return {\n        setUser,\n        resetUser: setUser,\n        setSession,\n        resetSession: setSession,\n        getSession,\n        setView,\n        getView,\n        setPage,\n        getPage,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "import { TransportItemType } from '../../transports';\nimport { deepEqual, defaultLogLevel, getCurrentTimestamp, isNull, stringifyObjectValues } from '../../utils';\nimport { timestampToIsoString } from '../../utils/date';\nimport { defaultLogArgsSerializer } from './const';\nexport function initializeLogsAPI(_unpatchedConsole, internalLogger, config, metas, transports, tracesApi) {\n    var _a;\n    internalLogger.debug('Initializing logs API');\n    let lastPayload = null;\n    const logArgsSerializer = (_a = config.logArgsSerializer) !== null && _a !== void 0 ? _a : defaultLogArgsSerializer;\n    const pushLog = (args, { context, level, skipDedupe, spanContext, timestampOverwriteMs } = {}) => {\n        try {\n            const item = {\n                type: TransportItemType.LOG,\n                payload: {\n                    message: logArgsSerializer(args),\n                    level: level !== null && level !== void 0 ? level : defaultLogLevel,\n                    context: stringifyObjectValues(context),\n                    timestamp: timestampOverwriteMs ? timestampToIsoString(timestampOverwriteMs) : getCurrentTimestamp(),\n                    trace: spanContext\n                        ? {\n                            trace_id: spanContext.traceId,\n                            span_id: spanContext.spanId,\n                        }\n                        : tracesApi.getTraceContext(),\n                },\n                meta: metas.value,\n            };\n            const testingPayload = {\n                message: item.payload.message,\n                level: item.payload.level,\n                context: item.payload.context,\n            };\n            if (!skipDedupe && config.dedupe && !isNull(lastPayload) && deepEqual(testingPayload, lastPayload)) {\n                internalLogger.debug('Skipping log push because it is the same as the last one\\n', item.payload);\n                return;\n            }\n            lastPayload = testingPayload;\n            internalLogger.debug('Pushing log\\n', item);\n            transports.execute(item);\n        }\n        catch (err) {\n            internalLogger.error('Error pushing log\\n', err);\n        }\n    };\n    return {\n        pushLog,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "import { TransportItemType } from '../../transports';\nimport { deepEqual, getCurrentTimestamp, isNull, stringifyObjectValues } from '../../utils';\nimport { timestampToIsoString } from '../../utils/date';\nexport function initializeMeasurementsAPI(_unpatchedConsole, internalLogger, config, metas, transports, tracesApi) {\n    internalLogger.debug('Initializing measurements API');\n    let lastPayload = null;\n    const pushMeasurement = (payload, { skipDedupe, context, spanContext, timestampOverwriteMs } = {}) => {\n        try {\n            const item = {\n                type: TransportItemType.MEASUREMENT,\n                payload: Object.assign(Object.assign({}, payload), { trace: spanContext\n                        ? {\n                            trace_id: spanContext.traceId,\n                            span_id: spanContext.spanId,\n                        }\n                        : tracesApi.getTraceContext(), timestamp: timestampOverwriteMs ? timestampToIsoString(timestampOverwriteMs) : getCurrentTimestamp(), context: stringifyObjectValues(context) }),\n                meta: metas.value,\n            };\n            const testingPayload = {\n                type: item.payload.type,\n                values: item.payload.values,\n                context: item.payload.context,\n            };\n            if (!skipDedupe && config.dedupe && !isNull(lastPayload) && deepEqual(testingPayload, lastPayload)) {\n                internalLogger.debug('Skipping measurement push because it is the same as the last one\\n', item.payload);\n                return;\n            }\n            lastPayload = testingPayload;\n            internalLogger.debug('Pushing measurement\\n', item);\n            transports.execute(item);\n        }\n        catch (err) {\n            internalLogger.error('Error pushing measurement\\n', err);\n        }\n    };\n    return {\n        pushMeasurement,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "import { TransportItemType } from '../../transports';\nimport { deepEqual, getCurrentTimestamp, isNull, stringifyObjectValues } from '../../utils';\nimport { timestampToIsoString } from '../../utils/date';\nexport function initializeEventsAPI(_unpatchedConsole, internalLogger, config, metas, transports, tracesApi) {\n    let lastPayload = null;\n    const pushEvent = (name, attributes, domain, { skipDedupe, spanContext, timestampOverwriteMs } = {}) => {\n        try {\n            const item = {\n                meta: metas.value,\n                payload: {\n                    name,\n                    domain: domain !== null && domain !== void 0 ? domain : config.eventDomain,\n                    attributes: stringifyObjectValues(attributes),\n                    timestamp: timestampOverwriteMs ? timestampToIsoString(timestampOverwriteMs) : getCurrentTimestamp(),\n                    trace: spanContext\n                        ? {\n                            trace_id: spanContext.traceId,\n                            span_id: spanContext.spanId,\n                        }\n                        : tracesApi.getTraceContext(),\n                },\n                type: TransportItemType.EVENT,\n            };\n            const testingPayload = {\n                name: item.payload.name,\n                attributes: item.payload.attributes,\n                domain: item.payload.domain,\n            };\n            if (!skipDedupe && config.dedupe && !isNull(lastPayload) && deepEqual(testingPayload, lastPayload)) {\n                internalLogger.debug('Skipping event push because it is the same as the last one\\n', item.payload);\n                return;\n            }\n            lastPayload = testingPayload;\n            internalLogger.debug('Pushing event\\n', item);\n            transports.execute(item);\n        }\n        catch (err) {\n            internalLogger.error('Error pushing event', err);\n        }\n    };\n    return {\n        pushEvent,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "export function noop() { }\n//# sourceMappingURL=noop.js.map", "import { noop } from '../utils';\nexport var InternalLoggerLevel;\n(function (InternalLoggerLevel) {\n    InternalLoggerLevel[InternalLoggerLevel[\"OFF\"] = 0] = \"OFF\";\n    InternalLoggerLevel[InternalLoggerLevel[\"ERROR\"] = 1] = \"ERROR\";\n    InternalLoggerLevel[InternalLoggerLevel[\"WARN\"] = 2] = \"WARN\";\n    InternalLoggerLevel[InternalLoggerLevel[\"INFO\"] = 3] = \"INFO\";\n    InternalLoggerLevel[InternalLoggerLevel[\"VERBOSE\"] = 4] = \"VERBOSE\";\n})(InternalLoggerLevel || (InternalLoggerLevel = {}));\nexport const defaultInternalLoggerPrefix = 'Faro';\nexport const defaultInternalLogger = {\n    debug: noop,\n    error: noop,\n    info: noop,\n    prefix: defaultInternalLoggerPrefix,\n    warn: noop,\n};\nexport const defaultInternalLoggerLevel = InternalLoggerLevel.ERROR;\n//# sourceMappingURL=const.js.map", "export const defaultUnpatchedConsole = Object.assign({}, console);\n//# sourceMappingURL=const.js.map", "import { defaultUnpatchedConsole } from '../unpatchedConsole';\nimport { noop } from '../utils';\nimport { defaultInternalLogger, defaultInternalLoggerLevel, InternalLoggerLevel } from './const';\nexport function createInternalLogger(unpatchedConsole = defaultUnpatchedConsole, internalLoggerLevel = defaultInternalLoggerLevel) {\n    const internalLogger = defaultInternalLogger;\n    if (internalLoggerLevel > InternalLoggerLevel.OFF) {\n        internalLogger.error =\n            internalLoggerLevel >= InternalLoggerLevel.ERROR\n                ? function (...args) {\n                    unpatchedConsole.error(`${internalLogger.prefix}\\n`, ...args);\n                }\n                : noop;\n        internalLogger.warn =\n            internalLoggerLevel >= InternalLoggerLevel.WARN\n                ? function (...args) {\n                    unpatchedConsole.warn(`${internalLogger.prefix}\\n`, ...args);\n                }\n                : noop;\n        internalLogger.info =\n            internalLoggerLevel >= InternalLoggerLevel.INFO\n                ? function (...args) {\n                    unpatchedConsole.info(`${internalLogger.prefix}\\n`, ...args);\n                }\n                : noop;\n        internalLogger.debug =\n            internalLoggerLevel >= InternalLoggerLevel.VERBOSE\n                ? function (...args) {\n                    unpatchedConsole.debug(`${internalLogger.prefix}\\n`, ...args);\n                }\n                : noop;\n    }\n    return internalLogger;\n}\n//# sourceMappingURL=createInternalLogger.js.map", "import { defaultInternalLogger } from './const';\nimport { createInternalLogger } from './createInternalLogger';\nexport let internalLogger = defaultInternalLogger;\nexport function initializeInternalLogger(unpatchedConsole, config) {\n    internalLogger = createInternalLogger(unpatchedConsole, config.internalLoggerLevel);\n    return internalLogger;\n}\n//# sourceMappingURL=initialize.js.map", "// This does not uses isUndefined method because it will throw an error in non-browser environments\nexport const globalObject = (typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof global !== 'undefined'\n        ? global\n        : typeof self !== 'undefined'\n            ? self\n            : undefined);\n//# sourceMappingURL=globalObject.js.map", "// auto-generated by bin/genVersion.ts\nexport const VERSION = '1.14.1';\n//# sourceMappingURL=version.js.map", "export const internalGlobalObjectKey = '_faroInternal';\n//# sourceMappingURL=const.js.map", "import { setFaroOnGlobalObject } from './faroGlobalObject';\nimport { setInternalFaroOnGlobalObject } from './internalFaroGlobalObject';\nexport let faro = {};\nexport function registerFaro(unpatchedConsole, internalLogger, config, metas, transports, api, instrumentations) {\n    internalLogger.debug('Initializing Faro');\n    faro = {\n        api,\n        config,\n        instrumentations,\n        internalLogger,\n        metas,\n        pause: transports.pause,\n        transports,\n        unpatchedConsole,\n        unpause: transports.unpause,\n    };\n    setInternalFaroOnGlobalObject(faro);\n    setFaroOnGlobalObject(faro);\n    return faro;\n}\n//# sourceMappingURL=registerFaro.js.map", "import { globalObject } from '../globalObject';\nimport { internalGlobalObjectKey } from './const';\nexport function getInternalFromGlobalObject() {\n    return globalObject[internalGlobalObjectKey];\n}\nexport function setInternalFaroOnGlobalObject(faro) {\n    if (!faro.config.isolate) {\n        faro.internalLogger.debug('Registering internal Faro instance on global object');\n        Object.defineProperty(globalObject, internalGlobalObjectKey, {\n            configurable: false,\n            enumerable: false,\n            writable: false,\n            value: faro,\n        });\n    }\n    else {\n        faro.internalLogger.debug('Skipping registering internal Faro instance on global object');\n    }\n}\nexport function isInternalFaroOnGlobalObject() {\n    return internalGlobalObjectKey in globalObject;\n}\n//# sourceMappingURL=internalFaroGlobalObject.js.map", "import { globalObject } from '../globalObject';\nexport function setFaroOnGlobalObject(faro) {\n    if (!faro.config.preventGlobalExposure) {\n        faro.internalLogger.debug(`Registering public faro reference in the global scope using \"${faro.config.globalObjectKey}\" key`);\n        if (faro.config.globalObjectKey in globalObject) {\n            faro.internalLogger.warn(`Skipping global registration due to key \"${faro.config.globalObjectKey}\" being used already. Please set \"globalObjectKey\" to something else or set \"preventGlobalExposure\" to \"true\"`);\n            return;\n        }\n        Object.defineProperty(globalObject, faro.config.globalObjectKey, {\n            configurable: false,\n            writable: false,\n            value: faro,\n        });\n    }\n    else {\n        faro.internalLogger.debug('Skipping registering public Faro instance in the global scope');\n    }\n}\n//# sourceMappingURL=faroGlobalObject.js.map", "const DEFAULT_SEND_TIMEOUT_MS = 250;\nconst DEFAULT_BATCH_ITEM_LIMIT = 50;\nexport class BatchExecutor {\n    constructor(sendFn, options) {\n        var _a, _b;\n        this.signalBuffer = [];\n        this.itemLimit = (_a = options === null || options === void 0 ? void 0 : options.itemLimit) !== null && _a !== void 0 ? _a : DEFAULT_BATCH_ITEM_LIMIT;\n        this.sendTimeout = (_b = options === null || options === void 0 ? void 0 : options.sendTimeout) !== null && _b !== void 0 ? _b : DEFAULT_SEND_TIMEOUT_MS;\n        this.paused = (options === null || options === void 0 ? void 0 : options.paused) || false;\n        this.sendFn = sendFn;\n        this.flushInterval = -1;\n        if (!this.paused) {\n            this.start();\n        }\n        // Send batched/buffered data when user navigates to new page, switches or closes the tab, minimizes or closes the browser.\n        // If on mobile, it also sends data if user switches from the browser to a different app.\n        document.addEventListener('visibilitychange', () => {\n            if (document.visibilityState === 'hidden') {\n                this.flush();\n            }\n        });\n    }\n    addItem(item) {\n        if (this.paused) {\n            return;\n        }\n        this.signalBuffer.push(item);\n        if (this.signalBuffer.length >= this.itemLimit) {\n            this.flush();\n        }\n    }\n    start() {\n        this.paused = false;\n        if (this.sendTimeout > 0) {\n            this.flushInterval = window.setInterval(() => this.flush(), this.sendTimeout);\n        }\n    }\n    pause() {\n        this.paused = true;\n        clearInterval(this.flushInterval);\n    }\n    groupItems(items) {\n        const itemMap = new Map();\n        items.forEach((item) => {\n            const metaKey = JSON.stringify(item.meta);\n            let currentItems = itemMap.get(metaKey);\n            if (currentItems === undefined) {\n                currentItems = [item];\n            }\n            else {\n                currentItems = [...currentItems, item];\n            }\n            itemMap.set(metaKey, currentItems);\n        });\n        return Array.from(itemMap.values());\n    }\n    flush() {\n        if (this.paused || this.signalBuffer.length === 0) {\n            return;\n        }\n        const itemGroups = this.groupItems(this.signalBuffer);\n        itemGroups.forEach(this.sendFn);\n        this.signalBuffer = [];\n    }\n}\n//# sourceMappingURL=batchExecutor.js.map", "import { BatchExecutor } from './batchExecutor';\nexport function initializeTransports(unpatchedConsole, internalLogger, config, metas) {\n    var _a;\n    internalLogger.debug('Initializing transports');\n    const transports = [];\n    let paused = config.paused;\n    let beforeSendHooks = [];\n    const add = (...newTransports) => {\n        internalLogger.debug('Adding transports');\n        newTransports.forEach((newTransport) => {\n            internalLogger.debug(`Adding \"${newTransport.name}\" transport`);\n            const exists = transports.some((existingTransport) => existingTransport === newTransport);\n            if (exists) {\n                internalLogger.warn(`Transport ${newTransport.name} is already added`);\n                return;\n            }\n            newTransport.unpatchedConsole = unpatchedConsole;\n            newTransport.internalLogger = internalLogger;\n            newTransport.config = config;\n            newTransport.metas = metas;\n            transports.push(newTransport);\n        });\n    };\n    const addBeforeSendHooks = (...newBeforeSendHooks) => {\n        internalLogger.debug('Adding beforeSendHooks\\n', beforeSendHooks);\n        newBeforeSendHooks.forEach((beforeSendHook) => {\n            if (beforeSendHook) {\n                beforeSendHooks.push(beforeSendHook);\n            }\n        });\n    };\n    const applyBeforeSendHooks = (items) => {\n        let filteredItems = items;\n        for (const hook of beforeSendHooks) {\n            const modified = filteredItems.map(hook).filter(Boolean);\n            if (modified.length === 0) {\n                return [];\n            }\n            filteredItems = modified;\n        }\n        return filteredItems;\n    };\n    const batchedSend = (items) => {\n        const filteredItems = applyBeforeSendHooks(items);\n        if (filteredItems.length === 0) {\n            return;\n        }\n        for (const transport of transports) {\n            internalLogger.debug(`Transporting item using ${transport.name}\\n`, filteredItems);\n            if (transport.isBatched()) {\n                transport.send(filteredItems);\n            }\n        }\n    };\n    const instantSend = (item) => {\n        var _a, _b;\n        // prevent all beforeSend hooks being executed twice if batching is enabled.\n        if (((_a = config.batching) === null || _a === void 0 ? void 0 : _a.enabled) && transports.every((transport) => transport.isBatched())) {\n            return;\n        }\n        const [filteredItem] = applyBeforeSendHooks([item]);\n        if (filteredItem === undefined) {\n            return;\n        }\n        for (const transport of transports) {\n            internalLogger.debug(`Transporting item using ${transport.name}\\n`, filteredItem);\n            if (!transport.isBatched()) {\n                transport.send(filteredItem);\n            }\n            else if (!((_b = config.batching) === null || _b === void 0 ? void 0 : _b.enabled)) {\n                transport.send([filteredItem]);\n            }\n        }\n    };\n    let batchExecutor;\n    if ((_a = config.batching) === null || _a === void 0 ? void 0 : _a.enabled) {\n        batchExecutor = new BatchExecutor(batchedSend, {\n            sendTimeout: config.batching.sendTimeout,\n            itemLimit: config.batching.itemLimit,\n            paused,\n        });\n    }\n    // Send a signal to the appropriate transports\n    //\n    // 1. If SDK is paused, early return\n    // 2. If batching is not enabled send the signal to all transports\n    //    instantly.\n    // 3i. If batching is enabled, enqueue the signal\n    // 3ii. Send the signal instantly to all un-batched transports\n    const execute = (item) => {\n        var _a;\n        if (paused) {\n            return;\n        }\n        if ((_a = config.batching) === null || _a === void 0 ? void 0 : _a.enabled) {\n            batchExecutor === null || batchExecutor === void 0 ? void 0 : batchExecutor.addItem(item);\n        }\n        instantSend(item);\n    };\n    const getBeforeSendHooks = () => [...beforeSendHooks];\n    const isPaused = () => paused;\n    const pause = () => {\n        internalLogger.debug('Pausing transports');\n        batchExecutor === null || batchExecutor === void 0 ? void 0 : batchExecutor.pause();\n        paused = true;\n    };\n    const remove = (...transportsToRemove) => {\n        internalLogger.debug('Removing transports');\n        transportsToRemove.forEach((transportToRemove) => {\n            internalLogger.debug(`Removing \"${transportToRemove.name}\" transport`);\n            const existingTransportIndex = transports.indexOf(transportToRemove);\n            if (existingTransportIndex === -1) {\n                internalLogger.warn(`Transport \"${transportToRemove.name}\" is not added`);\n                return;\n            }\n            transports.splice(existingTransportIndex, 1);\n        });\n    };\n    const removeBeforeSendHooks = (...beforeSendHooksToRemove) => {\n        beforeSendHooks.filter((beforeSendHook) => !beforeSendHooksToRemove.includes(beforeSendHook));\n    };\n    const unpause = () => {\n        internalLogger.debug('Unpausing transports');\n        batchExecutor === null || batchExecutor === void 0 ? void 0 : batchExecutor.start();\n        paused = false;\n    };\n    return {\n        add,\n        addBeforeSendHooks,\n        getBeforeSendHooks,\n        execute,\n        isPaused,\n        pause,\n        remove,\n        removeBeforeSendHooks,\n        get transports() {\n            return [...transports];\n        },\n        unpause,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "import { defaultUnpatchedConsole } from './const';\nexport let unpatchedConsole = defaultUnpatchedConsole;\nexport function initializeUnpatchedConsole(config) {\n    var _a;\n    unpatchedConsole = (_a = config.unpatchedConsole) !== null && _a !== void 0 ? _a : unpatchedConsole;\n    return unpatchedConsole;\n}\n//# sourceMappingURL=initialize.js.map", "import { initializeAPI } from './api';\nimport { initializeInstrumentations, registerInitialInstrumentations } from './instrumentations';\nimport { initializeInternalLogger } from './internalLogger';\nimport { initializeMetas, registerInitialMetas } from './metas';\nimport { isInternalFaroOnGlobalObject, registerFaro } from './sdk';\nimport { initializeTransports, registerInitialTransports } from './transports';\nimport { initializeUnpatchedConsole } from './unpatchedConsole';\nexport function initializeFaro(config) {\n    const unpatchedConsole = initializeUnpatchedConsole(config);\n    const internalLogger = initializeInternalLogger(unpatchedConsole, config);\n    if (isInternalFaroOnGlobalObject() && !config.isolate) {\n        internalLogger.error('Faro is already registered. Either add instrumentations, transports etc. to the global faro instance or use the \"isolate\" property');\n        return undefined;\n    }\n    internalLogger.debug('Initializing');\n    // Initializing the APIs\n    const metas = initializeMetas(unpatchedConsole, internalLogger, config);\n    const transports = initializeTransports(unpatchedConsole, internalLogger, config, metas);\n    const api = initializeAPI(unpatchedConsole, internalLogger, config, metas, transports);\n    const instrumentations = initializeInstrumentations(unpatchedConsole, internalLogger, config, metas, transports, api);\n    const faro = registerFaro(unpatchedConsole, internalLogger, config, metas, transports, api, instrumentations);\n    // make sure Faro is initialized before registering default metas, instrumentations, transports etc.\n    registerInitialMetas(faro);\n    registerInitialTransports(faro);\n    registerInitialInstrumentations(faro);\n    return faro;\n}\n//# sourceMappingURL=initialize.js.map", "import { isFunction } from '../utils';\nexport function initializeMetas(_unpatchedConsole, internalLogger, _config) {\n    let items = [];\n    let listeners = [];\n    const getValue = () => items.reduce((acc, item) => Object.assign(acc, isFunction(item) ? item() : item), {});\n    const notifyListeners = () => {\n        if (listeners.length) {\n            const value = getValue();\n            listeners.forEach((listener) => listener(value));\n        }\n    };\n    const add = (...newItems) => {\n        internalLogger.debug('Adding metas\\n', newItems);\n        items.push(...newItems);\n        notifyListeners();\n    };\n    const remove = (...itemsToRemove) => {\n        internalLogger.debug('Removing metas\\n', itemsToRemove);\n        items = items.filter((currentItem) => !itemsToRemove.includes(currentItem));\n        notifyListeners();\n    };\n    const addListener = (listener) => {\n        internalLogger.debug('Adding metas listener\\n', listener);\n        listeners.push(listener);\n    };\n    const removeListener = (listener) => {\n        internalLogger.debug('Removing metas listener\\n', listener);\n        listeners = listeners.filter((currentListener) => currentListener !== listener);\n    };\n    return {\n        add,\n        remove,\n        addListener,\n        removeListener,\n        get value() {\n            return getValue();\n        },\n    };\n}\n//# sourceMappingURL=initialize.js.map", "export function initializeInstrumentations(unpatchedConsole, internalLogger, config, metas, transports, api) {\n    internalLogger.debug('Initializing instrumentations');\n    const instrumentations = [];\n    const add = (...newInstrumentations) => {\n        internalLogger.debug('Adding instrumentations');\n        newInstrumentations.forEach((newInstrumentation) => {\n            internalLogger.debug(`Adding \"${newInstrumentation.name}\" instrumentation`);\n            const exists = instrumentations.some((existingInstrumentation) => existingInstrumentation.name === newInstrumentation.name);\n            if (exists) {\n                internalLogger.warn(`Instrumentation ${newInstrumentation.name} is already added`);\n                return;\n            }\n            newInstrumentation.unpatchedConsole = unpatchedConsole;\n            newInstrumentation.internalLogger = internalLogger;\n            newInstrumentation.config = config;\n            newInstrumentation.metas = metas;\n            newInstrumentation.transports = transports;\n            newInstrumentation.api = api;\n            instrumentations.push(newInstrumentation);\n            newInstrumentation.initialize();\n        });\n    };\n    const remove = (...instrumentationsToRemove) => {\n        internalLogger.debug('Removing instrumentations');\n        instrumentationsToRemove.forEach((instrumentationToRemove) => {\n            var _a, _b;\n            internalLogger.debug(`Removing \"${instrumentationToRemove.name}\" instrumentation`);\n            const existingInstrumentationIndex = instrumentations.reduce((acc, existingInstrumentation, existingTransportIndex) => {\n                if (acc === null && existingInstrumentation.name === instrumentationToRemove.name) {\n                    return existingTransportIndex;\n                }\n                return null;\n            }, null);\n            if (!existingInstrumentationIndex) {\n                internalLogger.warn(`Instrumentation \"${instrumentationToRemove.name}\" is not added`);\n                return;\n            }\n            (_b = (_a = instrumentations[existingInstrumentationIndex]).destroy) === null || _b === void 0 ? void 0 : _b.call(_a);\n            instrumentations.splice(existingInstrumentationIndex, 1);\n        });\n    };\n    return {\n        add,\n        get instrumentations() {\n            return [...instrumentations];\n        },\n        remove,\n    };\n}\n//# sourceMappingURL=initialize.js.map", "import { getBundleId } from '../utils/sourceMaps';\nimport { VERSION } from '../version';\nexport function registerInitialMetas(faro) {\n    var _a, _b;\n    const initial = {\n        sdk: {\n            version: VERSION,\n        },\n        app: {\n            bundleId: faro.config.app.name && getBundleId(faro.config.app.name),\n        },\n    };\n    const session = (_a = faro.config.sessionTracking) === null || _a === void 0 ? void 0 : _a.session;\n    if (session) {\n        faro.api.setSession(session);\n    }\n    if (faro.config.app) {\n        initial.app = Object.assign(Object.assign({}, faro.config.app), initial.app);\n    }\n    if (faro.config.user) {\n        initial.user = faro.config.user;\n    }\n    if (faro.config.view) {\n        initial.view = faro.config.view;\n    }\n    faro.metas.add(initial, ...((_b = faro.config.metas) !== null && _b !== void 0 ? _b : []));\n}\n//# sourceMappingURL=registerInitial.js.map", "import { globalObject } from '../globalObject';\nexport function getBundleId(appName) {\n    return globalObject === null || globalObject === void 0 ? void 0 : globalObject[`__faroBundleId_${appName}`];\n}\n//# sourceMappingURL=sourceMaps.js.map", "export function registerInitialTransports(faro) {\n    faro.transports.add(...faro.config.transports);\n    faro.transports.addBeforeSendHooks(faro.config.beforeSend);\n}\n//# sourceMappingURL=registerInitial.js.map", "export function registerInitialInstrumentations(faro) {\n    faro.instrumentations.add(...faro.config.instrumentations);\n}\n//# sourceMappingURL=registerInitial.js.map", "export const defaultGlobalObjectKey = 'faro';\nexport const defaultBatchingConfig = {\n    enabled: true,\n    sendTimeout: 250,\n    itemLimit: 50,\n};\n//# sourceMappingURL=const.js.map", "export const defaultEventDomain = 'browser';\n//# sourceMappingURL=consts.js.map", "export const newLineString = '\\n';\nexport const evalString = 'eval';\nexport const unknownSymbolString = '?';\nexport const atString = '@';\nexport const webkitLineRegex = /^\\s*at (?:(.*\\).*?|.*?) ?\\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\\/)?.*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\nexport const webkitEvalRegex = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\nexport const webkitEvalString = 'eval';\nexport const webkitAddressAtString = 'address at ';\nexport const webkitAddressAtStringLength = webkitAddressAtString.length;\nexport const firefoxLineRegex = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\\/.*?|\\[native code]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nexport const firefoxEvalRegex = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\nexport const firefoxEvalString = ' > eval';\nexport const safariExtensionString = 'safari-extension';\nexport const safariWebExtensionString = 'safari-web-extension';\nexport const reactMinifiedRegex = /Minified React error #\\d+;/i;\n//# sourceMappingURL=const.js.map", "import { unknownSymbolString } from './const';\nexport function buildStackFrame(filename, func, lineno, colno) {\n    const stackFrame = {\n        filename: filename || document.location.href,\n        function: func || unknownSymbolString,\n    };\n    if (lineno !== undefined) {\n        stackFrame.lineno = lineno;\n    }\n    if (colno !== undefined) {\n        stackFrame.colno = colno;\n    }\n    return stackFrame;\n}\n//# sourceMappingURL=buildStackFrame.js.map", "import { atString, safariExtensionString, safariWebExtensionString } from './const';\nexport function getDataFromSafariExtensions(func, filename) {\n    const isSafariExtension = func === null || func === void 0 ? void 0 : func.includes(safariExtensionString);\n    const isSafariWebExtension = !isSafariExtension && (func === null || func === void 0 ? void 0 : func.includes(safariWebExtensionString));\n    if (!isSafariExtension && !isSafariWebExtension) {\n        return [func, filename];\n    }\n    return [\n        (func === null || func === void 0 ? void 0 : func.includes(atString)) ? func.split(atString)[0] : func,\n        isSafariExtension ? `${safariExtensionString}:${filename}` : `${safariWebExtensionString}:${filename}`,\n    ];\n}\n//# sourceMappingURL=getDataFromSafariExtensions.js.map", "import { isNumber } from '@grafana/faro-core';\nimport { buildStackFrame } from './buildStackFrame';\nimport { evalString, firefoxEvalRegex, firefoxEvalString, firefoxLineRegex, newLineString, reactMinifiedRegex, webkitAddressAtString, webkitAddressAtStringLength, webkitEvalRegex, webkitEvalString, webkitLineRegex, } from './const';\nimport { getDataFromSafariExtensions } from './getDataFromSafariExtensions';\nexport function getStackFramesFromError(error) {\n    let lines = [];\n    if (error.stacktrace) {\n        lines = error.stacktrace.split(newLineString).filter((_line, idx) => idx % 2 === 0);\n    }\n    else if (error.stack) {\n        lines = error.stack.split(newLineString);\n    }\n    const stackFrames = lines.reduce((acc, line, idx) => {\n        let parts;\n        let func;\n        let filename;\n        let lineno;\n        let colno;\n        if ((parts = webkitLineRegex.exec(line))) {\n            func = parts[1];\n            filename = parts[2];\n            lineno = parts[3];\n            colno = parts[4];\n            if (filename === null || filename === void 0 ? void 0 : filename.startsWith(webkitEvalString)) {\n                const submatch = webkitEvalRegex.exec(filename);\n                if (submatch) {\n                    filename = submatch[1];\n                    lineno = submatch[2];\n                    colno = submatch[3];\n                }\n            }\n            filename = (filename === null || filename === void 0 ? void 0 : filename.startsWith(webkitAddressAtString))\n                ? filename.substring(webkitAddressAtStringLength)\n                : filename;\n            [func, filename] = getDataFromSafariExtensions(func, filename);\n        }\n        else if ((parts = firefoxLineRegex.exec(line))) {\n            func = parts[1];\n            filename = parts[3];\n            lineno = parts[4];\n            colno = parts[5];\n            if (!!filename && filename.includes(firefoxEvalString)) {\n                const submatch = firefoxEvalRegex.exec(filename);\n                if (submatch) {\n                    func = func || evalString;\n                    filename = submatch[1];\n                    lineno = submatch[2];\n                }\n            }\n            else if (idx === 0 && !colno && isNumber(error.columnNumber)) {\n                colno = String(error.columnNumber + 1);\n            }\n            [func, filename] = getDataFromSafariExtensions(func, filename);\n        }\n        if (filename || func) {\n            acc.push(buildStackFrame(filename, func, lineno ? Number(lineno) : undefined, colno ? Number(colno) : undefined));\n        }\n        return acc;\n    }, []);\n    if (reactMinifiedRegex.test(error.message)) {\n        return stackFrames.slice(1);\n    }\n    return stackFrames;\n}\n//# sourceMappingURL=getStackFramesFromError.js.map", "import { getStackFramesFromError } from './getStackFramesFromError';\nexport function parseStacktrace(error) {\n    return {\n        frames: getStackFramesFromError(error),\n    };\n}\n//# sourceMappingURL=parseStacktrace.js.map", "export const STORAGE_KEY = 'com.grafana.faro.session';\nexport const SESSION_EXPIRATION_TIME = 4 * 60 * 60 * 1000; // hrs\nexport const SESSION_INACTIVITY_TIME = 15 * 60 * 1000; // minutes\nexport const STORAGE_UPDATE_DELAY = 1 * 1000; // seconds\n/**\n * @deprecated MAX_SESSION_PERSISTENCE_TIME_BUFFER is not used anymore. The constant will be removed in the future\n */\nexport const MAX_SESSION_PERSISTENCE_TIME_BUFFER = 1 * 60 * 1000;\nexport const MAX_SESSION_PERSISTENCE_TIME = SESSION_INACTIVITY_TIME;\nexport const defaultSessionTrackingConfig = {\n    enabled: true,\n    persistent: false,\n    maxSessionPersistenceTime: MAX_SESSION_PERSISTENCE_TIME,\n};\n//# sourceMappingURL=sessionConstants.js.map", "export const unknownString = 'unknown';\n//# sourceMappingURL=consts.js.map", "import { UAParser } from 'ua-parser-js';\nimport { unknownString } from '@grafana/faro-core';\nexport const browserMeta = () => {\n    const parser = new UAParser();\n    const { name, version } = parser.getBrowser();\n    const { name: osName, version: osVersion } = parser.getOS();\n    const userAgent = parser.getUA();\n    const language = navigator.language;\n    const mobile = navigator.userAgent.includes('Mobi');\n    const brands = getBrands();\n    return {\n        browser: {\n            name: name !== null && name !== void 0 ? name : unknownString,\n            version: version !== null && version !== void 0 ? version : unknownString,\n            os: `${osName !== null && osName !== void 0 ? osName : unknownString} ${osVersion !== null && osVersion !== void 0 ? osVersion : unknownString}`,\n            userAgent: userAgent !== null && userAgent !== void 0 ? userAgent : unknownString,\n            language: language !== null && language !== void 0 ? language : unknownString,\n            mobile,\n            brands: brands !== null && brands !== void 0 ? brands : unknownString,\n            viewportWidth: `${window.innerWidth}`,\n            viewportHeight: `${window.innerHeight}`,\n        },\n    };\n    function getBrands() {\n        if (!name || !version) {\n            return undefined;\n        }\n        if ('userAgentData' in navigator && navigator.userAgentData) {\n            // userAgentData in experimental (only Chrome supports it) thus TS does not ship the respective type declarations\n            return navigator.userAgentData.brands;\n        }\n        return undefined;\n    }\n};\n//# sourceMappingURL=meta.js.map", "export const k6Meta = () => {\n    const k6Properties = window.k6;\n    return {\n        k6: Object.assign({ \n            // we only add the k6 meta if Far<PERSON> is running inside a k6 environment, so this is always true\n            isK6Browser: true }, ((k6Properties === null || k6Properties === void 0 ? void 0 : k6Properties.testRunId) && { testRunId: k6Properties === null || k6Properties === void 0 ? void 0 : k6Properties.testRunId })),\n    };\n};\n//# sourceMappingURL=meta.js.map", "import { isFunction } from '@grafana/faro-core';\nlet currentHref;\nlet pageId;\nexport function createPageMeta({ generatePageId, initialPageMeta } = {}) {\n    const pageMeta = () => {\n        const locationHref = location.href;\n        if (isFunction(generatePageId) && currentHref !== locationHref) {\n            currentHref = locationHref;\n            pageId = generatePageId(location);\n        }\n        return {\n            page: Object.assign(Object.assign({ url: locationHref }, (pageId ? { id: pageId } : {})), initialPageMeta),\n        };\n    };\n    return pageMeta;\n}\n//# sourceMappingURL=meta.js.map", "import { defaultInternalLogger } from '../internalLogger';\nimport { defaultUnpatchedConsole } from '../unpatchedConsole';\nexport class BaseExtension {\n    constructor() {\n        this.unpatchedConsole = defaultUnpatchedConsole;\n        this.internalLogger = defaultInternalLogger;\n        this.config = {};\n        this.metas = {};\n    }\n    logDebug(...args) {\n        this.internalLogger.debug(`${this.name}\\n`, ...args);\n    }\n    logInfo(...args) {\n        this.internalLogger.info(`${this.name}\\n`, ...args);\n    }\n    logWarn(...args) {\n        this.internalLogger.warn(`${this.name}\\n`, ...args);\n    }\n    logError(...args) {\n        this.internalLogger.error(`${this.name}\\n`, ...args);\n    }\n}\n//# sourceMappingURL=baseExtension.js.map", "import { BaseExtension } from '../extensions';\nexport class BaseTransport extends BaseExtension {\n    isBatched() {\n        return false;\n    }\n    getIgnoreUrls() {\n        return [];\n    }\n}\n//# sourceMappingURL=base.js.map", "import { TransportItemType, transportItemTypeToBodyKey } from './const';\nexport function mergeResourceSpans(traces, resourceSpans) {\n    var _a, _b;\n    if (resourceSpans === undefined) {\n        return traces;\n    }\n    if (traces === undefined) {\n        return {\n            resourceSpans,\n        };\n    }\n    const currentResource = (_a = traces.resourceSpans) === null || _a === void 0 ? void 0 : _a[0];\n    if (currentResource === undefined) {\n        return traces;\n    }\n    const currentSpans = (currentResource === null || currentResource === void 0 ? void 0 : currentResource.scopeSpans) || [];\n    const newSpans = ((_b = resourceSpans === null || resourceSpans === void 0 ? void 0 : resourceSpans[0]) === null || _b === void 0 ? void 0 : _b.scopeSpans) || [];\n    return Object.assign(Object.assign({}, traces), { resourceSpans: [\n            Object.assign(Object.assign({}, currentResource), { scopeSpans: [...currentSpans, ...newSpans] }),\n        ] });\n}\nexport function getTransportBody(item) {\n    let body = {\n        meta: {},\n    };\n    if (item[0] !== undefined) {\n        body.meta = item[0].meta;\n    }\n    item.forEach((currentItem) => {\n        switch (currentItem.type) {\n            case TransportItemType.LOG:\n            case TransportItemType.EVENT:\n            case TransportItemType.EXCEPTION:\n            case TransportItemType.MEASUREMENT:\n                const bk = transportItemTypeToBodyKey[currentItem.type];\n                const signals = body[bk];\n                body = Object.assign(Object.assign({}, body), { [bk]: signals === undefined ? [currentItem.payload] : [...signals, currentItem.payload] });\n                break;\n            case TransportItemType.TRACE:\n                body = Object.assign(Object.assign({}, body), { traces: mergeResourceSpans(body.traces, currentItem.payload.resourceSpans) });\n                break;\n        }\n    });\n    return body;\n}\n//# sourceMappingURL=utils.js.map", "/**\n * Tail based throttle which caches the args of the last call and updates\n */\nexport function throttle(callback, delay) {\n    let pause = false;\n    let lastPending;\n    const timeoutBehavior = () => {\n        if (lastPending == null) {\n            pause = false;\n            return;\n        }\n        callback(...lastPending);\n        lastPending = null;\n        setTimeout(timeoutBehavior, delay);\n    };\n    return (...args) => {\n        if (pause) {\n            lastPending = args;\n            return;\n        }\n        callback(...args);\n        pause = true;\n        setTimeout(timeoutBehavior, delay);\n    };\n}\n//# sourceMappingURL=throttle.js.map", "import { faro } from '@grafana/faro-core';\nexport const webStorageType = {\n    session: 'sessionStorage',\n    local: 'localStorage',\n};\n/**\n * Check if selected web storage mechanism is available.\n * @param type storage mechanism to test availability for.\n * @returns\n */\nexport function isWebStorageAvailable(type) {\n    var _a;\n    try {\n        let storage;\n        storage = window[type];\n        const testItem = '__faro_storage_test__';\n        storage.setItem(testItem, testItem);\n        storage.removeItem(testItem);\n        return true;\n    }\n    catch (error) {\n        // the above can throw\n        (_a = faro.internalLogger) === null || _a === void 0 ? void 0 : _a.info(`Web storage of type ${type} is not available. Reason: ${error}`);\n        return false;\n    }\n}\n/**\n * Get item from SessionStorage or LocalStorage.\n * @param key: the item key.\n * @param webStorageMechanism: wether the item shall be received form local storage or session storage. Defaults to local storage.\n */\nexport function getItem(key, webStorageMechanism) {\n    if (isWebStorageTypeAvailable(webStorageMechanism)) {\n        return window[webStorageMechanism].getItem(key);\n    }\n    return null;\n}\n/**\n * Store item in SessionStorage or LocalStorage.\n * @param key: the item key.\n * @param value: the item data.\n * @param webStorageMechanism: wether the item shall be received form local storage or session storage. Defaults to local storage.\n */\nexport function setItem(key, value, webStorageMechanism) {\n    if (isWebStorageTypeAvailable(webStorageMechanism)) {\n        try {\n            window[webStorageMechanism].setItem(key, value);\n        }\n        catch (error) {\n            // do nothing\n        }\n    }\n}\n/**\n * Remove item from SessionStorage or LocalStorage.\n * @param key: the item key.\n * @param webStorageMechanism: wether the item shall be received form local storage or session storage. Defaults to local storage.\n */\nexport function removeItem(key, webStorageMechanism) {\n    if (isWebStorageTypeAvailable(webStorageMechanism)) {\n        window[webStorageMechanism].removeItem(key);\n    }\n}\nexport const isLocalStorageAvailable = isWebStorageAvailable(webStorageType.local);\nexport const isSessionStorageAvailable = isWebStorageAvailable(webStorageType.session);\nfunction isWebStorageTypeAvailable(webStorageMechanism) {\n    if (webStorageMechanism === webStorageType.local) {\n        return isLocalStorageAvailable;\n    }\n    if (webStorageMechanism === webStorageType.session) {\n        return isSessionStorageAvailable;\n    }\n    return false;\n}\n//# sourceMappingURL=webStorage.js.map", "const alphabet = 'abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ0123456789';\nexport function genShortID(length = 10) {\n    return Array.from(Array(length))\n        .map(() => alphabet[Math.floor(Math.random() * alphabet.length)])\n        .join('');\n}\n//# sourceMappingURL=shortId.js.map", "/**\n * @deprecated The conventions object will be removed in a future version\n */\nexport const Conventions = {\n    /**\n     * @deprecated The event names object will be removed in a future version\n     */\n    EventNames: {\n        CLICK: 'click',\n        NAVIGATION: 'navigation',\n        SESSION_START: 'session_start',\n        VIEW_CHANGED: 'view_changed',\n    },\n};\nexport const EVENT_CLICK = 'click';\nexport const EVENT_NAVIGATION = 'navigation';\nexport const EVENT_VIEW_CHANGED = 'view_changed';\nexport const EVENT_SESSION_START = 'session_start';\nexport const EVENT_SESSION_RESUME = 'session_resume';\nexport const EVENT_SESSION_EXTEND = 'session_extend';\nexport const EVENT_OVERRIDES_SERVICE_NAME = 'service_name_override';\nexport const EVENT_ROUTE_CHANGE = 'route_change';\n//# sourceMappingURL=semantic.js.map", "import { faro } from '@grafana/faro-core';\nexport function isSampled() {\n    var _a, _b, _c;\n    const sendAllSignals = 1;\n    const sessionTracking = faro.config.sessionTracking;\n    let samplingRate = (_c = (_b = (_a = sessionTracking === null || sessionTracking === void 0 ? void 0 : sessionTracking.sampler) === null || _a === void 0 ? void 0 : _a.call(sessionTracking, { metas: faro.metas.value })) !== null && _b !== void 0 ? _b : sessionTracking === null || sessionTracking === void 0 ? void 0 : sessionTracking.samplingRate) !== null && _c !== void 0 ? _c : sendAllSignals;\n    if (typeof samplingRate !== 'number') {\n        const sendNoSignals = 0;\n        samplingRate = sendNoSignals;\n    }\n    return Math.random() < samplingRate;\n}\n//# sourceMappingURL=sampling.js.map", "import { dateNow, deepEqual, EVENT_OVERRIDES_SERVICE_NAME, faro, genShortID, isEmpty } from '@grafana/faro-core';\nimport { isLocalStorageAvailable, isSessionStorageAvailable } from '../../../utils';\nimport { isSampled } from './sampling';\nimport { SESSION_EXPIRATION_TIME, SESSION_INACTIVITY_TIME } from './sessionConstants';\nexport function createUserSessionObject({ sessionId, started, lastActivity, isSampled = true, } = {}) {\n    var _a, _b;\n    const now = dateNow();\n    const generateSessionId = (_b = (_a = faro.config) === null || _a === void 0 ? void 0 : _a.sessionTracking) === null || _b === void 0 ? void 0 : _b.generateSessionId;\n    if (sessionId == null) {\n        sessionId = typeof generateSessionId === 'function' ? generateSessionId() : genShortID();\n    }\n    return {\n        sessionId,\n        lastActivity: lastActivity !== null && lastActivity !== void 0 ? lastActivity : now,\n        started: started !== null && started !== void 0 ? started : now,\n        isSampled: isSampled,\n    };\n}\nexport function isUserSessionValid(session) {\n    if (session == null) {\n        return false;\n    }\n    const now = dateNow();\n    const lifetimeValid = now - session.started < SESSION_EXPIRATION_TIME;\n    if (!lifetimeValid) {\n        return false;\n    }\n    const inactivityPeriodValid = now - session.lastActivity < SESSION_INACTIVITY_TIME;\n    return inactivityPeriodValid;\n}\nexport function getUserSessionUpdater({ fetchUserSession, storeUserSession, }) {\n    return function updateSession({ forceSessionExtend } = { forceSessionExtend: false }) {\n        var _a, _b, _c;\n        if (!fetchUserSession || !storeUserSession) {\n            return;\n        }\n        const sessionTrackingConfig = faro.config.sessionTracking;\n        const isPersistentSessions = sessionTrackingConfig === null || sessionTrackingConfig === void 0 ? void 0 : sessionTrackingConfig.persistent;\n        if ((isPersistentSessions && !isLocalStorageAvailable) || (!isPersistentSessions && !isSessionStorageAvailable)) {\n            return;\n        }\n        const sessionFromStorage = fetchUserSession();\n        if (forceSessionExtend === false && isUserSessionValid(sessionFromStorage)) {\n            storeUserSession(Object.assign(Object.assign({}, sessionFromStorage), { lastActivity: dateNow() }));\n        }\n        else {\n            let newSession = addSessionMetadataToNextSession(createUserSessionObject({ isSampled: isSampled() }), sessionFromStorage);\n            storeUserSession(newSession);\n            (_a = faro.api) === null || _a === void 0 ? void 0 : _a.setSession(newSession.sessionMeta);\n            (_b = sessionTrackingConfig === null || sessionTrackingConfig === void 0 ? void 0 : sessionTrackingConfig.onSessionChange) === null || _b === void 0 ? void 0 : _b.call(sessionTrackingConfig, (_c = sessionFromStorage === null || sessionFromStorage === void 0 ? void 0 : sessionFromStorage.sessionMeta) !== null && _c !== void 0 ? _c : null, newSession.sessionMeta);\n        }\n    };\n}\nexport function addSessionMetadataToNextSession(newSession, previousSession) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const sessionWithMeta = Object.assign(Object.assign({}, newSession), { sessionMeta: {\n            id: newSession.sessionId,\n            attributes: Object.assign(Object.assign(Object.assign({}, (_b = (_a = faro.config.sessionTracking) === null || _a === void 0 ? void 0 : _a.session) === null || _b === void 0 ? void 0 : _b.attributes), ((_d = (_c = faro.metas.value.session) === null || _c === void 0 ? void 0 : _c.attributes) !== null && _d !== void 0 ? _d : {})), { isSampled: newSession.isSampled.toString() }),\n        } });\n    const overrides = (_f = (_e = faro.metas.value.session) === null || _e === void 0 ? void 0 : _e.overrides) !== null && _f !== void 0 ? _f : (_g = previousSession === null || previousSession === void 0 ? void 0 : previousSession.sessionMeta) === null || _g === void 0 ? void 0 : _g.overrides;\n    if (!isEmpty(overrides)) {\n        sessionWithMeta.sessionMeta.overrides = overrides;\n    }\n    const previousSessionId = previousSession === null || previousSession === void 0 ? void 0 : previousSession.sessionId;\n    if (previousSessionId != null) {\n        sessionWithMeta.sessionMeta.attributes['previousSession'] = previousSessionId;\n    }\n    return sessionWithMeta;\n}\nexport function getSessionMetaUpdateHandler({ fetchUserSession, storeUserSession, }) {\n    return function syncSessionIfChangedExternally(meta) {\n        const session = meta.session;\n        const sessionFromSessionStorage = fetchUserSession();\n        let sessionId = session === null || session === void 0 ? void 0 : session.id;\n        const sessionAttributes = session === null || session === void 0 ? void 0 : session.attributes;\n        const sessionOverrides = session === null || session === void 0 ? void 0 : session.overrides;\n        const storedSessionMeta = sessionFromSessionStorage === null || sessionFromSessionStorage === void 0 ? void 0 : sessionFromSessionStorage.sessionMeta;\n        const storedSessionMetaOverrides = storedSessionMeta === null || storedSessionMeta === void 0 ? void 0 : storedSessionMeta.overrides;\n        const hasSessionOverridesChanged = !!sessionOverrides && !deepEqual(sessionOverrides, storedSessionMetaOverrides);\n        const hasAttributesChanged = !!sessionAttributes && !deepEqual(sessionAttributes, storedSessionMeta === null || storedSessionMeta === void 0 ? void 0 : storedSessionMeta.attributes);\n        const hasSessionIdChanged = !!session && sessionId !== (sessionFromSessionStorage === null || sessionFromSessionStorage === void 0 ? void 0 : sessionFromSessionStorage.sessionId);\n        if (hasSessionIdChanged || hasAttributesChanged || hasSessionOverridesChanged) {\n            const userSession = addSessionMetadataToNextSession(createUserSessionObject({ sessionId, isSampled: isSampled() }), sessionFromSessionStorage);\n            storeUserSession(userSession);\n            sendOverrideEvent(hasSessionOverridesChanged, sessionOverrides, storedSessionMetaOverrides);\n            faro.api.setSession(userSession.sessionMeta);\n        }\n    };\n}\nfunction sendOverrideEvent(hasSessionOverridesChanged, sessionOverrides = {}, storedSessionOverrides = {}) {\n    var _a, _b, _c;\n    if (!hasSessionOverridesChanged) {\n        return;\n    }\n    const serviceName = sessionOverrides.serviceName;\n    const previousServiceName = (_c = (_a = storedSessionOverrides.serviceName) !== null && _a !== void 0 ? _a : (_b = faro.metas.value.app) === null || _b === void 0 ? void 0 : _b.name) !== null && _c !== void 0 ? _c : '';\n    if (serviceName && serviceName !== previousServiceName) {\n        faro.api.pushEvent(EVENT_OVERRIDES_SERVICE_NAME, {\n            serviceName,\n            previousServiceName,\n        });\n    }\n}\n//# sourceMappingURL=sessionManagerUtils.js.map", "import { faro, stringifyExternalJson } from '@grafana/faro-core';\nimport { throttle } from '../../../utils';\nimport { getItem, removeItem, setItem, webStorageType } from '../../../utils/webStorage';\nimport { STORAGE_KEY, STORAGE_UPDATE_DELAY } from './sessionConstants';\nimport { getSessionMetaUpdateHandler, getUserSessionUpdater } from './sessionManagerUtils';\nexport class PersistentSessionsManager {\n    constructor() {\n        this.updateSession = throttle(() => this.updateUserSession(), STORAGE_UPDATE_DELAY);\n        this.updateUserSession = getUserSessionUpdater({\n            fetchUserSession: PersistentSessionsManager.fetchUserSession,\n            storeUserSession: PersistentSessionsManager.storeUserSession,\n        });\n        this.init();\n    }\n    static removeUserSession() {\n        removeItem(STORAGE_KEY, PersistentSessionsManager.storageTypeLocal);\n    }\n    static storeUserSession(session) {\n        setItem(STORAGE_KEY, stringifyExternalJson(session), PersistentSessionsManager.storageTypeLocal);\n    }\n    static fetchUserSession() {\n        const storedSession = getItem(STORAGE_KEY, PersistentSessionsManager.storageTypeLocal);\n        if (storedSession) {\n            return JSON.parse(storedSession);\n        }\n        return null;\n    }\n    init() {\n        document.addEventListener('visibilitychange', () => {\n            if (document.visibilityState === 'visible') {\n                this.updateSession();\n            }\n        });\n        // Users can call the setSession() method, so we need to sync this with the local storage session\n        faro.metas.addListener(getSessionMetaUpdateHandler({\n            fetchUserSession: PersistentSessionsManager.fetchUserSession,\n            storeUserSession: PersistentSessionsManager.storeUserSession,\n        }));\n    }\n}\nPersistentSessionsManager.storageTypeLocal = webStorageType.local;\n//# sourceMappingURL=PersistentSessionsManager.js.map", "import { faro, stringifyExternalJson } from '@grafana/faro-core';\nimport { throttle } from '../../../utils';\nimport { getItem, removeItem, setItem, webStorageType } from '../../../utils/webStorage';\nimport { STORAGE_KEY, STORAGE_UPDATE_DELAY } from './sessionConstants';\nimport { getSessionMetaUpdateHandler, getUserSessionUpdater } from './sessionManagerUtils';\nexport class VolatileSessionsManager {\n    constructor() {\n        this.updateSession = throttle(() => this.updateUserSession(), STORAGE_UPDATE_DELAY);\n        this.updateUserSession = getUserSessionUpdater({\n            fetchUserSession: VolatileSessionsManager.fetchUserSession,\n            storeUserSession: VolatileSessionsManager.storeUserSession,\n        });\n        this.init();\n    }\n    static removeUserSession() {\n        removeItem(STORAGE_KEY, VolatileSessionsManager.storageTypeSession);\n    }\n    static storeUserSession(session) {\n        setItem(STORAGE_KEY, stringifyExternalJson(session), VolatileSessionsManager.storageTypeSession);\n    }\n    static fetchUserSession() {\n        const storedSession = getItem(STORAGE_KEY, VolatileSessionsManager.storageTypeSession);\n        if (storedSession) {\n            return JSON.parse(storedSession);\n        }\n        return null;\n    }\n    init() {\n        document.addEventListener('visibilitychange', () => {\n            if (document.visibilityState === 'visible') {\n                this.updateSession();\n            }\n        });\n        // Users can call the setSession() method, so we need to sync this with the local storage session\n        faro.metas.addListener(getSessionMetaUpdateHandler({\n            fetchUserSession: VolatileSessionsManager.fetchUserSession,\n            storeUserSession: VolatileSessionsManager.storeUserSession,\n        }));\n    }\n}\nVolatileSessionsManager.storageTypeSession = webStorageType.session;\n//# sourceMappingURL=VolatileSessionManager.js.map", "import { PersistentSessionsManager } from './PersistentSessionsManager';\nimport { VolatileSessionsManager } from './VolatileSessionManager';\nexport function getSessionManagerByConfig(sessionTrackingConfig) {\n    return (sessionTrackingConfig === null || sessionTrackingConfig === void 0 ? void 0 : sessionTrackingConfig.persistent) ? PersistentSessionsManager : VolatileSessionsManager;\n}\n//# sourceMappingURL=getSessionManagerByConfig.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { BaseTransport, createPromiseBuffer, getTransportBody, noop, VERSION, } from '@grafana/faro-core';\nimport { getSessionManagerByConfig } from '../../instrumentations/session/sessionManager';\nimport { getUserSessionUpdater } from '../../instrumentations/session/sessionManager/sessionManagerUtils';\nconst DEFAULT_BUFFER_SIZE = 30;\nconst DEFAULT_CONCURRENCY = 5; // chrome supports 10 total, firefox 17\nconst DEFAULT_RATE_LIMIT_BACKOFF_MS = 5000;\nconst BEACON_BODY_SIZE_LIMIT = 60000;\nconst TOO_MANY_REQUESTS = 429;\nconst ACCEPTED = 202;\nexport class FetchTransport extends BaseTransport {\n    constructor(options) {\n        var _a, _b, _c, _d;\n        super();\n        this.options = options;\n        this.name = '@grafana/faro-web-sdk:transport-fetch';\n        this.version = VERSION;\n        this.disabledUntil = new Date();\n        this.rateLimitBackoffMs = (_a = options.defaultRateLimitBackoffMs) !== null && _a !== void 0 ? _a : DEFAULT_RATE_LIMIT_BACKOFF_MS;\n        this.getNow = (_b = options.getNow) !== null && _b !== void 0 ? _b : (() => Date.now());\n        this.promiseBuffer = createPromiseBuffer({\n            size: (_c = options.bufferSize) !== null && _c !== void 0 ? _c : DEFAULT_BUFFER_SIZE,\n            concurrency: (_d = options.concurrency) !== null && _d !== void 0 ? _d : DEFAULT_CONCURRENCY,\n        });\n    }\n    send(items) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                if (this.disabledUntil > new Date(this.getNow())) {\n                    this.logWarn(`Dropping transport item due to too many requests. Backoff until ${this.disabledUntil}`);\n                    return Promise.resolve();\n                }\n                yield this.promiseBuffer.add(() => {\n                    const body = JSON.stringify(getTransportBody(items));\n                    const { url, requestOptions, apiKey } = this.options;\n                    const _a = requestOptions !== null && requestOptions !== void 0 ? requestOptions : {}, { headers } = _a, restOfRequestOptions = __rest(_a, [\"headers\"]);\n                    let sessionId;\n                    const sessionMeta = this.metas.value.session;\n                    if (sessionMeta != null) {\n                        sessionId = sessionMeta.id;\n                    }\n                    return fetch(url, Object.assign({ method: 'POST', headers: Object.assign(Object.assign(Object.assign({ 'Content-Type': 'application/json' }, (headers !== null && headers !== void 0 ? headers : {})), (apiKey ? { 'x-api-key': apiKey } : {})), (sessionId ? { 'x-faro-session-id': sessionId } : {})), body, keepalive: body.length <= BEACON_BODY_SIZE_LIMIT }, (restOfRequestOptions !== null && restOfRequestOptions !== void 0 ? restOfRequestOptions : {})))\n                        .then((response) => __awaiter(this, void 0, void 0, function* () {\n                        if (response.status === ACCEPTED) {\n                            const sessionExpired = response.headers.get('X-Faro-Session-Status') === 'invalid';\n                            if (sessionExpired) {\n                                this.extendFaroSession(this.config, this.logDebug);\n                            }\n                        }\n                        if (response.status === TOO_MANY_REQUESTS) {\n                            this.disabledUntil = this.getRetryAfterDate(response);\n                            this.logWarn(`Too many requests, backing off until ${this.disabledUntil}`);\n                        }\n                        // read the body so the connection can be closed\n                        response.text().catch(noop);\n                        return response;\n                    }))\n                        .catch((err) => {\n                        this.logError('Failed sending payload to the receiver\\n', JSON.parse(body), err);\n                    });\n                });\n            }\n            catch (err) {\n                this.logError(err);\n            }\n        });\n    }\n    getIgnoreUrls() {\n        var _a;\n        return [this.options.url].concat((_a = this.config.ignoreUrls) !== null && _a !== void 0 ? _a : []);\n    }\n    isBatched() {\n        return true;\n    }\n    getRetryAfterDate(response) {\n        const now = this.getNow();\n        const retryAfterHeader = response.headers.get('Retry-After');\n        if (retryAfterHeader) {\n            const delay = Number(retryAfterHeader);\n            if (!isNaN(delay)) {\n                return new Date(delay * 1000 + now);\n            }\n            const date = Date.parse(retryAfterHeader);\n            if (!isNaN(date)) {\n                return new Date(date);\n            }\n        }\n        return new Date(now + this.rateLimitBackoffMs);\n    }\n    extendFaroSession(config, logDebug) {\n        const SessionExpiredString = `Session expired`;\n        const sessionTrackingConfig = config.sessionTracking;\n        if (sessionTrackingConfig === null || sessionTrackingConfig === void 0 ? void 0 : sessionTrackingConfig.enabled) {\n            const { fetchUserSession, storeUserSession } = getSessionManagerByConfig(sessionTrackingConfig);\n            getUserSessionUpdater({ fetchUserSession, storeUserSession })({ forceSessionExtend: true });\n            logDebug(`${SessionExpiredString} created new session.`);\n        }\n        else {\n            logDebug(`${SessionExpiredString}.`);\n        }\n    }\n}\n//# sourceMappingURL=transport.js.map", "export function createPromiseBuffer(options) {\n    const { size, concurrency } = options;\n    const buffer = []; // pending, not-yet-started tasks\n    let inProgress = 0; // counter for tasks currently in progress\n    const work = () => {\n        // if there's space for a task and buffer is not empty,\n        // take one task from buffer and run it\n        if (inProgress < concurrency && buffer.length) {\n            const { producer, resolve, reject } = buffer.shift();\n            inProgress++;\n            producer().then((result) => {\n                inProgress--;\n                work();\n                resolve(result);\n            }, (reason) => {\n                inProgress--;\n                work();\n                reject(reason);\n            });\n        }\n    };\n    const add = (promiseProducer) => {\n        if (buffer.length + inProgress >= size) {\n            throw new Error('Task buffer full');\n        }\n        return new Promise((resolve, reject) => {\n            buffer.push({\n                producer: promiseProducer,\n                resolve,\n                reject,\n            });\n            work();\n        });\n    };\n    return {\n        add,\n    };\n}\n//# sourceMappingURL=promiseBuffer.js.map", "import { BaseExtension } from '../extensions';\nexport class BaseInstrumentation extends BaseExtension {\n    constructor() {\n        super(...arguments);\n        this.api = {};\n        this.transports = {};\n    }\n}\n//# sourceMappingURL=base.js.map", "export const primitiveUnhandledValue = 'Non-Error promise rejection captured with value:';\nexport const primitiveUnhandledType = 'UnhandledRejection';\nexport const domErrorType = 'DOMError';\nexport const domExceptionType = 'DOMException';\nexport const objectEventValue = 'Non-Error exception captured with keys:';\nexport const unknownSymbolString = '?';\nexport const valueTypeRegex = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;\n//# sourceMappingURL=const.js.map", "import { isDomError, isDomException, isError, isErrorEvent, isEvent, isObject, isString } from '@grafana/faro-core';\nimport { domErrorType, domExceptionType, objectEventValue, unknownSymbolString } from './const';\nimport { getValueAndTypeFromMessage } from './getValueAndTypeFromMessage';\nimport { buildStackFrame, getStackFramesFromError } from './stackFrames';\nexport function getErrorDetails(evt) {\n    let value;\n    let type;\n    let stackFrames = [];\n    let isDomErrorRes;\n    let isEventRes;\n    if (isErrorEvent(evt) && evt.error) {\n        value = evt.error.message;\n        type = evt.error.name;\n        stackFrames = getStackFramesFromError(evt.error);\n    }\n    else if ((isDomErrorRes = isDomError(evt)) || isDomException(evt)) {\n        const { name, message } = evt;\n        type = name !== null && name !== void 0 ? name : (isDomErrorRes ? domErrorType : domExceptionType);\n        value = message ? `${type}: ${message}` : type;\n    }\n    else if (isError(evt)) {\n        value = evt.message;\n        stackFrames = getStackFramesFromError(evt);\n    }\n    else if (isObject(evt) || (isEventRes = isEvent(evt))) {\n        type = isEventRes ? evt.constructor.name : undefined;\n        value = `${objectEventValue} ${Object.keys(evt)}`;\n    }\n    return [value, type, stackFrames];\n}\nexport function getDetailsFromErrorArgs(args) {\n    const [evt, source, lineno, colno, error] = args;\n    let value;\n    let type;\n    let stackFrames = [];\n    const eventIsString = isString(evt);\n    const initialStackFrame = buildStackFrame(source, unknownSymbolString, lineno, colno);\n    if (error || !eventIsString) {\n        [value, type, stackFrames] = getErrorDetails((error !== null && error !== void 0 ? error : evt));\n        if (stackFrames.length === 0) {\n            stackFrames = [initialStackFrame];\n        }\n    }\n    else if (eventIsString) {\n        [value, type] = getValueAndTypeFromMessage(evt);\n        stackFrames = [initialStackFrame];\n    }\n    return { value, type, stackFrames };\n}\nexport function getDetailsFromConsoleErrorArgs(args, serializer) {\n    if (isError(args[0])) {\n        return getDetailsFromErrorArgs(args);\n    }\n    else {\n        return { value: serializer(args) };\n    }\n}\n//# sourceMappingURL=getErrorDetails.js.map", "import { defaultExceptionType } from '@grafana/faro-core';\nimport { valueTypeRegex } from './const';\nexport function getValueAndTypeFromMessage(message) {\n    var _a, _b;\n    const groups = message.match(valueTypeRegex);\n    const type = (_a = groups === null || groups === void 0 ? void 0 : groups[1]) !== null && _a !== void 0 ? _a : defaultExceptionType;\n    const value = (_b = groups === null || groups === void 0 ? void 0 : groups[2]) !== null && _b !== void 0 ? _b : message;\n    return [value, type];\n}\n//# sourceMappingURL=getValueAndTypeFromMessage.js.map", "import { isPrimitive } from '@grafana/faro-core';\nimport { primitiveUnhandledType, primitiveUnhandledValue } from './const';\nimport { getErrorDetails } from './getErrorDetails';\nexport function registerOnunhandledrejection(api) {\n    window.addEventListener('unhandledrejection', (evt) => {\n        var _a, _b;\n        let error = evt;\n        if (error.reason) {\n            error = evt.reason;\n        }\n        else if ((_a = evt.detail) === null || _a === void 0 ? void 0 : _a.reason) {\n            error = (_b = evt.detail) === null || _b === void 0 ? void 0 : _b.reason;\n        }\n        let value;\n        let type;\n        let stackFrames = [];\n        if (isPrimitive(error)) {\n            value = `${primitiveUnhandledValue} ${String(error)}`;\n            type = primitiveUnhandledType;\n        }\n        else {\n            [value, type, stackFrames] = getErrorDetails(error);\n        }\n        if (value) {\n            api.pushError(new Error(value), { type, stackFrames });\n        }\n    });\n}\n//# sourceMappingURL=registerOnunhandledrejection.js.map", "import { BaseInstrumentation, VERSION } from '@grafana/faro-core';\nimport { registerOnerror } from './registerOnerror';\nimport { registerOnunhandledrejection } from './registerOnunhandledrejection';\nexport class ErrorsInstrumentation extends BaseInstrumentation {\n    constructor() {\n        super(...arguments);\n        this.name = '@grafana/faro-web-sdk:instrumentation-errors';\n        this.version = VERSION;\n    }\n    initialize() {\n        this.logDebug('Initializing');\n        registerOnerror(this.api);\n        registerOnunhandledrejection(this.api);\n    }\n}\n//# sourceMappingURL=instrumentation.js.map", "import { getDetailsFromErrorArgs } from './getErrorDetails';\nexport function registerOnerror(api) {\n    const oldOnerror = window.onerror;\n    window.onerror = (...args) => {\n        try {\n            const { value, type, stackFrames } = getDetailsFromErrorArgs(args);\n            if (value) {\n                api.pushError(new Error(value), { type, stackFrames });\n            }\n        }\n        finally {\n            oldOnerror === null || oldOnerror === void 0 ? void 0 : oldOnerror.apply(window, args);\n        }\n    };\n}\n//# sourceMappingURL=registerOnerror.js.map", "var e,n,t,r,i,o=-1,a=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(o=n.timeStamp,e(n))}),!0)},c=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),r=\"navigate\";o>=0?r=\"back-forward-cache\":t&&(document.prerendering||u()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":t.type&&(r=t.type.replace(/_/g,\"-\")));return{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},t||{})),r}}catch(e){}},d=function(e,n,t,r){var i,o;return function(a){n.value>=0&&(a||r)&&((o=n.value-(i||0))||void 0===i)&&(i=n.value,n.delta=o,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&e()}))},v=function(e){var n=!1;return function(){n||(e(),n=!0)}},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),a((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},C=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},b=[1800,3e3],S=function(e,n){n=n||{},C((function(){var t,r=E(),i=f(\"FCP\"),o=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-u(),0),i.entries.push(e),t(!0)))}))}));o&&(t=d(e,i,b,n.reportAllChanges),a((function(r){i=f(\"FCP\"),t=d(e,i,b,n.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,t(!0)}))})))}))},L=[.1,.25],w=function(e,n){n=n||{},S(v((function(){var t,r=f(\"CLS\",0),i=0,o=[],c=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=o[0],t=o[o.length-1];i&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}})),i>r.value&&(r.value=i,r.entries=o,t())},u=s(\"layout-shift\",c);u&&(t=d(e,r,L,n.reportAllChanges),p((function(){c(u.takeRecords()),t(!0)})),a((function(){i=0,r=f(\"CLS\",0),t=d(e,r,L,n.reportAllChanges),l((function(){return t()}))})),setTimeout(t,0))})))},A=0,I=1/0,P=0,M=function(e){e.forEach((function(e){e.interactionId&&(I=Math.min(I,e.interactionId),P=Math.max(P,e.interactionId),A=P?(P-I)/7+1:0)}))},k=function(){return e?A:performance.interactionCount||0},F=function(){\"interactionCount\"in performance||e||(e=s(\"event\",M,{type:\"event\",buffered:!0,durationThreshold:0}))},D=[],x=new Map,R=0,B=function(){var e=Math.min(D.length-1,Math.floor((k()-R)/50));return D[e]},H=[],q=function(e){if(H.forEach((function(n){return n(e)})),e.interactionId||\"first-input\"===e.entryType){var n=D[D.length-1],t=x.get(e.interactionId);if(t||D.length<10||e.duration>n.latency){if(t)e.duration>t.latency?(t.entries=[e],t.latency=e.duration):e.duration===t.latency&&e.startTime===t.entries[0].startTime&&t.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};x.set(r.id,r),D.push(r)}D.sort((function(e,n){return n.latency-e.latency})),D.length>10&&D.splice(10).forEach((function(e){return x.delete(e.id)}))}}},O=function(e){var n=self.requestIdleCallback||self.setTimeout,t=-1;return e=v(e),\"hidden\"===document.visibilityState?e():(t=n(e),p(e)),t},N=[200,500],j=function(e,n){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(n=n||{},C((function(){var t;F();var r,i=f(\"INP\"),o=function(e){O((function(){e.forEach(q);var n=B();n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())}))},c=s(\"event\",o,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});r=d(e,i,N,n.reportAllChanges),c&&(c.observe({type:\"first-input\",buffered:!0}),p((function(){o(c.takeRecords()),r(!0)})),a((function(){R=k(),D.length=0,x.clear(),i=f(\"INP\"),r=d(e,i,N,n.reportAllChanges)})))})))},_=[2500,4e3],z={},G=function(e,n){n=n||{},C((function(){var t,r=E(),i=f(\"LCP\"),o=function(e){n.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-u(),0),i.entries=[e],t())}))},c=s(\"largest-contentful-paint\",o);if(c){t=d(e,i,_,n.reportAllChanges);var m=v((function(){z[i.id]||(o(c.takeRecords()),c.disconnect(),z[i.id]=!0,t(!0))}));[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,(function(){return O(m)}),{once:!0,capture:!0})})),p(m),a((function(r){i=f(\"LCP\"),t=d(e,i,_,n.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,z[i.id]=!0,t(!0)}))}))}}))},J=[800,1800],K=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},Q=function(e,n){n=n||{};var t=f(\"TTFB\"),r=d(e,t,J,n.reportAllChanges);K((function(){var i=c();i&&(t.value=Math.max(i.responseStart-u(),0),t.entries=[i],r(!0),a((function(){t=f(\"TTFB\",0),(r=d(e,t,J,n.reportAllChanges))(!0)})))}))},U={passive:!0,capture:!0},V=new Date,W=function(e,i){n||(n=i,t=e,r=new Date,Z(removeEventListener),X())},X=function(){if(t>=0&&t<r-V){var e={entryType:\"first-input\",name:n.type,target:n.target,cancelable:n.cancelable,startTime:n.timeStamp,processingStart:n.timeStamp+t};i.forEach((function(n){n(e)})),i=[]}},Y=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){W(e,n),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",t,U),removeEventListener(\"pointercancel\",r,U)};addEventListener(\"pointerup\",t,U),addEventListener(\"pointercancel\",r,U)}(n,e):W(n,e)}},Z=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,Y,U)}))},$=[100,300],ee=function(e,r){r=r||{},C((function(){var o,c=E(),u=f(\"FID\"),l=function(e){e.startTime<c.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),o(!0))},m=function(e){e.forEach(l)},h=s(\"first-input\",m);o=d(e,u,$,r.reportAllChanges),h&&(p(v((function(){m(h.takeRecords()),h.disconnect()}))),a((function(){var a;u=f(\"FID\"),o=d(e,u,$,r.reportAllChanges),i=[],t=-1,n=null,Z(addEventListener),a=l,i.push(a),X()})))}))};export{L as CLSThresholds,b as FCPThresholds,$ as FIDThresholds,N as INPThresholds,_ as LCPThresholds,J as TTFBThresholds,w as onCLS,S as onFCP,ee as onFID,j as onINP,G as onLCP,Q as onTTFB};\n", "import { onCLS, onFCP, onFID, onINP, onLCP, onTTFB } from 'web-vitals';\nexport class WebVitalsBasic {\n    constructor(pushMeasurement, webVitalConfig) {\n        this.pushMeasurement = pushMeasurement;\n        this.webVitalConfig = webVitalConfig;\n    }\n    initialize() {\n        Object.entries(WebVitalsBasic.mapping).forEach(([indicator, executor]) => {\n            var _a;\n            executor((metric) => {\n                this.pushMeasurement({\n                    type: 'web-vitals',\n                    values: {\n                        [indicator]: metric.value,\n                    },\n                });\n            }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n        });\n    }\n}\nWebVitalsBasic.mapping = {\n    cls: onCLS,\n    fcp: onFCP,\n    fid: onFID,\n    inp: onINP,\n    lcp: onLCP,\n    ttfb: onTTFB,\n};\n//# sourceMappingURL=webVitalsBasic.js.map", "var t,e,n=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},r=function(t){if(\"loading\"===document.readyState)return\"loading\";var e=n();if(e){if(t<e.domInteractive)return\"loading\";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return\"dom-interactive\";if(0===e.domComplete||t<e.domComplete)return\"dom-content-loaded\"}return\"complete\"},i=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,\"\")},a=function(t,e){var n=\"\";try{for(;t&&9!==t.nodeType;){var r=t,a=r.id?\"#\"+r.id:i(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?\".\"+r.classList.value.trim().replace(/\\s+/g,\".\"):\"\");if(n.length+a.length>(e||100)-1)return n||a;if(n=n?a+\">\"+n:a,r.id)break;t=r.parentNode}}catch(t){}return n},o=-1,c=function(){return o},u=function(t){addEventListener(\"pageshow\",(function(e){e.persisted&&(o=e.timeStamp,t(e))}),!0)},s=function(){var t=n();return t&&t.activationStart||0},f=function(t,e){var r=n(),i=\"navigate\";c()>=0?i=\"back-forward-cache\":r&&(document.prerendering||s()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":r.type&&(i=r.type.replace(/_/g,\"-\")));return{name:t,value:void 0===e?-1:e,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},d=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},l=function(t,e,n,r){var i,a;return function(o){e.value>=0&&(o||r)&&((a=e.value-(i||0))||void 0===i)&&(i=e.value,e.delta=a,e.rating=function(t,e){return t>e[1]?\"poor\":t>e[0]?\"needs-improvement\":\"good\"}(e.value,n),t(e))}},m=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&t()}))},v=function(t){var e=!1;return function(){e||(t(),e=!0)}},g=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},T=function(t){\"hidden\"===document.visibilityState&&g>-1&&(g=\"visibilitychange\"===t.type?t.timeStamp:0,E())},y=function(){addEventListener(\"visibilitychange\",T,!0),addEventListener(\"prerenderingchange\",T,!0)},E=function(){removeEventListener(\"visibilitychange\",T,!0),removeEventListener(\"prerenderingchange\",T,!0)},S=function(){return g<0&&(g=h(),y(),u((function(){setTimeout((function(){g=h(),y()}),0)}))),{get firstHiddenTime(){return g}}},b=function(t){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return t()}),!0):t()},L=[1800,3e3],C=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FCP\"),a=d(\"paint\",(function(t){t.forEach((function(t){\"first-contentful-paint\"===t.name&&(a.disconnect(),t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries.push(t),n(!0)))}))}));a&&(n=l(t,i,L,e.reportAllChanges),u((function(r){i=f(\"FCP\"),n=l(t,i,L,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},M=[.1,.25],D=function(t,e){!function(t,e){e=e||{},C(v((function(){var n,r=f(\"CLS\",0),i=0,a=[],o=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=a[0],n=a[a.length-1];i&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(i+=t.value,a.push(t)):(i=t.value,a=[t])}})),i>r.value&&(r.value=i,r.entries=a,n())},c=d(\"layout-shift\",o);c&&(n=l(t,r,M,e.reportAllChanges),p((function(){o(c.takeRecords()),n(!0)})),u((function(){i=0,r=f(\"CLS\",0),n=l(t,r,M,e.reportAllChanges),m((function(){return n()}))})),setTimeout(n,0))})))}((function(e){var n=function(t){var e,n={};if(t.entries.length){var i=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(i&&i.sources&&i.sources.length){var o=(e=i.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];o&&(n={largestShiftTarget:a(o.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:o,largestShiftEntry:i,loadState:r(i.startTime)})}}return Object.assign(t,{attribution:n})}(e);t(n)}),e)},w=function(t,e){C((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:r(c())};if(t.entries.length){var i=n(),a=t.entries[t.entries.length-1];if(i){var o=i.activationStart||0,u=Math.max(0,i.responseStart-o);e={timeToFirstByte:u,firstByteToFCP:t.value-u,loadState:r(t.entries[0].startTime),navigationEntry:i,fcpEntry:a}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},x=0,I=1/0,k=0,A=function(t){t.forEach((function(t){t.interactionId&&(I=Math.min(I,t.interactionId),k=Math.max(k,t.interactionId),x=k?(k-I)/7+1:0)}))},F=function(){return t?x:performance.interactionCount||0},P=function(){\"interactionCount\"in performance||t||(t=d(\"event\",A,{type:\"event\",buffered:!0,durationThreshold:0}))},B=[],O=new Map,R=0,j=function(){var t=Math.min(B.length-1,Math.floor((F()-R)/50));return B[t]},q=[],H=function(t){if(q.forEach((function(e){return e(t)})),t.interactionId||\"first-input\"===t.entryType){var e=B[B.length-1],n=O.get(t.interactionId);if(n||B.length<10||t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===n.entries[0].startTime&&n.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};O.set(r.id,r),B.push(r)}B.sort((function(t,e){return e.latency-t.latency})),B.length>10&&B.splice(10).forEach((function(t){return O.delete(t.id)}))}}},N=function(t){var e=self.requestIdleCallback||self.setTimeout,n=-1;return t=v(t),\"hidden\"===document.visibilityState?t():(n=e(t),p(t)),n},W=[200,500],z=function(t,e){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(e=e||{},b((function(){var n;P();var r,i=f(\"INP\"),a=function(t){N((function(){t.forEach(H);var e=j();e&&e.latency!==i.value&&(i.value=e.latency,i.entries=e.entries,r())}))},o=d(\"event\",a,{durationThreshold:null!==(n=e.durationThreshold)&&void 0!==n?n:40});r=l(t,i,W,e.reportAllChanges),o&&(o.observe({type:\"first-input\",buffered:!0}),p((function(){a(o.takeRecords()),r(!0)})),u((function(){R=F(),B.length=0,O.clear(),i=f(\"INP\"),r=l(t,i,W,e.reportAllChanges)})))})))},U=[],V=[],_=0,G=new WeakMap,J=new Map,K=-1,Q=function(t){U=U.concat(t),X()},X=function(){K<0&&(K=N(Y))},Y=function(){J.size>10&&J.forEach((function(t,e){O.has(e)||J.delete(e)}));var t=B.map((function(t){return G.get(t.entries[0])})),e=V.length-50;V=V.filter((function(n,r){return r>=e||t.includes(n)}));for(var n=new Set,r=0;r<V.length;r++){var i=V[r];nt(i.startTime,i.processingEnd).forEach((function(t){n.add(t)}))}var a=U.length-1-50;U=U.filter((function(t,e){return t.startTime>_&&e>a||n.has(t)})),K=-1};q.push((function(t){t.interactionId&&t.target&&!J.has(t.interactionId)&&J.set(t.interactionId,t.target)}),(function(t){var e,n=t.startTime+t.duration;_=Math.max(_,t.processingEnd);for(var r=V.length-1;r>=0;r--){var i=V[r];if(Math.abs(n-i.renderTime)<=8){(e=i).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:n,entries:[t]},V.push(e)),(t.interactionId||\"first-input\"===t.entryType)&&G.set(t,e),X()}));var Z,$,tt,et,nt=function(t,e){for(var n,r=[],i=0;n=U[i];i++)if(!(n.startTime+n.duration<t)){if(n.startTime>e)break;r.push(n)}return r},rt=function(t,n){e||(e=d(\"long-animation-frame\",Q)),z((function(e){var n=function(t){var e=t.entries[0],n=G.get(e),i=e.processingStart,o=n.processingEnd,c=n.entries.sort((function(t,e){return t.processingStart-e.processingStart})),u=nt(e.startTime,o),s=t.entries.find((function(t){return t.target})),f=s&&s.target||J.get(e.interactionId),d=[e.startTime+e.duration,o].concat(u.map((function(t){return t.startTime+t.duration}))),l=Math.max.apply(Math,d),m={interactionTarget:a(f),interactionTargetElement:f,interactionType:e.name.startsWith(\"key\")?\"keyboard\":\"pointer\",interactionTime:e.startTime,nextPaintTime:l,processedEventEntries:c,longAnimationFrameEntries:u,inputDelay:i-e.startTime,processingDuration:o-i,presentationDelay:Math.max(l-o,0),loadState:r(e.startTime)};return Object.assign(t,{attribution:m})}(e);t(n)}),n)},it=[2500,4e3],at={},ot=function(t,e){!function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"LCP\"),a=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries=[t],n())}))},o=d(\"largest-contentful-paint\",a);if(o){n=l(t,i,it,e.reportAllChanges);var c=v((function(){at[i.id]||(a(o.takeRecords()),o.disconnect(),at[i.id]=!0,n(!0))}));[\"keydown\",\"click\"].forEach((function(t){addEventListener(t,(function(){return N(c)}),{once:!0,capture:!0})})),p(c),u((function(r){i=f(\"LCP\"),n=l(t,i,it,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,at[i.id]=!0,n(!0)}))}))}}))}((function(e){var r=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var r=n();if(r){var i=r.activationStart||0,o=t.entries[t.entries.length-1],c=o.url&&performance.getEntriesByType(\"resource\").filter((function(t){return t.name===o.url}))[0],u=Math.max(0,r.responseStart-i),s=Math.max(u,c?(c.requestStart||c.startTime)-i:0),f=Math.max(s,c?c.responseEnd-i:0),d=Math.max(f,o.startTime-i);e={element:a(o.element),timeToFirstByte:u,resourceLoadDelay:s-u,resourceLoadDuration:f-s,elementRenderDelay:d-f,navigationEntry:r,lcpEntry:o},o.url&&(e.url=o.url),c&&(e.lcpResourceEntry=c)}}return Object.assign(t,{attribution:e})}(e);t(r)}),e)},ct=[800,1800],ut=function t(e){document.prerendering?b((function(){return t(e)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return t(e)}),!0):setTimeout(e,0)},st=function(t,e){e=e||{};var r=f(\"TTFB\"),i=l(t,r,ct,e.reportAllChanges);ut((function(){var a=n();a&&(r.value=Math.max(a.responseStart-s(),0),r.entries=[a],i(!0),u((function(){r=f(\"TTFB\",0),(i=l(t,r,ct,e.reportAllChanges))(!0)})))}))},ft=function(t,e){st((function(e){var n=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var n=t.entries[0],r=n.activationStart||0,i=Math.max((n.workerStart||n.fetchStart)-r,0),a=Math.max(n.domainLookupStart-r,0),o=Math.max(n.connectStart-r,0),c=Math.max(n.connectEnd-r,0);e={waitingDuration:i,cacheDuration:a-i,dnsDuration:o-a,connectionDuration:c-o,requestDuration:t.value-c,navigationEntry:n}}return Object.assign(t,{attribution:e})}(e);t(n)}),e)},dt={passive:!0,capture:!0},lt=new Date,mt=function(t,e){Z||(Z=e,$=t,tt=new Date,gt(removeEventListener),pt())},pt=function(){if($>=0&&$<tt-lt){var t={entryType:\"first-input\",name:Z.type,target:Z.target,cancelable:Z.cancelable,startTime:Z.timeStamp,processingStart:Z.timeStamp+$};et.forEach((function(e){e(t)})),et=[]}},vt=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;\"pointerdown\"==t.type?function(t,e){var n=function(){mt(t,e),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",n,dt),removeEventListener(\"pointercancel\",r,dt)};addEventListener(\"pointerup\",n,dt),addEventListener(\"pointercancel\",r,dt)}(e,t):mt(e,t)}},gt=function(t){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(e){return t(e,vt,dt)}))},ht=[100,300],Tt=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FID\"),a=function(t){t.startTime<r.firstHiddenTime&&(i.value=t.processingStart-t.startTime,i.entries.push(t),n(!0))},o=function(t){t.forEach(a)},c=d(\"first-input\",o);n=l(t,i,ht,e.reportAllChanges),c&&(p(v((function(){o(c.takeRecords()),c.disconnect()}))),u((function(){var r;i=f(\"FID\"),n=l(t,i,ht,e.reportAllChanges),et=[],$=-1,Z=null,gt(addEventListener),r=a,et.push(r),pt()})))}))},yt=function(t,e){Tt((function(e){var n=function(t){var e=t.entries[0],n={eventTarget:a(e.target),eventType:e.name,eventTime:e.startTime,eventEntry:e,loadState:r(e.startTime)};return Object.assign(t,{attribution:n})}(e);t(n)}),e)};export{M as CLSThresholds,L as FCPThresholds,ht as FIDThresholds,W as INPThresholds,it as LCPThresholds,ct as TTFBThresholds,D as onCLS,w as onFCP,yt as onFID,rt as onINP,ot as onLCP,ft as onTTFB};\n", "export const NAVIGATION_ID_STORAGE_KEY = 'com.grafana.faro.lastNavigationId';\n//# sourceMappingURL=instrumentationConstants.js.map", "import { onCLS, onFCP, onFID, onINP, onLCP, onTTFB } from 'web-vitals/attribution';\nimport { unknownString } from '@grafana/faro-core';\nimport { getItem, webStorageType } from '../../utils';\nimport { NAVIGATION_ID_STORAGE_KEY } from '../instrumentationConstants';\n// duplicate keys saved in variables to save bundle size\n// refs: https://github.com/grafana/faro-web-sdk/pull/595#discussion_r1615833968\nconst loadStateKey = 'load_state';\nconst timeToFirstByteKey = 'time_to_first_byte';\nexport class WebVitalsWithAttribution {\n    constructor(corePushMeasurement, webVitalConfig) {\n        this.corePushMeasurement = corePushMeasurement;\n        this.webVitalConfig = webVitalConfig;\n    }\n    initialize() {\n        this.measureCLS();\n        this.measureFCP();\n        this.measureFID();\n        this.measureINP();\n        this.measureLCP();\n        this.measureTTFB();\n    }\n    measureCLS() {\n        var _a;\n        onCLS((metric) => {\n            const { loadState, largestShiftValue, largestShiftTime, largestShiftTarget } = metric.attribution;\n            const values = this.buildInitialValues(metric);\n            this.addIfPresent(values, 'largest_shift_value', largestShiftValue);\n            this.addIfPresent(values, 'largest_shift_time', largestShiftTime);\n            const context = this.buildInitialContext(metric);\n            this.addIfPresent(context, loadStateKey, loadState);\n            this.addIfPresent(context, 'largest_shift_target', largestShiftTarget);\n            this.pushMeasurement(values, context);\n        }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n    }\n    measureFCP() {\n        var _a;\n        onFCP((metric) => {\n            const { firstByteToFCP, timeToFirstByte, loadState } = metric.attribution;\n            const values = this.buildInitialValues(metric);\n            this.addIfPresent(values, 'first_byte_to_fcp', firstByteToFCP);\n            this.addIfPresent(values, timeToFirstByteKey, timeToFirstByte);\n            const context = this.buildInitialContext(metric);\n            this.addIfPresent(context, loadStateKey, loadState);\n            this.pushMeasurement(values, context);\n        }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n    }\n    measureFID() {\n        var _a;\n        onFID((metric) => {\n            const { eventTime, eventTarget, eventType, loadState } = metric.attribution;\n            const values = this.buildInitialValues(metric);\n            this.addIfPresent(values, 'event_time', eventTime);\n            const context = this.buildInitialContext(metric);\n            this.addIfPresent(context, 'event_target', eventTarget);\n            this.addIfPresent(context, 'event_type', eventType);\n            this.addIfPresent(context, loadStateKey, loadState);\n            this.pushMeasurement(values, context);\n        }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n    }\n    measureINP() {\n        var _a;\n        onINP((metric) => {\n            const { interactionTime, presentationDelay, inputDelay, processingDuration, nextPaintTime, loadState, interactionTarget, interactionType, } = metric.attribution;\n            const values = this.buildInitialValues(metric);\n            this.addIfPresent(values, 'interaction_time', interactionTime);\n            this.addIfPresent(values, 'presentation_delay', presentationDelay);\n            this.addIfPresent(values, 'input_delay', inputDelay);\n            this.addIfPresent(values, 'processing_duration', processingDuration);\n            this.addIfPresent(values, 'next_paint_time', nextPaintTime);\n            const context = this.buildInitialContext(metric);\n            this.addIfPresent(context, loadStateKey, loadState);\n            this.addIfPresent(context, 'interaction_target', interactionTarget);\n            this.addIfPresent(context, 'interaction_type', interactionType);\n            this.pushMeasurement(values, context);\n        }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n    }\n    measureLCP() {\n        var _a;\n        onLCP((metric) => {\n            const { elementRenderDelay, resourceLoadDelay, resourceLoadDuration, timeToFirstByte, element } = metric.attribution;\n            const values = this.buildInitialValues(metric);\n            this.addIfPresent(values, 'element_render_delay', elementRenderDelay);\n            this.addIfPresent(values, 'resource_load_delay', resourceLoadDelay);\n            this.addIfPresent(values, 'resource_load_duration', resourceLoadDuration);\n            this.addIfPresent(values, timeToFirstByteKey, timeToFirstByte);\n            const context = this.buildInitialContext(metric);\n            this.addIfPresent(context, 'element', element);\n            this.pushMeasurement(values, context);\n        }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n    }\n    measureTTFB() {\n        var _a;\n        onTTFB((metric) => {\n            const { dnsDuration, connectionDuration, requestDuration, waitingDuration, cacheDuration } = metric.attribution;\n            const values = this.buildInitialValues(metric);\n            this.addIfPresent(values, 'dns_duration', dnsDuration);\n            this.addIfPresent(values, 'connection_duration', connectionDuration);\n            this.addIfPresent(values, 'request_duration', requestDuration);\n            this.addIfPresent(values, 'waiting_duration', waitingDuration);\n            this.addIfPresent(values, 'cache_duration', cacheDuration);\n            const context = this.buildInitialContext(metric);\n            this.pushMeasurement(values, context);\n        }, { reportAllChanges: (_a = this.webVitalConfig) === null || _a === void 0 ? void 0 : _a.reportAllChanges });\n    }\n    buildInitialValues(metric) {\n        const indicator = metric.name.toLowerCase();\n        return {\n            [indicator]: metric.value,\n            delta: metric.delta,\n        };\n    }\n    buildInitialContext(metric) {\n        var _a;\n        const navigationEntryId = (_a = getItem(NAVIGATION_ID_STORAGE_KEY, webStorageType.session)) !== null && _a !== void 0 ? _a : unknownString;\n        return {\n            id: metric.id,\n            rating: metric.rating,\n            navigation_type: metric.navigationType,\n            navigation_entry_id: navigationEntryId,\n        };\n    }\n    pushMeasurement(values, context) {\n        const type = 'web-vitals';\n        this.corePushMeasurement({ type, values }, { context });\n    }\n    addIfPresent(source, key, metric) {\n        if (metric) {\n            source[key] = metric;\n        }\n    }\n}\n//# sourceMappingURL=webVitalsWithAttribution.js.map", "import { BaseInstrumentation, VERSION } from '@grafana/faro-core';\nimport { WebVitalsBasic } from './webVitalsBasic';\nimport { WebVitalsWithAttribution } from './webVitalsWithAttribution';\nexport class WebVitalsInstrumentation extends BaseInstrumentation {\n    constructor() {\n        super(...arguments);\n        this.name = '@grafana/faro-web-sdk:instrumentation-web-vitals';\n        this.version = VERSION;\n    }\n    initialize() {\n        this.logDebug('Initializing');\n        const webVitals = this.intializeWebVitalsInstrumentation();\n        webVitals.initialize();\n    }\n    intializeWebVitalsInstrumentation() {\n        var _a;\n        if ((_a = this.config) === null || _a === void 0 ? void 0 : _a.trackWebVitalsAttribution) {\n            return new WebVitalsWithAttribution(this.api.pushMeasurement, this.config.webVitalsInstrumentation);\n        }\n        return new WebVitalsBasic(this.api.pushMeasurement, this.config.webVitalsInstrumentation);\n    }\n}\n//# sourceMappingURL=instrumentation.js.map", "import { BaseInstrumentation, dateNow, EVENT_SESSION_EXTEND, EVENT_SESSION_RESUME, EVENT_SESSION_START, VERSION, } from '@grafana/faro-core';\nimport { createSession } from '../../metas';\nimport { getSessionManagerByConfig, isSampled } from './sessionManager';\nimport { PersistentSessionsManager } from './sessionManager/PersistentSessionsManager';\nimport { createUserSessionObject, isUserSessionValid } from './sessionManager/sessionManagerUtils';\nexport class SessionInstrumentation extends BaseInstrumentation {\n    constructor() {\n        super(...arguments);\n        this.name = '@grafana/faro-web-sdk:instrumentation-session';\n        this.version = VERSION;\n    }\n    sendSessionStartEvent(meta) {\n        var _a, _b;\n        const session = meta.session;\n        if (session && session.id !== ((_a = this.notifiedSession) === null || _a === void 0 ? void 0 : _a.id)) {\n            if (this.notifiedSession && this.notifiedSession.id === ((_b = session.attributes) === null || _b === void 0 ? void 0 : _b['previousSession'])) {\n                this.api.pushEvent(EVENT_SESSION_EXTEND, {}, undefined, { skipDedupe: true });\n                this.notifiedSession = session;\n                return;\n            }\n            this.notifiedSession = session;\n            // no need to add attributes and session id, they are included as part of meta\n            // automatically\n            this.api.pushEvent(EVENT_SESSION_START, {}, undefined, { skipDedupe: true });\n        }\n    }\n    createInitialSession(SessionManager, sessionsConfig) {\n        var _a, _b, _c, _d, _e, _f;\n        let storedUserSession = SessionManager.fetchUserSession();\n        if (sessionsConfig.persistent && sessionsConfig.maxSessionPersistenceTime && storedUserSession) {\n            const now = dateNow();\n            const shouldClearPersistentSession = storedUserSession.lastActivity < now - sessionsConfig.maxSessionPersistenceTime;\n            if (shouldClearPersistentSession) {\n                PersistentSessionsManager.removeUserSession();\n                storedUserSession = null;\n            }\n        }\n        let lifecycleType;\n        let initialSession;\n        if (isUserSessionValid(storedUserSession)) {\n            const sessionId = storedUserSession === null || storedUserSession === void 0 ? void 0 : storedUserSession.sessionId;\n            initialSession = createUserSessionObject({\n                sessionId,\n                isSampled: storedUserSession.isSampled || false,\n                started: storedUserSession === null || storedUserSession === void 0 ? void 0 : storedUserSession.started,\n            });\n            const storedUserSessionMeta = storedUserSession === null || storedUserSession === void 0 ? void 0 : storedUserSession.sessionMeta;\n            // For resumed sessions we want to merge the previous overrides with the configured ones.\n            // If the same key is present in both, the new one will override the old one.\n            const overrides = Object.assign(Object.assign({}, (_a = sessionsConfig.session) === null || _a === void 0 ? void 0 : _a.overrides), storedUserSessionMeta === null || storedUserSessionMeta === void 0 ? void 0 : storedUserSessionMeta.overrides);\n            initialSession.sessionMeta = Object.assign(Object.assign({}, sessionsConfig.session), { id: sessionId, attributes: Object.assign(Object.assign(Object.assign({}, (_b = sessionsConfig.session) === null || _b === void 0 ? void 0 : _b.attributes), storedUserSessionMeta === null || storedUserSessionMeta === void 0 ? void 0 : storedUserSessionMeta.attributes), { \n                    // For valid resumed sessions we do not want to recalculate the sampling decision on each init phase.\n                    isSampled: initialSession.isSampled.toString() }), overrides });\n            lifecycleType = EVENT_SESSION_RESUME;\n        }\n        else {\n            const sessionId = (_d = (_c = sessionsConfig.session) === null || _c === void 0 ? void 0 : _c.id) !== null && _d !== void 0 ? _d : createSession().id;\n            initialSession = createUserSessionObject({\n                sessionId,\n                isSampled: isSampled(),\n            });\n            const overrides = (_e = sessionsConfig.session) === null || _e === void 0 ? void 0 : _e.overrides;\n            initialSession.sessionMeta = Object.assign({ id: sessionId, attributes: Object.assign({ isSampled: initialSession.isSampled.toString() }, (_f = sessionsConfig.session) === null || _f === void 0 ? void 0 : _f.attributes) }, (overrides ? { overrides } : {}));\n            lifecycleType = EVENT_SESSION_START;\n        }\n        return { initialSession, lifecycleType };\n    }\n    registerBeforeSendHook(SessionManager) {\n        var _a;\n        const { updateSession } = new SessionManager();\n        (_a = this.transports) === null || _a === void 0 ? void 0 : _a.addBeforeSendHooks((item) => {\n            var _a, _b, _c;\n            updateSession();\n            const attributes = (_a = item.meta.session) === null || _a === void 0 ? void 0 : _a.attributes;\n            if (attributes && (attributes === null || attributes === void 0 ? void 0 : attributes['isSampled']) === 'true') {\n                let newItem = JSON.parse(JSON.stringify(item));\n                const newAttributes = (_b = newItem.meta.session) === null || _b === void 0 ? void 0 : _b.attributes;\n                newAttributes === null || newAttributes === void 0 ? true : delete newAttributes['isSampled'];\n                if (Object.keys(newAttributes !== null && newAttributes !== void 0 ? newAttributes : {}).length === 0) {\n                    (_c = newItem.meta.session) === null || _c === void 0 ? true : delete _c.attributes;\n                }\n                return newItem;\n            }\n            return null;\n        });\n    }\n    initialize() {\n        this.logDebug('init session instrumentation');\n        const sessionTrackingConfig = this.config.sessionTracking;\n        if (sessionTrackingConfig === null || sessionTrackingConfig === void 0 ? void 0 : sessionTrackingConfig.enabled) {\n            const SessionManager = getSessionManagerByConfig(sessionTrackingConfig);\n            this.registerBeforeSendHook(SessionManager);\n            const { initialSession, lifecycleType } = this.createInitialSession(SessionManager, sessionTrackingConfig);\n            SessionManager.storeUserSession(initialSession);\n            const initialSessionMeta = initialSession.sessionMeta;\n            this.notifiedSession = initialSessionMeta;\n            this.api.setSession(initialSessionMeta);\n            if (lifecycleType === EVENT_SESSION_START) {\n                this.api.pushEvent(EVENT_SESSION_START, {}, undefined, { skipDedupe: true });\n            }\n            if (lifecycleType === EVENT_SESSION_RESUME) {\n                this.api.pushEvent(EVENT_SESSION_RESUME, {}, undefined, { skipDedupe: true });\n            }\n        }\n        this.metas.addListener(this.sendSessionStartEvent.bind(this));\n    }\n}\n//# sourceMappingURL=instrumentation.js.map", "import { faro, genShortID } from '@grafana/faro-core';\nexport function createSession(attributes) {\n    var _a, _b, _c, _d;\n    return {\n        id: (_d = (_c = (_b = (_a = faro.config) === null || _a === void 0 ? void 0 : _a.sessionTracking) === null || _b === void 0 ? void 0 : _b.generateSessionId) === null || _c === void 0 ? void 0 : _c.call(_b)) !== null && _d !== void 0 ? _d : genShortID(),\n        attributes,\n    };\n}\n//# sourceMappingURL=createSession.js.map", "import { BaseInstrumentation, EVENT_VIEW_CHANGED, unknownString, VERSION } from '@grafana/faro-core';\n// all this does is send VIEW_CHANGED event\nexport class ViewInstrumentation extends BaseInstrumentation {\n    constructor() {\n        super(...arguments);\n        this.name = '@grafana/faro-web-sdk:instrumentation-view';\n        this.version = VERSION;\n    }\n    sendViewChangedEvent(meta) {\n        var _a, _b, _c, _d;\n        const view = meta.view;\n        if (view && view.name !== ((_a = this.notifiedView) === null || _a === void 0 ? void 0 : _a.name)) {\n            this.api.pushEvent(EVENT_VIEW_CHANGED, {\n                fromView: (_c = (_b = this.notifiedView) === null || _b === void 0 ? void 0 : _b.name) !== null && _c !== void 0 ? _c : unknownString,\n                toView: (_d = view.name) !== null && _d !== void 0 ? _d : unknownString,\n            }, undefined, { skipDedupe: true });\n            this.notifiedView = view;\n        }\n    }\n    initialize() {\n        this.metas.addListener(this.sendViewChangedEvent.bind(this));\n    }\n}\n//# sourceMappingURL=instrumentation.js.map", "export const NAVIGATION_ENTRY = 'navigation';\nexport const RESOURCE_ENTRY = 'resource';\n//# sourceMappingURL=performanceConstants.js.map", "import { isArray, unknownString } from '@grafana/faro-core';\nconst w3cTraceparentFormat = /^00-[a-f0-9]{32}-[a-f0-9]{16}-[0-9]{1,2}$/;\n// Extract traceparent from serverTiming, if present\nexport function getSpanContextFromServerTiming(serverTimings = []) {\n    for (const serverEntry of serverTimings) {\n        if (serverEntry.name === 'traceparent') {\n            if (!w3cTraceparentFormat.test(serverEntry.description)) {\n                continue;\n            }\n            const [, traceId, spanId] = serverEntry.description.split('-');\n            if (traceId != null && spanId != null) {\n                return { traceId, spanId };\n            }\n            break;\n        }\n    }\n    return undefined;\n}\nexport function performanceObserverSupported() {\n    return 'PerformanceObserver' in window;\n}\nexport function entryUrlIsIgnored(ignoredUrls = [], entryName) {\n    return ignoredUrls.some((url) => url && entryName.match(url) != null);\n}\nexport function onDocumentReady(handleReady) {\n    if (document.readyState === 'complete') {\n        handleReady();\n    }\n    else {\n        const readyStateCompleteHandler = () => {\n            if (document.readyState === 'complete') {\n                handleReady();\n                document.removeEventListener('readystatechange', readyStateCompleteHandler);\n            }\n        };\n        document.addEventListener('readystatechange', readyStateCompleteHandler);\n    }\n}\nexport function includePerformanceEntry(performanceEntryJSON, allowProps = {}) {\n    for (const [allowPropKey, allowPropValue] of Object.entries(allowProps)) {\n        const perfEntryPropVal = performanceEntryJSON[allowPropKey];\n        if (perfEntryPropVal == null) {\n            return false;\n        }\n        if (isArray(allowPropValue)) {\n            return allowPropValue.includes(perfEntryPropVal);\n        }\n        return perfEntryPropVal === allowPropValue;\n    }\n    // empty object allows all\n    return true;\n}\nexport function createFaroResourceTiming(resourceEntryRaw) {\n    const { connectEnd, connectStart, decodedBodySize, domainLookupEnd, domainLookupStart, duration, encodedBodySize, fetchStart, initiatorType, name, nextHopProtocol, redirectEnd, redirectStart, \n    // @ts-expect-error the renderBlockingStatus property is not available in all browsers\n    renderBlockingStatus: rbs, requestStart, responseEnd, responseStart, \n    // @ts-expect-error the renderBlockingStatus property is not available in all browsers\n    responseStatus, secureConnectionStart, transferSize, workerStart, } = resourceEntryRaw;\n    return {\n        name: name,\n        duration: toFaroPerformanceTimingString(duration),\n        tcpHandshakeTime: toFaroPerformanceTimingString(connectEnd - connectStart),\n        dnsLookupTime: toFaroPerformanceTimingString(domainLookupEnd - domainLookupStart),\n        tlsNegotiationTime: toFaroPerformanceTimingString(requestStart - secureConnectionStart),\n        responseStatus: toFaroPerformanceTimingString(responseStatus),\n        redirectTime: toFaroPerformanceTimingString(redirectEnd - redirectStart),\n        requestTime: toFaroPerformanceTimingString(responseStart - requestStart),\n        responseTime: toFaroPerformanceTimingString(responseEnd - responseStart),\n        fetchTime: toFaroPerformanceTimingString(responseEnd - fetchStart),\n        serviceWorkerTime: toFaroPerformanceTimingString(fetchStart - workerStart),\n        decodedBodySize: toFaroPerformanceTimingString(decodedBodySize),\n        encodedBodySize: toFaroPerformanceTimingString(encodedBodySize),\n        cacheHitStatus: getCacheType(),\n        renderBlockingStatus: toFaroPerformanceTimingString(rbs),\n        protocol: nextHopProtocol,\n        initiatorType: initiatorType,\n        visibilityState: document.visibilityState,\n        ttfb: toFaroPerformanceTimingString(responseStart - requestStart),\n        // TODO: add in future iteration, ideally after nested objects are supported by the collector.\n        // serverTiming: resourceEntryRaw.serverTiming,\n    };\n    function getCacheType() {\n        let cacheType = 'fullLoad';\n        if (transferSize === 0) {\n            if (decodedBodySize > 0) {\n                cacheType = 'cache';\n            }\n        }\n        else {\n            if (responseStatus != null) {\n                if (responseStatus === 304) {\n                    cacheType = 'conditionalFetch';\n                }\n            }\n            else if (encodedBodySize > 0 && transferSize < encodedBodySize) {\n                cacheType = 'conditionalFetch';\n            }\n        }\n        return cacheType;\n    }\n}\nexport function createFaroNavigationTiming(navigationEntryRaw) {\n    const { activationStart, domComplete, domContentLoadedEventEnd, domContentLoadedEventStart, domInteractive, fetchStart, loadEventEnd, loadEventStart, responseStart, type, } = navigationEntryRaw;\n    const parserStart = getDocumentParsingTime();\n    return Object.assign(Object.assign({}, createFaroResourceTiming(navigationEntryRaw)), { pageLoadTime: toFaroPerformanceTimingString(domComplete - fetchStart), documentParsingTime: toFaroPerformanceTimingString(parserStart ? domInteractive - parserStart : null), domProcessingTime: toFaroPerformanceTimingString(domComplete - domInteractive), domContentLoadHandlerTime: toFaroPerformanceTimingString(domContentLoadedEventEnd - domContentLoadedEventStart), onLoadTime: toFaroPerformanceTimingString(loadEventEnd - loadEventStart), \n        // For navigation entries we can calculate the TTFB based on activationStart. We overwrite the TTFB value coming with the resource entry.\n        // For more accuracy on prerendered pages page we calculate relative top the activationStart instead of the start of the navigation.\n        // clamp to 0 if activationStart occurs after first byte is received.\n        ttfb: toFaroPerformanceTimingString(Math.max(responseStart - (activationStart !== null && activationStart !== void 0 ? activationStart : 0), 0)), type: type });\n}\nfunction getDocumentParsingTime() {\n    var _a;\n    if (((_a = performance.timing) === null || _a === void 0 ? void 0 : _a.domLoading) != null) {\n        // the browser is about to start parsing the first received bytes of the HTML document.\n        // This property is deprecated but there isn't a really good alternative atm.\n        // For now we stick with domLoading and keep researching a better alternative.\n        return performance.timing.domLoading - performance.timeOrigin;\n    }\n    return null;\n}\nfunction toFaroPerformanceTimingString(v) {\n    if (v == null) {\n        return unknownString;\n    }\n    if (typeof v === 'number') {\n        return Math.round(v).toString();\n    }\n    return v.toString();\n}\n//# sourceMappingURL=performanceUtils.js.map", "import { faro, genShortID } from '@grafana/faro-core';\nimport { RESOURCE_ENTRY } from './performanceConstants';\nimport { createFaroResourceTiming, entryUrlIsIgnored, getSpanContextFromServerTiming, includePerformanceEntry, } from './performanceUtils';\nconst DEFAULT_TRACK_RESOURCES = { initiatorType: ['xmlhttprequest', 'fetch'] };\nexport function observeResourceTimings(faroNavigationId, pushEvent, ignoredUrls) {\n    const trackResources = faro.config.trackResources;\n    const observer = new PerformanceObserver((observedEntries) => {\n        const entries = observedEntries.getEntries();\n        for (const resourceEntryRaw of entries) {\n            if (entryUrlIsIgnored(ignoredUrls, resourceEntryRaw.name)) {\n                return;\n            }\n            const resourceEntryJson = resourceEntryRaw.toJSON();\n            let spanContext = getSpanContextFromServerTiming(resourceEntryJson === null || resourceEntryJson === void 0 ? void 0 : resourceEntryJson.serverTiming);\n            if ((trackResources == null && includePerformanceEntry(resourceEntryJson, DEFAULT_TRACK_RESOURCES)) ||\n                trackResources) {\n                const faroResourceEntry = Object.assign(Object.assign({}, createFaroResourceTiming(resourceEntryJson)), { faroNavigationId, faroResourceId: genShortID() });\n                pushEvent('faro.performance.resource', faroResourceEntry, undefined, {\n                    spanContext,\n                    timestampOverwriteMs: performance.timeOrigin + resourceEntryJson.startTime,\n                });\n            }\n        }\n    });\n    observer.observe({\n        type: RESOURCE_ENTRY,\n        buffered: true,\n    });\n}\n//# sourceMappingURL=resource.js.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { BaseInstrumentation, VERSION } from '@grafana/faro-core';\nimport { getNavigationTimings } from './navigation';\nimport { onDocumentReady, performanceObserverSupported } from './performanceUtils';\nimport { observeResourceTimings } from './resource';\nexport class PerformanceInstrumentation extends BaseInstrumentation {\n    constructor() {\n        super(...arguments);\n        this.name = '@grafana/faro-web-sdk:instrumentation-performance';\n        this.version = VERSION;\n    }\n    initialize() {\n        if (!performanceObserverSupported()) {\n            this.logDebug('performance observer not supported. Disable performance instrumentation.');\n            return;\n        }\n        onDocumentReady(() => __awaiter(this, void 0, void 0, function* () {\n            const pushEvent = this.api.pushEvent;\n            const ignoredUrls = this.getIgnoreUrls();\n            const { faroNavigationId } = yield getNavigationTimings(pushEvent, ignoredUrls);\n            if (faroNavigationId != null) {\n                observeResourceTimings(faroNavigationId, pushEvent, ignoredUrls);\n            }\n        }));\n    }\n    getIgnoreUrls() {\n        var _a;\n        return (_a = this.transports.transports) === null || _a === void 0 ? void 0 : _a.flatMap((transport) => transport.getIgnoreUrls());\n    }\n}\n//# sourceMappingURL=instrumentation.js.map", "import { genShortID, unknownString } from '@grafana/faro-core';\nimport { getItem, setItem, webStorageType } from '../../utils';\nimport { NAVIGATION_ID_STORAGE_KEY } from '../instrumentationConstants';\nimport { NAVIGATION_ENTRY } from './performanceConstants';\nimport { createFaroNavigationTiming, entryUrlIsIgnored, getSpanContextFromServerTiming } from './performanceUtils';\nexport function getNavigationTimings(pushEvent, ignoredUrls) {\n    let faroNavigationEntryResolve;\n    const faroNavigationEntryPromise = new Promise((resolve) => {\n        faroNavigationEntryResolve = resolve;\n    });\n    const observer = new PerformanceObserver((observedEntries) => {\n        var _a;\n        const [navigationEntryRaw] = observedEntries.getEntries();\n        if (navigationEntryRaw == null || entryUrlIsIgnored(ignoredUrls, navigationEntryRaw.name)) {\n            return;\n        }\n        const navEntryJson = navigationEntryRaw.toJSON();\n        let spanContext = getSpanContextFromServerTiming(navEntryJson === null || navEntryJson === void 0 ? void 0 : navEntryJson.serverTiming);\n        const faroPreviousNavigationId = (_a = getItem(NAVIGATION_ID_STORAGE_KEY, webStorageType.session)) !== null && _a !== void 0 ? _a : unknownString;\n        const faroNavigationEntry = Object.assign(Object.assign({}, createFaroNavigationTiming(navEntryJson)), { faroNavigationId: genShortID(), faroPreviousNavigationId });\n        setItem(NAVIGATION_ID_STORAGE_KEY, faroNavigationEntry.faroNavigationId, webStorageType.session);\n        pushEvent('faro.performance.navigation', faroNavigationEntry, undefined, {\n            spanContext,\n            timestampOverwriteMs: performance.timeOrigin + navEntryJson.startTime,\n        });\n        faroNavigationEntryResolve(faroNavigationEntry);\n    });\n    observer.observe({\n        type: NAVIGATION_ENTRY,\n        buffered: true,\n    });\n    return faroNavigationEntryPromise;\n}\n//# sourceMappingURL=navigation.js.map", "import { allLogLevels, BaseInstrumentation, defaultErrorArgsSerializer, defaultLogArgsSerializer, LogLevel, VERSION, } from '@grafana/faro-core';\nimport { getDetailsFromConsoleErrorArgs } from '../errors/getErrorDetails';\nexport class ConsoleInstrumentation extends BaseInstrumentation {\n    constructor(options = {}) {\n        super();\n        this.options = options;\n        this.name = '@grafana/faro-web-sdk:instrumentation-console';\n        this.version = VERSION;\n        this.errorSerializer = defaultLogArgsSerializer;\n    }\n    initialize() {\n        var _a, _b, _c, _d;\n        this.options = Object.assign(Object.assign({}, this.options), this.config.consoleInstrumentation);\n        const serializeErrors = ((_a = this.options) === null || _a === void 0 ? void 0 : _a.serializeErrors) || !!((_b = this.options) === null || _b === void 0 ? void 0 : _b.errorSerializer);\n        this.errorSerializer = serializeErrors\n            ? ((_d = (_c = this.options) === null || _c === void 0 ? void 0 : _c.errorSerializer) !== null && _d !== void 0 ? _d : defaultErrorArgsSerializer)\n            : defaultLogArgsSerializer;\n        allLogLevels\n            .filter((level) => { var _a, _b; return !((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.disabledLevels) !== null && _b !== void 0 ? _b : ConsoleInstrumentation.defaultDisabledLevels).includes(level); })\n            .forEach((level) => {\n            /* eslint-disable-next-line no-console */\n            console[level] = (...args) => {\n                var _a, _b;\n                try {\n                    if (level === LogLevel.ERROR && !((_a = this.options) === null || _a === void 0 ? void 0 : _a.consoleErrorAsLog)) {\n                        const { value, type, stackFrames } = getDetailsFromConsoleErrorArgs(args, this.errorSerializer);\n                        if (value && !type && !stackFrames) {\n                            this.api.pushError(new Error(ConsoleInstrumentation.consoleErrorPrefix + value));\n                            return;\n                        }\n                        this.api.pushError(new Error(ConsoleInstrumentation.consoleErrorPrefix + value), { type, stackFrames });\n                    }\n                    else if (level === LogLevel.ERROR && ((_b = this.options) === null || _b === void 0 ? void 0 : _b.consoleErrorAsLog)) {\n                        const { value, type, stackFrames } = getDetailsFromConsoleErrorArgs(args, this.errorSerializer);\n                        this.api.pushLog(value ? [ConsoleInstrumentation.consoleErrorPrefix + value] : args, {\n                            level,\n                            context: {\n                                value: value !== null && value !== void 0 ? value : '',\n                                type: type !== null && type !== void 0 ? type : '',\n                                stackFrames: (stackFrames === null || stackFrames === void 0 ? void 0 : stackFrames.length) ? defaultErrorArgsSerializer(stackFrames) : '',\n                            },\n                        });\n                    }\n                    else {\n                        this.api.pushLog(args, { level });\n                    }\n                }\n                catch (err) {\n                    this.logError(err);\n                }\n                finally {\n                    this.unpatchedConsole[level](...args);\n                }\n            };\n        });\n    }\n}\nConsoleInstrumentation.defaultDisabledLevels = [LogLevel.DEBUG, LogLevel.TRACE, LogLevel.LOG];\nConsoleInstrumentation.consoleErrorPrefix = 'console.error: ';\n//# sourceMappingURL=instrumentation.js.map", "import { ConsoleInstrumentation, ErrorsInstrumentation, PerformanceInstrumentation, SessionInstrumentation, ViewInstrumentation, WebVitalsInstrumentation, } from '../instrumentations';\nexport function getWebInstrumentations(options = {}) {\n    const instrumentations = [\n        new ErrorsInstrumentation(),\n        new WebVitalsInstrumentation(),\n        new SessionInstrumentation(),\n        new ViewInstrumentation(),\n    ];\n    if (options.enablePerformanceInstrumentation !== false) {\n        // unshift to ensure that initialization starts before the other instrumentations\n        instrumentations.unshift(new PerformanceInstrumentation());\n    }\n    if (options.captureConsole !== false) {\n        instrumentations.push(new ConsoleInstrumentation({\n            disabledLevels: options.captureConsoleDisabledLevels,\n        }));\n    }\n    return instrumentations;\n}\n//# sourceMappingURL=getWebInstrumentations.js.map", "import { createInternalLogger, defaultBatchingConfig, defaultGlobalObjectKey, defaultInternalLoggerLevel, defaultLogArgsSerializer, defaultUnpatchedConsole, isEmpty, isObject, } from '@grafana/faro-core';\nimport { defaultEventDomain } from '../consts';\nimport { parseStacktrace } from '../instrumentations';\nimport { defaultSessionTrackingConfig } from '../instrumentations/session';\nimport { browserMeta } from '../metas';\nimport { k6Meta } from '../metas/k6';\nimport { createPageMeta } from '../metas/page';\nimport { FetchTransport } from '../transports';\nimport { getWebInstrumentations } from './getWebInstrumentations';\nexport function makeCoreConfig(browserConfig) {\n    var _a;\n    const transports = [];\n    const internalLogger = createInternalLogger(browserConfig.unpatchedConsole, browserConfig.internalLoggerLevel);\n    if (browserConfig.transports) {\n        if (browserConfig.url || browserConfig.apiKey) {\n            internalLogger.error('if \"transports\" is defined, \"url\" and \"apiKey\" should not be defined');\n        }\n        transports.push(...browserConfig.transports);\n    }\n    else if (browserConfig.url) {\n        transports.push(new FetchTransport({\n            url: browserConfig.url,\n            apiKey: browserConfig.apiKey,\n        }));\n    }\n    else {\n        internalLogger.error('either \"url\" or \"transports\" must be defined');\n    }\n    const { app, batching, beforeSend, consoleInstrumentation, ignoreErrors, sessionTracking, trackResources, trackWebVitalsAttribution, user, view, geoLocationTracking, \n    // properties with default values\n    dedupe = true, eventDomain = defaultEventDomain, globalObjectKey = defaultGlobalObjectKey, instrumentations = getWebInstrumentations(), internalLoggerLevel = defaultInternalLoggerLevel, isolate = false, logArgsSerializer = defaultLogArgsSerializer, metas = createDefaultMetas(browserConfig), paused = false, preventGlobalExposure = false, unpatchedConsole = defaultUnpatchedConsole, webVitalsInstrumentation, } = browserConfig;\n    return {\n        app,\n        batching: Object.assign(Object.assign({}, defaultBatchingConfig), batching),\n        dedupe: dedupe,\n        globalObjectKey,\n        instrumentations,\n        internalLoggerLevel,\n        isolate,\n        logArgsSerializer,\n        metas,\n        parseStacktrace,\n        paused,\n        preventGlobalExposure,\n        transports,\n        unpatchedConsole,\n        beforeSend,\n        eventDomain,\n        ignoreErrors,\n        // ignore cloud collector urls by default. These are URLs ending with /collect or /collect/ followed by alphanumeric characters.\n        ignoreUrls: ((_a = browserConfig.ignoreUrls) !== null && _a !== void 0 ? _a : []).concat([/\\/collect(?:\\/[\\w]*)?$/]),\n        sessionTracking: Object.assign(Object.assign(Object.assign({}, defaultSessionTrackingConfig), sessionTracking), crateSessionMeta({ geoLocationTracking, sessionTracking })),\n        user,\n        view,\n        trackResources,\n        trackWebVitalsAttribution,\n        consoleInstrumentation,\n        webVitalsInstrumentation,\n    };\n}\nfunction createDefaultMetas(browserConfig) {\n    var _a, _b;\n    const { page, generatePageId } = (_a = browserConfig === null || browserConfig === void 0 ? void 0 : browserConfig.pageTracking) !== null && _a !== void 0 ? _a : {};\n    const initialMetas = [\n        browserMeta,\n        createPageMeta({ generatePageId, initialPageMeta: page }),\n        ...((_b = browserConfig.metas) !== null && _b !== void 0 ? _b : []),\n    ];\n    const isK6BrowserSession = isObject(window.k6);\n    if (isK6BrowserSession) {\n        return [...initialMetas, k6Meta];\n    }\n    return initialMetas;\n}\nfunction crateSessionMeta({ geoLocationTracking, sessionTracking, }) {\n    var _a;\n    const overrides = {};\n    if ((geoLocationTracking === null || geoLocationTracking === void 0 ? void 0 : geoLocationTracking.enabled) != null) {\n        overrides.geoLocationTrackingEnabled = geoLocationTracking.enabled;\n    }\n    if (isEmpty(overrides)) {\n        return {};\n    }\n    return {\n        session: Object.assign(Object.assign({}, ((_a = sessionTracking === null || sessionTracking === void 0 ? void 0 : sessionTracking.session) !== null && _a !== void 0 ? _a : {})), { overrides }),\n    };\n}\n//# sourceMappingURL=makeCoreConfig.js.map", "import { initializeFaro as coreInit } from '@grafana/faro-core';\nimport { makeCoreConfig } from './config';\nexport function initializeFaro(config) {\n    const coreConfig = makeCoreConfig(config);\n    if (!coreConfig) {\n        return undefined;\n    }\n    return coreInit(coreConfig);\n}\n//# sourceMappingURL=initialize.js.map", "import { type Environment } from '../getEnvironment';\nimport { type FaroEnvironment } from './getFaroEnvironment';\n\nexport const FARO_ENVIRONMENTS = new Map<Environment, FaroEnvironment>([\n  // Uncomment this map entry to test from your local machine\n  // [\n  //   'local',\n  //   {\n  //     environment: 'local',\n  //     appName: 'grafana-metricsdrilldown-app-local',\n  //     faroUrl: 'https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/b854cd2319527968f415fd44ea01fe8a',\n  //   },\n  // ],\n  // Always keep the options below\n  [\n    'dev',\n    {\n      environment: 'dev',\n      appName: 'grafana-metricsdrilldown-app-dev',\n      faroUrl: 'https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/8c57b32175ba39d35dfaccee7cd793c7',\n    },\n  ],\n  [\n    'ops',\n    {\n      environment: 'ops',\n      appName: 'grafana-metricsdrilldown-app-ops',\n      faroUrl: 'https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/d65ab91eb9c5e8c51b474d9313ba28f4',\n    },\n  ],\n  [\n    'prod',\n    {\n      environment: 'prod',\n      appName: 'grafana-metricsdrilldown-app-prod',\n      faroUrl: 'https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/0f4f1bbc97c9e2db4fa85ef75a559885',\n    },\n  ],\n]);\n", "import { getWebInstrumentations, initializeFaro, type Faro } from '@grafana/faro-web-sdk';\nimport { config } from '@grafana/runtime';\n\nimport { getFaroEnvironment } from './getFaroEnvironment';\nimport { PLUGIN_BASE_URL, PLUGIN_ID } from '../../constants';\nimport { GIT_COMMIT } from '../../version';\n\nlet faro: Faro | null = null;\n\nexport const getFaro = () => faro;\nexport const setFaro = (instance: Faro | null) => (faro = instance);\n\nexport function initFaro() {\n  if (getFaro()) {\n    return;\n  }\n\n  const faroEnvironment = getFaroEnvironment();\n  if (!faroEnvironment) {\n    return;\n  }\n\n  const { environment, faroUrl, appName } = faroEnvironment;\n  const { apps, bootData } = config;\n  const appRelease = apps[PLUGIN_ID].version;\n  const userEmail = bootData.user.email;\n\n  setFaro(\n    initializeFaro({\n      url: faroUrl,\n      app: {\n        name: appName,\n        release: appRelease,\n        version: GIT_COMMIT,\n        environment,\n      },\n      user: {\n        email: userEmail,\n      },\n      instrumentations: [\n        ...getWebInstrumentations({\n          captureConsole: false,\n        }),\n      ],\n      isolate: true,\n      beforeSend: (event) => {\n        if ((event.meta.page?.url ?? '').includes(PLUGIN_BASE_URL)) {\n          return event;\n        }\n\n        return null;\n      },\n    })\n  );\n}\n", "import { getEnvironment, type Environment } from '../getEnvironment';\nimport { FARO_ENVIRONMENTS } from './faro-environments';\n\nexport type FaroEnvironment = { environment: Environment; appName: string; faroUrl: string };\n\nexport function getFaroEnvironment() {\n  const environment = getEnvironment();\n\n  if (!environment || !FARO_ENVIRONMENTS.has(environment)) {\n    return;\n  }\n\n  return FARO_ENVIRONMENTS.get(environment) as FaroEnvironment;\n}\n", "export type Environment = 'local' | 'dev' | 'ops' | 'prod';\n\nconst MATCHERS: Array<{ regExp: RegExp; environment: Environment }> = [\n  {\n    regExp: /localhost/,\n    environment: 'local',\n  },\n  {\n    regExp: /grafana-dev\\.net/,\n    environment: 'dev',\n  },\n  {\n    regExp: /grafana-ops\\.net/,\n    environment: 'ops',\n  },\n  {\n    regExp: /grafana\\.net/,\n    environment: 'prod',\n  },\n];\n\nexport function getEnvironment(): Environment | null {\n  if (!window?.location?.host) {\n    return null;\n  }\n\n  const found = MATCHERS.find(({ regExp }) => regExp.test(window.location.host));\n\n  return found ? found.environment : null;\n}\n", "import { LogLevel } from '@grafana/faro-web-sdk';\n\nimport { getFaro } from '../faro/faro';\nimport { getEnvironment, type Environment } from '../getEnvironment';\n\nexport type ErrorContext = Record<string, string>;\n\n/**\n * Logger class that handles logging to both console and Grafana Faro.\n *\n * This class provides a unified logging interface that:\n * - Logs to console in non-production environments\n * - Sends logs to Faro for remote monitoring and error tracking\n * - Supports different log levels (trace, debug, info, log, warn, error)\n * - Handles error contexts for better error tracking\n *\n * Used throughout the application for consistent logging and error reporting.\n */\n\nexport class Logger {\n  #environment: Environment | null;\n\n  constructor() {\n    this.#environment = getEnvironment();\n  }\n\n  #callConsole(methodName: 'trace' | 'debug' | 'info' | 'log' | 'warn' | 'error', args: any[]) {\n    // silence console in production\n    if (this.#environment !== 'prod') {\n      console[methodName](...args); // eslint-disable-line no-console\n    }\n  }\n\n  trace() {\n    this.#callConsole('trace', []);\n\n    getFaro()?.api.pushLog([], {\n      level: LogLevel.TRACE,\n    });\n  }\n\n  debug(...args: any) {\n    this.#callConsole('debug', args);\n\n    getFaro()?.api.pushLog(args, {\n      level: LogLevel.DEBUG,\n    });\n  }\n\n  info(...args: any) {\n    this.#callConsole('info', args);\n\n    getFaro()?.api.pushLog(args, {\n      level: LogLevel.INFO,\n    });\n  }\n\n  log(...args: any) {\n    this.#callConsole('log', args);\n\n    getFaro()?.api.pushLog(args, {\n      level: LogLevel.LOG,\n    });\n  }\n\n  warn(...args: any) {\n    this.#callConsole('warn', args);\n\n    getFaro()?.api.pushLog(args, {\n      level: LogLevel.WARN,\n    });\n  }\n\n  error(error: Error, context?: ErrorContext) {\n    this.#callConsole('error', [error]);\n\n    if (context) {\n      this.#callConsole('error', ['Error context', context]);\n    }\n\n    // does not report an error, but an exception ;)\n    getFaro()?.api.pushError(error, {\n      context,\n    });\n  }\n}\n\nexport const logger = new Logger();\n", "export const GIT_COMMIT = '946548122146823e85bf98cdf38a77ff48b88a3e';\n", "/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.40\n   Copyright © 2012-2024 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.40',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS',\n        SUFFIX_BROWSER = ' Browser';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return map.hasOwnProperty('*') ? map['*'] : str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /\\b(?:mxbrowser|mxios|myie2)\\/?([-\\w\\.]*)\\b/i                       // Maxthon\n            ], [VERSION, [NAME, 'Maxthon']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\\/ ]?([\\w\\.]*)/i,      \n                                                                                // Lunascape/Maxthon/Netfront/Jasmine/Blazer/Sleipnir\n            // Trident based\n            /(avant|iemobile|slim(?:browser|boat|jet))[\\/ ]?([\\d\\.]*)/i,        // Avant/IEMobile/SlimBrowser/SlimBoat/Slimjet\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Blink/Webkit/KHTML based                                         // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ//Vivaldi/DuckDuckGo/Klar/Helio/Dragon\n            /(heytap|ovi|115)browser\\/([\\d\\.]+)/i,                              // HeyTap/Ovi/115\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /quark(?:pc)?\\/([-\\w\\.]+)/i                                         // Quark\n            ], [VERSION, [NAME, 'Quark']], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI' + SUFFIX_BROWSER]], [\n            /fxios\\/([\\w\\.-]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihoobrowser\\/?([\\w\\.]*)/i                                       // 360\n            ], [VERSION, [NAME, '360']], [\n            /\\b(qq)\\/([\\w\\.]+)/i                                                // QQ\n            ], [[NAME, /(.+)/, '$1Browser'], VERSION], [\n            /(oculus|sailfish|huawei|vivo|pico)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1' + SUFFIX_BROWSER], VERSION], [              // Oculus/Sailfish/HuaweiBrowser/VivoBrowser/PicoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345(?=browser|chrome|explorer))\\w*[\\/ ]?v?([\\w\\.]+)/i   // QQ/2345\n            ], [NAME, VERSION], [\n            /(lbbrowser|rekonq)/i,                                              // LieBao Browser/Rekonq\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n            /ome\\/([\\w\\.]+) \\w* ?(iron) saf/i,                                  // Iron\n            /ome\\/([\\w\\.]+).+qihu (360)[es]e/i                                  // 360\n            ], [VERSION, NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /(wolvic|librewolf)\\/([\\w\\.]+)/i                                    // Wolvic/LibreWolf\n            ], [NAME, VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i                                              // Links\n            ], [NAME, [VERSION, /_/g, '.']], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-(?![lr])\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]((?!sm-[lr])[-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Honor\n            /(?:honor)([-\\w ]+)[;\\)]/i\n            ], [MODEL, [VENDOR, 'Honor'], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+; (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo(?!bc)\\w\\w)( bui|\\))/i,                           // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // TCL\n            /droid [\\w\\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\\w*(\\)| bui)/i\n            ], [MODEL, [VENDOR, 'TCL'], [TYPE, TABLET]], [\n\n            // itel\n            /(itel) ((\\w+))/i\n            ], [[VENDOR, lowerize], MODEL, [TYPE, strMapper, { 'tablet' : ['p10001l', 'w7001'], '*' : 'mobile' }]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // Energizer\n            /; (energy ?\\w+)(?: bui|\\))/i,\n            /; energizer ([\\w ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Energizer'], [TYPE, MOBILE]], [\n\n            // Cat\n            /; cat (b35);/i,\n            /; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Cat'], [TYPE, MOBILE]], [\n\n            // Smartfren\n            /((?:new )?andromax[\\w- ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Smartfren'], [TYPE, MOBILE]], [\n\n            // Nothing\n            /droid.+; (a(?:015|06[35]|142p?))/i\n            ], [MODEL, [VENDOR, 'Nothing'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron/Infinix/Tecno/Micromax/Advan\n            /; (imo) ((?!tab)[\\w ]+?)(?: bui|\\))/i,                             // IMO\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(imo) (tab \\w+)/i,                                                 // IMO\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /\\b(sm-[lr]\\d\\d[05][fnuw]?s?)\\b/i                                   // Samsung Galaxy Watch\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, WEARABLE]], [\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // XR\n            ///////////////////\n\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /(pico) (4|neo3(?: link|pro)?)/i                                    // Pico\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /; (quest( \\d| pro)?)/i                                             // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /(arkweb)\\/([\\w\\.]+)/i                                              // ArkWeb\n            ], [NAME, VERSION], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna/Servo\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS/OpenHarmony\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1308__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8146__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1159__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7694__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.amdO = {};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js?_cache=\" + {\"78\":\"aef19aea7ae38d61f33b\",\"256\":\"91a505d2def3511566fa\",\"513\":\"bd6b649be2e9bf43de55\",\"601\":\"4787f18fd19f3b2ec3ea\",\"871\":\"d98efe39b170214658e1\",\"944\":\"641a2488269c99381577\"}[chunkId] + \"\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-metricsdrilldown-app/\";", "\n__webpack_require__.sriHashes = {\"78\":\"*-*-*-CHUNK-SRI-HASH-FXV9QDCMFRcvoSB1ifm26wHkcpW+M=\",\"256\":\"*-*-*-CHUNK-SRI-HASH-tPMrPsWDa4Pd96GjEFyTPRt50P49w=\",\"513\":\"*-*-*-CHUNK-SRI-HASH-eCcyaaEqP2rn6PFCxdqMW7mzdibs4=\",\"601\":\"*-*-*-CHUNK-SRI-HASH-F62nl9OjC4INwun97a7X/CjdDC9Gg=\",\"871\":\"*-*-*-CHUNK-SRI-HASH-e9K/qf2Dm/ZJdY/UopVzoMAYQYASU=\",\"944\":\"*-*-*-CHUNK-SRI-HASH-9Nvrh568fe5XpRS5Cb19/W6QCSzAA=\"};", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_metricsdrilldown_app\"] = self[\"webpackChunkgrafana_metricsdrilldown_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "\nimport amdMetaModule from 'amd-module';\n\n__webpack_public_path__ =\n  amdMetaModule && amdMetaModule.uri\n    ? amdMetaModule.uri.slice(0, amdMetaModule.uri.lastIndexOf('/') + 1)\n    : 'public/plugins/grafana-metricsdrilldown-app/';\n", "// CAUTION: Imports in this file will contribute to the module.tsx bundle size\nimport {\n  PluginExtensionPoints,\n  type PluginExtensionAddedLinkConfig,\n  type PluginExtensionPanelContext,\n} from '@grafana/data';\nimport { type DataQuery } from '@grafana/schema';\n\nimport { PLUGIN_BASE_URL, ROUTES } from '../constants';\nimport { logger } from '../tracking/logger/logger';\n\nconst PRODUCT_NAME = 'Grafana Metrics Drilldown';\nconst title = `Open in ${PRODUCT_NAME}`;\nconst description = `Open current query in the ${PRODUCT_NAME} view`;\nconst category = 'metrics-drilldown';\nconst icon = 'gf-prometheus';\n\nexport const linkConfigs: PluginExtensionAddedLinkConfig[] = [\n  {\n    targets: [PluginExtensionPoints.DashboardPanelMenu, PluginExtensionPoints.ExploreToolbarAction],\n    title,\n    description,\n    icon,\n    category,\n    path: createAppUrl(ROUTES.Drilldown),\n    configure: (context) => {\n      if (typeof context === 'undefined') {\n        return;\n      }\n\n      if ('pluginId' in context && context.pluginId !== 'timeseries') {\n        return;\n      }\n\n      const queries = (context as PluginExtensionPanelContext).targets.filter(isPromQuery);\n\n      if (!queries?.length) {\n        return;\n      }\n\n      const { datasource, expr } = queries[0];\n\n      if (!expr || !(datasource?.type === 'prometheus')) {\n        return;\n      }\n\n      const query = parsePromQueryRegex(expr);\n\n      const timeRange =\n        'timeRange' in context &&\n        typeof context.timeRange === 'object' &&\n        context.timeRange !== null &&\n        'from' in context.timeRange &&\n        'to' in context.timeRange\n          ? (context.timeRange as { from: string; to: string })\n          : undefined;\n\n      const params = appendUrlParameters([\n        [UrlParameters.Metric, query.metric],\n        [UrlParameters.TimeRangeFrom, timeRange?.from],\n        [UrlParameters.TimeRangeTo, timeRange?.to],\n        [UrlParameters.DatasourceId, datasource.uid],\n        ...query.labelFilters.map(\n          (f) => [UrlParameters.Filters, `${f.label}${f.op}${f.value}`] as [UrlParameterType, string]\n        ),\n      ]);\n\n      const pathToMetricView = createAppUrl(ROUTES.Drilldown, params);\n\n      return {\n        path: pathToMetricView,\n      };\n    },\n  },\n];\n\nexport function createAppUrl(route: string, urlParams?: URLSearchParams): string {\n  return `${PLUGIN_BASE_URL}/${route}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\n// We can't use `src/shared.ts` vars here because of the impacts of its imports on the module.tsx bundle size\nexport const UrlParameters = {\n  TimeRangeFrom: 'from',\n  TimeRangeTo: 'to',\n  Metric: 'metric',\n  DatasourceId: `var-ds`,\n  Filters: `var-filters`,\n} as const;\n\nexport type UrlParameterType = (typeof UrlParameters)[keyof typeof UrlParameters];\n\nexport function appendUrlParameters(\n  params: Array<[UrlParameterType, string | undefined]>,\n  initialParams?: URLSearchParams\n): URLSearchParams {\n  const searchParams = new URLSearchParams(initialParams?.toString());\n\n  params.forEach(([key, value]) => {\n    if (value) {\n      searchParams.append(key, value);\n    }\n  });\n\n  return searchParams;\n}\n\ntype PromQuery = DataQuery & { expr: string };\n\nfunction isPromQuery(query: DataQuery): query is PromQuery {\n  return 'expr' in query;\n}\n\ntype PromLabelFilter = { label: string; op: string; value: string };\ntype ParsedPromQuery = { metric: string | undefined; labelFilters: PromLabelFilter[]; query: string };\n\n/**\n * TODO: Replace this with a `@grafana/prometheus` solution after\n * https://github.com/grafana/grafana/issues/99111 is resolved.\n * This will allow us to parse PromQL queries in a more robust way,\n * without compromising our `module.tsx` bundle size.\n *\n * Parses a PromQL query string using regular expressions to extract the\n * metric name and label filters. This is a lightweight alternative to\n * heavier parsing libraries.\n *\n * Note: This parser is simplified and may not cover all complex PromQL syntaxes,\n * especially nested functions or advanced selectors. It prioritizes common cases.\n */\nexport function parsePromQueryRegex(query: string): ParsedPromQuery {\n  let metric: string | undefined = undefined;\n  const labelFilters: PromLabelFilter[] = [];\n  const trimmedQuery = query.trim();\n\n  // First, try to find the metric name and labels in the most common format\n  const queryMatch = trimmedQuery.match(/^([a-zA-Z_:][a-zA-Z0-9_:]*)\\s*(?:\\{([^}]*)\\})?/);\n\n  if (queryMatch) {\n    const potentialMetric = queryMatch[1];\n    const labelsContent = queryMatch[2];\n\n    // Check if this is a function call\n    const nextCharIndex = queryMatch[0].length;\n    const nextChar = trimmedQuery[nextCharIndex];\n    const isLikelyFunctionCall = nextChar === '(';\n\n    if (!(isLikelyFunctionCall && knownKeywords.has(potentialMetric))) {\n      metric = potentialMetric;\n    }\n\n    // Parse labels if content exists\n    if (labelsContent !== undefined) {\n      parseLabels(labelsContent, labelFilters);\n    }\n  }\n\n  // If we didn't find a metric or it was a function call, try to find the metric inside the query\n  if (!metric) {\n    // Look for the first occurrence of a metric pattern that's not a known keyword\n    const metricPattern = /([a-zA-Z_:][a-zA-Z0-9_:]*)\\s*(?:\\{([^}]*)\\})?/g;\n    let match;\n    while ((match = metricPattern.exec(trimmedQuery)) !== null) {\n      const potentialMetric = match[1];\n      const labelsContent = match[2];\n\n      if (!knownKeywords.has(potentialMetric)) {\n        metric = potentialMetric;\n        if (labelsContent) {\n          parseLabels(labelsContent, labelFilters);\n        }\n        break;\n      }\n    }\n  }\n\n  return { metric, labelFilters, query };\n}\n\nfunction parseLabels(labelsContent: string, labelFilters: PromLabelFilter[]): void {\n  const labelParts = labelsContent.split(',');\n  const labelRegex = /^\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*([=!~]+)\\s*\"((?:[^\"\\\\]|\\\\.)*)\"\\s*$/;\n\n  labelParts.forEach((part) => {\n    if (part.trim() === '') {\n      return;\n    }\n    const match = part.match(labelRegex);\n    if (match) {\n      const unescapedValue = match[3].replace(/\\\\(.)/g, '$1');\n      labelFilters.push({ label: match[1], op: match[2], value: unescapedValue });\n    } else {\n      logger.warn(`[Metrics Drilldown] Could not parse label part: \"${part}\" for labels: ${labelsContent}`);\n    }\n  });\n}\n\nconst knownKeywords = new Set([\n  'rate',\n  'increase',\n  'sum',\n  'avg',\n  'count',\n  'max',\n  'min',\n  'stddev',\n  'stdvar',\n  'topk',\n  'bottomk',\n  'quantile',\n  'histogram_quantile',\n  'label_replace',\n  'label_join',\n  'vector',\n  'scalar',\n  'time',\n  'timestamp',\n  'month',\n  'year',\n  'day_of_month',\n  'day_of_week',\n  'days_in_month',\n  'hour',\n  'minute',\n  'by',\n  'without',\n  'on',\n  'ignoring',\n  'group_left',\n  'group_right',\n]);\n", "import { AppPlugin, type AppRootProps } from '@grafana/data';\nimport { LoadingPlaceholder } from '@grafana/ui';\nimport React, { lazy, Suspense } from 'react';\n\nimport { linkConfigs } from 'extensions/links';\n\nconst LazyApp = lazy(async () => {\n  const { wasmSupported } = await import('./services/sorting');\n  const { default: initOutlier } = await import('@bsull/augurs/outlier');\n\n  if (wasmSupported()) {\n    await initOutlier();\n    console.info('WASM supported');\n  }\n\n  return import('./App/App');\n});\n\nconst App = (props: AppRootProps) => (\n  <Suspense fallback={<LoadingPlaceholder text=\"\" />}>\n    <LazyApp {...props} />\n  </Suspense>\n);\n\nexport const plugin = new AppPlugin<{}>().setRootPage(App);\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "LogLevel", "defaultLogLevel", "LOG", "allLogLevels", "TRACE", "DEBUG", "INFO", "WARN", "ERROR", "PLUGIN_ID", "pluginJson", "PLUGIN_BASE_URL", "ROUTES", "Home", "Trail", "Drilldown", "TransportItemType", "transportItemTypeToBodyKey", "EXCEPTION", "MEASUREMENT", "EVENT", "isTypeof", "value", "type", "isToString", "Object", "prototype", "toString", "call", "isInstanceOf", "reference", "err", "isNull", "isString", "isNumber", "isNaN", "isObject", "isFunction", "isArray", "isEventDefined", "Event", "isErrorDefined", "Error", "isError", "isEmpty", "length", "keys", "stringifyExternalJson", "json", "JSON", "stringify", "valueSeen", "WeakSet", "_key", "has", "add", "getCircularDependencyReplacer", "stringifyObjectValues", "obj", "o", "key", "entries", "String", "dateNow", "Date", "now", "getCurrentTimestamp", "toISOString", "timestampToIsoString", "deepEqual", "a", "b", "aIsArray", "bIsArray", "idx", "aIsObject", "bIsObject", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "a<PERSON><PERSON>", "includes", "defaultExceptionType", "defaultErrorArgsSerializer", "args", "map", "arg", "join", "stacktraceParser", "initializeExceptionsAPI", "_unpatchedConsole", "internalLogger", "config", "metas", "transports", "tracesApi", "_a", "debug", "lastPayload", "parseStacktrace", "changeStacktraceParser", "newStacktraceParser", "ignoreErrors", "getStacktraceParser", "pushError", "error", "skip<PERSON><PERSON><PERSON>", "stackFrames", "context", "spanContext", "timestampOverwriteMs", "message", "name", "stack", "patterns", "msg", "some", "pattern", "match", "isErrorIgnored", "item", "meta", "payload", "timestamp", "trace", "trace_id", "traceId", "span_id", "spanId", "getTraceContext", "assign", "parseCause", "frames", "undefined", "stacktrace", "testingPayload", "stackTrace", "dedupe", "execute", "cause", "defaultLogArgsSerializer", "initializeAPI", "unpatchedConsole", "_config", "otel", "getOTEL", "ctx", "getSpanContext", "active", "initOTEL", "isOTELInitialized", "pushTraces", "initializeTracesAPI", "metaSession", "metaUser", "metaView", "metaPage", "setUser", "user", "remove", "setSession", "session", "options", "newOverrides", "overrides", "getSession", "getPage", "page", "resetUser", "resetSession", "<PERSON><PERSON><PERSON><PERSON>", "view", "previousView", "get<PERSON>iew", "setPage", "pageMeta", "id", "initializeMetaAPI", "logArgsSerializer", "pushLog", "level", "initializeLogsAPI", "pushMeasurement", "values", "initializeMeasurementsAPI", "pushEvent", "attributes", "domain", "eventDomain", "initializeEventsAPI", "noop", "InternalLoggerLevel", "defaultInternalLogger", "info", "prefix", "warn", "defaultInternalLoggerLevel", "defaultUnpatchedConsole", "console", "createInternalLogger", "internalLoggerLevel", "OFF", "VERBOSE", "initializeInternalLogger", "globalThis", "g", "self", "VERSION", "faro", "registerFaro", "api", "instrumentations", "pause", "unpause", "isolate", "defineProperty", "configurable", "enumerable", "writable", "setInternalFaroOnGlobalObject", "preventGlobalExposure", "globalObjectKey", "setFaroOnGlobalObject", "BatchExecutor", "constructor", "sendFn", "_b", "this", "signalBuffer", "itemLimit", "sendTimeout", "paused", "flushInterval", "start", "document", "addEventListener", "visibilityState", "flush", "addItem", "push", "window", "setInterval", "clearInterval", "groupItems", "items", "itemMap", "Map", "for<PERSON>ach", "metaKey", "currentItems", "get", "set", "Array", "from", "initializeTransports", "beforeSendHooks", "applyBeforeSendHooks", "filteredItems", "hook", "modified", "filter", "Boolean", "batchedSend", "transport", "isBatched", "send", "batchExecutor", "batching", "enabled", "newTransports", "newTransport", "existingTransport", "addBeforeSendHooks", "newBeforeSendHooks", "beforeSendHook", "getBeforeSendHooks", "every", "filteredItem", "instantSend", "isPaused", "transportsToRemove", "transportToRemove", "existingTransportIndex", "indexOf", "splice", "removeBeforeSendHooks", "beforeSendHooksToRemove", "initializeUnpatchedConsole", "initializeFaro", "listeners", "getValue", "reduce", "acc", "notifyListeners", "listener", "newItems", "itemsToRemove", "currentItem", "addListener", "removeListener", "currentListener", "initializeMetas", "newInstrumentations", "newInstrumentation", "existingInstrumentation", "initialize", "instrumentationsToRemove", "instrumentationToRemove", "existingInstrumentationIndex", "destroy", "initializeInstrumentations", "initial", "sdk", "version", "app", "bundleId", "appName", "sessionTracking", "registerInitialMetas", "beforeSend", "registerInitialTransports", "registerInitialInstrumentations", "defaultGlobalObjectKey", "defaultBatchingConfig", "defaultEventDomain", "newLineString", "evalString", "unknownSymbolString", "atString", "webkitLineRegex", "webkitEvalRegex", "webkitEvalString", "webkitAddressAtString", "webkitAddressAtStringLength", "firefoxLineRegex", "firefoxEvalRegex", "firefoxEvalString", "safariExtensionString", "safariWebExtensionString", "reactMinifiedRegex", "buildStackFrame", "filename", "func", "lineno", "colno", "stackFrame", "location", "href", "function", "getDataFromSafariExtensions", "isSafariExtension", "isSafariWebExtension", "split", "getStackFramesFromError", "lines", "_line", "line", "parts", "exec", "startsWith", "submatch", "substring", "columnNumber", "Number", "test", "slice", "STORAGE_KEY", "SESSION_INACTIVITY_TIME", "defaultSessionTrackingConfig", "persistent", "maxSessionPersistenceTime", "unknownString", "browserMeta", "parser", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "osName", "osVersion", "getOS", "userAgent", "getUA", "language", "navigator", "mobile", "brands", "userAgentData", "getBrands", "browser", "os", "viewportWidth", "innerWidth", "viewportHeight", "innerHeight", "k6Meta", "k6Properties", "k6", "isK6Browser", "testRunId", "currentHref", "pageId", "createPageMeta", "generatePageId", "initialPageMeta", "locationHref", "url", "BaseExtension", "logDebug", "logInfo", "log<PERSON>arn", "logError", "BaseTransport", "getIgnoreUrls", "mergeResourceSpans", "traces", "resourceSpans", "currentResource", "currentSpans", "scopeSpans", "newSpans", "throttle", "callback", "delay", "lastPending", "timeout<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "webStorageType", "isWebStorageAvailable", "storage", "testItem", "setItem", "removeItem", "getItem", "webStorageMechanism", "isWebStorageTypeAvailable", "isLocalStorageAvailable", "isSessionStorageAvailable", "alphabet", "genShortID", "Math", "floor", "random", "EVENT_SESSION_START", "EVENT_SESSION_RESUME", "EVENT_OVERRIDES_SERVICE_NAME", "isSampled", "_c", "samplingRate", "sampler", "createUserSessionObject", "sessionId", "started", "lastActivity", "generateSessionId", "isUserSessionValid", "getUserSessionUpdater", "fetchUserSession", "storeUserSession", "forceSessionExtend", "sessionTrackingConfig", "isPersistentSessions", "sessionFromStorage", "newSession", "addSessionMetadataToNextSession", "sessionMeta", "onSessionChange", "previousSession", "_d", "_e", "_f", "_g", "sessionWithMeta", "previousSessionId", "getSessionMetaUpdateHandler", "sessionFromSessionStorage", "sessionAttributes", "sessionOverrides", "storedSessionMeta", "storedSessionMetaOverrides", "hasSessionOverridesChanged", "hasAttributesChanged", "userSession", "storedSessionOverrides", "serviceName", "previousServiceName", "sendOverrideEvent", "PersistentSessionsManager", "updateSession", "updateUserSession", "init", "removeUserSession", "storageTypeLocal", "storedSession", "parse", "VolatileSessionsManager", "storageTypeSession", "getSessionManagerByConfig", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__rest", "s", "t", "p", "hasOwnProperty", "getOwnPropertySymbols", "i", "propertyIsEnumerable", "FetchTransport", "super", "disabledUntil", "rateLimitBackoffMs", "defaultRateLimitBackoffMs", "getNow", "<PERSON><PERSON><PERSON><PERSON>", "size", "concurrency", "buffer", "work", "producer", "shift", "reason", "promiseProducer", "createPromiseBuffer", "bufferSize", "body", "bk", "signals", "getTransportBody", "requestOptions", "<PERSON><PERSON><PERSON><PERSON>", "headers", "restOfRequestOptions", "fetch", "method", "keepalive", "response", "status", "extendFaroSession", "getRetryAfterDate", "text", "catch", "concat", "ignoreUrls", "retryAfterHeader", "date", "SessionExpiredString", "BaseInstrumentation", "arguments", "valueTypeRegex", "getErrorDetails", "evt", "isDomErrorRes", "isEventRes", "isErrorEvent", "isDomError", "isDomException", "isEvent", "getDetailsFromErrorArgs", "source", "eventIsString", "initialStackFrame", "groups", "getValueAndTypeFromMessage", "getDetailsFromConsoleErrorArgs", "serializer", "registerOnunhandledrejection", "detail", "isPrimitive", "ErrorsInstrumentation", "oldOnerror", "onerror", "registerOnerror", "n", "r", "persisted", "timeStamp", "c", "performance", "getEntriesByType", "responseStart", "u", "activationStart", "f", "prerendering", "wasDiscarded", "replace", "rating", "delta", "navigationType", "PerformanceObserver", "supportedEntryTypes", "getEntries", "observe", "buffered", "d", "l", "requestAnimationFrame", "v", "m", "h", "T", "y", "removeEventListener", "E", "firstHiddenTime", "C", "S", "disconnect", "startTime", "max", "reportAllChanges", "L", "A", "I", "M", "interactionId", "min", "k", "interactionCount", "F", "durationThreshold", "D", "x", "R", "H", "q", "entryType", "duration", "latency", "sort", "delete", "O", "requestIdleCallback", "N", "_", "z", "J", "K", "readyState", "U", "passive", "capture", "V", "W", "Z", "X", "target", "cancelable", "processingStart", "Y", "$", "WebVitalsBasic", "webVitalConfig", "mapping", "indicator", "executor", "metric", "cls", "hadRecentInput", "takeRecords", "fcp", "fid", "inp", "PerformanceEventTiming", "B", "clear", "lcp", "once", "ttfb", "domInteractive", "domContentLoadedEventStart", "domComplete", "nodeName", "nodeType", "toLowerCase", "toUpperCase", "classList", "trim", "parentNode", "WeakMap", "Set", "nt", "processingEnd", "abs", "renderTime", "tt", "et", "it", "at", "ct", "ut", "st", "dt", "lt", "mt", "gt", "pt", "vt", "ht", "yt", "Tt", "eventTarget", "eventType", "eventTime", "eventEntry", "loadState", "attribution", "NAVIGATION_ID_STORAGE_KEY", "loadStateKey", "timeToFirstByteKey", "WebVitalsWithAttribution", "corePushMeasurement", "measureCLS", "measureFCP", "measureFID", "measureINP", "measureLCP", "measureTTFB", "sources", "find", "node", "largestShiftTarget", "largestShiftTime", "largestShiftValue", "largestShiftSource", "largestShiftEntry", "buildInitialValues", "addIfPresent", "buildInitialContext", "timeToFirstByte", "firstByteToFCP", "navigationEntry", "fcpEntry", "interaction<PERSON>arget", "interactionTargetElement", "interactionType", "interactionTime", "nextPaintTime", "processedEventEntries", "longAnimationFrameEntries", "inputDelay", "processingDuration", "presentationDelay", "resourceLoadDelay", "resourceLoadDuration", "elementRenderDelay", "requestStart", "responseEnd", "element", "lcpEntry", "lcpResourceEntry", "waitingDuration", "cacheDuration", "dnsDuration", "connectionDuration", "requestDuration", "workerStart", "fetchStart", "domainLookupStart", "connectStart", "connectEnd", "navigationEntryId", "navigation_type", "navigation_entry_id", "WebVitalsInstrumentation", "intializeWebVitalsInstrumentation", "trackWebVitalsAttribution", "webVitalsInstrumentation", "SessionInstrumentation", "sendSessionStartEvent", "notifiedSession", "createInitialSession", "Session<PERSON>anager", "sessionsConfig", "lifecycleType", "initialSession", "storedUserSession", "storedUserSessionMeta", "createSession", "registerBeforeSendHook", "newItem", "newAttributes", "initialSessionMeta", "bind", "ViewInstrumentation", "sendViewChangedEvent", "notifiedView", "fromView", "to<PERSON>ie<PERSON>", "w3cTraceparentFormat", "getSpanContextFromServerTiming", "serverTimings", "serverEntry", "description", "entryUrlIsIgnored", "ignoredUrls", "entryName", "includePerformanceEntry", "performanceEntryJSON", "allowProps", "allowPropKey", "allowPropValue", "perfEntryPropVal", "createFaroResourceTiming", "resourceEntryRaw", "decodedBodySize", "domainLookupEnd", "encodedBodySize", "initiatorType", "nextHopProtocol", "redirectEnd", "redirectStart", "renderBlockingStatus", "rbs", "responseStatus", "secureConnectionStart", "transferSize", "toFaroPerformanceTimingString", "tcpHandshakeTime", "dnsLookupTime", "tlsNegotiationTime", "redirectTime", "requestTime", "responseTime", "fetchTime", "serviceWorkerTime", "cacheHitStatus", "cacheType", "getCacheType", "protocol", "createFaroNavigationTiming", "navigationEntryRaw", "domContentLoadedEventEnd", "loadEventEnd", "loadEventStart", "parserStart", "timing", "domLoading", "<PERSON><PERSON><PERSON><PERSON>", "getDocumentParsingTime", "pageLoadTime", "documentParsingTime", "domProcessingTime", "domContentLoadHandlerTime", "onLoadTime", "round", "DEFAULT_TRACK_RESOURCES", "PerformanceInstrumentation", "handleReady", "readyStateCompleteHandler", "onDocumentReady", "faroNavigationId", "faroNavigationEntryResolve", "faroNavigationEntryPromise", "observedEntries", "navEntryJson", "toJSON", "serverTiming", "faroPreviousNavigationId", "faroNavigationEntry", "getNavigationTimings", "trackResources", "resourceEntryJson", "faroResourceEntry", "faroResourceId", "observeResourceTimings", "flatMap", "ConsoleInstrumentation", "errorSerializer", "consoleInstrumentation", "serializeErrors", "disabledLevels", "defaultDisabledLevels", "consoleErrorAsLog", "consoleErrorPrefix", "getWebInstrumentations", "enablePerformanceInstrumentation", "unshift", "captureConsole", "captureConsoleDisabledLevels", "createDefaultMetas", "browserConfig", "pageTracking", "initialMetas", "crateSessionMeta", "geoLocationTracking", "geoLocationTrackingEnabled", "coreConfig", "makeCoreConfig", "FARO_ENVIRONMENTS", "environment", "faroUrl", "getFaro", "setFaro", "instance", "initFaro", "faroEnvironment", "getEnvironment", "getFaroEnvironment", "apps", "bootData", "appRelease", "userEmail", "email", "release", "GIT_COMMIT", "event", "MATCHERS", "regExp", "host", "found", "methodName", "logger", "log", "FUNC_TYPE", "UNDEF_TYPE", "OBJ_TYPE", "STR_TYPE", "MAJOR", "MODEL", "NAME", "TYPE", "VENDOR", "ARCHITECTURE", "CONSOLE", "MOBILE", "TABLET", "SMARTTV", "WEARABLE", "EMBEDDED", "AMAZON", "APPLE", "ASUS", "BLACKBERRY", "BROWSER", "CHROME", "FIREFOX", "GOOGLE", "HUAWEI", "LG", "MICROSOFT", "MOTOROLA", "OPERA", "SAMSUNG", "SHARP", "SONY", "XIAOMI", "ZEBRA", "FACEBOOK", "CHROMIUM_OS", "MAC_OS", "SUFFIX_BROWSER", "enumerize", "arr", "enums", "str1", "str2", "lowerize", "str", "len", "rgxMapper", "ua", "arrays", "j", "matches", "regex", "props", "strMapper", "windowsVersionMap", "regexes", "cpu", "device", "engine", "EDGE", "extensions", "getResult", "_navigator", "_ua", "_uach", "_rgxmap", "mergedRegexes", "extend", "_isSelfNav", "_browser", "brave", "isBrave", "getCPU", "_cpu", "getDevice", "_device", "standalone", "maxTouchPoints", "getEngine", "_engine", "_os", "platform", "setUA", "CPU", "DEVICE", "ENGINE", "OS", "module", "exports", "j<PERSON><PERSON><PERSON>", "Zepto", "prop", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__1308__", "__WEBPACK_EXTERNAL_MODULE__8146__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__1159__", "__WEBPACK_EXTERNAL_MODULE__7694__", "__WEBPACK_EXTERNAL_MODULE__1269__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "loaded", "__webpack_modules__", "amdO", "getter", "__esModule", "getPrototypeOf", "mode", "ns", "create", "def", "current", "getOwnPropertyNames", "definition", "chunkId", "all", "promises", "Function", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "origin", "crossOrigin", "integrity", "sri<PERSON><PERSON><PERSON>", "onScriptComplete", "prev", "onload", "clearTimeout", "doneFns", "<PERSON><PERSON><PERSON><PERSON>", "fn", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "nmd", "paths", "children", "baseURI", "installedChunks", "installedChunkData", "promise", "errorType", "realSrc", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "chunkIds", "moreModules", "runtime", "chunkLoadingGlobal", "lastIndexOf", "PRODUCT_NAME", "title", "linkConfigs", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "ExploreToolbarAction", "icon", "category", "path", "createAppUrl", "configure", "pluginId", "queries", "isPromQuery", "datasource", "expr", "query", "labelFilters", "<PERSON><PERSON><PERSON><PERSON>", "queryMatch", "potentialMetric", "labelsContent", "knownKeywords", "parse<PERSON><PERSON><PERSON>", "metricPattern", "parsePromQueryRegex", "timeRange", "params", "initialParams", "searchParams", "URLSearchParams", "append", "appendUrlParameters", "UrlParameters", "Metric", "TimeRangeFrom", "TimeRangeTo", "to", "DatasourceId", "uid", "Filters", "label", "op", "route", "urlParams", "labelParts", "labelRegex", "part", "unescapedValue", "LazyApp", "lazy", "wasmSupported", "default", "initOutlier", "plugin", "AppPlugin", "setRootPage", "Suspense", "fallback", "LoadingPlaceholder", "linkConfig", "addLink"], "sourceRoot": ""}