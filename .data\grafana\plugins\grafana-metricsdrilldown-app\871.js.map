{"version": 3, "file": "871.js?_cache=d98efe39b170214658e1", "mappings": "mOAAO,MAIMA,EAAY,CAAC,EC8G1B,IA1FO,IAAIC,EAAwB,CAE/BC,YAAa,6DAEbC,SAAU,CACNC,YAAa,KAEjBC,SAAU,CACN,CAAC,IAAK,KACN,CAAC,IAAK,KACN,CAAC,IAAK,MAEVC,iBAAkB,CACd,CAAEC,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAMC,MAAO,MAEzBC,iBAAkB,CACd,CAAEF,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAKC,MAAO,KACpB,CAAED,KAAM,IAAMC,MAAO,KACrB,CAAED,KAAM,IAAKC,MAAO,MAExBE,QAAS,CAAC,GAIVC,EAAe,CACf,MACA,MACA,MACA,MACA,QACA,SACA,SACA,QACA,eACA,UACA,OACA,YA8CAC,EAAuB,GAClBC,EAAK,EAAGC,EAAiBH,EAAcE,EAAKC,EAAeC,OAAQF,IAAM,CAC9E,IAAIG,EAAMF,EAAeD,GACzBD,EAAqBK,KAAKD,EAAM,aACpC,CAGA,IAAIE,EAAiB,CACjB,KACA,WACA,cACA,aACA,KACA,WAGAC,EAAsB,IAAMD,EAAeE,QAAO,SAAUC,EAAMC,GAAQ,OAAOD,EAAO,IAAMC,CAAM,IAAK,IAczGC,EAAWZ,EAAaa,OAxEZ,CACZ,MACA,SACA,OACA,UACA,YACA,YACA,eACA,cACA,gBACA,QACA,QACA,MACA,QACA,qBACA,eACA,OACA,SACA,WACA,QACA,aACA,gBACA,KACA,OACA,QACA,SACA,QACA,iBACA,OACA,SACA,QACA,SACA,OACA,YACA,OACA,OACA,YACA,SACA,SAkC0CA,OAAOZ,GAAsBY,OAAON,GAAgBM,OAJ7E,CACjB,WAKOC,EAAW,CAClBC,YAAY,EACZC,aAAc,GACdC,aAAc,UACdL,SAAUA,EACVM,UAlBY,CACZ,IAAK,IAAK,IAAK,IAAK,IAAK,IACzB,KAAM,KAAM,IAAK,IAAK,KAAM,KAC5B,MAAO,KAAM,UAgBbX,eAAgBC,EAEhBW,QAAS,uBACTC,QAAS,wEACTC,OAAQ,cACRC,YAAa,oBACbC,aAAc,oBACdC,UAAW,iCACXC,cAAe,mCACfC,YAAa,UAEbC,UAAW,CACPC,KAAM,CAEF,CAAC,2BAA4B,OAAQ,YAErC,CAAC,+BAAgC,OAEjC,CAAC,UAAW,WAEZ,CAAC,eAAgB,CACTC,MAAO,CACH,YAAa,OACb,WAAY,gBAIxB,CAAC,kBAAmB,kBACpB,CAAC,kBAAmB,kBACpB,CAAC,IAAK,SAAU,kBAChB,CAAC,IAAK,SAAU,kBAChB,CAAC,IAAK,SAAU,oBAEhB,CAAEC,QAAS,eAEX,CAAC,aAAc,aACf,CAAC,mBAAoB,aACrB,CAAC,WAAY,CACLD,MAAO,CACH,aAAc,YACd,WAAY,MAIxB,CAAC,cAAe,UAChB,CAAC,uCAAwC,gBACzC,CAAC,yCAA0C,gBAC3C,CAAC,gDAAiD,cAClD,CAAC,gCAAiC,gBAClC,CAAC,oCAAqC,iBACtC,CAAC,6BAA8B,UAC/B,CAAC,qBAAsB,WAE3BE,cAAe,CACX,CAAC,UAAW,UACZ,CAAC,WAAY,iBACb,CAAC,MAAO,yBACR,CAAC,IAAK,SAAU,SAEpBC,cAAe,CACX,CAAC,UAAW,UACZ,CAAC,WAAY,iBACb,CAAC,MAAO,yBACR,CAAC,IAAK,SAAU,SAEpBC,gBAAiB,CACb,CAAC,WAAY,UACb,CAAC,WAAY,iBACb,CAAC,MAAO,yBACR,CAAC,IAAK,SAAU,SAEpBC,QAAS,CACL,CAAC,SAAU,OACX,CAAC,KAAM,aAAc,SAEzBC,WAAY,CACR,CAAC,aAAc,YAKhBC,EAAyB,CAChCC,uBAAwB,WAWpB,MAAO,CAAEC,YARS1B,EAAS2B,KAAI,SAAUC,GACrC,MAAO,CACHC,MAAOD,EACPE,KAAMrD,EAAUsD,mBAAmBC,QACnCC,WAAYL,EACZM,gBAAiBzD,EAAU0D,6BAA6BC,gBAEhE,IAEJ,E", "sources": ["webpack://grafana-metricsdrilldown-app/./stubs/monaco-editor.ts", "webpack://grafana-metricsdrilldown-app/../node_modules/monaco-promql/promql/promql.js"], "sourcesContent": ["export const editor = {\n  IEditorContribution: {},\n};\n\nexport const languages = {};\n", "// The MIT License (MIT)\n//\n// Copyright (c) <PERSON><PERSON> and <PERSON><PERSON> @ Amadeus IT Group\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\n'use strict';\nimport { languages } from \"monaco-editor\";\n// noinspection JSUnusedGlobalSymbols\nexport var languageConfiguration = {\n    // the default separators except `@$`\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^`~!#%^&*()\\-=+\\[{\\]}\\\\|;:'\",.<>\\/?\\s]+)/g,\n    // Not possible to make comments in PromQL syntax\n    comments: {\n        lineComment: '#',\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')'],\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: '\\'', close: '\\'' },\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: '\\'', close: '\\'' },\n        { open: '<', close: '>' },\n    ],\n    folding: {}\n};\n// PromQL Aggregation Operators\n// (https://prometheus.io/docs/prometheus/latest/querying/operators/#aggregation-operators)\nvar aggregations = [\n    'sum',\n    'min',\n    'max',\n    'avg',\n    'group',\n    'stddev',\n    'stdvar',\n    'count',\n    'count_values',\n    'bottomk',\n    'topk',\n    'quantile',\n];\n// PromQL functions\n// (https://prometheus.io/docs/prometheus/latest/querying/functions/)\nvar functions = [\n    'abs',\n    'absent',\n    'ceil',\n    'changes',\n    'clamp_max',\n    'clamp_min',\n    'day_of_month',\n    'day_of_week',\n    'days_in_month',\n    'delta',\n    'deriv',\n    'exp',\n    'floor',\n    'histogram_quantile',\n    'holt_winters',\n    'hour',\n    'idelta',\n    'increase',\n    'irate',\n    'label_join',\n    'label_replace',\n    'ln',\n    'log2',\n    'log10',\n    'minute',\n    'month',\n    'predict_linear',\n    'rate',\n    'resets',\n    'round',\n    'scalar',\n    'sort',\n    'sort_desc',\n    'sqrt',\n    'time',\n    'timestamp',\n    'vector',\n    'year',\n];\n// PromQL specific functions: Aggregations over time\n// (https://prometheus.io/docs/prometheus/latest/querying/functions/#aggregation_over_time)\nvar aggregationsOverTime = [];\nfor (var _i = 0, aggregations_1 = aggregations; _i < aggregations_1.length; _i++) {\n    var agg = aggregations_1[_i];\n    aggregationsOverTime.push(agg + '_over_time');\n}\n// PromQL vector matching + the by and without clauses\n// (https://prometheus.io/docs/prometheus/latest/querying/operators/#vector-matching)\nvar vectorMatching = [\n    'on',\n    'ignoring',\n    'group_right',\n    'group_left',\n    'by',\n    'without',\n];\n// Produce a regex matching elements : (elt1|elt2|...)\nvar vectorMatchingRegex = \"(\" + vectorMatching.reduce(function (prev, curr) { return prev + \"|\" + curr; }) + \")\";\n// PromQL Operators\n// (https://prometheus.io/docs/prometheus/latest/querying/operators/)\nvar operators = [\n    '+', '-', '*', '/', '%', '^',\n    '==', '!=', '>', '<', '>=', '<=',\n    'and', 'or', 'unless',\n];\n// PromQL offset modifier\n// (https://prometheus.io/docs/prometheus/latest/querying/basics/#offset-modifier)\nvar offsetModifier = [\n    'offset',\n];\n// Merging all the keywords in one list\nvar keywords = aggregations.concat(functions).concat(aggregationsOverTime).concat(vectorMatching).concat(offsetModifier);\n// noinspection JSUnusedGlobalSymbols\nexport var language = {\n    ignoreCase: false,\n    defaultToken: '',\n    tokenPostfix: '.promql',\n    keywords: keywords,\n    operators: operators,\n    vectorMatching: vectorMatchingRegex,\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/^%]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    digits: /\\d+(_+\\d+)*/,\n    octaldigits: /[0-7]+(_+[0-7]+)*/,\n    binarydigits: /[0-1]+(_+[0-1]+)*/,\n    hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n    integersuffix: /(ll|LL|u|U|l|L)?(ll|LL|u|U|l|L)?/,\n    floatsuffix: /[fFlL]?/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // 'by', 'without' and vector matching\n            [/@vectorMatching\\s*(?=\\()/, 'type', '@clauses'],\n            // labels\n            [/[a-z_]\\w*(?=\\s*(=|!=|=~|!~))/, 'tag'],\n            // comments\n            [/(^#.*$)/, 'comment'],\n            // all keywords have the same color\n            [/[a-zA-Z_]\\w*/, {\n                    cases: {\n                        '@keywords': 'type',\n                        '@default': 'identifier'\n                    }\n                }],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'([^'\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string_double'],\n            [/'/, 'string', '@string_single'],\n            [/`/, 'string', '@string_backtick'],\n            // whitespace\n            { include: '@whitespace' },\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [/@symbols/, {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }],\n            // numbers\n            [/\\d+[smhdwy]/, 'number'],\n            [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, 'number.float'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, 'number.float'],\n            [/0[xX][0-9a-fA-F']*[0-9a-fA-F](@integersuffix)/, 'number.hex'],\n            [/0[0-7']*[0-7](@integersuffix)/, 'number.octal'],\n            [/0[bB][0-1']*[0-1](@integersuffix)/, 'number.binary'],\n            [/\\d[\\d']*\\d(@integersuffix)/, 'number'],\n            [/\\d(@integersuffix)/, 'number'],\n        ],\n        string_double: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop']\n        ],\n        string_single: [\n            [/[^\\\\']+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/'/, 'string', '@pop']\n        ],\n        string_backtick: [\n            [/[^\\\\`$]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/`/, 'string', '@pop']\n        ],\n        clauses: [\n            [/[^(,)]/, 'tag'],\n            [/\\)/, 'identifier', '@pop']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, 'white'],\n        ],\n    },\n};\n// noinspection JSUnusedGlobalSymbols\nexport var completionItemProvider = {\n    provideCompletionItems: function () {\n        // To simplify, we made the choice to never create automatically the parenthesis behind keywords\n        // It is because in PromQL, some keywords need parenthesis behind, some don't, some can have but it's optional.\n        var suggestions = keywords.map(function (value) {\n            return {\n                label: value,\n                kind: languages.CompletionItemKind.Keyword,\n                insertText: value,\n                insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet\n            };\n        });\n        return { suggestions: suggestions };\n    }\n};\n"], "names": ["languages", "languageConfiguration", "wordPattern", "comments", "lineComment", "brackets", "autoClosingPairs", "open", "close", "surroundingPairs", "folding", "aggregations", "aggregationsOverTime", "_i", "aggregations_1", "length", "agg", "push", "vectorMatching", "vectorMatchingRegex", "reduce", "prev", "curr", "keywords", "concat", "language", "ignoreCase", "defaultToken", "tokenPostfix", "operators", "symbols", "escapes", "digits", "octaldigits", "binarydigits", "hexdigits", "integersuffix", "floatsuffix", "tokenizer", "root", "cases", "include", "string_double", "string_single", "string_backtick", "clauses", "whitespace", "completionItemProvider", "provideCompletionItems", "suggestions", "map", "value", "label", "kind", "CompletionItemKind", "Keyword", "insertText", "insertTextRules", "CompletionItemInsertTextRule", "InsertAsSnippet"], "sourceRoot": ""}