#!/usr/bin/env python3
"""
Test script to check if the circular import issue is fixed
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Test importing the routes module directly
    print("Testing direct import of routes module...")
    from CTFd.plugins.security_monitor.routes import security_monitor_bp
    print("✓ Routes module imported successfully!")
    
    # Test importing the __init__ module
    print("Testing import of __init__ module...")
    from CTFd.plugins.security_monitor import platform_metrics
    print("✓ __init__ module imported successfully!")
    print(f"✓ platform_metrics found: {type(platform_metrics)}")
    
    print("\n🎉 All imports successful! The circular import issue is fixed.")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("The circular import issue still exists.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
