#!/bin/bash
# Check status of all monitoring services

echo "=== CTFd Security Monitoring Status Check ==="
echo

# Detect docker-compose command
if docker compose version >/dev/null 2>&1; then
    DC="docker compose"
else
    DC="docker-compose"
fi

echo "📊 Container Status:"
$DC ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" | grep -E "(NAME|ctfd|grafana|prometheus|loki|promtail|nginx)" || echo "No containers found"

echo
echo "🔍 Service Health Checks:"

# Check CTFd
echo -n "CTFd (port 8000): "
if curl -s -f http://localhost:8000 >/dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not accessible"
fi

# Check Grafana
echo -n "Grafana (port 3000): "
if curl -s -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not accessible"
fi

# Check Prometheus
echo -n "Prometheus (port 9090): "
if curl -s -f http://localhost:9090 >/dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not accessible"
fi

# Check Loki
echo -n "Loki (port 3100): "
if curl -s -f http://localhost:3100/ready >/dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not accessible"
fi

# Check Nginx
echo -n "Nginx (port 82): "
if curl -s -f http://localhost:82 >/dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not accessible"
fi

echo
echo "🔌 Security Monitor Plugin Status:"

# Check if plugin loaded
if $DC logs ctfd 2>&1 | grep -q "Loaded module.*security_monitor"; then
    echo "✅ Security Monitor plugin loaded"
    
    # Check if Prometheus endpoint works
    echo -n "Prometheus metrics endpoint: "
    if curl -s -f http://localhost:8000/admin/plugins/security_monitor/api/prometheus 2>&1 | grep -q "401"; then
        echo "✅ Endpoint exists (requires auth)"
    else
        echo "❌ Endpoint not accessible"
    fi
else
    echo "❌ Security Monitor plugin not loaded"
fi

echo
echo "📈 Prometheus Targets:"
# Check Prometheus targets
curl -s http://localhost:9090/api/v1/targets 2>/dev/null | grep -o '"job":"[^"]*"' | sed 's/"job":"//g' | sed 's/"//g' | while read job; do
    echo "  - $job"
done

echo
echo "🔗 Access URLs:"
echo "  📊 CTFd Admin: http://localhost:8000/admin"
echo "  🔒 Security Monitor: http://localhost:8000/admin/plugins/security_monitor/dashboard"
echo "  📈 Grafana: http://localhost:3000 (admin/admin)"
echo "  📉 Prometheus: http://localhost:9090"
echo "  🗄️ Prometheus Targets: http://localhost:9090/targets"
echo

# Check for recent errors
echo "⚠️ Recent Errors (if any):"
$DC logs --tail 50 2>&1 | grep -i "error\|failed\|permission" | tail -5 || echo "No recent errors found"
