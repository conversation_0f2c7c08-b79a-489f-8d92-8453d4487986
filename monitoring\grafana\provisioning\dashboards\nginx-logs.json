{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "<PERSON>", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"showLabels": false, "showCommonLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false, "showAbsoluteTime": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "maxLines": 100}, "targets": [{"expr": "{job=\"nginx\"} |= \"limiting requests\"", "refId": "A"}], "title": "Nginx Rate Limit Blocks", "type": "logs"}, {"datasource": "<PERSON>", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["count"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(count_over_time({job=\"nginx\"} |= \"limiting requests\" [1h]))", "refId": "A"}], "title": "Rate Limit Blocks (1 hour)", "type": "stat"}, {"datasource": "<PERSON>", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 6, "options": {"showLabels": false, "showCommonLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false, "showAbsoluteTime": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "maxLines": 200}, "targets": [{"expr": "{job=\"nginx\"} |~ \"(4[0-9]{2}|5[0-9]{2})\"", "refId": "A"}], "title": "<PERSON>in<PERSON> Errors (4xx/5xx)", "type": "logs"}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["nginx", "logs", "rate-limiting"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Nginx Access Logs", "uid": "nginx-logs", "version": 0}