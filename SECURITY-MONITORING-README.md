# CTFd Security Monitoring Setup

## Overview

Your CTFd platform has been configured with comprehensive security monitoring using:

1. **Security Monitor Plugin** - Custom CTFd plugin for platform security
2. **Grafana** - Visualization and dashboards (port 3000)
3. **Prometheus** - Metrics collection (port 9090)
4. **Loki + Promtail** - Log aggregation (port 3100)

## Architecture

```
CTFd (port 8000)
├── Security Monitor Plugin
│   ├── Admin Dashboard (/admin/plugins/security_monitor/dashboard)
│   ├── Event Logging
│   ├── Alert Management
│   └── Prometheus Metrics (/admin/plugins/security_monitor/api/prometheus)
│
├── Nginx (port 82)
│   └── Rate Limiting (conf/nginx/security.conf)
│
└── Monitoring Stack
    ├── Prometheus → Collects metrics from CTFd
    ├── Loki → Aggregates logs
    ├── Promtail → Ships logs to Loki
    └── Grafana → Visualizes everything
```

## Access Points

### 1. CTFd Admin Panel
- **URL**: http://localhost:8000/admin
- **Security Monitor**: Look for "Security Monitor" in the admin menu
- **Features**:
  - Real-time security dashboard
  - Event viewer with filtering
  - Alert management
  - Security configuration

### 2. Grafana Dashboards
- **URL**: http://localhost:3000
- **Login**: admin/admin
- **Dashboard**: CTFd Security Dashboard
- **Features**:
  - Active users gauge
  - Request rate graphs
  - Failed login statistics
  - Rate limit violations
  - Response time metrics

### 3. Direct Access URLs
- **Prometheus**: http://localhost:9090
- **Loki**: http://localhost:3100

## What's Monitored

### Platform Security (Non-Intrusive)
✅ Login/registration attempts  
✅ API rate limiting  
✅ Failed authentication  
✅ Request patterns  
✅ Container lifecycle  
✅ Resource usage  

### NOT Monitored (Keeps Challenges Exploitable)
❌ Inside challenge containers  
❌ Challenge exploit attempts  
❌ Container network traffic  
❌ System calls in containers  

## Quick Start

1. **Restart Services**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

2. **Verify Plugin Loading**
   ```bash
   docker-compose logs ctfd | grep "security_monitor"
   ```

3. **Access Admin Panel**
   - Navigate to http://localhost:8000/admin
   - Login with admin credentials
   - Click "Security Monitor" in the menu

4. **Access Grafana**
   - Navigate to http://localhost:3000
   - Login with admin/admin
   - Go to Dashboards → CTFd Security Dashboard

## Troubleshooting

### Plugin Not Showing in Admin Panel?

1. **Check Plugin Loading**
   ```bash
   docker-compose exec ctfd ls -la /opt/CTFd/CTFd/plugins/
   ```

2. **Check Whitelist**
   ```bash
   docker-compose exec ctfd env | grep PLUGIN_WHITELIST
   ```
   Should include: `security_monitor`

3. **Check Logs**
   ```bash
   docker-compose logs ctfd | tail -50
   ```

4. **Restart Container**
   ```bash
   docker-compose restart ctfd
   ```

### Grafana Not Showing Data?

1. **Check Prometheus Targets**
   - Go to http://localhost:9090/targets
   - Verify CTFd target is UP

2. **Check Data Sources in Grafana**
   - Login to Grafana
   - Go to Configuration → Data Sources
   - Test Prometheus and Loki connections

3. **Generate Some Events**
   - Try failed logins
   - Make rapid requests
   - Create containers

## Configuration Files

- **Plugin Config**: `CTFd/plugins/security_monitor/config.py`
- **Nginx Rate Limits**: `conf/nginx/security.conf`
- **Prometheus**: `monitoring/prometheus.yml`
- **Loki**: `monitoring/loki-config.yaml`
- **Promtail**: `monitoring/promtail-config.yaml`
- **Grafana Dashboard**: `monitoring/grafana/provisioning/dashboards/ctfd-security.json`

## Security Thresholds

Default thresholds (configurable in admin panel):

- **Login Rate**: 5 attempts/minute
- **Registration Rate**: 3 attempts/hour
- **API Rate**: 30 requests/minute
- **Global Rate**: 60 requests/minute
- **Failed Login Alert**: 10 failures trigger alert
- **Rate Limit Alert**: 20 violations trigger alert

## Maintenance

### Log Rotation
Logs are automatically rotated:
- Docker logs: 10MB max, 3 files
- Security logs: 7 days retention

### Database Cleanup
Old security events can be cleaned:
```sql
DELETE FROM security_events WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### Monitoring Health
Check monitoring stack:
```bash
docker-compose ps | grep -E "(prometheus|grafana|loki|promtail)"
```

## Additional Features

### Email Alerts (Optional)
Configure in Security Monitor settings:
- Alert email address
- Severity threshold
- Auto-blocking settings

### Custom Alerts
Add custom Grafana alerts:
1. Edit dashboard in Grafana
2. Add alert rules to panels
3. Configure notification channels

## Support

For issues:
1. Check container logs
2. Verify configurations
3. Test individual components
4. Review this documentation

The security monitoring is designed to protect your CTF platform while keeping challenges fully exploitable!
