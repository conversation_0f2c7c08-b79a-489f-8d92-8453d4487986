{% extends "admin/plugins.html" %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1>Security Monitor</h1>
        
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Total Requests</h5>
                        <h2 id="total-requests">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Active Users</h5>
                        <h2 id="active-users">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Failed Logins</h5>
                        <h2 id="failed-logins">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Containers</h5>
                        <h2 id="container-count">0</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Requests Per Minute</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="requests-chart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Top Endpoints</h5>
                    </div>
                    <div class="card-body">
                        <div id="top-endpoints"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Alerts -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Security Alerts</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Type</th>
                                    <th>IP</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody id="alerts-table">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart setup
const ctx = document.getElementById('requests-chart').getContext('2d');
const requestsChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [],
        datasets: [{
            label: 'Requests per Minute',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Update function
function updateDashboard() {
    fetch('/admin/monitor/api/metrics')
        .then(response => response.json())
        .then(data => {
            // Update stats
            document.getElementById('total-requests').textContent = data.total_requests;
            document.getElementById('active-users').textContent = data.active_users;
            document.getElementById('failed-logins').textContent = data.failed_logins_total;
            document.getElementById('container-count').textContent = data.container_count;
            
            // Update chart
            if (data.requests_per_minute.length > 0) {
                const labels = data.requests_per_minute.map(item => new Date(item[0] * 60000).toLocaleTimeString());
                const values = data.requests_per_minute.map(item => item[1]);
                
                requestsChart.data.labels = labels;
                requestsChart.data.datasets[0].data = values;
                requestsChart.update();
            }
            
            // Update top endpoints
            const endpointsHtml = data.top_endpoints.map(([endpoint, count]) => 
                `<div class="d-flex justify-content-between">
                    <span>${endpoint}</span>
                    <span class="badge badge-primary">${count}</span>
                </div>`
            ).join('');
            document.getElementById('top-endpoints').innerHTML = endpointsHtml;
            
            // Update alerts
            const alertsHtml = data.alerts.slice(-10).reverse().map(alert => 
                `<tr>
                    <td>${new Date(alert.timestamp).toLocaleString()}</td>
                    <td><span class="badge badge-warning">${alert.type}</span></td>
                    <td>${alert.ip}</td>
                    <td>${JSON.stringify(alert.details)}</td>
                </tr>`
            ).join('');
            document.getElementById('alerts-table').innerHTML = alertsHtml;
        });
}

// Update every 5 seconds
updateDashboard();
setInterval(updateDashboard, 5000);
</script>
{% endblock %}
